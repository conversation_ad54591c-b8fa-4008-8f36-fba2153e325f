client_id: 1 # 编号
app:
  name: "company_center" #应用名
  env: "test" # dev test or production
  domain: "testplatform.smm.cn"
  url_prefix: "conferencecenter"
server:
  http:
    public: #外网端口
      ip: 0.0.0.0
      port: 8038
    inner: #内网端口
      ip: 0.0.0.0
      port: 8039
log:
  level: "DEBUG"
data:
  mysql:
    ip: "********"
    port: "3306"
    database: "conference_center"
    user_name: "root"
    password: "smmdb2016"
    maxconn: 50
    trace_on: true
  mysql_event:
    ip: "********"
    port: "3306"
    database: "event_center"
    user_name: "root"
    password: "smmdb2016"
    maxconn: 50
    trace_on: true
  redis:
    ip: "**********"
    port: "6379"
    max_conn: 50
    prefix: "conference:"
    password: "crs-k0zgk15o:SEe323#dfwC"
    db: 10
rpc:
  admin_center: "https://testplatform.smm.cn/admincenter"
  user_center: "https://testplatform.smm.cn/usercenter"
  news_center: "https://testplatform.smm.cn/newscenter"
  order_center: "https://testplatform.smm.cn/ordercenter"
  meeting_center: "https://xytest.smm.cn/meeting/meeting"
  vcode_center: "http://testplatform.smm.cn/vcodecenter"
  vcode_center_inner: "http://**********:6087/vcodecenter/inner"
kafka:
  brokers: [ "********:9092" ]
  conference:
    brokers: [ "********:9092" ]
    topics: [ "HandBookData" ]
    process_switch: "1"
notify_conf:
  notify_level: 1
  wechat_addr: "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=6b3e5236-9f39-4de7-b7a4-4bce16609ec4"
  kafka_addr: "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=afcd2ed4-0aa7-425a-a260-da52644891ba"
ytx_conf:
  handbook_template_id: "2458482"
ucloud_conf:
  project_id: "org-bfxtjp"
  handbook_template_id: "UTN231009B6EJTE"
  sign: "上海有色网"
set_id: 715
