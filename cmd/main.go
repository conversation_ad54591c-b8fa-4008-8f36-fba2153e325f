package main

import (
	"conferencecenter/internal/conf"
	"conferencecenter/internal/corecontext"
	"conferencecenter/internal/db"
	"conferencecenter/internal/handler"
	"conferencecenter/internal/pkg/utils"
	"conferencecenter/internal/router"
	"conferencecenter/internal/task"
	"encoding/json"
	"fmt"
	"log"
	_ "net/http/pprof"
	"os"

	"git.code.tencent.com/smmit/smmbase/logger"
)

var (
	configFile string
)

func getEnv() string {
	env := os.Getenv("SERVER_ENV")
	if env == "" {
		return "dev"
	}
	return env
}

func init() {
	configFile = fmt.Sprintf("./configs/%s.yaml", getEnv())
}

//	@title			Swagger Example API
//	@version		1.0
//	@description	This is a sample server celler server.
//	@termsOfService	http://swagger.io/terms/

//	@contact.name	API Support
//	@contact.url	http://www.swagger.io/support
//	@contact.email	<EMAIL>

//	@license.name	Apache 2.0
//	@license.url	http://www.apache.org/licenses/LICENSE-2.0.html

//	@host		localhost:8010
//	@BasePath	/v1

// @securityDefinitions.apikey	JWT
// @in							header
// @name						Authorization
// @description				    Description for what is this security definition being used
func main() {
	log.Println(configFile)
	cfg, err := conf.Init(configFile)
	if err != nil {
		panic(err)
	}
	ctx := corecontext.InitSvcContext(cfg)

	// 执行命令
	if RunCmd() != 0 {
		_ = logger.Info("命令执行完成，退出程序，后续请自行检查执行过程是否有错误")
		return
	}

	task.StartCronTask()

	if utils.IsNotEnv() {
		// 初始化客户端
		handler.InitKafkaProducer()

		// 消费kfk数据
		handler.StartKafkaConsumer()
	}

	//init2()

	// 启动内网服务
	go router.StartInner(ctx)

	router.StartService(ctx)
}

func init2() {
	list, _, err := db.GetCnFreeVisitorInfo(nil, 1, "", "", "", "", 0, "", "", "", 0, 0, "")
	if err != nil {
		logger.Error(fmt.Sprintf("获取中文免费观众信息失败：%v", err))
		return
	}
	for _, visitor := range list {

		if utils.IsEmpty(visitor.PaperAnswer) {
			continue
		}
		answerList := PaperAnswerList{}
		err := json.Unmarshal([]byte(visitor.PaperAnswer), &answerList)
		if err != nil {
			logger.Error(fmt.Sprintf("获取中文免费观众信息失败：%v", err))
		}
		if answerList.Content != nil {

			for _, v := range answerList.Content {

				if v.Question == "请列举您希望在本届展会上见到的品牌？（最多三家）" {

					if utils.NotEmpty(v.Choice.Choice) {
						fmt.Println(v.Choice.Choice)
					}
				}
			}
		}
	}
}

type PaperAnswerList struct {
	Id      int64      `gorm:"column:id" db:"id" json:"id" form:"id"`
	Content []Question `gorm:"column:content" db:"content" json:"content" form:"content"`
}

type Question struct {
	Question string `gorm:"column:question" db:"question" json:"question" form:"question"`
	Choice   Choice `gorm:"column:choice" db:"choice" json:"choice" form:"choice"`
}

type Choice struct {
	Choice string `gorm:"column:choice" db:"choice" json:"choice" form:"choice"`
}
