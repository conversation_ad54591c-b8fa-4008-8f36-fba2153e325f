package main

import (
	"conferencecenter/internal/conf"
	"conferencecenter/internal/corecontext"
	"conferencecenter/internal/model"
	"conferencecenter/internal/pkg/utils"
	"conferencecenter/internal/service"
	"fmt"
	"git.code.tencent.com/smmit/smmbase/qiniu"
	"github.com/skip2/go-qrcode"
	"testing"
)

func testInit() {
	configFile = fmt.Sprintf("../configs/%s.yaml", getEnv())
	cfg, err := conf.Init(configFile)
	if err != nil {
		panic(err)
	}
	_ = corecontext.InitSvcContext(cfg)
}

func Test_Main2(t *testing.T) {
	examples := [][2]string{
		{"2025-03-16", "2025-03-16"}, // 同一天
		{"2025-03-16", "2025-03-17"}, // 同月
		{"2025-03-16", "2025-04-17"}, // 跨月
		{"2025-03-16", "2026-03-17"}, // 跨年
	}

	for _, ex := range examples {
		result, err := utils.FormatDateRange(ex[0], ex[1])
		if err != nil {
			fmt.Println("Error:", err)
			continue
		}
		fmt.Printf("%s ~ %s => %s\n", ex[0], ex[1], result)
	}
}

func Test_Main(t *testing.T) {
	value := utils.CheckComCodeGeneral("BJ1234567890")
	fmt.Println(value)
	_ = service.SendHandBookDataNotify(model.KfkHandBookData{ConferenceId: 100, CnConferenceName: "hhh", HandBookId: 100, CnHandBookName: "123", SubscribeId: 10, CnHandBookUrl: "https://www.baidu.com", Cellphone: "13061771527"})
}

func TestTrans(t *testing.T) {
	String := utils.TransStringFirstString("温")
	fmt.Println(String)
}

func TestImage(t *testing.T) {
	// 生成二维码字节流
	qrCode, err := qrcode.Encode("P24212011639102080", qrcode.Medium, 256) // 256 为二维码尺寸
	if err != nil {
		fmt.Printf("Error generating QR Code: %v\n", err)
		return
	}
	imageUrl, err := qiniu.NewSmmQiniuUpload().QiniuUploadImage(qiniu.CLNB_QR_CODE, qrCode, 0, "")
	if err != nil {
		return
	}
	fmt.Println(fmt.Sprintf("生成二维码：%v", imageUrl))
}
