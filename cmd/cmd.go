package main

import (
	"conferencecenter/internal/service"
	"flag"
	"fmt"
	"git.code.tencent.com/smmit/smmbase/logger"
	"time"
)

var (
	syncComInfo bool
)

func RunCmd() (res int) {
	// 命令参数
	gteID := flag.Int64("gte_id", 0, "开始Id")
	lteID := flag.Int64("lte_id", 0, "结束Id")

	// 命令
	flag.BoolVar(&syncComInfo, "sync_com_info", false, "加载爬虫数据库里的企业信息到企业库")

	flag.Parse()
	if !flag.Parsed() {
		_ = logger.Error("Flag not parsed!!!")
	}

	exec := func() int {
		if syncComInfo {
			fmt.Println("5秒后同步企业信息")
			time.Sleep(5 * time.Second)
			return syncSpiderComInfo(*gteID, *lteID)
		}
		return 0
	}

	return exec()
}

func syncSpiderComInfo(sId, eId int64) int {
	service.SyncComInfo(sId, eId)
	return 1
}
