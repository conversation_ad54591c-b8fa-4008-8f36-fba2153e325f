# 活动日程导入功能增强设计文档

## 概述

本设计文档描述了如何增强现有的 `AdminImportEventSchedule` 方法，使其支持通过活动编号来修改现有活动数据。新功能将支持活动的更新操作，同时保持向后兼容性和数据完整性。

## 架构

### 整体流程架构

```mermaid
graph TD
    A[Excel文件上传] --> B[解析Excel内容]
    B --> C[数据校验]
    C --> D[活动编号分组]
    D --> E[数据库事务开始]
    E --> F[处理每个活动组]
    F --> G{活动编号存在?}
    G -->|是| H[更新活动信息]
    G -->|否| I[创建新活动]
    H --> J[软删除现有日程和嘉宾]
    I --> K[创建日程和嘉宾]
    J --> K
    K --> L[事务提交]
    L --> M[返回处理结果]
```

### 数据处理流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Handler as Handler层
    participant Service as Service层
    participant DB as 数据库层
    
    Client->>Handler: 上传Excel文件
    Handler->>Service: AdminImportEventSchedule()
    Service->>Service: 解析Excel内容
    Service->>Service: 数据校验
    Service->>Service: 按活动编号分组
    Service->>DB: 开始事务
    loop 每个活动组
        Service->>DB: 查询活动是否存在
        alt 活动存在
            Service->>DB: 更新活动信息
            Service->>DB: 软删除现有日程
            Service->>DB: 软删除现有嘉宾
        else 活动不存在
            Service->>DB: 创建新活动
        end
        Service->>DB: 创建新日程
        Service->>DB: 创建新嘉宾
    end
    Service->>DB: 提交事务
    Service->>Handler: 返回处理结果
    Handler->>Client: 返回响应
```

## 组件和接口

### 数据结构增强

#### EventScheduleImportRow 结构体扩展

```go
type EventScheduleImportRow struct {
    EventCode        string `json:"event_code"`        // 新增：活动编号
    CategoryName     string `json:"category_name"`
    EventName        string `json:"event_name"`
    EventNameEn      string `json:"event_name_en"`
    EventDate        string `json:"event_date"`
    ShowEventDetail  string `json:"show_event_detail"`
    ScheduleTitle    string `json:"schedule_title"`
    ScheduleTitleEn  string `json:"schedule_title_en"`
    ScheduleType     string `json:"schedule_type"`
    ScheduleTime     string `json:"schedule_time"`
    ScheduleDesc     string `json:"schedule_desc"`
    ScheduleDescEn   string `json:"schedule_desc_en"`
    GuestName        string `json:"guest_name"`
    GuestNameEn      string `json:"guest_name_en"`
    GuestPosition    string `json:"guest_position"`
    GuestPositionEn  string `json:"guest_position_en"`
    GuestCompany     string `json:"guest_company"`
    GuestCompanyEn   string `json:"guest_company_en"`
    GuestIdentity    string `json:"guest_identity"`
    ConferenceId     int64  `json:"conference_id"`
}
```

#### 活动分组数据结构

```go
type EventGroup struct {
    EventCode    string                    `json:"event_code"`
    EventInfo    EventScheduleImportRow    `json:"event_info"`
    Schedules    []ScheduleGroup           `json:"schedules"`
}

type ScheduleGroup struct {
    ScheduleInfo EventScheduleImportRow    `json:"schedule_info"`
    Guests       []EventScheduleImportRow  `json:"guests"`
}
```

### 核心处理接口

#### 数据分组接口

```go
func groupDataByEvent(dataRows []EventScheduleImportRow) ([]EventGroup, error)
```

#### 活动处理接口

```go
func processEventGroup(tx *gorm.DB, group EventGroup, conferenceId int64, categoryMap map[string]model.ConferenceEventCategory) error
```

#### 数据清理接口

```go
func softDeleteEventSchedulesAndGuests(tx *gorm.DB, eventId int64, conferenceId int64) error
```

## 数据模型

### 数据库表关系

```mermaid
erDiagram
    CONFERENCE_EVENT {
        int64 id PK
        int64 conference_id
        string cn_name
        string en_name
        string event_time
        int category_type
        int is_display_page
        int deleted
        string sorting
        string create_time
    }
    
    EVENT_SCHEDULE {
        int64 id PK
        int64 conference_id
        int64 event_id FK
        string cn_title
        string en_title
        string schedule_start
        string schedule_end
        string cn_content
        string en_content
        int is_agenda
        int deleted
        float32 sorting
        datetime create_time
    }
    
    EVENT_SCHEDULE_GUEST {
        int64 id PK
        int64 conference_id
        int64 event_id FK
        int64 schedule_id FK
        string cn_appellation
        string en_appellation
        string cn_position
        string en_position
        string cn_company
        string en_company
        string guest_identity
        string deleted
        string sorting
        datetime create_time
    }
    
    CONFERENCE_EVENT ||--o{ EVENT_SCHEDULE : "has"
    EVENT_SCHEDULE ||--o{ EVENT_SCHEDULE_GUEST : "has"
```

### 数据处理逻辑

#### 活动编号映射策略

1. **活动编号唯一性**: 在同一个 conference_id 下，活动编号应该是唯一的
2. **数据分组**: 按活动编号对Excel行进行分组
3. **活动信息提取**: 从同一组的第一行提取活动基本信息
4. **日程信息聚合**: 将同一组内具有日程标题的行作为日程记录
5. **嘉宾信息关联**: 将嘉宾信息关联到对应的日程

## 错误处理

### 错误类型定义

```go
type ImportError struct {
    RowNumber int    `json:"row_number"`
    Field     string `json:"field"`
    Message   string `json:"message"`
    Code      string `json:"code"`
}

const (
    ERROR_MISSING_REQUIRED_FIELD = "MISSING_REQUIRED_FIELD"
    ERROR_INVALID_FORMAT         = "INVALID_FORMAT"
    ERROR_DUPLICATE_EVENT_CODE   = "DUPLICATE_EVENT_CODE"
    ERROR_CATEGORY_NOT_FOUND     = "CATEGORY_NOT_FOUND"
    ERROR_DATABASE_OPERATION     = "DATABASE_OPERATION"
)
```

### 错误处理策略

1. **数据校验错误**: 收集所有校验错误，一次性返回给用户
2. **数据库操作错误**: 立即回滚事务，返回具体错误信息
3. **部分成功处理**: 不支持部分成功，要么全部成功，要么全部失败

## 测试策略

### 单元测试覆盖

1. **数据解析测试**: 测试Excel解析和字段映射
2. **数据校验测试**: 测试各种校验规则
3. **数据分组测试**: 测试按活动编号分组逻辑
4. **数据库操作测试**: 测试CRUD操作和事务处理
5. **错误处理测试**: 测试各种异常情况

### 集成测试场景

1. **纯新增场景**: 所有活动编号都不存在
2. **纯更新场景**: 所有活动编号都已存在
3. **混合场景**: 部分新增，部分更新
4. **错误场景**: 各种数据错误和系统错误

### 性能测试考虑

1. **大文件处理**: 测试处理大量数据的性能
2. **事务性能**: 测试长事务的性能影响
3. **内存使用**: 测试内存使用情况
4. **并发处理**: 测试并发导入的安全性

## 实现细节

### 关键算法

#### 数据分组算法

```go
func groupDataByEvent(dataRows []EventScheduleImportRow) ([]EventGroup, error) {
    eventMap := make(map[string]*EventGroup)
    
    for i, row := range dataRows {
        if row.EventCode == "" {
            return nil, fmt.Errorf("第%d行: 活动编号不能为空", i+2)
        }
        
        group, exists := eventMap[row.EventCode]
        if !exists {
            group = &EventGroup{
                EventCode: row.EventCode,
                EventInfo: row,
                Schedules: make([]ScheduleGroup, 0),
            }
            eventMap[row.EventCode] = group
        }
        
        // 如果有日程标题，创建或查找对应的日程组
        if row.ScheduleTitle != "" {
            scheduleGroup := findOrCreateScheduleGroup(group, row)
            
            // 如果有嘉宾信息，添加到日程组
            if row.GuestName != "" {
                scheduleGroup.Guests = append(scheduleGroup.Guests, row)
            }
        }
    }
    
    // 转换为切片返回
    result := make([]EventGroup, 0, len(eventMap))
    for _, group := range eventMap {
        result = append(result, *group)
    }
    
    return result, nil
}
```

#### 软删除策略

```go
func softDeleteEventSchedulesAndGuests(tx *gorm.DB, eventId int64, conferenceId int64) error {
    // 软删除所有嘉宾
    err := tx.Model(&model.EventScheduleGuest{}).
        Where("event_id = ? AND conference_id = ? AND deleted = '0'", eventId, conferenceId).
        Update("deleted", "1").Error
    if err != nil {
        return fmt.Errorf("软删除嘉宾失败: %v", err)
    }
    
    // 软删除所有日程
    err = tx.Model(&model.EventSchedule{}).
        Where("event_id = ? AND conference_id = ? AND deleted = 0", eventId, conferenceId).
        Update("deleted", 1).Error
    if err != nil {
        return fmt.Errorf("软删除日程失败: %v", err)
    }
    
    return nil
}
```

### 事务管理

```go
func (s *EventService) processImportWithTransaction(groups []EventGroup, conferenceId int64) error {
    return s.db.Transaction(func(tx *gorm.DB) error {
        for _, group := range groups {
            if err := s.processEventGroup(tx, group, conferenceId); err != nil {
                return err
            }
        }
        return nil
    })
}
```

### 性能优化考虑

1. **批量操作**: 使用批量插入减少数据库交互
2. **索引优化**: 确保相关字段有适当的索引
3. **内存管理**: 避免一次性加载过大的数据集
4. **连接池**: 合理配置数据库连接池

## 安全考虑

1. **输入验证**: 严格验证所有输入数据
2. **SQL注入防护**: 使用参数化查询
3. **权限检查**: 验证用户对conference_id的操作权限
4. **文件安全**: 验证上传文件的类型和大小
5. **事务隔离**: 确保并发操作的数据一致性