

ALTER TABLE conference_center.conference_event ADD  `cn_sponsorship_type` varchar(200) NOT NULL DEFAULT '' COMMENT '赞助类型（0赞助方，1合作伙伴）';
ALTER TABLE conference_center.conference_event ADD  `en_sponsorship_type` varchar(200) NOT NULL DEFAULT '' COMMENT '赞助类型（0赞助方，1合作伙伴）';
ALTER TABLE conference_center.conference_event ADD  `cn_sponsors2` varchar(200) NOT NULL DEFAULT '' COMMENT '赞助方-中文';
ALTER TABLE conference_center.conference_event ADD  `en_sponsors2` varchar(200) NOT NULL DEFAULT '' COMMENT '赞助方-英文';
ALTER TABLE conference_center.conference_event ADD  `cn_sponsors_logo2` varchar(200) NOT NULL DEFAULT '' COMMENT '赞助方Logo-英文';
ALTER TABLE conference_center.conference_event ADD  `en_sponsors_logo2` varchar(200) NOT NULL DEFAULT '' COMMENT '赞助方Logo-英文';
ALTER TABLE conference_center.conference_event ADD  `cn_sponsorship_type2` varchar(200) NOT NULL DEFAULT '' COMMENT '赞助类型（0赞助方，1合作伙伴）';
ALTER TABLE conference_center.conference_event ADD  `en_sponsorship_type2` varchar(200) NOT NULL DEFAULT '' COMMENT '赞助类型（0赞助方，1合作伙伴）';




ALTER TABLE conference_center.conference ADD work_wechat_chain varchar(300) NOT NULL COMMENT '企业微信链接';

UPDATE conference SET work_wechat_chain='https://work.weixin.qq.com/ca/cawcde2ecda8d0bdd1' WHERE id=1 ;

UPDATE conference SET work_wechat_chain='https://work.weixin.qq.com/ca/cawcde2ecda8d0bdd1' WHERE id=3 ;



