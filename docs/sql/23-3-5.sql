




ALTER TABLE conference_center.contact_information CHANGE is_displayed cn_is_displayed int(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '中文其他页底是否显示（0  否 1  是）';

ALTER TABLE conference_center.contact_information ADD en_is_displayed int(20) NOT NULL COMMENT '英文其他页底是否显示（0  否 1  是）';


CREATE TABLE `conference_from_config` (
                                          `id` int(11) NOT NULL AUTO_INCREMENT,
                                          `from_id` int(11) NOT NULL,
                                          `from_name` varchar(255) NOT NULL,
                                          `conference_id` int(11) NOT NULL,
                                          `deleted` int(11) NOT NULL,
                                          PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Records of conference_from_config
-- ----------------------------
BEGIN;
INSERT INTO `conference_from_config` (`id`, `from_id`, `from_name`, `conference_id`, `deleted`) VALUES (1, 1, '曲欢欢', 1, 0);
INSERT INTO `conference_from_config` (`id`, `from_id`, `from_name`, `conference_id`, `deleted`) VALUES (2, 2, '齐文娟', 1, 0);
INSERT INTO `conference_from_config` (`id`, `from_id`, `from_name`, `conference_id`, `deleted`) VALUES (3, 3, '郭莎莎', 1, 0);
INSERT INTO `conference_from_config` (`id`, `from_id`, `from_name`, `conference_id`, `deleted`) VALUES (4, 4, 'CLNB公众号', 1, 0);
INSERT INTO `conference_from_config` (`id`, `from_id`, `from_name`, `conference_id`, `deleted`) VALUES (5, 5, 'CLNB置换媒体', 1, 0);
INSERT INTO `conference_from_config` (`id`, `from_id`, `from_name`, `conference_id`, `deleted`) VALUES (6, 6, '尹锁敏', 1, 0);
INSERT INTO `conference_from_config` (`id`, `from_id`, `from_name`, `conference_id`, `deleted`) VALUES (7, 7, 'EDM渠道', 1, 0);
INSERT INTO `conference_from_config` (`id`, `from_id`, `from_name`, `conference_id`, `deleted`) VALUES (8, 8, 'SMS渠道', 1, 0);
INSERT INTO `conference_from_config` (`id`, `from_id`, `from_name`, `conference_id`, `deleted`) VALUES (9, 9, 'SMM官网', 1, 0);
INSERT INTO `conference_from_config` (`id`, `from_id`, `from_name`, `conference_id`, `deleted`) VALUES (10, 10, 'AI短信', 1, 0);
INSERT INTO `conference_from_config` (`id`, `from_id`, `from_name`, `conference_id`, `deleted`) VALUES (11, 11, '衡水外呼', 1, 0);
INSERT INTO `conference_from_config` (`id`, `from_id`, `from_name`, `conference_id`, `deleted`) VALUES (12, 12, '亿点外呼', 1, 0);
INSERT INTO `conference_from_config` (`id`, `from_id`, `from_name`, `conference_id`, `deleted`) VALUES (13, 13, '公众号-王晶晶', 1, 0);
INSERT INTO `conference_from_config` (`id`, `from_id`, `from_name`, `conference_id`, `deleted`) VALUES (14, 14, '公众号-赵玉菲', 1, 0);
INSERT INTO `conference_from_config` (`id`, `from_id`, `from_name`, `conference_id`, `deleted`) VALUES (15, 15, 'SMM公众号-于柯1', 1, 0);
INSERT INTO `conference_from_config` (`id`, `from_id`, `from_name`, `conference_id`, `deleted`) VALUES (16, 16, 'SMM公众号-于柯2', 1, 0);
INSERT INTO `conference_from_config` (`id`, `from_id`, `from_name`, `conference_id`, `deleted`) VALUES (17, 17, 'SMM公众号-井晓雯1', 1, 0);
INSERT INTO `conference_from_config` (`id`, `from_id`, `from_name`, `conference_id`, `deleted`) VALUES (18, 18, 'SMM公众号-井晓雯2', 1, 0);
INSERT INTO `conference_from_config` (`id`, `from_id`, `from_name`, `conference_id`, `deleted`) VALUES (19, 19, 'SMM公众号-隋慧姿', 1, 0);
INSERT INTO `conference_from_config` (`id`, `from_id`, `from_name`, `conference_id`, `deleted`) VALUES (20, 20, 'SMM公众号-张翼', 1, 0);
INSERT INTO `conference_from_config` (`id`, `from_id`, `from_name`, `conference_id`, `deleted`) VALUES (21, 21, '合作方-储能热榜', 1, 0);
INSERT INTO `conference_from_config` (`id`, `from_id`, `from_name`, `conference_id`, `deleted`) VALUES (22, 22, '合作方-新能源商业评论', 1, 0);
INSERT INTO `conference_from_config` (`id`, `from_id`, `from_name`, `conference_id`, `deleted`) VALUES (23, 23, '销售-新能源组', 1, 0);
INSERT INTO `conference_from_config` (`id`, `from_id`, `from_name`, `conference_id`, `deleted`) VALUES (24, 24, '销售-铝组', 1, 0);
INSERT INTO `conference_from_config` (`id`, `from_id`, `from_name`, `conference_id`, `deleted`) VALUES (25, 25, '销售-光伏组', 1, 0);
INSERT INTO `conference_from_config` (`id`, `from_id`, `from_name`, `conference_id`, `deleted`) VALUES (26, 26, '销售-铜组', 1, 0);
INSERT INTO `conference_from_config` (`id`, `from_id`, `from_name`, `conference_id`, `deleted`) VALUES (27, 27, '海外信息', 1, 0);
INSERT INTO `conference_from_config` (`id`, `from_id`, `from_name`, `conference_id`, `deleted`) VALUES (28, 28, '海外营销', 1, 0);
INSERT INTO `conference_from_config` (`id`, `from_id`, `from_name`, `conference_id`, `deleted`) VALUES (29, 29, '海外市场', 1, 0);
INSERT INTO `conference_from_config` (`id`, `from_id`, `from_name`, `conference_id`, `deleted`) VALUES (30, 30, '掌上有色APP', 1, 0);
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;
