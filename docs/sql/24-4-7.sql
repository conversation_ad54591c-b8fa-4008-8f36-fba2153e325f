


ALTER TABLE conference_center.exhibition_guideline CHANGE floor_graph cn_floor_graph varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' NOT NULL COMMENT '展馆平面图url';

ALTER TABLE conference_center.exhibition_guideline ADD en_floor_graph varchar(100) NOT NULL COMMENT '英文展馆平面图url';


ALTER TABLE conference_center.conference ADD template_type int(10) NOT NULL COMMENT '展会日程模版类型:1时间&主题;0议程&非议程';



ALTER TABLE conference_center.bd_vid_log ADD form_type int(10) NOT NULL COMMENT '表单类型（1观众，2赞助及广告机会，3展览展位，4购票参会）';
