-- 为conference_event表添加event_code字段
-- 用于支持活动编号功能

ALTER TABLE `conference_event` 
ADD COLUMN `event_code` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '活动编号' AFTER `conference_id`;

-- 为event_code字段添加索引，提高查询性能
ALTER TABLE `conference_event` 
ADD INDEX `idx_conference_event_code` (`conference_id`, `event_code`);

-- 为现有数据生成默认的活动编号（可选，根据实际需求调整）
-- UPDATE `conference_event` 
-- SET `event_code` = CONCAT('EVENT_', id) 
-- WHERE `event_code` = '' AND `deleted` = 0;
