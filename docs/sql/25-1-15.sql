
ALTER TABLE conference_center.conference_event ADD  `cn_sponsors` varchar(200) NOT NULL DEFAULT '' COMMENT '赞助方-中文';
ALTER TABLE conference_center.conference_event ADD  `en_sponsors` varchar(200) NOT NULL DEFAULT '' COMMENT '赞助方-英文';
ALTER TABLE conference_center.conference_event ADD  `cn_sponsors_logo` varchar(200) NOT NULL DEFAULT '' COMMENT '赞助方Logo-中文';
ALTER TABLE conference_center.conference_event ADD  `en_sponsors_logo` varchar(200) NOT NULL DEFAULT '' COMMENT '赞助方Logo-英文';


ALTER TABLE conference_center.conference_previous ADD  `en_pdf` varchar(200) NOT NULL DEFAULT '' COMMENT '';


UPDATE forum_config_list SET cn_name='新能源电池材料论坛',en_name = 'New Energy Battery Materials Forum' WHERE id =29;

UPDATE forum_config_list SET cn_name='铝压铸产业发展论坛'  WHERE id =41;


DELETE FROM forum_config_list WHERE id =30;
DELETE FROM forum_config_list WHERE id =32;


INSERT INTO conference_center.`forum_config_list` ( `conference_id`, `cn_name`, `en_name`, `type`) VALUES (1, '商用车动力电池技术与应用论坛', 'Commercial Vehicle Power Battery Technology and Application Forum', 0);

INSERT INTO conference_center.`forum_config_list` ( `conference_id`, `cn_name`, `en_name`, `type`) VALUES (3, '铝产业链可持续发展论坛', 'The Sustainable Development Forum of Aluminum Industry Chain', 0);



