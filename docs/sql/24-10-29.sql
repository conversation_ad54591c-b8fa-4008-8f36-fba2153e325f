



ALTER TABLE conference_center.cn_free_visitor ADD  `main_products` varchar(255) NOT NULL DEFAULT '' COMMENT '主推产品';

ALTER TABLE conference_center.cn_free_visitor ADD  `enterprise_type` varchar(255) NOT NULL DEFAULT '' COMMENT '企业类型';

ALTER TABLE conference_center.cn_free_visitor ADD  `procurement_items` varchar(255) NOT NULL DEFAULT '' COMMENT '采购产品';

ALTER TABLE conference_center.cn_free_visitor ADD  `order_quantity` varchar(255) NOT NULL DEFAULT '' COMMENT '采购数量';




ALTER TABLE conference_center.en_free_visitor ADD  `main_products` varchar(255) NOT NULL DEFAULT '' COMMENT '主推产品';

ALTER TABLE conference_center.en_free_visitor ADD  `enterprise_type` varchar(255) NOT NULL DEFAULT '' COMMENT '企业类型';

ALTER TABLE conference_center.en_free_visitor ADD  `procurement_items` varchar(255) NOT NULL DEFAULT '' COMMENT '采购产品';

ALTER TABLE conference_center.en_free_visitor ADD  `order_quantity` varchar(255) NOT NULL DEFAULT '' COMMENT '采购数量';






ALTER TABLE conference_center.conference_clue ADD  `main_products` varchar(255) NOT NULL DEFAULT '' COMMENT '主推产品';

ALTER TABLE conference_center.conference_clue ADD  `enterprise_type` varchar(255) NOT NULL DEFAULT '' COMMENT '企业类型';



ALTER TABLE conference_center.conference_register ADD  `main_products` varchar(255) NOT NULL DEFAULT '' COMMENT '主推产品';

ALTER TABLE conference_center.conference_register ADD  `enterprise_type` varchar(255) NOT NULL DEFAULT '' COMMENT '企业类型';




ALTER TABLE conference_center.bd_vid_log ADD  `return_msg` varchar(2000) NOT NULL DEFAULT '' COMMENT '企业类型';

