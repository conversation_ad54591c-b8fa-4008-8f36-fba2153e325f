ALTER TABLE conference_center.event_schedule MODIFY COLUMN sorting DECIMAL(10,2) NOT NULL;
ALTER TABLE conference_center.conference_information  MODIFY COLUMN sorting DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '排序';
ALTER TABLE conference_center.conference_information  MODIFY COLUMN cn_sorting DECIMAL(10,2) NOT NULL;
ALTER TABLE conference_center.conference_information  MODIFY COLUMN en_sorting DECIMAL(10,2) NOT NULL;
ALTER TABLE conference_center.conference_data  MODIFY COLUMN sorting DECIMAL(10,2) NOT NULL;
ALTER TABLE conference_center.cn_free_visitor ADD from_name varchar(100) DEFAULT '' NOT NULL COMMENT '渠道名称';
ALTER TABLE conference_center.en_free_visitor ADD from_name varchar(100) DEFAULT '' NOT NULL COMMENT '渠道名称';
ALTER TABLE conference_center.conference_clue ADD from_name varchar(100) DEFAULT '' NOT NULL COMMENT '渠道名称';
ALTER TABLE conference_center.media_registration ADD from_name varchar(100) DEFAULT '' NOT NULL COMMENT '渠道名称';
ALTER TABLE conference_center.conference_register ADD from_name varchar(100) DEFAULT '' NOT NULL COMMENT '渠道名称';
ALTER TABLE conference_center.company_handbook_subscribe ADD cn_conference_name varchar(100) DEFAULT '' NOT NULL COMMENT '中文展会名称';
ALTER TABLE conference_center.company_handbook_subscribe ADD en_conference_name varchar(100) DEFAULT '' NOT NULL COMMENT '英文展会名称';
ALTER TABLE conference_center.contact_information   MODIFY COLUMN sorting DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '排序';
ALTER TABLE conference_center.conference_data   MODIFY COLUMN sorting DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '排序';
ALTER TABLE conference_center.conference_previous   MODIFY COLUMN sort DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '排序';
ALTER TABLE conference_center.audience_conference_company   MODIFY COLUMN sort DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '排序';
ALTER TABLE conference_center.production_management   MODIFY COLUMN sorting DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '排序';
ALTER TABLE conference_center.venue   MODIFY COLUMN sorting DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '排序';
ALTER TABLE conference_center.audience_after_report   MODIFY COLUMN sort DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '排序';
ALTER TABLE conference_center.audience_question_content   MODIFY COLUMN sort DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '排序';
ALTER TABLE conference_center.audience_question_type   MODIFY COLUMN sort DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '排序';
ALTER TABLE conference_center.company_question_content   MODIFY COLUMN sort DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '排序';
ALTER TABLE conference_center.company_question_type   MODIFY COLUMN sort DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '排序';
ALTER TABLE conference_center.audience_question_content   MODIFY COLUMN sort DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '排序';
ALTER TABLE conference_center.conference_floor_graph    MODIFY COLUMN sort DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '排序';
ALTER TABLE conference_center.conference_ticket_price   MODIFY COLUMN cn_sorting DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '排序';
ALTER TABLE conference_center.conference_ticket_price   MODIFY COLUMN en_sorting DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '排序';
ALTER TABLE conference_center.conference_rights_interests   MODIFY COLUMN cn_sorting DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '排序';
ALTER TABLE conference_center.conference_rights_interests   MODIFY COLUMN en_sorting DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '排序';
ALTER TABLE conference_center.conference_column   MODIFY COLUMN sorting DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '排序';
ALTER TABLE conference_center.conference_organization   MODIFY COLUMN sorting DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '排序';
ALTER TABLE conference_center.company_handbook   MODIFY COLUMN sort DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '排序';
ALTER TABLE conference_center.conference_event   MODIFY COLUMN sorting DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '排序';
ALTER TABLE conference_center.conference_information  MODIFY COLUMN cn_sorting DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '排序';
ALTER TABLE conference_center.conference_information  MODIFY COLUMN en_sorting DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '排序';