



ALTER TABLE conference_center.exhibition_guideline ADD cn_hotel_button varchar(100) NOT NULL COMMENT '中文酒店按钮';
ALTER TABLE conference_center.exhibition_guideline ADD en_hotel_button varchar(100) NOT NULL COMMENT '英文酒店按钮';


ALTER TABLE conference_center.cn_free_visitor ADD id_no varchar(100) NOT NULL COMMENT '身份证号';


ALTER TABLE conference_center.conference_register ADD cn_or_en int(10) DEFAULT 0 NOT NULL;



ALTER TABLE conference_center.question_paper ADD conference_id int(10) DEFAULT 0 NOT NULL;


UPDATE question_paper SET conference_id =1 WHERE id=2;
