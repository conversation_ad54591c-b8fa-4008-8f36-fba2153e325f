


ALTER TABLE conference_center.conference_information CHANGE picture cn_picture varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'logo图-中文';

ALTER TABLE conference_center.conference_information ADD en_picture varchar(300) NOT NULL COMMENT 'logo图-英文';

ALTER TABLE conference_center.audience_conference_company ADD exhibitor_video_link varchar(300) NOT NULL COMMENT '展商视频';
ALTER TABLE conference_center.audience_conference_company ADD exhibitor_video_cover varchar(300) NOT NULL COMMENT '展商视频封面';
ALTER TABLE conference_center.audience_conference_company ADD exhibitor_news_id varchar(10) NOT NULL COMMENT '展商新闻id';

ALTER TABLE conference_center.conference_clue ADD source_id varchar(100) NOT NULL COMMENT '来源ID';
ALTER TABLE conference_center.conference_clue ADD source_name varchar(100) NOT NULL COMMENT '来源名称';

ALTER TABLE conference_center.conference_information CHANGE sorting cn_sorting int(11) NOT NULL COMMENT '排序';

ALTER TABLE conference_center.conference_information ADD en_sorting int(10) NOT NULL COMMENT '排序英文';


ALTER TABLE conference_center.conference_information ADD sorting int(10) NOT NULL COMMENT '排序';
