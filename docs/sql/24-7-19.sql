



ALTER TABLE conference_center.conference_company ADD exhibition_qr_code varchar(300) NOT NULL COMMENT '成功二维码';

ALTER TABLE conference_center.conference_company ADD exhibition_tips varchar(1000) NOT NULL COMMENT '成功提示语';

ALTER TABLE conference_center.conference ADD cn_top_left_logo varchar(300) NOT NULL COMMENT '左上角logo';
ALTER TABLE conference_center.conference ADD en_top_left_logo varchar(300) NOT NULL COMMENT '左上角logo';



ALTER TABLE conference_center.audience_conference_company ADD  `year` varchar(255) NOT NULL DEFAULT '2024' COMMENT '年度'




CREATE TABLE `conference_news_set` (
                                       `id` int(11) NOT NULL AUTO_INCREMENT,
                                       `conference_id` int(11) NOT NULL,
                                       `set_id` int(11) NOT NULL,
                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4;



INSERT INTO `conference_news_set` (`id`, `conference_id`, `set_id`) VALUES (1, 1, 493);
INSERT INTO `conference_news_set` (`id`, `conference_id`, `set_id`) VALUES (2, 2, 524);



INSERT INTO `forum_config_list` ( `conference_id`, `cn_name`, `en_name`, `type`) VALUES (2, '宏观&电机行业发展论坛', '', 0);
INSERT INTO `forum_config_list` ( `conference_id`, `cn_name`, `en_name`, `type`) VALUES (2, '电机创新原材料论坛', '', 0);
INSERT INTO `forum_config_list` ( `conference_id`, `cn_name`, `en_name`, `type`) VALUES (2, '电机创新节能技术论坛', '', 0);

