
ALTER TABLE conference_center.conference_sponsor ADD cn_title varchar(100) NOT NULL COMMENT '赞助标题-中文';
ALTER TABLE conference_center.conference_sponsor ADD en_title varchar(100) NOT NULL COMMENT '赞助标题-英文';
ALTER TABLE conference_center.conference_sponsor ADD cn_button varchar(100) NOT NULL COMMENT '赞助按钮名称-中文';
ALTER TABLE conference_center.conference_sponsor ADD en_button varchar(100) NOT NULL COMMENT '赞助按钮名称-英文';


ALTER TABLE conference_center.exhibition_guideline ADD cn_heading varchar(100) NOT NULL COMMENT '上标题-中文';
ALTER TABLE conference_center.exhibition_guideline ADD en_heading varchar(100) NOT NULL COMMENT '上标题-英文';
ALTER TABLE conference_center.exhibition_guideline ADD cn_headlining varchar(100) NOT NULL COMMENT '下标题-中文';
ALTER TABLE conference_center.exhibition_guideline ADD en_headlining varchar(100) NOT NULL COMMENT '下标题-英文';



ALTER TABLE conference_center.conference_rights_interests CHANGE sorting cn_sorting int(20) NOT NULL COMMENT '排序';
ALTER TABLE conference_center.conference_ticket_price CHANGE is_displayed cn_is_displayed   int(20) NOT NULL COMMENT '是否显示购买按钮（1展示，0不展示';
ALTER TABLE conference_center.conference_ticket_price CHANGE maximum cn_maximum int(20) NOT NULL COMMENT '最大人数';
ALTER TABLE conference_center.conference_ticket_price CHANGE sorting cn_sorting int(20) NOT NULL COMMENT '排序';


ALTER TABLE conference_center.conference_ticket_price ADD en_is_displayed int(20) NOT NULL COMMENT '是否显示购买按钮（1展示，0不展示';
ALTER TABLE conference_center.conference_ticket_price ADD en_maximum int(20) NOT NULL COMMENT '最大人数';
ALTER TABLE conference_center.conference_ticket_price ADD en_sorting int(20) NOT NULL COMMENT '排序';
ALTER TABLE conference_center.conference_rights_interests ADD en_sorting int(20) NOT NULL COMMENT '排序';





ALTER TABLE conference_center.conference_ticket_config CHANGE price_template cn_price_template int(11) NOT NULL;
ALTER TABLE conference_center.conference_ticket_config ADD en_price_template int(10) NOT NULL;

ALTER TABLE conference_center.conference_rights_ticket ADD is_cn int(10) NOT NULL;



