


CREATE TABLE `media_registration` (
                                      `id` int(11) NOT NULL AUTO_INCREMENT,
                                      `meeting_name` varchar(255) NOT NULL COMMENT '会议',
                                      `meeting_id` int(11) NOT NULL COMMENT '会议ID',
                                      `media_id` int(11) NOT NULL,
                                      `media_name` varchar(255) NOT NULL,
                                      `user_id` int(11) NOT NULL COMMENT '用户ID',
                                      `first_name` varchar(255) NOT NULL COMMENT '姓',
                                      `last_name` varchar(255) NOT NULL COMMENT '名',
                                      `organisation` varchar(255) NOT NULL COMMENT '组织',
                                      `job_title` varchar(255) NOT NULL COMMENT '职位',
                                      `email` varchar(255) NOT NULL COMMENT '邮箱',
                                      `phone` varchar(255) NOT NULL COMMENT '手机',
                                      `country` varchar(255) NOT NULL COMMENT '国家',
                                      `create_time` datetime NOT NULL COMMENT '提交时间',
                                      `deleted` int(11) NOT NULL DEFAULT '2',
                                      `source_id` varchar(300) NOT NULL COMMENT '来源',
                                      `from_id` varchar(300) NOT NULL COMMENT '参会身份',
                                      `is_agree_send_message` int(5) NOT NULL DEFAULT '1' COMMENT '是否同意发送邮件(2同意，1不同意)',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1213 DEFAULT CHARSET=utf8mb4 COMMENT='媒体注册';


CREATE TABLE `media_registration_column` (
                                             `id` int(11) NOT NULL AUTO_INCREMENT,
                                             `meeting_id` int(11) NOT NULL,
                                             `column_id` int(11) NOT NULL,
                                             `sub_section_id` int(11) NOT NULL DEFAULT '0',
                                             `is_sub_section` int(11) NOT NULL,
                                             `is_displayed` int(11) NOT NULL,
                                             `name` varchar(255) NOT NULL,
                                             `sorting` int(11) NOT NULL,
                                             `deleted` varchar(255) NOT NULL,
                                             `create_time` datetime NOT NULL,
                                             `update_time` datetime NOT NULL,
                                             `create_admin` varchar(255) NOT NULL,
                                             `update_admin` varchar(255) NOT NULL,
                                             `is_select_all` int(11) NOT NULL,
                                             PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1286 DEFAULT CHARSET=utf8mb4 COMMENT='媒体注册栏目信息';
