# 活动日程导入Excel模板格式说明

## 概述

本文档描述了活动日程导入功能的Excel模板格式。新版本支持通过活动编号来修改现有活动数据。

## Excel表格格式

### 表头结构（第一行）

| 列号 | 字段名称 | 是否必填 | 说明 |
|------|----------|----------|------|
| A | 活动编号（必填） | 是 | 活动的唯一标识符，用于区分新增和修改操作 |
| B | 活动分类（必填） | 是 | 活动所属的分类名称 |
| C | 活动名称（必填） | 是 | 活动的中文名称 |
| D | 活动名称英文 | 否 | 活动的英文名称 |
| E | 活动日期（必填） | 是 | 活动日期，格式：YYYY-MM-DD |
| F | 是否展示活动详情 | 否 | 是/否，默认为"是" |
| G | 日程标题（必填） | 是 | 日程的中文标题 |
| H | 标题英文 | 否 | 日程的英文标题 |
| I | 日程类型 | 否 | 议程/非议程，默认为"议程" |
| J | 日程时间（必填） | 是 | 格式：HH:MM-HH:MM，如：09:00-10:30 |
| K | 日程描述 | 否 | 日程的中文描述 |
| L | 描述英文 | 否 | 日程的英文描述 |
| M | 嘉宾称谓 | 否 | 嘉宾的中文称谓 |
| N | 称谓英文 | 否 | 嘉宾的英文称谓 |
| O | 嘉宾职位 | 否 | 嘉宾的中文职位 |
| P | 职位英文 | 否 | 嘉宾的英文职位 |
| Q | 嘉宾公司 | 否 | 嘉宾的中文公司名称 |
| R | 公司英文 | 否 | 嘉宾的英文公司名称 |
| S | 嘉宾身份 | 否 | Panelist/Moderator |

## 功能说明

### 新增活动
- 当活动编号在当前展会下不存在时，系统将创建新的活动记录
- 同时创建对应的日程和嘉宾信息

### 修改活动
- 当活动编号在当前展会下已存在时，系统将：
  1. 更新活动的基本信息（名称、日期、分类等）
  2. 保留活动的ID和创建时间
  3. 软删除该活动下的所有现有日程和嘉宾
  4. 重新创建Excel中的日程和嘉宾信息

### 数据校验规则

1. **必填项校验**：活动编号、活动分类、活动名称、活动日期、日程标题、日程时间必须填写
2. **格式校验**：
   - 活动日期：YYYY-MM-DD格式
   - 日程时间：HH:MM-HH:MM格式
   - 是否展示活动详情：只能为"是"或"否"
   - 日程类型：只能为"议程"或"非议程"
   - 嘉宾身份：只能为"Panelist"或"Moderator"
3. **唯一性校验**：同一Excel文件中活动编号不能重复
4. **存在性校验**：活动分类必须在系统中已存在

## 示例数据

```
活动编号（必填） | 活动分类（必填） | 活动名称（必填） | 活动名称英文 | 活动日期（必填） | 是否展示活动详情 | 日程标题（必填） | 标题英文 | 日程类型 | 日程时间（必填） | 日程描述 | 描述英文 | 嘉宾称谓 | 称谓英文 | 嘉宾职位 | 职位英文 | 嘉宾公司 | 公司英文 | 嘉宾身份
EVENT001 | 主论坛 | 开幕式 | Opening Ceremony | 2024-03-15 | 是 | 欢迎致辞 | Welcome Speech | 议程 | 09:00-09:30 | 大会开幕致辞 | Opening remarks | 张三 | Zhang San | 总裁 | President | ABC公司 | ABC Company | Moderator
EVENT001 | 主论坛 | 开幕式 | Opening Ceremony | 2024-03-15 | 是 | 主题演讲 | Keynote Speech | 议程 | 09:30-10:30 | 行业发展趋势 | Industry trends | 李四 | Li Si | 专家 | Expert | XYZ研究院 | XYZ Institute | Panelist
```

## 注意事项

1. Excel文件必须包含表头行
2. 活动编号在同一展会下必须唯一
3. 修改现有活动时，会完全替换该活动的日程和嘉宾信息
4. 删除操作使用软删除，不会物理删除数据
5. 建议在导入前备份重要数据
