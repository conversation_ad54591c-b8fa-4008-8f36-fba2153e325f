# 技术栈

## 核心技术

- **编程语言**: Go 1.18+
- **Web框架**: <PERSON><PERSON> (HTTP路由和中间件)
- **数据库**: MySQL 配合 GORM ORM
- **缓存**: Redis (使用 Redigo 客户端)
- **消息队列**: Apache Kafka (使用 Shopify Sarama)
- **API文档**: Swagger/OpenAPI 配合 swaggo

## 主要依赖

- **基础库**: `git.code.tencent.com/smmit/smmbase` (腾讯内部库)
- **数据库**: `gorm.io/gorm` + `gorm.io/driver/mysql`
- **HTTP客户端**: `github.com/go-resty/resty/v2`
- **配置管理**: `github.com/jinzhu/configor` (基于YAML)
- **日志**: `go.uber.org/zap`
- **Excel处理**: `github.com/xuri/excelize/v2`
- **邮件**: `gopkg.in/mailgun/mailgun-go.v1`
- **二维码**: `github.com/skip2/go-qrcode`
- **中文处理**: 拼音转换库

## 构建系统

### 可用命令

```bash
# 生成 Swagger 文档
make swag

# 手动运行 swagger 生成
swag init -g cmd/main.go
```

### 环境配置

- **开发环境**: `configs/dev.yaml`
- **测试环境**: `configs/test.yaml`
- 环境由 `SERVER_ENV` 环境变量决定 (默认为 "dev")

### 运行应用

```bash
# 设置环境 (可选，默认为 dev)
export SERVER_ENV=dev

# 运行应用
go run cmd/main.go
```

## 架构模式

- **整洁架构**: 分层关注点分离
- **依赖注入**: 配置驱动的初始化
- **仓储模式**: 数据访问抽象
- **服务层**: 业务逻辑封装