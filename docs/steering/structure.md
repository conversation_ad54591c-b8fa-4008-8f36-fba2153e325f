# 项目结构

## 目录组织

```
conferencecenter/
├── cmd/                    # 应用程序入口点
│   ├── main.go            # 主应用程序入口
│   └── cmd.go             # 命令行接口
├── internal/              # 私有应用程序代码
│   ├── handler/           # HTTP处理器 (控制器)
│   ├── service/           # 业务逻辑层
│   ├── db/                # 数据访问层
│   ├── model/             # 数据模型和实体
│   ├── protocol/          # 请求/响应 DTOs
│   ├── router/            # 路由定义
│   ├── conf/              # 配置管理
│   ├── constant/          # 应用程序常量
│   ├── errcode/           # 错误代码定义
│   ├── mw/                # 中间件
│   ├── pkg/               # 内部包
│   │   ├── cache/         # 缓存工具
│   │   ├── logger/        # 日志工具
│   │   ├── mysql/         # 数据库初始化
│   │   └── utils/         # 通用工具
│   ├── rpc/               # 外部服务客户端
│   ├── memory/            # 内存数据管理
│   ├── msgqueue/          # 消息队列处理
│   ├── task/              # 后台任务
│   └── weChat/            # 微信集成
├── configs/               # 配置文件
│   ├── dev.yaml          # 开发环境配置
│   └── test.yaml         # 测试环境配置
├── docs/                  # 文档和API规范
│   ├── swagger.json      # OpenAPI规范
│   └── sql/              # 数据库迁移脚本
└── Makefile              # 构建自动化
```

## 分层架构

### 请求流程
```
HTTP请求 → 路由 → 处理器 → 服务 → 数据库 → 模型
```

### 层级职责

- **Handler**: HTTP请求/响应处理、输入验证、Swagger文档
- **Service**: 业务逻辑、编排、外部服务调用
- **DB**: 数据访问、数据库查询、事务管理
- **Model**: 数据结构、表定义、GORM模型
- **Protocol**: 请求/响应DTOs、API契约

## 命名约定

### 文件和包
- 包名: 小写，单个单词
- 文件名: 多词概念使用 snake_case
- Go文件: 描述性名称匹配其主要职责

### 数据库和模型
- 表名: snake_case (例如: `conference_event_category`)
- 数据库字段: snake_case (例如: `cn_name`, `en_name`)
- Go结构体字段: PascalCase 配合结构体标签
- 多语言字段: `cn_*` (中文), `en_*` (英文)

### API和处理器
- 处理器函数: 管理API使用 `Admin*` 前缀
- Swagger标签: 管理后台使用中文描述
- 路由组: 按功能区域逻辑分组

## 代码组织模式

### 模型定义
```go
type ConferenceEvent struct {
    Id           int64  `json:"id" db:"id" gorm:"id" form:"id"`
    CnName       string `json:"cn_name" db:"cn_name" gorm:"cn_name" form:"cn_name"`
    EnName       string `json:"en_name" db:"en_name" gorm:"en_name" form:"en_name"`
    // ... 其他字段
}

func (*ConferenceEvent) TableName() string {
    return "conference_event"
}
```

### 处理器模式
```go
func AdminSomeAction(c *gin.Context) {
    var req protocol.ReqSomeAction
    var res protocol.ResSomeAction
    var err protocol.ResError
    
    defer func() {
        wrapResp(c, res, addCallback, err)
    }()
    
    // 实现...
}
```

## 配置管理

- 基于环境的配置加载
- 所有配置文件使用YAML格式
- 环境变量: `SERVER_ENV` (默认为 "dev")
- 配置文件模式: `configs/{environment}.yaml`