basePath: /v1
definitions:
  model.AnnualSelection:
    properties:
      application_link:
        description: 参评报名入口链接
        type: string
      companies_link:
        description: 参评企业入口链接
        type: string
      conference_id:
        description: 展会id
        type: integer
      id:
        type: integer
      introduction:
        description: 评选介绍
        type: string
      judges_scoring_link:
        description: 评委打分入口链接
        type: string
      process:
        description: 评选流程
        type: string
    type: object
  model.ChannelSource:
    properties:
      id:
        type: string
      name:
        description: 渠道来源名称
        type: string
    type: object
  model.ConferenceBottomPage:
    properties:
      cn_logo:
        description: 二维码链接-中文
        type: string
      cn_name:
        description: 单位名称/二维码名称-中文
        type: string
      cn_sorting:
        description: 排序-中文
        type: string
      conference_id:
        description: 展会ID
        type: integer
      create_admin:
        type: string
      create_time:
        type: string
      deleted:
        type: integer
      en_logo:
        description: 二维码链接-英文
        type: string
      en_name:
        description: 单位名称/二维码名称-英文
        type: string
      en_sorting:
        description: 排序-英文
        type: string
      id:
        type: integer
      update_admin:
        type: string
      update_time:
        type: string
    type: object
  model.ConferenceColumn:
    properties:
      cn_name:
        description: 栏目名称-中文
        type: string
      conference_id:
        description: 展会ID
        type: integer
      create_admin:
        type: string
      create_time:
        type: string
      deleted:
        type: integer
      en_name:
        description: 栏目名称-英文
        type: string
      id:
        type: integer
      is_sub_section:
        type: integer
      sorting:
        description: 排序
        type: number
      sub_section_id:
        type: integer
      type:
        description: 信息类型（1赞助商&合作商&媒体，2嘉宾）
        type: integer
      update_admin:
        type: string
      update_time:
        type: string
    type: object
  model.ConferenceData:
    properties:
      cn_data_name:
        description: 指标名称-中文
        type: string
      cn_data_value:
        description: 指标数据-中文
        type: string
      conference_id:
        type: integer
      create_admin:
        type: string
      create_time:
        type: string
      data_image:
        description: /图标图片
        type: string
      deleted:
        type: integer
      en_data_name:
        description: 指标名称-英文
        type: string
      en_data_value:
        description: 指标数据-英文
        type: string
      id:
        type: integer
      sorting:
        description: 排序
        type: number
      update_admin:
        type: string
      update_time:
        type: string
    type: object
  model.ConferenceEnterpriseType:
    properties:
      conference_id:
        description: 展会ID
        type: integer
      id:
        description: 会议嘉宾等信息
        type: integer
      value:
        type: string
    type: object
  model.ConferenceFromConfig:
    properties:
      from_id:
        type: string
      from_name:
        type: string
    type: object
  model.ConferenceInformation:
    properties:
      cn_appellation:
        description: 称谓
        type: string
      cn_company:
        description: 公司
        type: string
      cn_content:
        description: 嘉宾介绍(或嘉宾身份)--中文
        type: string
      cn_link:
        description: 链接
        type: string
      cn_picture:
        description: logo图-中文
        type: string
      cn_position:
        description: 职位
        type: string
      cn_sorting:
        description: 排序-中文
        type: number
      column_id:
        description: 栏目ID
        type: integer
      conference_id:
        description: 展会ID
        type: integer
      create_admin:
        type: string
      create_time:
        type: string
      deleted:
        type: integer
      en_appellation:
        description: 称谓
        type: string
      en_company:
        description: 公司
        type: string
      en_content:
        description: 嘉宾介绍(或嘉宾身份)--英文
        type: string
      en_link:
        description: 链接
        type: string
      en_picture:
        description: logo图-英文
        type: string
      en_position:
        description: 职位
        type: string
      en_sorting:
        description: 排序-英文
        type: number
      id:
        description: 会议嘉宾等信息
        type: integer
      sorting:
        description: 排序
        type: number
      type:
        description: 信息类型（1赞助商，2嘉宾,3合作商,4媒体，9日程嘉宾）
        type: integer
      update_admin:
        type: string
      update_time:
        type: string
    type: object
  model.ConferenceOrganization:
    properties:
      cn_name:
        description: 单位名称/二维码名称-中文
        type: string
      conference_id:
        description: 展会ID
        type: integer
      create_admin:
        type: string
      create_time:
        type: string
      deleted:
        type: integer
      en_name:
        description: 单位名称/二维码名称-英文
        type: string
      id:
        type: integer
      logo:
        description: 二维码链接/现场照片
        type: string
      sorting:
        description: 排序
        type: string
      type:
        description: 数据类型（1主办单位，2承办单位，3二维码信息，4现场图片）
        type: string
      update_admin:
        type: string
      update_time:
        type: string
    type: object
  model.ConferenceRegisterUser:
    properties:
      company:
        description: 公司
        type: string
      create_time:
        description: 报名时间
        type: string
      deleted:
        description: 删除状态
        type: integer
      email:
        description: 邮箱
        type: string
      first_name:
        description: 名 （中文只用first_name）
        type: string
      id:
        type: integer
      job_title:
        description: 职位
        type: string
      last_name:
        description: 姓
        type: string
      mobile:
        description: 手机号
        type: string
      register_id:
        description: 报名ID
        type: integer
    type: object
  model.ConferenceRightsInterests:
    properties:
      cn_content:
        description: 权益内容-中文
        type: string
      cn_sorting:
        description: 排序
        type: number
      conference_id:
        description: 展会ID
        type: integer
      create_admin:
        type: string
      create_time:
        type: string
      deleted:
        type: integer
      en_content:
        description: 权益内容-英文
        type: string
      en_sorting:
        description: 排序
        type: number
      id:
        type: integer
      type:
        description: /模版类型 （0权益模版，1人数金额模版）
        type: integer
      update_admin:
        type: string
      update_time:
        type: string
    type: object
  model.ConferenceTicketPrice:
    properties:
      cn_button_link:
        description: 自定义按钮跳转链接-中文
        type: string
      cn_button_name:
        description: 自定义按钮名称-中文
        type: string
      cn_currency_unit:
        description: 货币单位（1中文，2美元，3欧元）
        type: integer
      cn_is_displayed:
        description: 是否显示购买按钮（1展示，0不展示）
        type: integer
      cn_maximum:
        description: 最大人数
        type: integer
      cn_name:
        description: 票种名称中文
        type: string
      cn_registration_page_name:
        description: 报名页门票名称-中文
        type: string
      cn_service_id:
        description: 中文服务ID
        type: string
      cn_sorting:
        description: 排序
        type: number
      cn_standard_fee:
        description: 中文服务金额信息
        type: string
      conference_id:
        description: 展会ID
        type: integer
      create_admin:
        type: string
      create_time:
        type: string
      deleted:
        type: integer
      en_button_link:
        description: 自定义按钮跳转链接-英文
        type: string
      en_button_name:
        description: 自定义按钮名称-英文
        type: string
      en_currency_unit:
        description: 货币单位（1中文，2美元，3欧元）
        type: integer
      en_is_displayed:
        description: 是否显示购买按钮（1展示，0不展示）
        type: integer
      en_maximum:
        description: 最大人数
        type: integer
      en_name:
        description: 票种名称英文
        type: string
      en_registration_page_name:
        description: 报名页门票名称-英文
        type: string
      en_service_id:
        description: 英文服务ID
        type: string
      en_sorting:
        description: 排序
        type: number
      en_standard_fee:
        description: 英文服务金额信息
        type: string
      id:
        type: integer
      type:
        description: /模版类型 （0权益模版，1人数金额模版）
        type: integer
      update_admin:
        type: string
      update_time:
        type: string
    type: object
  model.ContactInformation:
    properties:
      cn_content:
        description: 中文内容
        type: string
      cn_is_displayed:
        description: 中文其他页底是否显示（0  否 1  是）
        type: integer
      cn_title:
        description: 标题-中文
        type: string
      conference_id:
        type: integer
      create_admin:
        type: string
      create_time:
        type: string
      deleted:
        type: integer
      en_content:
        description: 英文内容
        type: string
      en_is_displayed:
        description: 英文其他页底是否显示（0  否 1  是）
        type: integer
      en_title:
        description: 标题-英文
        type: string
      id:
        type: integer
      sorting:
        description: 排序
        type: number
      update_admin:
        type: string
      update_time:
        type: string
    type: object
  model.EventExpertInfo:
    properties:
      company:
        description: 公司
        type: string
      id:
        type: integer
    type: object
  model.EventSchedule:
    properties:
      cn_content:
        description: 会议日程描述--中文
        type: string
      cn_title:
        description: 展会日程名称--中文
        type: string
      conference_id:
        description: 展会id
        type: integer
      create_admin:
        type: string
      create_time:
        type: string
      day_id:
        description: 属于哪一天
        type: integer
      deleted:
        type: integer
      en_content:
        description: 会议日程描述--英文
        type: string
      en_title:
        description: 展会日程名称--英文
        type: string
      event_id:
        description: 活动id
        type: integer
      forum_id:
        description: 分论坛ID
        type: integer
      id:
        type: integer
      is_agenda:
        description: 0议程、1餐食、2咖啡、3鸡尾酒
        type: integer
      schedule_end:
        type: string
      schedule_start:
        type: string
      sorting:
        type: number
      update_admin:
        type: string
      update_time:
        type: string
    type: object
  model.EventScheduleDate:
    properties:
      cn_day_name:
        description: 日期展示名称
        type: string
      conference_id:
        description: 展会ID
        type: integer
      create_admin:
        description: 创建用户
        type: string
      create_time:
        description: 创建时间
        type: string
      date:
        description: 日期值
        type: string
      deleted:
        type: integer
      en_day_name:
        description: 日期展示名称
        type: string
      end_date_time:
        description: 日期时间值
        type: string
      event_id:
        description: 活动ID
        type: integer
      id:
        type: integer
      sorting:
        description: 排序
        type: string
      start_date_time:
        description: 日期时间值
        type: string
      time:
        description: 时间值
        type: string
      update_admin:
        description: 更新用户
        type: string
      update_time:
        description: 更新时间
        type: string
    type: object
  model.EventScheduleForum:
    properties:
      cn_name:
        description: 分论坛名称-中文
        type: string
      conference_id:
        description: 展会ID
        type: integer
      create_admin:
        type: string
      create_time:
        type: string
      day_id:
        description: 日期ID
        type: integer
      deleted:
        type: integer
      en_name:
        description: 分论坛名称-英文
        type: string
      event_id:
        description: 活动ID
        type: integer
      id:
        type: integer
      sorting:
        description: 排序
        type: number
      update_admin:
        type: string
      update_time:
        type: string
    type: object
  model.EventScheduleGuest:
    properties:
      cn_appellation:
        description: 称谓
        type: string
      cn_company:
        description: 公司
        type: string
      cn_position:
        description: 职位
        type: string
      conference_id:
        description: 展会id
        type: integer
      create_admin:
        type: string
      create_time:
        type: string
      deleted:
        type: string
      en_appellation:
        description: 称谓
        type: string
      en_company:
        description: 公司
        type: string
      en_position:
        description: 职位
        type: string
      event_id:
        description: 活动ID
        type: integer
      guest_identity:
        description: 嘉宾身份
        type: string
      id:
        type: integer
      picture:
        description: 图片
        type: string
      schedule_id:
        description: 日程ID
        type: integer
      sorting:
        description: 排序
        type: string
      update_admin:
        type: string
      update_time:
        type: string
    type: object
  model.ForumCategoryConfig:
    properties:
      id:
        type: integer
      name:
        description: 名称
        type: string
    type: object
  model.ForumConfigList:
    properties:
      cn_name:
        description: 中文会议名
        type: string
      conference_id:
        description: 展会ID
        type: integer
      en_name:
        description: 英文会议名
        type: string
      id:
        type: integer
      type:
        type: integer
    type: object
  model.MediaRegistration:
    properties:
      conference_id:
        description: 会展ID
        type: integer
      conference_name:
        description: 会议
        type: string
      country:
        description: 国家
        type: string
      create_admin:
        description: 创建用户
        type: string
      create_time:
        description: 提交时间
        type: string
      email:
        description: 邮箱
        type: string
      first_name:
        description: 姓
        type: string
      from_id:
        description: 渠道
        type: string
      from_name:
        description: 渠道名称
        type: string
      id:
        type: integer
      is_agree_send_message:
        description: 是否同意发送邮件(2同意，1不同意)
        type: integer
      job_title:
        description: 职位
        type: string
      language:
        description: 语言
        type: string
      last_name:
        description: 名
        type: string
      name:
        description: 姓名
        type: string
      organisation:
        description: 组织
        type: string
      phone:
        description: 手机
        type: string
      source_id:
        description: 来源ID
        type: string
      update_admin:
        description: 更新用户
        type: string
      update_time:
        description: 更新时间
        type: string
      user_id:
        description: 用户ID
        type: integer
    required:
    - name
    type: object
  protocol.AdminConferencePrevious:
    properties:
      cn_name:
        description: 名称中文
        type: string
      cn_pdf:
        description: 链接中文
        type: string
      cn_url:
        description: 链接中文
        type: string
      conference_id:
        description: 展会ID
        type: integer
      create_time:
        description: 创建时间
        type: string
      create_user:
        description: 创建人
        type: string
      en_name:
        description: 名称英文
        type: string
      en_pdf:
        description: 链接英文
        type: string
      en_url:
        description: 链接英文
        type: string
      id:
        type: integer
      sort:
        description: 排序
        type: number
      update_time:
        description: 更新时间
        type: string
      update_user:
        description: 更新人
        type: string
    type: object
  protocol.AfterReport:
    properties:
      cn_name:
        description: 展后报告名称
        type: string
      cn_pdf:
        description: 中文展后报告pdf
        type: string
      conference_id:
        description: 展会id
        type: integer
      en_name:
        description: 展后报告英文名称
        type: string
      en_pdf:
        description: 英文展后报告pdf
        type: string
      id:
        description: id
        type: integer
      sort:
        description: 排序
        type: number
    type: object
  protocol.ApplyConference:
    properties:
      cn_apply_conference:
        type: string
      conference_id:
        description: 展会id
        type: integer
      en_apply_conference:
        type: string
    type: object
  protocol.ButtonParam:
    properties:
      cn_name:
        description: 按钮名称中文(必填)
        type: string
      cn_url:
        description: 跳转链接中文(必填)
        type: string
      en_name:
        description: 按钮名称英文(英文链接非空时，英文名称不能为空)
        type: string
      en_url:
        description: 跳转链接英文(英文名称非空时，英文链接不能为空)
        type: string
    type: object
  protocol.CityInfo:
    properties:
      cn_city_overview:
        description: 中文城市概况
        type: string
      conference_id:
        description: 展会id
        type: integer
      en_city_overview:
        description: 英文城市概况
        type: string
    type: object
  protocol.CnVisitor:
    properties:
      cn_company:
        description: 公司
        type: string
      cn_conference_name:
        description: 中文展会名称
        type: string
      department:
        description: 部门
        type: string
      email:
        description: 邮箱
        type: string
      enterprise_type:
        description: 企业类型
        type: string
      from_name:
        type: string
      fromId:
        description: 渠道
        type: string
      id_no:
        description: 身份证号
        type: string
      job_title:
        description: 职位
        type: string
      main_products:
        description: 主推产品
        type: string
      name:
        description: 姓名
        type: string
      order_quantity:
        description: 采购数量
        type: string
      papaer_answer:
        description: 问卷答案
        type: string
      procurement_items:
        description: 采购产品
        type: string
      source:
        description: 来源
        type: string
      submitTime:
        description: 报名时间
        type: string
      telephone:
        description: 移动电话
        type: string
      verification_code:
        description: 验证码
        type: string
      willing:
        description: 是否愿意参与免费组团参观计划
        type: string
    type: object
  protocol.CommonQuestionAnswer:
    properties:
      cn_question_answer:
        description: 答案内容中文
        type: string
      cn_question_content:
        description: 问题内容中文
        type: string
      cn_question_type:
        description: 展商问题类型中文
        type: string
      en_question_answer:
        description: 问题英文
        type: string
      en_question_content:
        description: 问题英文
        type: string
      en_question_type:
        description: 展商问题类型英文
        type: string
      id:
        description: id
        type: integer
      sort:
        description: 排序
        type: number
    type: object
  protocol.CommonQuestionType:
    properties:
      cn_question_type:
        description: 展商问题类型中文
        type: string
      conference_id:
        description: 展会id
        type: integer
      en_question_type:
        description: 展商问题类型英文
        type: string
      id:
        description: 主键id
        type: integer
      sort:
        description: 排序
        type: number
    type: object
  protocol.CompanyBearing:
    properties:
      cn_booth_number:
        description: 中文展位号
        type: string
      cn_content:
        description: 中文正文内容
        type: string
      cn_exhibitor_news_id:
        description: 中文展商新闻id
        type: integer
      cn_exhibitor_venue_id:
        description: 展馆id
        type: integer
      cn_exhibitor_venue_name:
        description: 场馆名称
        type: string
      cn_exhibitor_video_cover:
        description: 中文展商视频封面
        type: string
      cn_exhibitor_video_link:
        description: 中文展商视频
        type: string
      cn_news:
        description: 中文新闻
        type: string
      cn_news_title:
        description: 中文新闻标题
        type: string
      cn_news_url:
        description: 中文新闻链接
        type: string
      conference_id:
        description: 展会id
        type: integer
      en_booth_number:
        description: 英文展位号
        type: string
      logo:
        description: logo的url
        type: string
      pub_time:
        description: 发布时间
        type: string
      source:
        description: 来源
        type: string
    type: object
  protocol.CompanyDirectories:
    properties:
      cn_booth_number:
        description: 中文展位号
        type: string
      cn_conference_company:
        description: 中文展商名称
        type: string
      cn_exhibitor_introduction:
        description: 中文展商介绍
        type: string
      cn_url:
        description: 中文展商链接
        type: string
      cn_venue_name:
        description: 中文场馆名称
        type: string
      en_booth_number:
        description: 英文展位号
        type: string
      en_conference_company:
        description: 英文展商名称
        type: string
      en_url:
        description: 英文展商链接
        type: string
      en_venue_name:
        description: 英文场馆名称
        type: string
      id:
        description: 展商主键id
        type: integer
      logo:
        description: logo的url
        type: string
      production_list:
        description: 新品推荐
        items:
          $ref: '#/definitions/protocol.ProductionManagement'
        type: array
      venue_id:
        description: 场馆id
        type: integer
      year:
        description: 年度
        type: string
    type: object
  protocol.CompanyHandbook:
    properties:
      cn_name:
        description: 中文展商手册名称
        type: string
      cn_pdf:
        description: 中文pdf
        type: string
      conference_id:
        description: 展会id
        type: integer
      en_name:
        description: 英文展商手册名称
        type: string
      en_pdf:
        description: 英文pdf
        type: string
      id:
        description: 展商手册id 0为新增
        type: integer
      sort:
        description: 排序
        type: number
    type: object
  protocol.CompanyManagement:
    properties:
      cn_booth_number:
        description: 中文展位号
        type: string
      cn_conference_company:
        description: 中文展商名称
        type: string
      cn_exhibitor_introduction:
        description: 中文展商介绍
        type: string
      cn_exhibitor_news_id:
        description: 中文展商新闻id
        type: integer
      cn_exhibitor_venue_id:
        description: 展馆id
        type: integer
      cn_exhibitor_video_cover:
        description: 中文展商视频封面
        type: string
      cn_exhibitor_video_link:
        description: 中文展商视频
        type: string
      cn_url:
        description: 中文展商链接
        type: string
      conference_id:
        description: 展会id
        type: integer
      en_booth_number:
        description: 英文展位号
        type: string
      en_conference_company:
        description: 英文展商名称
        type: string
      en_url:
        description: 英文展商链接
        type: string
      id:
        description: 主键id
        type: integer
      logo:
        description: logo的url
        type: string
      sort:
        description: 排序
        type: number
      year:
        description: 年度
        type: string
    type: object
  protocol.ConferenceClue:
    properties:
      booth_type:
        description: 展位类型
        type: string
      cellphone:
        description: 手机号
        type: string
      cn_conference_name:
        description: 展会名称中文
        type: string
      cn_short_name:
        description: 展会简称中文
        type: string
      company:
        description: 公司
        type: string
      conference_id:
        description: 展会id
        type: integer
      country:
        description: 国家
        type: string
      create_time:
        description: 提交时间
        type: string
      email:
        description: 邮箱
        type: string
      en_conference_name:
        description: 展会名称英文
        type: string
      en_short_name:
        description: 展会简称英文
        type: string
      exhibition_area:
        description: 感兴趣的展区
        type: string
      fromId:
        description: 渠道
        type: string
      id:
        description: 主键ID
        type: integer
      job_title:
        description: 职位
        type: string
      name:
        description: 姓名
        type: string
      source_name:
        description: 来源
        type: string
      status:
        description: 分配状态,1已分配,0未分配
        type: integer
      user_id:
        description: 用户id
        type: integer
    type: object
  protocol.ConferenceColumnInfo:
    properties:
      cn_name:
        description: 栏目名称-中文
        type: string
      conference_id:
        description: 展会ID
        type: integer
      conference_information_list:
        description: 栏目数据信息列表
        items:
          $ref: '#/definitions/model.ConferenceInformation'
        type: array
      en_name:
        description: 栏目名称-英文
        type: string
      id:
        type: integer
      is_sub_section:
        type: integer
      sorting:
        description: 排序
        type: number
      sub_section_id:
        type: integer
      sun_column_list:
        description: 子栏目列表
        items:
          $ref: '#/definitions/protocol.ConferenceColumnInfo'
        type: array
      type:
        description: 信息类型（1赞助商&合作商&媒体，2嘉宾）
        type: integer
    type: object
  protocol.ConferenceEventCategoryInfo:
    properties:
      cn_button_link:
        description: 中文预约参会按钮跳转链接
        type: string
      cn_content:
        description: 中文内容
        type: string
      cn_name:
        description: 活动分类名称-中文
        type: string
      cn_place:
        description: 中文地点
        type: string
      cn_title:
        description: 中文标题
        type: string
      conference_id:
        description: 展会id
        type: integer
      en_button_link:
        description: 英文预约参会按钮跳转链接
        type: string
      en_content:
        description: 英文内容
        type: string
      en_name:
        description: 活动分类名称-英文
        type: string
      en_place:
        description: 英文地点
        type: string
      en_title:
        description: 英文标题
        type: string
      end_time:
        description: 结束日期
        type: string
      entry_type:
        description: 入场类型
        type: string
      id:
        type: integer
      picture:
        description: 图片
        type: string
      sorting:
        description: 排序                                                                      //活动时间
        type: number
      start_time:
        description: 开始日期
        type: string
      type:
        description: 关联导航菜单
        type: integer
    type: object
  protocol.ConferenceEventInfo:
    properties:
      category_type:
        description: 所属活动分类（1CLNB展前会，2CLNB主论坛，3CLNB分论坛，4户外赛事，5场馆赛事，6Releasing Stage
          新品发布）
        type: integer
      cn_button_link:
        description: 中文预约参会按钮跳转链接
        type: string
      cn_category_name:
        type: string
      cn_introduce:
        description: 展会活动介绍--中文
        type: string
      cn_link:
        description: 展会活动标题--中文
        type: string
      cn_name:
        description: 展会活动名称--中文
        type: string
      cn_place:
        description: 展会活动地点--中文
        type: string
      cn_sponsors:
        description: 赞助方-中文
        type: string
      cn_sponsors_logo:
        description: 赞助方Logo-中文
        type: string
      cn_sponsors_logo2:
        description: 赞助方Logo-中文
        type: string
      cn_sponsors2:
        description: 赞助方-中文
        type: string
      cn_sponsorship_type:
        description: 赞助类型（0赞助方，1合作伙伴）
        type: string
      cn_sponsorship_type2:
        description: 赞助类型（0赞助方，1合作伙伴）
        type: string
      conference_id:
        description: 展会id
        type: integer
      create_time:
        description: 创建时间
        type: string
      en_button_link:
        description: 英文预约参会按钮跳转链接
        type: string
      en_category_name:
        type: string
      en_introduce:
        description: 展会活动介绍--英文
        type: string
      en_link:
        description: 展会活动标题--英文
        type: string
      en_name:
        description: 展会活动名称--英文
        type: string
      en_place:
        description: 展会活动地点--英文
        type: string
      en_sponsors:
        description: 赞助方-英文
        type: string
      en_sponsors_logo:
        description: 赞助方Logo-英文
        type: string
      en_sponsors_logo2:
        description: 赞助方Logo-英文
        type: string
      en_sponsors2:
        description: 赞助方-英文
        type: string
      en_sponsorship_type:
        description: 赞助类型（0赞助方，1合作伙伴）
        type: string
      en_sponsorship_type2:
        description: 赞助类型（0赞助方，1合作伙伴）
        type: string
      event_time:
        description: 活动时间-开始日期
        type: string
      id:
        type: integer
      is_display_page:
        description: 是否展示活动详情页（0是，1否）
        type: string
      is_sub_section:
        description: 是否子论坛(0不是子论坛，其他时为父级论坛ID)
        type: integer
      schedule_is_forum:
        description: 日程是否有论坛（0无分论坛，1有分论坛）
        type: string
      sorting:
        description: 排序
        type: string
      template_type:
        description: 日程模板 (0 议程&非议程模板,  1是时间&主题 )
        type: integer
      times:
        description: 活动时间
        type: string
    type: object
  protocol.ConferenceExhibition:
    properties:
      cellphone:
        description: 手机号
        type: string
      cn_conference_name:
        description: 展会名称中文
        type: string
      cn_short_name:
        description: 展会简称中文
        type: string
      company:
        description: 公司
        type: string
      conference_id:
        description: 展会id
        type: integer
      country:
        description: 国家
        type: string
      create_time:
        description: 提交时间
        type: string
      email:
        description: 邮箱
        type: string
      en_conference_name:
        description: 展会名称英文
        type: string
      en_short_name:
        description: 展会简称英文
        type: string
      enterprise_type:
        description: 企业类型
        type: string
      exhibition_area:
        description: 感兴趣的展区
        type: string
      from_name:
        description: 渠道名称
        type: string
      fromId:
        description: 渠道
        type: string
      id:
        description: 主键ID
        type: integer
      main_products:
        description: 主推产品
        type: string
      name:
        description: 姓名
        type: string
      source_name:
        description: 来源
        type: string
      status:
        description: 分配状态,1已分配,0未分配
        type: integer
      user_id:
        description: 用户id
        type: integer
    type: object
  protocol.ConferenceNameItem:
    properties:
      conference_id:
        description: 会议ID
        type: integer
      conference_name:
        description: 会议名称
        type: string
    type: object
  protocol.ConferenceNews:
    properties:
      cn_exhibitor_news_id:
        description: 中文展商新闻id
        type: integer
      cn_news:
        description: 中文新闻
        type: string
      cn_news_title:
        description: 中文新闻标题
        type: string
      cn_news_url:
        description: 中文新闻链接
        type: string
      pub_time:
        description: 发布时间
        type: string
      source:
        description: 来源
        type: string
      thumb:
        description: 新闻封面
        type: string
    type: object
  protocol.ConferenceTicketPriceInfo:
    properties:
      cn_button_link:
        description: 自定义按钮跳转链接-中文
        type: string
      cn_button_name:
        description: 自定义按钮名称-中文
        type: string
      cn_currency_unit:
        description: 货币单位（1中文，2美元，3欧元）
        type: integer
      cn_is_displayed:
        description: 是否显示购买按钮（1展示，0不展示）
        type: integer
      cn_maximum:
        description: 最大人数
        type: integer
      cn_name:
        description: 票种名称中文
        type: string
      cn_registration_page_name:
        description: 报名页门票名称-中文
        type: string
      cn_rights_id:
        type: string
      cn_service_id:
        description: 中文服务ID
        type: string
      cn_sorting:
        description: 排序
        type: number
      cn_standard_fee:
        description: 中文服务金额信息
        type: string
      conference_id:
        description: 展会ID
        type: integer
      create_time:
        type: string
      en_button_link:
        description: 自定义按钮跳转链接-英文
        type: string
      en_button_name:
        description: 自定义按钮名称-英文
        type: string
      en_currency_unit:
        description: 货币单位（1中文，2美元，3欧元）
        type: integer
      en_is_displayed:
        description: 是否显示购买按钮（1展示，0不展示）
        type: integer
      en_maximum:
        description: 最大人数
        type: integer
      en_name:
        description: 票种名称英文
        type: string
      en_registration_page_name:
        description: 报名页门票名称-英文
        type: string
      en_rights_id:
        type: string
      en_service_id:
        description: 英文服务ID
        type: string
      en_sorting:
        description: 排序
        type: number
      en_standard_fee:
        description: 英文服务金额信息
        type: string
      id:
        type: integer
    type: object
  protocol.ConferenceTicketsRightsInfo:
    properties:
      cn_content:
        description: 权益内容-中文
        type: string
      conference_id:
        description: 展会id
        type: integer
      en_content:
        description: 权益内容-英文
        type: string
      id:
        type: integer
      is_selected:
        description: 是否被选中 （true已被选中）
        type: boolean
      ticket_id:
        description: 票种ID
        type: integer
    type: object
  protocol.EnVisitor:
    properties:
      city:
        description: 城市
        type: string
      company:
        description: 公司
        type: string
      country_residence:
        description: 现居国家
        type: string
      email:
        description: 邮箱
        type: string
      en_conference_name:
        description: 中文展会名称
        type: string
      enterprise_type:
        description: 企业类型
        type: string
      first_name:
        description: 名
        type: string
      from_name:
        type: string
      fromId:
        description: 渠道
        type: string
      full_name:
        description: 姓名
        type: string
      job_title:
        description: 职位
        type: string
      last_name:
        description: 姓
        type: string
      main_focus:
        description: 关注行业
        type: string
      main_products:
        description: 主推产品
        type: string
      nationality:
        description: 国家
        type: string
      order_quantity:
        description: 采购数量
        type: string
      phone:
        description: 电话
        type: string
      procurement_items:
        description: 采购产品
        type: string
      source:
        description: 来源
        type: string
      submitTime:
        description: 报名时间
        type: string
      telephone:
        description: 移动电话
        type: string
    type: object
  protocol.EventScheduleInfo:
    properties:
      cn_button_link:
        description: 中文预约参会按钮跳转链接
        type: string
      cn_content:
        description: 会议日程描述--中文
        type: string
      cn_title:
        description: 展会日程名称--中文
        type: string
      conference_id:
        description: 展会id
        type: integer
      day_id:
        description: 属于哪一天
        type: integer
      en_button_link:
        description: 英文预约参会按钮跳转链接
        type: string
      en_content:
        description: 会议日程描述--英文
        type: string
      en_title:
        description: 展会日程名称--英文
        type: string
      event_id:
        description: 活动id
        type: integer
      forum_id:
        description: 分论坛ID
        type: integer
      guest_list:
        description: 日程嘉宾列表
        items:
          $ref: '#/definitions/model.EventScheduleGuest'
        type: array
      id:
        type: integer
      is_agenda:
        description: 0议程、1餐食、2咖啡、3鸡尾酒
        type: integer
      schedule_end:
        type: string
      schedule_start:
        type: string
      sorting:
        type: number
    type: object
  protocol.ExhibitionSuccess:
    properties:
      conference_id:
        description: 展会id
        type: integer
      exhibition_qr_code:
        description: 申请参展成功二维码
        type: string
      exhibition_tips:
        description: 申请参展成功文案提示语
        type: string
    type: object
  protocol.FloorGraph:
    properties:
      cn_graph_name:
        description: 中文展馆名称
        type: string
      cn_graph_url:
        description: 中文平面图url
        type: string
      en_graph_name:
        description: 英文展馆名称
        type: string
      en_graph_url:
        description: 英文平面图url
        type: string
      id:
        description: id
        type: integer
      sort:
        description: 排序
        type: number
    type: object
  protocol.FreeVisitor:
    properties:
      cn_visitor:
        $ref: '#/definitions/protocol.CnVisitor'
      en_visitor:
        $ref: '#/definitions/protocol.EnVisitor'
    type: object
  protocol.HallInfo:
    properties:
      cn_floor_graph:
        description: 展馆平面图url
        type: string
      cn_heading:
        description: 上标题-中文
        type: string
      cn_headlining:
        description: 下标题-中文
        type: string
      conference_id:
        description: 展会id
        type: integer
      en_floor_graph:
        description: 英文展馆平面图url
        type: string
      en_heading:
        description: 上标题-英文
        type: string
      en_headlining:
        description: 下标题-英文
        type: string
      google_url:
        description: 谷歌地图分享链接
        type: string
      location_latitude:
        description: 展馆位置纬度
        type: string
      location_longitude:
        description: 展馆位置经度
        type: string
    type: object
  protocol.HandbookContent:
    properties:
      cn_handbook_content:
        description: 展商手册中文页面内容
        type: string
      cn_handbook_title:
        description: 展商手册中文标题
        type: string
      conference_id:
        description: 展会id
        type: integer
      en_handbook_content:
        description: 展商手册英文页面内容
        type: string
      en_handbook_title:
        description: 展商手册英文标题
        type: string
    type: object
  protocol.HotelPage:
    properties:
      cn_booking_url:
        description: 中文预定表单链接
        type: string
      cn_hotel_button:
        description: 中文酒店按钮
        type: string
      cn_hotel_content:
        description: 中文酒店页面内容
        type: string
      cn_hotel_excel:
        description: 中文酒店表格
        type: string
      conference_id:
        description: 展会id
        type: integer
      en_booking_url:
        description: 英文预定表单链接
        type: string
      en_hotel_button:
        description: 英文酒店按钮
        type: string
      en_hotel_content:
        description: 英文酒店页面内容
        type: string
      en_hotel_excel:
        description: 中文酒店表格
        type: string
    type: object
  protocol.IndustryFocus:
    properties:
      id:
        type: integer
      industry_name:
        type: string
    type: object
  protocol.IndustryNews:
    properties:
      cn_news:
        description: 中文新闻
        type: string
      cn_news_title:
        description: 中文新闻标题
        type: string
      cn_news_url:
        description: 中文新闻链接
        type: string
      news_id:
        description: 新闻id
        type: integer
      pub_time:
        description: 发布时间
        type: string
      source:
        description: 来源
        type: string
      thumb:
        description: 新闻封面
        type: string
    type: object
  protocol.Invitation:
    properties:
      cn_invitation:
        description: 中文观展邀请
        type: string
      conference_id:
        description: 展会id
        type: integer
      en_invitation:
        description: 英文观展邀请
        type: string
    type: object
  protocol.OnlineRegister:
    properties:
      cn_content:
        description: 中文内容
        type: string
      cn_url:
        description: 中文在线登记表表单链接
        type: string
      conference_id:
        description: 展会id
        type: integer
      en_content:
        description: 英文内容
        type: string
      en_url:
        description: 英文在线登记表表单链接
        type: string
    type: object
  protocol.Option:
    properties:
      choice:
        description: 选项文案
        type: string
      need_note:
        description: 是否需要补充内容（展示问卷用）
        type: boolean
      need_show:
        description: 是否需要列表页展示
        type: boolean
      sorting:
        description: 排序（展示问卷用）
        type: number
    type: object
  protocol.PaperCollection:
    properties:
      cn_above_name:
        description: 中文按钮上方名称
        type: string
      cn_button_name:
        description: 中文按钮名称
        type: string
      cn_content:
        description: 中文页面内容
        type: string
      cn_form_url:
        description: 中文表单链接
        type: string
      conference_id:
        description: 展会id
        type: integer
      en_above_name:
        description: 英文按钮上方名称
        type: string
      en_button_name:
        description: 英文按钮名称
        type: string
      en_content:
        description: 中文页面内容
        type: string
      en_form_url:
        description: 英文表单链接
        type: string
      id:
        type: integer
    type: object
  protocol.ProductionManagement:
    properties:
      audience_company_id:
        type: integer
      cn_name:
        description: 中文产品名称
        type: string
      en_name:
        description: 英文产品名称
        type: string
      id:
        type: integer
      logo:
        type: string
      sorting:
        description: 排序
        type: number
    type: object
  protocol.QA:
    properties:
      cn_answer:
        description: 中文答案
        type: string
      cn_question:
        description: 中文问题
        type: string
      en_answer:
        description: 英文答案
        type: string
      en_question:
        description: 英文问题
        type: string
      id:
        description: id
        type: integer
    type: object
  protocol.QuestionAndChoice:
    properties:
      choice:
        description: 选项
        items:
          $ref: '#/definitions/protocol.Option'
        type: array
      need_answer:
        description: 是否必答
        type: boolean
      number:
        description: 序号
        type: integer
      question:
        description: 问题内容
        type: string
      question_type:
        description: 问题类型 1 单选 2 多选 3 填空
        type: integer
      sorting:
        description: 排序
        type: number
    type: object
  protocol.QuestionContent:
    properties:
      cn_question_answer:
        description: 答案中文
        type: string
      cn_question_content:
        description: 问题中文
        type: string
      en_question_answer:
        description: 问题英文
        type: string
      en_question_content:
        description: 问题英文
        type: string
      id:
        description: 主键id
        type: integer
      question_type_id:
        description: 问题类型id
        type: integer
      sort:
        description: 排序
        type: number
    type: object
  protocol.QuestionPaper:
    properties:
      questionAndChoiceList:
        description: PaperName             string              `json:"paper_name"`
          //问卷名称
        items:
          $ref: '#/definitions/protocol.QuestionAndChoice'
        type: array
    type: object
  protocol.QuestionPaperAnswer:
    properties:
      choice:
        allOf:
        - $ref: '#/definitions/protocol.Option'
        description: 选项
      question:
        description: 问题内容
        type: string
    type: object
  protocol.QuestionType:
    properties:
      cn_type:
        description: 中文问题类型
        type: string
      en_type:
        description: 英文问题类型
        type: string
      q_as:
        description: 问答内容
        items:
          $ref: '#/definitions/protocol.QA'
        type: array
      type_id:
        description: 问题类型id
        type: integer
    type: object
  protocol.ReqAddConferenceRegister:
    properties:
      bd_vid:
        description: 百度跟踪ID
        type: string
      cellphone:
        description: 手机号
        type: string
      cn_or_en:
        description: 0 中文 1 英文
        type: integer
      company:
        description: 公司
        type: string
      conference_id:
        description: 展会ID
        type: integer
      email:
        description: 邮箱
        type: string
      enterprise_type:
        description: 企业类型
        type: string
      first_name:
        description: 购买人姓名 (中文购买是必填)
        type: string
      fromId:
        description: 渠道
        type: string
      id:
        description: 添加时不传修改时必传
        type: integer
      interest_meetings:
        description: 感兴趣的会议（多个用英文逗号分割）
        type: string
      job_title:
        description: 职位
        type: string
      last_name:
        description: 购买人姓名 (中文购买时不用填写)
        type: string
      main_products:
        description: 主推产品
        type: string
      register_user:
        description: 第二步时再传
        items:
          $ref: '#/definitions/protocol.ReqConferenceRegisterUser'
        type: array
      source_id:
        description: 来源ID
        type: string
      source_name:
        description: 来源名称
        type: string
    type: object
  protocol.ReqAdminConferenceInfo:
    properties:
      button_li:
        description: 跳转按钮
        items:
          $ref: '#/definitions/protocol.ButtonParam'
        type: array
      cn_location:
        description: 地点中文
        type: string
      cn_name:
        description: 展会名称中文
        type: string
      cn_share_content:
        description: 分享内容中文
        type: string
      cn_share_graph:
        description: 分享图链接中文
        type: string
      cn_share_title:
        description: 分享标题中文
        type: string
      cn_top_left_logo:
        description: 左上角logo
        type: string
      en_location:
        description: 地点英文
        type: string
      en_name:
        description: 展会名称英文
        type: string
      en_share_content:
        description: 分享内容英文
        type: string
      en_share_graph:
        description: 分享图链接英文
        type: string
      en_share_title:
        description: 分享标题英文
        type: string
      en_top_left_logo:
        description: 左上角logo
        type: string
      end_time:
        description: 结束时间戳
        type: integer
      h5_back:
        description: H5背景图
        type: string
      id:
        description: 展会ID,大于0表示编辑;0表示新增
        minimum: 0
        type: integer
      meeting_sys_id:
        description: 同步会议系统编号
        type: string
      pc_back:
        description: PC背景图
        type: string
      qwx_url:
        description: 企业微信通知地址
        type: string
      start_time:
        description: 开始时间戳
        type: integer
      tiny_logo:
        description: 小logo
        type: string
      top_logo:
        description: 页顶logo
        type: string
      video_back:
        description: 视频背景
        type: string
    required:
    - cn_location
    - cn_name
    - end_time
    - start_time
    - tiny_logo
    - top_logo
    type: object
  protocol.ReqAdminSaveConferenceEvent:
    properties:
      category_type:
        description: 所属活动分类（1CLNB展前会，2CLNB主论坛，3CLNB分论坛，4户外赛事，5场馆赛事，6Releasing Stage
          新品发布）
        type: integer
      cn_name:
        description: 展会活动名称--中文
        type: string
      cn_sponsors:
        description: 赞助方-中文
        type: string
      cn_sponsors_logo:
        description: 赞助方Logo-中文
        type: string
      cn_sponsors_logo2:
        description: 赞助方Logo-中文
        type: string
      cn_sponsors2:
        description: 赞助方-中文
        type: string
      cn_sponsorship_type:
        description: 赞助类型（0赞助方，1合作伙伴）
        type: string
      cn_sponsorship_type2:
        description: 赞助类型（0赞助方，1合作伙伴）
        type: string
      conference_id:
        description: 展会id
        type: integer
      en_name:
        description: 展会活动名称--英文
        type: string
      en_sponsors:
        description: 赞助方-英文
        type: string
      en_sponsors_logo:
        description: 赞助方Logo-英文
        type: string
      en_sponsors_logo2:
        description: 赞助方Logo-英文
        type: string
      en_sponsors2:
        description: 赞助方-英文
        type: string
      en_sponsorship_type:
        description: 赞助类型（0赞助方，1合作伙伴）
        type: string
      en_sponsorship_type2:
        description: 赞助类型（0赞助方，1合作伙伴）
        type: string
      id:
        description: 添加时不传修改时必传
        type: integer
      is_display_page:
        description: 是否展示活动详情页（0是，1否）
        type: string
      schedule_is_forum:
        description: 日程是否有论坛（0无分论坛，1有分论坛）
        type: string
      sorting:
        description: 排序
        type: string
      start_time:
        description: 活动时间-开始日期
        type: string
      template_type:
        description: 日程模板 (0 议程&非议程模板,  1是时间&主题 )
        type: integer
    type: object
  protocol.ReqAdminSaveConferenceEventCategory:
    properties:
      cn_button_link:
        description: 中文预约参会按钮跳转链接
        type: string
      cn_content:
        description: 中文内容
        type: string
      cn_name:
        description: 活动分类名称-中文
        type: string
      cn_place:
        description: 中文地点
        type: string
      cn_title:
        description: 中文标题
        type: string
      conference_id:
        description: 展会id
        type: integer
      en_button_link:
        description: 英文预约参会按钮跳转链接
        type: string
      en_content:
        description: 英文内容
        type: string
      en_name:
        description: 活动分类名称-英文
        type: string
      en_place:
        description: 英文地点
        type: string
      en_title:
        description: 英文标题
        type: string
      end_time:
        description: 结束日期
        type: string
      entry_type:
        description: 入场类型
        type: string
      id:
        description: 添加时不传修改时必传
        type: integer
      picture:
        description: 图片
        type: string
      sorting:
        description: 排序
        type: number
      start_time:
        description: 开始日期
        type: string
      type:
        description: 关联导航菜单（1CLNB展前会，2CLNB主论坛，3CLNB分论坛，4户外赛事，5场馆赛事，6Releasing Stage
          新品发布）
        type: integer
    type: object
  protocol.ReqAdminUpdConferenceRegister:
    properties:
      adminEmail:
        type: string
      amount:
        type: number
      coupon_code:
        type: string
      currency_unit:
        description: 1人民币   2美金  3欧元
        type: integer
      custom_info:
        type: string
      id:
        type: integer
      off_vaule:
        type: string
      order_number:
        type: string
      order_status:
        type: integer
      pay_method:
        type: string
      quantity:
        type: integer
      service_id:
        type: string
      service_name:
        type: string
      source:
        type: string
      user_id:
        type: integer
    type: object
  protocol.ReqConferenceRegisterUser:
    properties:
      company:
        description: 公司
        type: string
      email:
        description: 邮箱
        type: string
      first_name:
        description: 名 （中文只用first_name）
        type: string
      job_title:
        description: 职位
        type: string
      last_name:
        description: 姓 购买人姓名 (中文购买时不用填写)
        type: string
      mobile:
        description: 手机号
        type: string
      user_id:
        type: integer
    type: object
  protocol.ReqSaveFreeVisitorInfo:
    properties:
      bd_vid:
        description: 百度跟踪ID
        type: string
      cn_or_en:
        description: 0 中文 1 英文
        type: integer
      cn_visitor:
        $ref: '#/definitions/protocol.CnVisitor'
      conference_id:
        type: integer
      en_visitor:
        $ref: '#/definitions/protocol.EnVisitor'
      fromId:
        description: 渠道
        type: string
      source_id:
        description: 来源
        type: string
    required:
    - conference_id
    type: object
  protocol.ReqSaveProductionManagement:
    properties:
      audience_company_id:
        description: 展商id
        type: integer
      cn_name:
        description: 中文产品名称
        type: string
      en_name:
        description: 英文产品名称
        type: string
      id:
        description: 主键id
        type: integer
      logo:
        type: string
      sorting:
        description: 排序
        type: number
    type: object
  protocol.ReqSaveQuestionPaper:
    properties:
      content:
        description: 问卷内容
        items:
          $ref: '#/definitions/protocol.QuestionPaperAnswer'
        type: array
      id:
        description: 上一步提交基本信息返回的id
        type: integer
    type: object
  protocol.ReqSaveVenue:
    properties:
      cn_venue_name:
        description: 中文场馆名称
        type: string
      conference_id:
        type: integer
      en_venue_name:
        description: 英文场馆名称
        type: string
      id:
        type: integer
      sorting:
        description: 排序
        type: number
      userName:
        type: string
    type: object
  protocol.ResAboutUs:
    properties:
      cn_about_us:
        type: string
      conference_id:
        type: integer
      create_time:
        type: string
      create_user:
        type: string
      en_about_us:
        type: string
      update_time:
        type: string
      update_user:
        type: string
    type: object
  protocol.ResAdminColumnList:
    properties:
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        description: 赞助商，合作方，媒体信息
        items:
          $ref: '#/definitions/model.ConferenceColumn'
        type: array
    type: object
  protocol.ResAdminConferenceBottomPageList:
    properties:
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        items:
          $ref: '#/definitions/model.ConferenceBottomPage'
        type: array
    type: object
  protocol.ResAdminConferenceChannelSourceList:
    properties:
      list:
        items:
          $ref: '#/definitions/model.ChannelSource'
        type: array
    type: object
  protocol.ResAdminConferenceDataList:
    properties:
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        items:
          $ref: '#/definitions/model.ConferenceData'
        type: array
    type: object
  protocol.ResAdminConferenceEventCategoryList:
    properties:
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        items:
          $ref: '#/definitions/protocol.ConferenceEventCategoryInfo'
        type: array
    type: object
  protocol.ResAdminConferenceEventList:
    properties:
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        items:
          $ref: '#/definitions/protocol.ConferenceEventInfo'
        type: array
    type: object
  protocol.ResAdminConferenceList:
    properties:
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        items:
          $ref: '#/definitions/protocol.ResConferenceInfo'
        type: array
    type: object
  protocol.ResAdminConferenceNameList:
    properties:
      list:
        description: 会议名称列表
        items:
          $ref: '#/definitions/protocol.ConferenceNameItem'
        type: array
    type: object
  protocol.ResAdminConferenceOrganizationList:
    properties:
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        items:
          $ref: '#/definitions/model.ConferenceOrganization'
        type: array
    type: object
  protocol.ResAdminConferenceRegisterList:
    properties:
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        items:
          $ref: '#/definitions/protocol.ResConferenceRegisterList'
        type: array
    type: object
  protocol.ResAdminConferenceRegisterUserList:
    properties:
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        items:
          $ref: '#/definitions/model.ConferenceRegisterUser'
        type: array
    type: object
  protocol.ResAdminContactInformationList:
    properties:
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        items:
          $ref: '#/definitions/model.ContactInformation'
        type: array
    type: object
  protocol.ResAdminEnterpriseTypeList:
    properties:
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        items:
          $ref: '#/definitions/model.ConferenceEnterpriseType'
        type: array
    type: object
  protocol.ResAdminEventScheduleForumList:
    properties:
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        items:
          $ref: '#/definitions/model.EventScheduleForum'
        type: array
    type: object
  protocol.ResAdminEventScheduleGuestList:
    properties:
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        items:
          $ref: '#/definitions/model.EventScheduleGuest'
        type: array
    type: object
  protocol.ResAdminEventScheduleList:
    properties:
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        items:
          $ref: '#/definitions/model.EventSchedule'
        type: array
    type: object
  protocol.ResAdminInformationList:
    properties:
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        items:
          $ref: '#/definitions/model.ConferenceInformation'
        type: array
    type: object
  protocol.ResAdminMediaRegistrationList:
    properties:
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        items:
          $ref: '#/definitions/model.MediaRegistration'
        type: array
    type: object
  protocol.ResAdminPreviousList:
    properties:
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        items:
          $ref: '#/definitions/protocol.AdminConferencePrevious'
        type: array
    type: object
  protocol.ResAdminRightsInterestsList:
    properties:
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        items:
          $ref: '#/definitions/model.ConferenceRightsInterests'
        type: array
    type: object
  protocol.ResAdminRightsTicketList:
    properties:
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        items:
          $ref: '#/definitions/protocol.ConferenceTicketsRightsInfo'
        type: array
    type: object
  protocol.ResAdminSubscribeList:
    properties:
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        items:
          $ref: '#/definitions/protocol.ResHandBookSubscribeInfo'
        type: array
    type: object
  protocol.ResAdminTicketPriceList:
    properties:
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        items:
          $ref: '#/definitions/model.ConferenceTicketPrice'
        type: array
    type: object
  protocol.ResAnnualSelectionList:
    properties:
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        items:
          $ref: '#/definitions/model.AnnualSelection'
        type: array
    type: object
  protocol.ResAudiencePreRegister:
    properties:
      cn_handbook_name:
        description: 中文手册名称
        type: string
      cn_title:
        description: 中文页面标题
        type: string
      cn_url:
        description: 中文观众手册url
        type: string
      cn_visiting_value:
        description: 中文参观价值
        type: string
      conference_id:
        description: 展会id
        type: integer
      en_handbook_name:
        description: 英文手册名称
        type: string
      en_title:
        description: 英文页面标题
        type: string
      en_url:
        description: 英文观众手册url
        type: string
      en_visiting_value:
        description: 英文参观价值
        type: string
    type: object
  protocol.ResAudienceVisitingInfo:
    properties:
      cn_visiting_value:
        description: 中文参观价值
        type: string
      conference_id:
        description: 展会id
        type: integer
      en_visiting_value:
        description: 英文参观价值
        type: string
    type: object
  protocol.ResButtonInfo:
    properties:
      cn_name:
        description: 按钮名称中文
        type: string
      cn_url:
        description: 跳转链接
        type: string
      en_name:
        description: 按钮名称英文
        type: string
      en_url:
        description: 跳转链接英文
        type: string
      id:
        type: integer
    type: object
  protocol.ResCommonQA:
    properties:
      cn_question_types:
        description: 问题类型结构体
        items:
          $ref: '#/definitions/protocol.QuestionType'
        type: array
    type: object
  protocol.ResCommonQuestionAnswer:
    properties:
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        items:
          $ref: '#/definitions/protocol.CommonQuestionAnswer'
        type: array
    type: object
  protocol.ResCommonQuestionType:
    properties:
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        items:
          $ref: '#/definitions/protocol.CommonQuestionType'
        type: array
    type: object
  protocol.ResCompanyBearingList:
    properties:
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        items:
          $ref: '#/definitions/protocol.CompanyBearing'
        type: array
    type: object
  protocol.ResCompanyHandbook:
    properties:
      cn_name:
        description: 中文展商手册名称
        type: string
      cn_pdf:
        description: 中文pdf
        type: string
      conference_id:
        description: 展会id
        type: integer
      en_name:
        description: 英文展商手册名称
        type: string
      en_pdf:
        description: 英文pdf
        type: string
      id:
        description: id
        type: integer
      sort:
        description: 排序
        type: number
    type: object
  protocol.ResCompanyHandbookList:
    properties:
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        items:
          $ref: '#/definitions/protocol.ResCompanyHandbook'
        type: array
    type: object
  protocol.ResConferenceClueList:
    properties:
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        items:
          $ref: '#/definitions/protocol.ConferenceClue'
        type: array
    type: object
  protocol.ResConferenceEvent:
    properties:
      cn_date:
        type: string
      date:
        type: string
      en_date:
        type: string
      event_list:
        items:
          $ref: '#/definitions/protocol.ConferenceEventInfo'
        type: array
      id:
        type: integer
    type: object
  protocol.ResConferenceExhibitionList:
    properties:
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        items:
          $ref: '#/definitions/protocol.ConferenceExhibition'
        type: array
    type: object
  protocol.ResConferenceFromConfigList:
    properties:
      list:
        description: 票种列表
        items:
          $ref: '#/definitions/model.ConferenceFromConfig'
        type: array
    type: object
  protocol.ResConferenceInfo:
    properties:
      button_li:
        description: 跳转按钮
        items:
          $ref: '#/definitions/protocol.ResButtonInfo'
        type: array
      cn_after_report:
        description: 中文上届报告
        type: string
      cn_floor_graph:
        description: 展馆平面图url
        type: string
      cn_location:
        description: 地点中文
        type: string
      cn_name:
        description: 展会名称中文
        type: string
      cn_share_content:
        description: 分享内容中文
        type: string
      cn_share_graph:
        description: 分享图链接中文
        type: string
      cn_share_title:
        description: 分享标题中文
        type: string
      cn_short_name:
        description: 展会简称中文
        type: string
      cn_top_left_logo:
        description: 左上角logo
        type: string
      create_time:
        description: 创建时间
        type: string
      create_user:
        description: 创建人
        type: string
      en_after_report:
        description: 英文上届报告
        type: string
      en_floor_graph:
        description: 展馆平面图url
        type: string
      en_location:
        description: 地点英文
        type: string
      en_name:
        description: 展会名称英文
        type: string
      en_share_content:
        description: 分享内容英文
        type: string
      en_share_graph:
        description: 分享图链接英文
        type: string
      en_share_title:
        description: 分享标题英文
        type: string
      en_short_name:
        description: 展会简称英文
        type: string
      en_top_left_logo:
        description: 左上角logo
        type: string
      end_time:
        description: 结束时间
        type: string
      h5_back:
        description: H5背景图
        type: string
      id:
        description: 展会ID
        type: integer
      meeting_sys_id:
        description: FloorGraph     string          `json:"floor_graph"`      // 展馆平面图url
        type: string
      pc_back:
        description: PC背景图
        type: string
      qwx_url:
        description: 企业微信通知地址
        type: string
      start_time:
        description: 开始时间
        type: string
      status:
        description: 展会状态:1正常;2下线;3删除
        type: integer
      template_type:
        description: 展会日程模版类型:1时间&主题;0议程&非议程
        type: integer
      tiny_logo:
        description: 小logo
        type: string
      top_logo:
        description: 页顶logo
        type: string
      update_time:
        description: 更新时间
        type: string
      update_user:
        description: 更新人
        type: string
      video_back:
        description: 视频背景
        type: string
    type: object
  protocol.ResConferenceRegister:
    properties:
      company:
        description: 公司
        type: string
      conference_id:
        description: 展会ID
        type: integer
      conference_name:
        description: 展会名称
        type: string
      create_time:
        description: 报名时间
        type: string
      email:
        description: 邮箱
        type: string
      first_name:
        description: 名 （中文只用first_name）
        type: string
      id:
        type: integer
      last_name:
        description: 姓
        type: string
      mobile:
        description: 手机号
        type: string
      order_id:
        description: 订单号ID
        type: string
      order_num:
        description: 订单数量
        type: integer
      order_status:
        description: 订单状态
        type: integer
      service_id:
        description: 服务ID
        type: string
      source_id:
        description: 来源ID
        type: string
      source_name:
        description: 来源名称
        type: string
      user_id:
        description: 购买用户ID
        type: integer
    type: object
  protocol.ResConferenceRegisterList:
    properties:
      company:
        description: 公司
        type: string
      conference_id:
        description: 展会ID
        type: integer
      conference_name:
        description: 展会名称
        type: string
      create_time:
        description: 报名时间
        type: string
      email:
        description: 邮箱
        type: string
      enterprise_type:
        description: 企业类型
        type: string
      first_name:
        description: 名 （中文只用first_name）
        type: string
      fromId:
        description: 渠道
        type: string
      id:
        type: integer
      interested_meetings:
        description: 感兴趣的会议（多个用英文逗号分割）
        type: string
      last_name:
        description: 姓
        type: string
      main_products:
        description: 主推产品
        type: string
      mobile:
        description: 手机号
        type: string
      order_id:
        description: 订单号ID
        type: string
      order_num:
        description: 订单数量
        type: integer
      order_status:
        description: 订单状态
        type: integer
      register_num:
        description: 参会人信息
        type: integer
      service_id:
        description: 服务ID
        type: string
      source_id:
        description: 来源ID
        type: string
      source_name:
        description: 来源名称
        type: string
      user_company:
        description: 公司
        type: string
      user_email:
        description: 邮箱
        type: string
      user_first_name:
        description: 名 （中文只用first_name）
        type: string
      user_id:
        description: 购买用户ID
        type: integer
      user_job_title:
        description: 职位
        type: string
      user_last_name:
        description: 姓
        type: string
      user_mobile:
        description: 手机号
        type: string
    type: object
  protocol.ResConferenceRightsInterestsInfo:
    properties:
      cn_content:
        description: 权益内容-中文
        type: string
      cn_sorting:
        description: 排序
        type: number
      conference_id:
        description: 展会ID
        type: integer
      en_content:
        description: 权益内容-英文
        type: string
      en_sorting:
        description: 排序
        type: number
      id:
        type: integer
      type:
        description: /模版类型 （0权益模版，1人数金额模版）
        type: integer
    type: object
  protocol.ResConferenceTicketConfigInfo:
    properties:
      cn_price_template:
        description: 价格模板 (0 人数金额模版,  1 票种权益表格模版)
        type: integer
      conference_id:
        description: 展会ID
        type: integer
      en_price_template:
        description: 价格模板 (0 人数金额模版,  1 票种权益表格模版)
        type: integer
      id:
        type: integer
    type: object
  protocol.ResContactUs:
    properties:
      cn_contact:
        description: 联系我们内容-中文
        type: string
      conference_id:
        description: 展会id
        type: integer
      create_time:
        type: string
      create_user:
        type: string
      en_contact:
        description: 联系我们内容-英文
        type: string
      id:
        description: 主键id
        type: integer
      sort:
        description: 排序
        type: integer
      update_time:
        type: string
      update_user:
        type: string
    type: object
  protocol.ResContactUsList:
    properties:
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        items:
          $ref: '#/definitions/protocol.ResContactUs'
        type: array
    type: object
  protocol.ResEventExpertInfoList:
    properties:
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        items:
          $ref: '#/definitions/model.EventExpertInfo'
        type: array
    type: object
  protocol.ResForumCategoryConfigList:
    properties:
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        description: 日程列表
        items:
          $ref: '#/definitions/model.ForumCategoryConfig'
        type: array
    type: object
  protocol.ResFreeVisitorInfo:
    properties:
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        items:
          $ref: '#/definitions/protocol.FreeVisitor'
        type: array
    type: object
  protocol.ResHandBookSubscribeInfo:
    properties:
      cellphone:
        description: 手机
        type: string
      cn_conference_name:
        description: 展会名称中文
        type: string
      conference_id:
        description: 展会ID
        type: integer
      create_time:
        description: 提交时间
        type: string
      email:
        description: 邮箱
        type: string
      en_conference_name:
        description: 展会名称英文
        type: string
      id:
        type: integer
    type: object
  protocol.ResIndustryInfo:
    properties:
      company_bearing_list:
        items:
          $ref: '#/definitions/protocol.CompanyBearing'
        type: array
      conference_news:
        items:
          $ref: '#/definitions/protocol.ConferenceNews'
        type: array
      industry_news_list:
        items:
          $ref: '#/definitions/protocol.IndustryNews'
        type: array
    type: object
  protocol.ResMapInfo:
    properties:
      conference_id:
        description: 展会ID
        type: integer
      short_name:
        description: 展会简称
        type: string
    type: object
  protocol.ResPageFloorGraph:
    properties:
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        description: 展览平面图
        items:
          $ref: '#/definitions/protocol.FloorGraph'
        type: array
    type: object
  protocol.ResPageProductionManagement:
    properties:
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        items:
          $ref: '#/definitions/protocol.ProductionManagement'
        type: array
    type: object
  protocol.ResPagination:
    properties:
      current_page:
        description: 当前页码
        type: integer
      page_count:
        description: 总页数
        type: integer
      page_size:
        description: 每页大小
        type: integer
      total:
        description: 总数
        type: integer
    type: object
  protocol.ResSearchData:
    properties:
      category_type:
        description: 关联导航菜单（1CLNB展前会，2CLNB主论坛，3CLNB分论坛，4户外赛事，5场馆赛事，6Releasing Stage
          新品发布）
        type: integer
      content:
        description: 内容
        type: string
      data_id:
        description: 数据ID(用于跳转传参)
        type: integer
      data_type:
        description: 数据类型(1:活动,2:展商,3:嘉宾)
        type: integer
      title:
        description: 标题
        type: string
    type: object
  protocol.ResSearchDataList:
    properties:
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        items:
          $ref: '#/definitions/protocol.ResSearchData'
        type: array
    type: object
  protocol.ResSubmitFreeVisitor:
    properties:
      code_photo:
        description: 二维码
        type: string
      company:
        description: 公司
        type: string
      end_time:
        description: 结束时间
        type: string
      error_string:
        description: 错误信息
        type: string
      id:
        description: 主键
        type: integer
      location:
        description: 会议地点
        type: string
      name:
        description: 名称
        type: string
      start_time:
        description: 开始时间
        type: string
    type: object
  protocol.ResSubmitQuestionPaper:
    properties:
      code_photo:
        description: 二维码
        type: string
      company:
        description: 公司
        type: string
      end_time:
        description: 结束时间
        type: string
      error_string:
        description: 错误信息
        type: string
      location:
        description: 会议地点
        type: string
      name:
        description: 名称
        type: string
      start_time:
        description: 开始时间
        type: string
    type: object
  protocol.ResUserConferenceEventList:
    properties:
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        description: Category ConferenceEventCategory `json:"category_info"`
        items:
          $ref: '#/definitions/protocol.ResConferenceEvent'
        type: array
    type: object
  protocol.ResUserEventScheduleForumList:
    properties:
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        description: 日程列表
        items:
          $ref: '#/definitions/model.EventScheduleForum'
        type: array
    type: object
  protocol.ResUserEventScheduleList:
    properties:
      category_type:
        description: 活动分类
        type: integer
      cn_button_link:
        description: 中文预约参会按钮跳转链接
        type: string
      cn_name:
        description: 活动名称-中文
        type: string
      cn_sponsors:
        description: 赞助方-中文
        type: string
      cn_sponsors_logo:
        description: 赞助方Logo-中文
        type: string
      cn_sponsors_logo2:
        description: 赞助方Logo-中文
        type: string
      cn_sponsors2:
        description: 赞助方-中文
        type: string
      cn_sponsorship_type:
        description: 赞助类型（0赞助方，1合作伙伴）
        type: string
      cn_sponsorship_type2:
        description: 赞助类型（0赞助方，1合作伙伴）
        type: string
      en_button_link:
        description: 英文预约参会按钮跳转链接
        type: string
      en_name:
        description: 活动名称-英文
        type: string
      en_sponsors:
        description: 赞助方-英文
        type: string
      en_sponsors_logo:
        description: 赞助方Logo-英文
        type: string
      en_sponsors_logo2:
        description: 赞助方Logo-英文
        type: string
      en_sponsors2:
        description: 赞助方-英文
        type: string
      en_sponsorship_type:
        description: 赞助类型（0赞助方，1合作伙伴）
        type: string
      en_sponsorship_type2:
        description: 赞助类型（0赞助方，1合作伙伴）
        type: string
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        description: 日程列表
        items:
          $ref: '#/definitions/protocol.EventScheduleInfo'
        type: array
      template_type:
        description: 日程模版类型
        type: integer
    type: object
  protocol.ResUserForumConfigList:
    properties:
      list:
        description: 票种列表
        items:
          $ref: '#/definitions/model.ForumConfigList'
        type: array
    type: object
  protocol.ResUserPreviousList:
    properties:
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        items:
          $ref: '#/definitions/protocol.UserPreviousInfo'
        type: array
    type: object
  protocol.ResUserTicketPriceList:
    properties:
      cn_price_template:
        description: 价格模板 (0 人数金额模版,  1 票种权益表格模版)
        type: integer
      cn_rights_list:
        description: 权益列表
        items:
          $ref: '#/definitions/model.ConferenceRightsInterests'
        type: array
      en_price_template:
        description: 价格模板 (0 人数金额模版,  1 票种权益表格模版)
        type: integer
      en_rights_list:
        description: 权益列表
        items:
          $ref: '#/definitions/model.ConferenceRightsInterests'
        type: array
      info:
        $ref: '#/definitions/protocol.ResPagination'
      list:
        description: 票种列表
        items:
          $ref: '#/definitions/protocol.ConferenceTicketPriceInfo'
        type: array
    type: object
  protocol.ResWebConferenceIntroductionList:
    properties:
      cn_conference_bottom_page:
        description: 大会组织结构底部页面列表-中文
        items:
          $ref: '#/definitions/model.ConferenceBottomPage'
        type: array
      conference_data:
        description: 大会数据列表
        items:
          $ref: '#/definitions/model.ConferenceData'
        type: array
      conference_id:
        type: integer
      conference_organization:
        description: 大会组织结构列表
        items:
          $ref: '#/definitions/model.ConferenceOrganization'
        type: array
      contact_information:
        description: 大会组织-联系信息
        items:
          $ref: '#/definitions/model.ContactInformation'
        type: array
      en_conference_bottom_page:
        description: 大会组织结构底部页面列表-英文
        items:
          $ref: '#/definitions/model.ConferenceBottomPage'
        type: array
      info:
        $ref: '#/definitions/protocol.ResPagination'
      introduction_video:
        description: 介绍视频
        type: string
      video_cover:
        description: 视频封面
        type: string
    type: object
  protocol.ResWebGuestInformationList:
    properties:
      guest_list:
        description: 嘉宾信息
        items:
          $ref: '#/definitions/model.ConferenceInformation'
        type: array
      info:
        $ref: '#/definitions/protocol.ResPagination'
    type: object
  protocol.RespAdminConferenceEventCategoryInfo:
    properties:
      cn_button_link:
        description: 中文预约参会按钮跳转链接
        type: string
      cn_content:
        description: 中文内容
        type: string
      cn_name:
        description: 活动分类名称-中文
        type: string
      cn_place:
        description: 中文地点
        type: string
      cn_title:
        description: 中文标题
        type: string
      conference_id:
        description: 展会id
        type: integer
      en_button_link:
        description: 英文预约参会按钮跳转链接
        type: string
      en_content:
        description: 英文内容
        type: string
      en_name:
        description: 活动分类名称-英文
        type: string
      en_place:
        description: 英文地点
        type: string
      en_title:
        description: 英文标题
        type: string
      end_time:
        description: 结束日期
        type: string
      entry_type:
        description: 入场类型
        type: string
      id:
        type: integer
      picture:
        description: 图片
        type: string
      sorting:
        description: 排序
        type: number
      start_time:
        description: 开始日期
        type: string
      type:
        description: 关联导航菜单（1CLNB展前会，2CLNB主论坛，3CLNB分论坛，4户外赛事，5场馆赛事，6Releasing Stage
          新品发布）
        type: integer
    type: object
  protocol.RespAdminConferenceEventInfo:
    properties:
      category_type:
        description: 所属活动分类（1CLNB展前会，2CLNB主论坛，3CLNB分论坛，4户外赛事，5场馆赛事，6Releasing Stage
          新品发布）
        type: integer
      cn_name:
        description: 展会活动名称--中文
        type: string
      cn_sponsors:
        description: 赞助方-中文
        type: string
      cn_sponsors_logo:
        description: 赞助方Logo-中文
        type: string
      cn_sponsors_logo2:
        description: 赞助方Logo-中文
        type: string
      cn_sponsors2:
        description: 赞助方-中文
        type: string
      cn_sponsorship_type:
        description: 赞助类型（0赞助方，1合作伙伴）
        type: string
      cn_sponsorship_type2:
        description: 赞助类型（0赞助方，1合作伙伴）
        type: string
      conference_id:
        description: 展会id
        type: integer
      deleted:
        type: integer
      en_name:
        description: 展会活动名称--英文
        type: string
      en_sponsors:
        description: 赞助方-英文
        type: string
      en_sponsors_logo:
        description: 赞助方Logo-英文
        type: string
      en_sponsors_logo2:
        description: 赞助方Logo-英文
        type: string
      en_sponsors2:
        description: 赞助方-英文
        type: string
      en_sponsorship_type:
        description: 赞助类型（0赞助方，1合作伙伴）
        type: string
      en_sponsorship_type2:
        description: 赞助类型（0赞助方，1合作伙伴）
        type: string
      event_time:
        description: 活动时间-开始日期
        type: string
      id:
        type: integer
      is_display_page:
        description: 是否展示活动详情页（0是，1否）
        type: string
      schedule_date_list:
        description: 日程日期信息列表
        items:
          $ref: '#/definitions/model.EventScheduleDate'
        type: array
      schedule_is_forum:
        description: 日程是否有论坛（0无分论坛，1有分论坛）
        type: string
      schedule_total:
        description: 活动日程总天数
        type: integer
      sorting:
        description: 排序
        type: string
    type: object
  protocol.RespBottomPageInfo:
    properties:
      cn_logo:
        description: 二维码链接-中文
        type: string
      cn_name:
        description: 单位名称/二维码名称-中文
        type: string
      cn_sorting:
        description: 排序-中文
        type: string
      conference_id:
        type: integer
      create_admin:
        type: string
      create_time:
        type: string
      deleted:
        type: string
      en_logo:
        description: 二维码链接-英文
        type: string
      en_name:
        description: 单位名称/二维码名称-英文
        type: string
      en_sorting:
        description: 排序-英文
        type: string
      id:
        type: integer
      update_admin:
        type: string
      update_time:
        type: string
    type: object
  protocol.RespConferenceColumnInfo:
    properties:
      cn_name:
        description: 论坛名称
        type: string
      conference_id:
        description: 展会ID
        type: integer
      create_admin:
        type: string
      create_time:
        type: string
      deleted:
        type: integer
      en_name:
        description: 论坛名称
        type: string
      id:
        description: 服务ID
        type: integer
      is_sub_section:
        type: integer
      sorting:
        description: 排序
        type: number
      update_admin:
        type: string
      update_time:
        type: string
    type: object
  protocol.RespConferenceInformationInfo:
    properties:
      cn_appellation:
        description: 称谓
        type: string
      cn_company:
        description: 公司
        type: string
      cn_content:
        description: 嘉宾介绍--中文
        type: string
      cn_link:
        description: 链接
        type: string
      cn_picture:
        description: logo图-中文
        type: string
      cn_position:
        description: 职位
        type: string
      cn_sorting:
        description: 排序-中文
        type: number
      column_id:
        description: 栏目ID
        type: integer
      conference_id:
        description: 展会ID
        type: integer
      create_admin:
        type: string
      create_time:
        type: string
      deleted:
        type: integer
      en_appellation:
        description: 称谓
        type: string
      en_company:
        description: 公司
        type: string
      en_content:
        description: 嘉宾介绍--英文
        type: string
      en_link:
        description: 链接
        type: string
      en_picture:
        description: logo图-英文
        type: string
      en_position:
        description: 职位
        type: string
      en_sorting:
        description: 排序-英文
        type: number
      id:
        description: 服务ID
        type: integer
      sorting:
        description: 排序
        type: number
      type:
        description: 信息类型（1赞助商&合作商&媒体，2嘉宾）
        type: integer
      update_admin:
        type: string
      update_time:
        type: string
    type: object
  protocol.RespConferenceSponsorInfo:
    properties:
      cn_button:
        description: 赞助按钮名称-中文
        type: string
      cn_content:
        type: string
      cn_pdf:
        type: string
      cn_title:
        description: 赞助标题-中文
        type: string
      conference_id:
        type: integer
      en_button:
        description: 赞助按钮名称-英文
        type: string
      en_content:
        type: string
      en_pdf:
        type: string
      en_title:
        description: 赞助标题-英文
        type: string
      id:
        type: integer
    type: object
  protocol.RespEventDateInfo:
    properties:
      date:
        description: 日期值
        type: string
      time:
        description: 时间值
        type: string
    type: object
  protocol.RespEventScheduleGuestInfo:
    properties:
      cn_appellation:
        description: 称谓
        type: string
      cn_company:
        description: 公司
        type: string
      cn_position:
        description: 职位
        type: string
      conference_id:
        description: 展会id
        type: integer
      deleted:
        type: integer
      en_appellation:
        description: 称谓
        type: string
      en_company:
        description: 公司
        type: string
      en_position:
        description: 职位
        type: string
      guest_identity:
        description: 嘉宾身份
        type: string
      id:
        type: integer
      picture:
        description: 图片
        type: string
      schedule_id:
        description: 日程ID
        type: integer
      sorting:
        description: 排序
        type: string
    type: object
  protocol.RespEventScheduleInfo:
    properties:
      cn_content:
        description: 会议日程描述--中文
        type: string
      cn_title:
        description: 展会日程名称--中文
        type: string
      conference_id:
        description: 展会id
        type: integer
      day_id:
        description: 属于哪一天
        type: integer
      deleted:
        type: integer
      en_content:
        description: 会议日程描述--英文
        type: string
      en_title:
        description: 展会日程名称--英文
        type: string
      event_id:
        description: 活动id
        type: integer
      forum_id:
        description: 分论坛ID
        type: integer
      id:
        type: integer
      is_agenda:
        description: 0议程、1餐食、2咖啡、3鸡尾酒
        type: integer
      schedule_end:
        description: 日程结束时间 （一般展示时分"15:28"）
        type: string
      schedule_start:
        description: 日程开始时间 （一般展示时分"15:18"）
        type: string
      sorting:
        description: 排序
        type: number
    type: object
  protocol.RespIntroductionDataInfo:
    properties:
      cn_data_name:
        description: 指标名称-中文
        type: string
      cn_data_value:
        description: 指标数据-中文
        type: string
      conference_id:
        type: integer
      create_admin:
        type: string
      create_time:
        type: string
      data_image:
        description: /图标图片
        type: string
      deleted:
        type: string
      en_data_name:
        description: 指标名称-英文
        type: string
      en_data_value:
        description: 指标数据-英文
        type: string
      id:
        type: integer
      sorting:
        description: 排序
        type: number
      update_admin:
        type: string
      update_time:
        type: string
    type: object
  protocol.RespIntroductionInfo:
    properties:
      cn_content:
        description: 大会介绍-页面内容-中文
        type: string
      conference_id:
        description: 展会ID
        type: integer
      en_content:
        description: 大会介绍-页面内容-英文
        type: string
      id:
        type: integer
      introduction_diagram:
        description: 会议介绍配图
        type: string
      introduction_video:
        description: 介绍视频
        type: string
      video_cover:
        description: 视频封面
        type: string
    type: object
  protocol.RespOrganizationInfo:
    properties:
      cn_name:
        description: 单位名称/二维码名称-中文
        type: string
      conference_id:
        type: integer
      create_admin:
        type: string
      create_time:
        type: string
      deleted:
        type: string
      en_name:
        description: 单位名称/二维码名称-英文
        type: string
      id:
        type: integer
      logo:
        description: 二维码链接
        type: string
      sorting:
        description: 排序
        type: string
      type:
        description: 数据类型（1主办单位，2承办单位，3二维码信息，4现场图片）
        type: string
      update_admin:
        type: string
      update_time:
        type: string
    type: object
  protocol.RespTicketPriceInfo:
    properties:
      cn_button_link:
        description: 自定义按钮跳转链接-中文
        type: string
      cn_button_name:
        description: 自定义按钮名称-中文
        type: string
      cn_currency_unit:
        description: 货币单位（1中文，2美元，3欧元）
        type: integer
      cn_is_displayed:
        description: 是否显示购买按钮（1展示，0不展示）
        type: integer
      cn_maximum:
        description: 最大人数
        type: integer
      cn_name:
        description: 票种名称中文
        type: string
      cn_registration_page_name:
        description: 报名页门票名称-中文
        type: string
      cn_service_id:
        description: 中文服务ID
        type: string
      cn_sorting:
        description: 排序
        type: number
      cn_standard_fee:
        description: 中文服务金额信息
        type: string
      conference_id:
        description: 展会ID
        type: integer
      en_button_link:
        description: 自定义按钮跳转链接-英文
        type: string
      en_button_name:
        description: 自定义按钮名称-英文
        type: string
      en_currency_unit:
        description: 货币单位（1中文，2美元，3欧元）
        type: integer
      en_is_displayed:
        description: 是否显示购买按钮（1展示，0不展示）
        type: integer
      en_maximum:
        description: 最大人数
        type: integer
      en_name:
        description: 票种名称英文
        type: string
      en_registration_page_name:
        description: 报名页门票名称-英文
        type: string
      en_service_id:
        description: 英文服务ID
        type: string
      en_sorting:
        description: 排序
        type: number
      en_standard_fee:
        description: 英文服务金额信息
        type: string
      id:
        type: integer
      type:
        description: 是否活动的门票信息（1主会议，0其他活动）
        type: integer
    type: object
  protocol.RespUserConferenceEventInfo:
    properties:
      category_type:
        description: 所属活动分类（1CLNB展前会，2CLNB主论坛，3CLNB分论坛，4户外赛事，5场馆赛事，6Releasing Stage
          新品发布）
        type: integer
      cn_name:
        description: 展会活动名称--中文
        type: string
      cn_sponsors:
        description: 赞助方-中文
        type: string
      cn_sponsors_logo:
        description: 赞助方Logo-中文
        type: string
      cn_sponsors_logo2:
        description: 赞助方Logo-中文
        type: string
      cn_sponsors2:
        description: 赞助方-中文
        type: string
      cn_sponsorship_type:
        description: 赞助类型（0赞助方，1合作伙伴）
        type: string
      cn_sponsorship_type2:
        description: 赞助类型（0赞助方，1合作伙伴）
        type: string
      conference_id:
        description: 展会id
        type: integer
      deleted:
        type: integer
      en_name:
        description: 展会活动名称--英文
        type: string
      en_sponsors:
        description: 赞助方-英文
        type: string
      en_sponsors_logo:
        description: 赞助方Logo-英文
        type: string
      en_sponsors_logo2:
        description: 赞助方Logo-英文
        type: string
      en_sponsors2:
        description: 赞助方-英文
        type: string
      en_sponsorship_type:
        description: 赞助类型（0赞助方，1合作伙伴）
        type: string
      en_sponsorship_type2:
        description: 赞助类型（0赞助方，1合作伙伴）
        type: string
      event_date_list:
        description: 活动详情日期时间
        items:
          $ref: '#/definitions/protocol.RespEventDateInfo'
        type: array
      guest_list:
        description: 嘉宾信息
        items:
          $ref: '#/definitions/model.ConferenceInformation'
        type: array
      id:
        type: integer
      information_list:
        description: 赞助商，合作伙伴，媒体等信息
        items:
          $ref: '#/definitions/protocol.ConferenceColumnInfo'
        type: array
      is_display_page:
        description: 是否展示活动详情页（0是，1否）
        type: string
      schedule_date_list:
        description: 日程日期信息列表
        items:
          $ref: '#/definitions/model.EventScheduleDate'
        type: array
      schedule_is_forum:
        description: 日程是否有论坛（0无分论坛，1有分论坛）
        type: string
      schedule_total:
        description: 活动日程总天数
        type: integer
      sorting:
        description: 排序
        type: string
    type: object
  protocol.RespWebColumnInformationList:
    properties:
      list:
        description: 赞助商，合作方，媒体信息
        items:
          $ref: '#/definitions/protocol.ConferenceColumnInfo'
        type: array
    type: object
  protocol.Response:
    properties:
      code:
        type: integer
      data: {}
      error_desc:
        description: 异常情况显示真实的异常信息
        type: string
      msg:
        type: string
    type: object
  protocol.SetUp:
    properties:
      cn_set_up:
        description: 中文现场搭建
        type: string
      conference_id:
        description: 展会id
        type: integer
      en_set_up:
        description: 英文现场搭建
        type: string
    type: object
  protocol.TrafficService:
    properties:
      cn_traffic_content:
        description: 中文交通内容
        type: string
      conference_id:
        description: 展会id
        type: integer
      en_traffic_content:
        description: 英文交通内容
        type: string
    type: object
  protocol.UserPreviousInfo:
    properties:
      cn_name:
        description: 名称中文
        type: string
      cn_pdf:
        description: PDF中文
        type: string
      cn_url:
        description: 链接中文
        type: string
      en_name:
        description: 名称英文
        type: string
      en_pdf:
        description: PDF英文
        type: string
      en_url:
        description: 链接英文
        type: string
      id:
        type: integer
      sort:
        description: 排序
        type: number
    type: object
  protocol.Venue:
    properties:
      cn_venue_name:
        description: 中文场馆名称
        type: string
      conference_id:
        type: integer
      en_venue_name:
        description: 英文场馆名称
        type: string
      id:
        type: integer
      sorting:
        description: 排序
        type: number
    type: object
host: localhost:8010
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: This is a sample server celler server.
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: Swagger Example API
  version: "1.0"
paths:
  /admin/add_edit/venue:
    post:
      consumes:
      - application/json
      description: 新增、编辑场馆信息
      parameters:
      - description: admin_token
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: Query Params
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/protocol.ReqSaveVenue'
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  type: string
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 新增、编辑场馆信息（2.0修改 ，新增接口）
      tags:
      - 后台-场馆信息
  /admin/audience/add_edit/production:
    post:
      consumes:
      - application/json
      description: 保存或编辑产品信息
      parameters:
      - description: admin_token
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: Query Params
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/protocol.ReqSaveProductionManagement'
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  type: string
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 保存或编辑产品信息 （2.0新增）
      tags:
      - 后台-产品管理
  /admin/audience/after/report:
    get:
      consumes:
      - multipart/form-data
      description: 获取展后报告
      parameters:
      - description: admin_token
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.AfterReport'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获取展后报告
      tags:
      - 管理后台-展后报告
  /admin/audience/after/report/list:
    get:
      consumes:
      - multipart/form-data
      description: 获取展后报告列表
      parameters:
      - description: admin_token
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/protocol.AfterReport'
                  type: array
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获取展后报告列表
      tags:
      - 管理后台-展后报告
  /admin/audience/company/management:
    get:
      consumes:
      - multipart/form-data
      description: 获取展商管理后台
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 分页大小
        in: query
        name: page_size
        type: integer
      - description: 年度
        in: query
        name: year
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.CompanyManagement'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获取展商管理后台（2.0修改 ，新增字段）
      tags:
      - 管理后台-展商管理
  /admin/audience/delete:
    post:
      consumes:
      - multipart/form-data
      description: 删除展后报告
      parameters:
      - description: admin_token
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 删除展后报告
      tags:
      - 管理后台-展后报告
  /admin/audience/delete/company:
    post:
      consumes:
      - multipart/form-data
      description: 删除观众展商管理后台
      parameters:
      - description: admin_token
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 删除观众展商管理后台
      tags:
      - 管理后台-展商管理
  /admin/audience/delete/floor/graph:
    post:
      consumes:
      - multipart/form-data
      description: 删除展览平面图
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 删除展览平面图
      tags:
      - 管理后台-展览平面图
  /admin/audience/delete/production:
    post:
      consumes:
      - multipart/form-data
      description: 删除产品信息
      parameters:
      - description: admin_token
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 产品信息id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  type: string
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 删除产品信息（2.0新增）
      tags:
      - 后台-产品管理
  /admin/audience/edit/company:
    post:
      consumes:
      - multipart/form-data
      description: 编辑展商管理后台
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文展位号
        in: formData
        name: cn_booth_number
        type: string
      - description: 中文展商名称
        in: formData
        name: cn_conference_company
        type: string
      - description: 中文展商介绍
        in: formData
        name: cn_exhibitor_introduction
        type: string
      - description: 中文展商新闻id
        in: formData
        name: cn_exhibitor_news_id
        type: integer
      - description: 展馆id
        in: formData
        name: cn_exhibitor_venue_id
        type: integer
      - description: 中文展商视频封面
        in: formData
        name: cn_exhibitor_video_cover
        type: string
      - description: 中文展商视频
        in: formData
        name: cn_exhibitor_video_link
        type: string
      - description: 中文展商链接
        in: formData
        name: cn_url
        type: string
      - description: 展会id
        in: formData
        name: conference_id
        type: integer
      - description: 英文展位号
        in: formData
        name: en_booth_number
        type: string
      - description: 英文展商名称
        in: formData
        name: en_conference_company
        type: string
      - description: 英文展商链接
        in: formData
        name: en_url
        type: string
      - description: 展商id
        in: formData
        name: id
        type: integer
      - description: logo的url
        in: formData
        name: logo
        type: string
      - description: 排序
        in: formData
        name: sort
        type: number
      - description: 年度
        in: formData
        name: year
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 编辑展商管理后台 （2.0修改 ，新增字段）
      tags:
      - 管理后台-展商管理
  /admin/audience/export/free/visitor:
    get:
      consumes:
      - multipart/form-data
      description: 导出免费观众
      parameters:
      - description: admin_token
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 0 中文 1 英文
        in: query
        name: cn_or_en
        type: integer
      - description: 会议id
        in: query
        name: conference_id
        type: integer
      - description: 会议名称
        in: query
        name: conference_name
        type: string
      - description: 邮箱
        in: query
        name: email
        type: string
      - description: 结束时间
        in: query
        name: end_time
        type: string
      - description: 企业类型
        in: query
        name: enterprise_type
        type: string
      - description: 渠道
        in: query
        name: fromId
        type: string
      - description: 渠道名称
        in: query
        name: from_name
        type: string
      - description: 姓名
        in: query
        name: full_name
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 分页大小
        in: query
        name: page_size
        type: integer
      - description: 来源ID
        in: query
        name: source_id
        type: integer
      - description: 开始时间
        in: query
        name: start_time
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 导出免费观众
      tags:
      - 管理后台-免费观众
  /admin/audience/floor/graph:
    get:
      consumes:
      - multipart/form-data
      description: 管理后台查询展览平面图
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResPageFloorGraph'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 管理后台查询展览平面图
      tags:
      - 管理后台-展览平面图
  /admin/audience/free/visitor:
    get:
      consumes:
      - multipart/form-data
      description: 获取免费观众信息
      parameters:
      - description: admin_token
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 0 中文 1 英文
        in: query
        name: cn_or_en
        type: integer
      - description: 会议id
        in: query
        name: conference_id
        type: integer
      - description: 会议名称
        in: query
        name: conference_name
        type: string
      - description: 邮箱
        in: query
        name: email
        type: string
      - description: 结束时间
        in: query
        name: end_time
        type: string
      - description: 企业类型
        in: query
        name: enterprise_type
        type: string
      - description: 渠道
        in: query
        name: fromId
        type: string
      - description: 渠道名称
        in: query
        name: from_name
        type: string
      - description: 姓名
        in: query
        name: full_name
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 分页大小
        in: query
        name: page_size
        type: integer
      - description: 来源ID
        in: query
        name: source_id
        type: integer
      - description: 开始时间
        in: query
        name: start_time
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResFreeVisitorInfo'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获取免费观众信息
      tags:
      - 管理后台-免费观众
  /admin/audience/fromId:
    get:
      consumes:
      - multipart/form-data
      description: 获取所有渠道
      parameters:
      - description: admin_token
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  items:
                    type: string
                  type: array
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获取所有渠道
      tags:
      - 管理后台-免费观众
  /admin/audience/import:
    post:
      consumes:
      - application/json
      description: 海外观众邮箱导入
      parameters:
      - description: admin_token
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: Query Params
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/protocol.ReqSaveProductionManagement'
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  type: string
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 海外观众邮箱导入
      tags:
      - 后台-观众-海外观众邮箱导入发送邮件
  /admin/audience/paper/collection:
    get:
      consumes:
      - multipart/form-data
      description: 获取论文征集数据
      parameters:
      - description: admin_token
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.PaperCollection'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获取论文征集数据
      tags:
      - 管理后台-论文征集
  /admin/audience/pre/register:
    get:
      consumes:
      - application/json
      description: 获取观众预登记记录api
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResAudiencePreRegister'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获取观众预登记记录
      tags:
      - 管理后台-观众预登记
  /admin/audience/production/management:
    get:
      consumes:
      - multipart/form-data
      description: 后台获取产品管理数据
      parameters:
      - description: admin_token
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 展商id
        in: query
        name: id
        type: integer
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 分页大小
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResPageProductionManagement'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 后台获取产品管理数据（2.0新增）
      tags:
      - 后台-产品管理
  /admin/audience/record/company:
    post:
      consumes:
      - multipart/form-data
      description: 记录展商管理后台
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文展位号
        in: formData
        name: cn_booth_number
        type: string
      - description: 中文展商名称
        in: formData
        name: cn_conference_company
        type: string
      - description: 中文展商介绍
        in: formData
        name: cn_exhibitor_introduction
        type: string
      - description: 中文展商新闻id
        in: formData
        name: cn_exhibitor_news_id
        type: integer
      - description: 展馆id
        in: formData
        name: cn_exhibitor_venue_id
        type: integer
      - description: 中文展商视频封面
        in: formData
        name: cn_exhibitor_video_cover
        type: string
      - description: 中文展商视频
        in: formData
        name: cn_exhibitor_video_link
        type: string
      - description: 中文展商链接
        in: formData
        name: cn_url
        type: string
      - description: 展会id
        in: formData
        name: conference_id
        type: integer
      - description: 英文展位号
        in: formData
        name: en_booth_number
        type: string
      - description: 英文展商名称
        in: formData
        name: en_conference_company
        type: string
      - description: 英文展商链接
        in: formData
        name: en_url
        type: string
      - description: 展商id
        in: formData
        name: id
        type: integer
      - description: logo的url
        in: formData
        name: logo
        type: string
      - description: 排序
        in: formData
        name: sort
        type: number
      - description: 年度
        in: formData
        name: year
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 记录展商管理后台 （2.0修改 ，新增字段）
      tags:
      - 管理后台-展商管理
  /admin/audience/record/floor/graph:
    post:
      consumes:
      - multipart/form-data
      description: 记录展览平面图
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文展馆名称
        in: query
        name: cn_graph_name
        type: string
      - description: 中文平面图url
        in: query
        name: cn_graph_url
        type: string
      - description: 展会id
        in: query
        name: conference_id
        type: integer
      - description: 英文展馆名称
        in: query
        name: en_graph_name
        type: string
      - description: 英文平面图url
        in: query
        name: en_graph_url
        type: string
      - description: 排序
        in: query
        name: sort
        type: number
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 记录展览平面图
      tags:
      - 管理后台-展览平面图
  /admin/audience/record/register:
    post:
      consumes:
      - multipart/form-data
      description: 记录观众预登记记录
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文手册名称
        in: formData
        name: cn_handbook_name
        type: string
      - description: 中文页面标题
        in: formData
        name: cn_title
        type: string
      - description: 中文观众手册url
        in: formData
        name: cn_url
        type: string
      - description: 展会id
        in: formData
        name: conference_id
        type: integer
      - description: 英文手册名称
        in: formData
        name: en_handbook_name
        type: string
      - description: 英文页面标题
        in: formData
        name: en_title
        type: string
      - description: 英文观众手册url
        in: formData
        name: en_url
        type: string
      - description: 展会id
        in: formData
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 记录观众预登记记录
      tags:
      - 管理后台-观众预登记
  /admin/audience/save/after/report:
    post:
      consumes:
      - multipart/form-data
      description: 新增或编辑展后报告
      parameters:
      - description: admin_token
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 展后报告名称
        in: formData
        name: cn_name
        type: string
      - description: 中文展后报告pdf
        in: formData
        name: cn_pdf
        type: string
      - description: 展会id
        in: formData
        name: conference_id
        type: integer
      - description: 展后报告英文名称
        in: formData
        name: en_name
        type: string
      - description: 英文展后报告pdf
        in: formData
        name: en_pdf
        type: string
      - description: 主键id 修改时传
        in: formData
        name: id
        type: integer
      - description: 排序
        in: formData
        name: sort
        type: number
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 新增或编辑展后报告
      tags:
      - 管理后台-展后报告
  /admin/audience/save/paper:
    post:
      consumes:
      - multipart/form-data
      description: 新增编辑论文征集
      parameters:
      - description: admin_token
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文按钮上方名称
        in: formData
        name: cn_above_name
        type: string
      - description: 中文按钮名称
        in: formData
        name: cn_button_name
        type: string
      - description: 中文页面内容
        in: formData
        name: cn_content
        type: string
      - description: 中文表单链接
        in: formData
        name: cn_form_url
        type: string
      - description: 展会id
        in: formData
        name: conference_id
        type: integer
      - description: 英文按钮上方名称
        in: formData
        name: en_above_name
        type: string
      - description: 英文按钮名称
        in: formData
        name: en_button_name
        type: string
      - description: 英文页面内容
        in: formData
        name: en_content
        type: string
      - description: 英文表单链接
        in: formData
        name: en_form_url
        type: string
      - in: formData
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 新增编辑论文征集
      tags:
      - 管理后台-论文征集
  /admin/audience/single/company:
    get:
      consumes:
      - multipart/form-data
      description: 根据id获取展商信息
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 展商id
        in: query
        name: id
        type: integer
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 分页大小
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.CompanyManagement'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 根据id获取展商信息 （2.0修改 ，新增展商相关字段）
      tags:
      - 管理后台-展商管理
  /admin/audience/single/floor/graph:
    get:
      consumes:
      - multipart/form-data
      description: 根据id获取展览平面图信息
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.FloorGraph'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 根据id获取展览平面图信息
      tags:
      - 管理后台-展览平面图
  /admin/audience/update/floor/graph:
    post:
      consumes:
      - multipart/form-data
      description: 更新展览平面图
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文展馆名称
        in: query
        name: cn_graph_name
        type: string
      - description: 中文平面图url
        in: query
        name: cn_graph_url
        type: string
      - description: 展会id
        in: query
        name: conference_id
        type: integer
      - description: 英文展馆名称
        in: query
        name: en_graph_name
        type: string
      - description: 英文平面图url
        in: query
        name: en_graph_url
        type: string
      - description: 主键id
        in: query
        name: id
        type: integer
      - description: 排序
        in: query
        name: sort
        type: number
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 更新展览平面图
      tags:
      - 管理后台-展览平面图
  /admin/audience/visiting/info:
    get:
      consumes:
      - application/json
      description: 获取参观价值api
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResAudienceVisitingInfo'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获取参观价值
      tags:
      - 管理后台-观众-参观价值
  /admin/audience/visiting/value:
    post:
      consumes:
      - multipart/form-data
      description: 参观价值
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文参观价值
        in: formData
        name: cn_visiting_value
        type: string
      - description: 展会id
        in: formData
        name: conference_id
        type: integer
      - description: 英文参观价值
        in: formData
        name: en_visiting_value
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 参观价值 （2.0新增）
      tags:
      - 管理后台-观众-参观价值
  /admin/bottom_page/add_edit:
    post:
      consumes:
      - multipart/form-data
      description: 新增或修改组织架构底部页面二维码API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 二维码链接-中文
        in: formData
        name: cn_logo
        type: string
      - description: 单位名称/二维码名称-中文
        in: formData
        name: cn_name
        type: string
      - description: 排序-中文
        in: formData
        name: cn_sorting
        type: string
      - description: 展会ID
        in: formData
        name: conference_id
        type: integer
      - description: 二维码链接-英文
        in: formData
        name: en_logo
        type: string
      - description: 单位名称/二维码名称-英文
        in: formData
        name: en_name
        type: string
      - description: 排序-英文
        in: formData
        name: en_sorting
        type: string
      - description: 添加时不传修改时必传
        in: formData
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 新增或修改组织架构底部页面二维码
      tags:
      - 管理后台-首页其他-组织架构
  /admin/bottom_page/delete/id:
    post:
      consumes:
      - multipart/form-data
      description: 删除组织架构底部页面二维码API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 组织架构ID
        in: formData
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 删除组织架构底部页面二维码
      tags:
      - 管理后台-首页其他-组织架构
  /admin/bottom_page/detail/id:
    get:
      consumes:
      - multipart/form-data
      description: 查询组织架构底部页面二维码API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 组织架构ID
        in: formData
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.RespBottomPageInfo'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 组织架构底部页面二维码详情
      tags:
      - 管理后台-首页其他-组织架构
  /admin/bottom_page/list:
    get:
      consumes:
      - multipart/form-data
      description: 组织架构底部页面二维码列表API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 展会ID
        in: query
        name: conference_id
        type: integer
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 分页大小
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResAdminConferenceBottomPageList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 组织架构底部页面二维码列表
      tags:
      - 管理后台-首页其他-组织架构
  /admin/clue/export:
    get:
      consumes:
      - multipart/form-data
      description: 导出展会线索列表
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 关联展会ID
        in: query
        name: conference_id
        type: integer
      - description: 展会名称
        in: query
        name: conference_name
        type: string
      - description: 结束时间
        in: query
        name: end_time
        type: string
      - description: 企业类型
        in: query
        name: enterprise_type
        type: string
      - description: 渠道
        in: query
        name: fromId
        type: string
      - description: 渠道名称
        in: query
        name: from_name
        type: string
      - description: 搜索词
        in: query
        name: keyword
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 分页大小
        in: query
        name: page_size
        type: integer
      - description: 来源id
        in: query
        name: source_id
        type: integer
      - description: 开始时间
        in: query
        name: start_time
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: excel文件
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 导出展会线索列表
      tags:
      - 管理后台-展会线索
  /admin/clue/list:
    get:
      consumes:
      - multipart/form-data
      description: 获取展会线索列表
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 关联展会ID
        in: query
        name: conference_id
        type: integer
      - description: 展会名称
        in: query
        name: conference_name
        type: string
      - description: 结束时间
        in: query
        name: end_time
        type: string
      - description: 企业类型
        in: query
        name: enterprise_type
        type: string
      - description: 渠道
        in: query
        name: fromId
        type: string
      - description: 渠道名称
        in: query
        name: from_name
        type: string
      - description: 搜索词
        in: query
        name: keyword
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 分页大小
        in: query
        name: page_size
        type: integer
      - description: 来源id
        in: query
        name: source_id
        type: integer
      - description: 开始时间
        in: query
        name: start_time
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResConferenceClueList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获取展会线索列表
      tags:
      - 管理后台-展会线索
  /admin/column/add_edit:
    post:
      consumes:
      - multipart/form-data
      description: 新增或修改通用模块API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 栏目名称-中文
        in: formData
        name: cn_name
        type: string
      - description: 展会id
        in: formData
        name: conference_id
        type: integer
      - description: 栏目名称-英文
        in: formData
        name: en_name
        type: string
      - description: 修改时必传，添加时不传
        in: formData
        name: id
        type: integer
      - description: 是否有子栏目
        in: formData
        name: is_sub_section
        type: integer
      - description: 排序
        in: formData
        name: sorting
        type: number
      - description: 父栏目ID（如果不是从父级栏目来可以不传）
        in: formData
        name: sub_section_id
        type: integer
      - description: 信息类型（1赞助商，3合作商，4媒体）
        in: formData
        name: type
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 新增或修改通用模块
      tags:
      - 管理后台-通用模块
  /admin/column/delete/id:
    post:
      consumes:
      - multipart/form-data
      description: 删除通用模块API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 栏目ID
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 删除通用模块信息
      tags:
      - 管理后台-通用模块
  /admin/column/detail/id:
    get:
      consumes:
      - multipart/form-data
      description: 查询展会通用模块详情API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 栏目ID
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.RespConferenceColumnInfo'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 查询通用模块详情信息
      tags:
      - 管理后台-通用模块
  /admin/column/list:
    get:
      consumes:
      - multipart/form-data
      description: 查询栏目信息列表API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 展会id
        in: query
        name: conference_id
        type: integer
      - description: 搜索
        in: query
        name: name
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 分页大小
        in: query
        name: page_size
        type: integer
      - description: 子栏目ID
        in: query
        name: sub_section_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResAdminColumnList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 查询栏目信息列表
      tags:
      - 管理后台-通用模块
  /admin/company/apply/conference:
    get:
      consumes:
      - multipart/form-data
      description: 获取展会价值数据
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ApplyConference'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获取展会价值数据 （2.0修改 ，之前页面叫申请参展）
      tags:
      - 管理后台-展商-展会价值
  /admin/company/delete/handbook:
    post:
      consumes:
      - multipart/form-data
      description: 删除展商手册
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 问答手册主键id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 删除展商手册
      tags:
      - 管理后台-展商手册
  /admin/company/delete/question/content:
    post:
      consumes:
      - multipart/form-data
      description: 删除展商常见问题内容
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 问答内容主键id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 删除展商常见问题内容
      tags:
      - 管理后台-展商常见问题
  /admin/company/delete/question/type:
    post:
      consumes:
      - multipart/form-data
      description: 删除展商常见问题类型
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 问题类型id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 删除展商常见问题类型
      tags:
      - 管理后台-展商常见问题
  /admin/company/edit/question/content:
    post:
      consumes:
      - multipart/form-data
      description: 编辑展商常见问题内容
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 答案内容中文
        in: query
        name: cn_question_answer
        type: string
      - description: 问题内容中文
        in: query
        name: cn_question_content
        type: string
      - description: 答案内容英文
        in: query
        name: en_question_answer
        type: string
      - description: 问题内容英文
        in: query
        name: en_question_content
        type: string
      - description: 问答内容主键id
        in: query
        name: id
        type: integer
      - description: 问题类型id
        in: query
        name: question_type_id
        type: integer
      - description: 排序
        in: query
        name: sort
        type: number
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 编辑展商常见问题内容
      tags:
      - 管理后台-展商常见问题
  /admin/company/edit/question/type:
    post:
      consumes:
      - multipart/form-data
      description: 编辑展商常见问题类型
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 展商问题类型中文
        in: query
        name: cn_question_type
        type: string
      - description: 展会id
        in: query
        name: conference_id
        type: integer
      - description: 展商问题类型英文
        in: query
        name: en_question_type
        type: string
      - description: 主键id
        in: query
        name: id
        type: integer
      - description: 排序
        in: query
        name: sort
        type: number
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 编辑展商常见问题类型
      tags:
      - 管理后台-展商常见问题
  /admin/company/exhibition/success:
    get:
      consumes:
      - multipart/form-data
      description: 获取申请参展成功提示
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ExhibitionSuccess'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获取申请参展成功提示
      tags:
      - 管理后台-展商-申请参展
  /admin/company/get/handbook:
    get:
      consumes:
      - multipart/form-data
      description: 根据id获取展商手册
      parameters:
      - description: admin_token
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 展商id
        in: query
        name: id
        type: integer
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 分页大小
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.CompanyHandbook'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 根据id获取展商手册
      tags:
      - 管理后台-展商手册
  /admin/company/get/handbook/content:
    get:
      consumes:
      - multipart/form-data
      description: 获取展商手册页面内容
      parameters:
      - description: admin_token
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.HandbookContent'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获取展商手册页面内容
      tags:
      - 管理后台-展商手册
  /admin/company/get/invitation:
    get:
      consumes:
      - multipart/form-data
      description: 获取观展邀请函
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.Invitation'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获取观展邀请函
      tags:
      - 管理后台-展商观展邀请函
  /admin/company/get/question/content:
    get:
      consumes:
      - multipart/form-data
      description: 根据id获取展商问题答案
      parameters:
      - description: admin_token
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 问答内容主键id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.QuestionContent'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 根据id获取展商问题答案
      tags:
      - 管理后台-展商常见问题
  /admin/company/get/question/content/list:
    get:
      consumes:
      - multipart/form-data
      description: 获得展商常见问题内容列表
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 问题类型id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResCommonQuestionAnswer'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获得展商常见问题内容列表
      tags:
      - 管理后台-展商常见问题
  /admin/company/get/question/type:
    get:
      consumes:
      - multipart/form-data
      description: 根据id获取展商问题类型
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 问题类型id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.CommonQuestionType'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 根据id获取展商问题类型
      tags:
      - 管理后台-展商常见问题
  /admin/company/get/question/type/list:
    get:
      consumes:
      - multipart/form-data
      description: 获取展商常见问题类型列表
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResCommonQuestionType'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获取展商常见问题类型列表
      tags:
      - 管理后台-展商常见问题
  /admin/company/get/set/up:
    get:
      consumes:
      - multipart/form-data
      description: 获取现场搭建时间
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.SetUp'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获取现场搭建时间
      tags:
      - 管理后台-展商现场搭建
  /admin/company/handbook/list:
    get:
      consumes:
      - multipart/form-data
      description: 获取展商手册列表
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResCompanyHandbook'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获取展商手册列表
      tags:
      - 管理后台-展商手册
  /admin/company/online/register:
    get:
      consumes:
      - multipart/form-data
      description: 获取展商在线登记数据
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.OnlineRegister'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获取展商在线登记数据
      tags:
      - 管理后台-展商在线登记
  /admin/company/save/apply/conference:
    post:
      consumes:
      - multipart/form-data
      description: 保存展商展会价值
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - in: formData
        name: cn_apply_conference
        type: string
      - description: 展会id
        in: formData
        name: conference_id
        type: integer
      - in: formData
        name: en_apply_conference
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 保存展商展会价值 （2.0修改 ，之前页面叫申请参展）
      tags:
      - 管理后台-展商-展会价值
  /admin/company/save/exhibition/success:
    post:
      consumes:
      - multipart/form-data
      description: 保存申请参展成功提示
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 展会id
        in: formData
        name: conference_id
        type: integer
      - description: 申请参展成功二维码
        in: formData
        name: exhibition_qr_code
        type: string
      - description: 申请参展成功文案提示语
        in: formData
        name: exhibition_tips
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 保存申请参展成功提示
      tags:
      - 管理后台-展商-申请参展
  /admin/company/save/handbook:
    post:
      consumes:
      - multipart/form-data
      description: 添加展商手册
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文展商手册名称
        in: query
        name: cn_name
        type: string
      - description: 中文pdf
        in: query
        name: cn_pdf
        type: string
      - description: 展会id
        in: query
        name: conference_id
        type: integer
      - description: 英文展商手册名称
        in: query
        name: en_name
        type: string
      - description: 英文pdf
        in: query
        name: en_pdf
        type: string
      - description: 展商手册id 0为新增
        in: query
        name: id
        type: integer
      - description: 排序
        in: query
        name: sort
        type: number
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 添加展商手册
      tags:
      - 管理后台-展商手册
  /admin/company/save/handbook/content:
    post:
      consumes:
      - multipart/form-data
      description: 保存展商手册页面内容
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 展商手册中文页面内容
        in: query
        name: cn_handbook_content
        type: string
      - description: 展商手册中文标题
        in: query
        name: cn_handbook_title
        type: string
      - description: 展会id
        in: query
        name: conference_id
        type: integer
      - description: 展商手册英文页面内容
        in: query
        name: en_handbook_content
        type: string
      - description: 展商手册英文标题
        in: query
        name: en_handbook_title
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 保存展商手册页面内容
      tags:
      - 管理后台-展商手册
  /admin/company/save/invitation:
    post:
      consumes:
      - multipart/form-data
      description: 保存观展邀请函
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文观展邀请
        in: query
        name: cn_invitation
        type: string
      - description: 展会id
        in: query
        name: conference_id
        type: integer
      - description: 英文观展邀请
        in: query
        name: en_invitation
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 保存观展邀请函
      tags:
      - 管理后台-展商观展邀请函
  /admin/company/save/online/register:
    post:
      consumes:
      - multipart/form-data
      description: 保存展商在线登记数据
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文内容
        in: query
        name: cn_content
        type: string
      - description: 中文在线登记表表单链接
        in: query
        name: cn_url
        type: string
      - description: 展会id
        in: query
        name: conference_id
        type: integer
      - description: 英文内容
        in: query
        name: en_content
        type: string
      - description: 英文在线登记表表单链接
        in: query
        name: en_url
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 保存展商在线登记数据
      tags:
      - 管理后台-展商在线登记
  /admin/company/save/question/content:
    post:
      consumes:
      - multipart/form-data
      description: 保存展商常见问题内容
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 答案中文
        in: query
        name: cn_question_answer
        type: string
      - description: 问题中文
        in: query
        name: cn_question_content
        type: string
      - description: 问题英文
        in: query
        name: en_question_answer
        type: string
      - description: 问题英文
        in: query
        name: en_question_content
        type: string
      - description: 主键id
        in: query
        name: id
        type: integer
      - description: 问题类型id
        in: query
        name: question_type_id
        type: integer
      - description: 排序
        in: query
        name: sort
        type: number
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 保存展商常见问题内容
      tags:
      - 管理后台-展商常见问题
  /admin/company/save/question/type:
    post:
      consumes:
      - multipart/form-data
      description: 保存展商常见问题类型
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 展商问题类型中文
        in: query
        name: cn_question_type
        type: string
      - description: 展会id
        in: query
        name: conference_id
        type: integer
      - description: 展商问题类型英文
        in: query
        name: en_question_type
        type: string
      - description: 主键id
        in: query
        name: id
        type: integer
      - description: 排序
        in: query
        name: sort
        type: number
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 保存展商常见问题类型
      tags:
      - 管理后台-展商常见问题
  /admin/company/save/set/up:
    post:
      consumes:
      - multipart/form-data
      description: 保存现场搭建
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文现场搭建
        in: query
        name: cn_set_up
        type: string
      - description: 展会id
        in: query
        name: conference_id
        type: integer
      - description: 英文现场搭建
        in: query
        name: en_set_up
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 保存现场搭建
      tags:
      - 管理后台-展商现场搭建
  /admin/company/update/handbook:
    post:
      consumes:
      - multipart/form-data
      description: 更新展商手册
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文展商手册名称
        in: query
        name: cn_name
        type: string
      - description: 中文pdf
        in: query
        name: cn_pdf
        type: string
      - description: 展会id
        in: query
        name: conference_id
        type: integer
      - description: 英文展商手册名称
        in: query
        name: en_name
        type: string
      - description: 英文pdf
        in: query
        name: en_pdf
        type: string
      - description: 展商手册id 0为新增
        in: query
        name: id
        type: integer
      - description: 排序
        in: query
        name: sort
        type: number
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 更新展商手册
      tags:
      - 管理后台-展商手册
  /admin/conference/add_edit:
    post:
      consumes:
      - multipart/form-data
      description: 新增或修改基本信息API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: json body
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/protocol.ReqAdminConferenceInfo'
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 新增或修改基本信息
      tags:
      - 管理后台-首页
  /admin/conference/detail:
    get:
      consumes:
      - multipart/form-data
      description: 查询展会基本信息API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResConferenceInfo'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 查询展会基本信息
      tags:
      - 管理后台-首页
  /admin/conference/list:
    get:
      consumes:
      - multipart/form-data
      description: 查询展会列表API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 搜索词
        in: query
        name: keyword
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 分页大小
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResAdminConferenceList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 查询展会列表
      tags:
      - 管理后台-首页
  /admin/conference/name/list:
    get:
      consumes:
      - multipart/form-data
      description: 查询展会列表API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 1 参展信息 2 赞助信息 3 付费信息 4 国内免费观众 5 国外免费观众 6 国内媒体报名信息 7 国外媒体报名信息
        in: query
        name: type
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResAdminConferenceNameList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 查询展会列表
      tags:
      - 管理后台-首页
  /admin/conference/set_status:
    post:
      consumes:
      - multipart/form-data
      description: 设置展会状态API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 展会ID
        in: formData
        name: conference_id
        required: true
        type: integer
      - description: 展会状态,1启用,2下线,3删除
        enum:
        - 1
        - 2
        - 3
        in: formData
        name: status
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 设置展会状态
      tags:
      - 管理后台-首页
  /admin/conference/set_template:
    post:
      consumes:
      - multipart/form-data
      description: 设置日程模版类型API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 展会ID
        in: formData
        name: conference_id
        required: true
        type: integer
      - description: 展会日程模版类型:1时间&主题;0议程&非议程
        in: formData
        name: template_type
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 设置展会日程模版类型
      tags:
      - 管理后台-首页
  /admin/contact_information/add_edit:
    post:
      consumes:
      - multipart/form-data
      description: 新增或修改组织架构联系信息API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文内容
        in: formData
        name: cn_content
        type: string
      - description: 中文其他页底是否显示（0  否 1  是）
        in: formData
        name: cn_is_displayed
        type: integer
      - description: 标题-中文
        in: formData
        name: cn_title
        type: string
      - description: 会展ID
        in: formData
        name: conference_id
        type: integer
      - description: 英文内容
        in: formData
        name: en_content
        type: string
      - description: 英文其他页底是否显示（0  否 1  是）
        in: formData
        name: en_is_displayed
        type: integer
      - description: 标题-英文
        in: formData
        name: en_title
        type: string
      - description: 添加时不传修改时必传
        in: formData
        name: id
        type: integer
      - description: 排序
        in: formData
        name: sorting
        type: number
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 新增或修改组织架构联系信息（2.0新增）
      tags:
      - 管理后台-组织架构-联系信息
  /admin/contact_information/delete/id:
    post:
      consumes:
      - multipart/form-data
      description: 删除组织架构联系信息API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 组织架构ID
        in: formData
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 删除组织架构联系信息（2.0新增）
      tags:
      - 管理后台-组织架构-联系信息
  /admin/contact_information/detail/id:
    get:
      consumes:
      - multipart/form-data
      description: 查询组织架构联系信息API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 组织架构ID
        in: formData
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.RespBottomPageInfo'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 组织架构联系信息详情（2.0新增）
      tags:
      - 管理后台-组织架构-联系信息
  /admin/contact_information/list:
    get:
      consumes:
      - multipart/form-data
      description: 组织架构联系信息列表API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 展会ID
        in: query
        name: conference_id
        type: integer
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 分页大小
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResAdminContactInformationList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 组织架构联系信息列表（2.0新增）
      tags:
      - 管理后台-组织架构-联系信息
  /admin/data/add_edit:
    post:
      consumes:
      - multipart/form-data
      description: 新增或修改大会介绍数据API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 指标名称-中文
        in: formData
        name: cn_data_name
        type: string
      - description: 指标数据-中文
        in: formData
        name: cn_data_value
        type: string
      - in: formData
        name: conference_id
        type: integer
      - description: /图标图片
        in: formData
        name: data_image
        type: string
      - description: 指标名称-英文
        in: formData
        name: en_data_name
        type: string
      - description: 指标数据-英文
        in: formData
        name: en_data_value
        type: string
      - description: 添加时不传修改时必传
        in: formData
        name: id
        type: integer
      - description: 排序
        in: formData
        name: sorting
        type: number
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 新增或修改大会介绍数据
      tags:
      - 管理后台-首页其他-数据
  /admin/data/delete/id:
    post:
      consumes:
      - multipart/form-data
      description: 删除大会介绍数据API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 展会数据ID
        in: formData
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 删除大会介绍数据
      tags:
      - 管理后台-首页其他-数据
  /admin/data/detail/id:
    get:
      consumes:
      - multipart/form-data
      description: 查询大会介绍数据API
      parameters:
      - description: auth_token
        in: header
        name: SMM-ADMIN-TOKEN
        type: string
      - description: 展会数据ID
        in: formData
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.RespIntroductionDataInfo'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 大会介绍数据详情
      tags:
      - 管理后台-首页其他-数据
  /admin/data/list:
    get:
      consumes:
      - multipart/form-data
      description: 大会介绍数据列表API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 展会ID
        in: query
        name: conference_id
        type: integer
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 分页大小
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResAdminConferenceDataList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 大会介绍数据列表
      tags:
      - 管理后台-首页其他-数据
  /admin/delete/venue:
    post:
      consumes:
      - multipart/form-data
      description: 删除场馆
      parameters:
      - description: admin_token
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 场馆id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  type: string
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 删除场馆（2.0修改 ，新增接口）
      tags:
      - 后台-场馆信息
  /admin/event/add_edit:
    post:
      consumes:
      - multipart/form-data
      description: 新增或修改活动一览表API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: json body
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/protocol.ReqAdminSaveConferenceEvent'
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 新增或修改活动一览表(2.0添加活动分类字段)
      tags:
      - 管理后台-活动一览表
  /admin/event/category/add_edit:
    post:
      consumes:
      - multipart/form-data
      description: 新增或修改活动分类管理API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: json body
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/protocol.ReqAdminSaveConferenceEventCategory'
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 新增或修改活动分类管理(2.0新增)
      tags:
      - 管理后台-活动分类管理
  /admin/event/category/delete/id:
    post:
      consumes:
      - multipart/form-data
      description: 删除活动分类API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 活动ID
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 删除活动分类(2.0新增)
      tags:
      - 管理后台-活动分类管理
  /admin/event/category/detail/id:
    get:
      consumes:
      - multipart/form-data
      description: 查询活动分类详情API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 活动ID
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.RespAdminConferenceEventCategoryInfo'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 活动分类详情(2.0新增)
      tags:
      - 管理后台-活动分类管理
  /admin/event/category/list:
    get:
      consumes:
      - multipart/form-data
      description: 活动分类管理API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 展会ID
        in: query
        name: conference_id
        type: integer
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 分页大小
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResAdminConferenceEventCategoryList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 查询活动分类列表(2.0新增)
      tags:
      - 管理后台-活动分类管理
  /admin/event/delete/id:
    post:
      consumes:
      - multipart/form-data
      description: 删除活动一览表API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 活动ID
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 删除活动一览表
      tags:
      - 管理后台-活动一览表
  /admin/event/detail/id:
    get:
      consumes:
      - multipart/form-data
      description: 查询活动一览表API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 活动ID
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.RespAdminConferenceEventInfo'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 活动一览表详情
      tags:
      - 管理后台-活动一览表
  /admin/event/list:
    get:
      consumes:
      - multipart/form-data
      description: 查询活动列表API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 展会ID
        in: query
        name: conference_id
        type: integer
      - description: 会议名称/地点
        in: query
        name: name_place
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 分页大小
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResAdminConferenceEventList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 查询活动一览表列表
      tags:
      - 管理后台-活动一览表
  /admin/event/schedule/import:
    post:
      consumes:
      - multipart/form-data
      description: 批量导入活动日程EXCEL文件，支持批量创建活动、日程和嘉宾信息
      parameters:
      - description: 管理员认证token
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 展会ID
        in: query
        name: conference_id
        required: true
        type: integer
      - description: Excel文件，包含活动日程数据
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: 导入成功，返回导入结果信息
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  type: string
              type: object
        "400":
          description: 参数错误或文件格式错误
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 批量导入活动日程EXCEL
      tags:
      - 管理后台-活动日程
  /admin/exhibition/export:
    get:
      consumes:
      - multipart/form-data
      description: 导出参展信息列表
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 关联展会ID
        in: query
        name: conference_id
        type: integer
      - description: 展会名称
        in: query
        name: conference_name
        type: string
      - description: 结束时间
        in: query
        name: end_time
        type: string
      - description: 企业类型
        in: query
        name: enterprise_type
        type: string
      - description: 渠道
        in: query
        name: fromId
        type: string
      - description: 渠道名称
        in: query
        name: from_name
        type: string
      - description: 搜索词
        in: query
        name: keyword
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 分页大小
        in: query
        name: page_size
        type: integer
      - description: 来源id
        in: query
        name: source_id
        type: integer
      - description: 开始时间
        in: query
        name: start_time
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: excel文件
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 导出参展信息列表
      tags:
      - 管理后台-参展信息
  /admin/exhibition/list:
    get:
      consumes:
      - multipart/form-data
      description: 获取参展信息列表
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 关联展会ID
        in: query
        name: conference_id
        type: integer
      - description: 展会名称
        in: query
        name: conference_name
        type: string
      - description: 结束时间
        in: query
        name: end_time
        type: string
      - description: 企业类型
        in: query
        name: enterprise_type
        type: string
      - description: 渠道
        in: query
        name: fromId
        type: string
      - description: 渠道名称
        in: query
        name: from_name
        type: string
      - description: 搜索词
        in: query
        name: keyword
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 分页大小
        in: query
        name: page_size
        type: integer
      - description: 来源id
        in: query
        name: source_id
        type: integer
      - description: 开始时间
        in: query
        name: start_time
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResConferenceExhibitionList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获取参展信息列表
      tags:
      - 管理后台-参展信息
  /admin/from/config/list:
    get:
      consumes:
      - multipart/form-data
      description: 渠道映射列表API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 展会ID
        in: query
        name: conference_id
        type: integer
      - description: 1 参展信息 2 赞助信息 3 付费信息 4 国内免费观众 5 国外免费观众 6 国内媒体报名信息 7 国外媒体报名信息
        in: query
        name: type
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResConferenceFromConfigList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 渠道映射列表
      tags:
      - 管理后台-渠道映射
  /admin/from/config/list/v2:
    get:
      consumes:
      - multipart/form-data
      description: 渠道映射列表v2API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 展会ID
        in: query
        name: conference_id
        type: integer
      - description: 1 参展信息 2 赞助信息 3 付费信息 4 国内免费观众 5 国外免费观众 6 国内媒体报名信息 7 国外媒体报名信息
        in: query
        name: type
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResConferenceFromConfigList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 渠道映射列表v2(根据数据查询)
      tags:
      - 管理后台-渠道映射v2
  /admin/get/about/us:
    get:
      consumes:
      - multipart/form-data
      description: 获得关于我们
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: Query Params
        in: query
        name: conference_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResAboutUs'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获得关于我们
      tags:
      - 管理后台-关于我们
  /admin/get/city/info:
    get:
      consumes:
      - multipart/form-data
      description: 获得城市概况
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.CityInfo'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获得城市概况
      tags:
      - 管理后台-城市概况
  /admin/get/contact/us:
    get:
      consumes:
      - multipart/form-data
      description: 获得联系我们
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 展会id
        in: query
        name: conference_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResContactUsList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获得联系我们
      tags:
      - 管理后台-关于我们
  /admin/get/hall/info:
    get:
      consumes:
      - multipart/form-data
      description: 获取展馆概况
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.HallInfo'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获取展馆概况
      tags:
      - 管理后台-展馆概况
  /admin/get/hotel:
    get:
      consumes:
      - multipart/form-data
      description: 获取酒店住宿页面数据
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.HotelPage'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获取酒店住宿页面数据
      tags:
      - 管理后台-酒店住宿
  /admin/get/traffic/service:
    get:
      consumes:
      - multipart/form-data
      description: 获得交通服务信息
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.TrafficService'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获得交通服务信息
      tags:
      - 管理后台-交通服务
  /admin/get/venues:
    get:
      consumes:
      - multipart/form-data
      description: 获取场馆信息
      parameters:
      - description: admin_token
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/protocol.Venue'
                  type: array
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获取场馆信息（2.0修改 ，新增接口）
      tags:
      - 后台-场馆信息
  /admin/handbook/subscribe/export:
    get:
      consumes:
      - multipart/form-data
      description: 导出订阅列表API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 结束时间
        in: query
        name: end_time
        type: string
      - description: 搜索词
        in: query
        name: keyword
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 分页大小
        in: query
        name: page_size
        type: integer
      - description: 开始时间
        in: query
        name: start_time
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: excel文件
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 导出订阅列表
      tags:
      - 管理后台-通用
  /admin/handbook/subscribe/list:
    get:
      consumes:
      - multipart/form-data
      description: 查询订阅列表API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 结束时间
        in: query
        name: end_time
        type: string
      - description: 搜索词
        in: query
        name: keyword
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 分页大小
        in: query
        name: page_size
        type: integer
      - description: 开始时间
        in: query
        name: start_time
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResAdminSubscribeList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 查询订阅列表
      tags:
      - 管理后台-通用
  /admin/information/add_edit:
    post:
      consumes:
      - multipart/form-data
      description: 新增或修改公司信息（嘉宾）API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 称谓
        in: formData
        name: cn_appellation
        type: string
      - description: 公司
        in: formData
        name: cn_company
        type: string
      - description: 嘉宾介绍--中文
        in: formData
        name: cn_content
        type: string
      - description: 链接
        in: formData
        name: cn_link
        type: string
      - description: logo图-中文
        in: formData
        name: cn_picture
        type: string
      - description: 职位
        in: formData
        name: cn_position
        type: string
      - description: 排序-中文
        in: formData
        name: cn_sorting
        type: number
      - description: 栏目ID
        in: formData
        name: column_id
        type: integer
      - description: 展会ID
        in: formData
        name: conference_id
        type: integer
      - description: 称谓
        in: formData
        name: en_appellation
        type: string
      - description: 公司
        in: formData
        name: en_company
        type: string
      - description: 嘉宾介绍--英文
        in: formData
        name: en_content
        type: string
      - description: 链接
        in: formData
        name: en_link
        type: string
      - description: logo图-英文
        in: formData
        name: en_picture
        type: string
      - description: 职位
        in: formData
        name: en_position
        type: string
      - description: 排序-英文
        in: formData
        name: en_sorting
        type: number
      - in: formData
        name: id
        type: integer
      - description: 排序
        in: formData
        name: sorting
        type: number
      - description: 信息类型（1赞助商，2嘉宾，3合作商，4媒体）
        in: formData
        name: type
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 新增或修改公司信息（嘉宾）（2.0修改 赞助商，嘉宾都加上中英文图片和排序）
      tags:
      - 管理后台-通用模块
  /admin/information/delete/id:
    post:
      consumes:
      - multipart/form-data
      description: 删除公司信息（嘉宾）API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 栏目信息ID
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 删除公司信息（嘉宾）
      tags:
      - 管理后台-通用模块
  /admin/information/detail/id:
    get:
      consumes:
      - multipart/form-data
      description: 查询公司信息（嘉宾）详情API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 栏目信息ID
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.RespConferenceInformationInfo'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 查询公司信息（嘉宾）详情 （2.0修改 赞助商，嘉宾都加上中英文图片和排序）
      tags:
      - 管理后台-通用模块
  /admin/information/list:
    get:
      consumes:
      - multipart/form-data
      description: 查询公司信息（嘉宾列表）列表API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 栏目ID(嘉宾列表时不传)
        in: query
        name: column_id
        type: integer
      - description: 展会id
        in: query
        name: conference_id
        type: integer
      - description: 是否是中文 false 中文，true 英文
        in: query
        name: is_en
        type: boolean
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 分页大小
        in: query
        name: page_size
        type: integer
      - description: 模块类型（1赞助商&合作商&媒体，2嘉宾）
        in: query
        name: type
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResAdminInformationList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 查询公司信息（嘉宾列表）列表（2.0修改 赞助商，嘉宾都加上中英文图片和排序）
      tags:
      - 管理后台-通用模块
  /admin/introduction/add_edit:
    post:
      consumes:
      - multipart/form-data
      description: 新增或修改大会介绍和介绍视频API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 大会介绍-页面内容-中文
        in: formData
        name: cn_content
        type: string
      - description: 展会ID
        in: formData
        name: conference_id
        type: integer
      - description: 大会介绍-页面内容-英文
        in: formData
        name: en_content
        type: string
      - description: 添加时不传修改时必传
        in: formData
        name: id
        type: integer
      - description: 会议介绍配图
        in: formData
        name: introduction_diagram
        type: string
      - description: 介绍视频
        in: formData
        name: introduction_video
        type: string
      - description: 视频封面
        in: formData
        name: video_cover
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 新增或修改大会介绍和介绍视频
      tags:
      - 管理后台-首页其他-大会介绍和介绍视频
  /admin/introduction/detail/id:
    get:
      consumes:
      - multipart/form-data
      description: 查询大会介绍和介绍视频API
      parameters:
      - description: auth_token
        in: header
        name: SMM-ADMIN-TOKEN
        type: string
      - description: 展会ID
        in: formData
        name: conference_id
        type: integer
      - description: 1 参展信息 2 赞助信息 3 付费信息 4 国内免费观众 5 国外免费观众 6 国内媒体报名信息 7 国外媒体报名信息
        in: formData
        name: type
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.RespIntroductionInfo'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 大会介绍和介绍视频详情
      tags:
      - 管理后台-首页其他-大会介绍和介绍视频
  /admin/media_registration/config/add_edit:
    post:
      consumes:
      - multipart/form-data
      description: 新增或修改大会介绍和介绍视频API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 大会介绍-页面内容-中文
        in: formData
        name: cn_content
        type: string
      - description: 展会ID
        in: formData
        name: conference_id
        type: integer
      - description: 大会介绍-页面内容-英文
        in: formData
        name: en_content
        type: string
      - description: 添加时不传修改时必传
        in: formData
        name: id
        type: integer
      - description: 会议介绍配图
        in: formData
        name: introduction_diagram
        type: string
      - description: 介绍视频
        in: formData
        name: introduction_video
        type: string
      - description: 视频封面
        in: formData
        name: video_cover
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 新增或修改大会介绍和介绍视频
      tags:
      - 管理后台-首页其他-大会介绍和介绍视频
  /admin/media_registration/config/detail:
    get:
      consumes:
      - multipart/form-data
      description: 查询大会介绍和介绍视频API
      parameters:
      - description: auth_token
        in: header
        name: SMM-ADMIN-TOKEN
        type: string
      - description: 展会ID
        in: formData
        name: conference_id
        type: integer
      - description: 1 参展信息 2 赞助信息 3 付费信息 4 国内免费观众 5 国外免费观众 6 国内媒体报名信息 7 国外媒体报名信息
        in: formData
        name: type
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.RespIntroductionInfo'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 大会介绍和介绍视频详情
      tags:
      - 管理后台-首页其他-大会介绍和介绍视频
  /admin/media_registration/list:
    get:
      consumes:
      - multipart/form-data
      description: 大会介绍数据列表API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 展会ID
        in: query
        name: conference_id
        type: integer
      - description: 展会名称
        in: query
        name: conference_name
        type: string
      - in: query
        name: country
        type: string
      - in: query
        name: email
        type: string
      - in: query
        name: end_time
        type: string
      - description: 渠道
        in: query
        name: from_id
        type: string
      - description: 渠道名称
        in: query
        name: from_name
        type: string
      - description: 语言
        in: query
        name: language
        type: string
      - in: query
        name: media_name
        type: string
      - in: query
        name: name
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 分页大小
        in: query
        name: page_size
        type: integer
      - in: query
        name: phone
        type: string
      - description: 来源ID
        in: query
        name: source_id
        type: string
      - in: query
        name: start_time
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResAdminMediaRegistrationList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 大会介绍数据列表
      tags:
      - 管理后台-首页其他-数据
  /admin/organization/add_edit:
    post:
      consumes:
      - multipart/form-data
      description: 新增或修改组织架构API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 单位名称/二维码名称-中文
        in: formData
        name: cn_name
        type: string
      - description: 展会ID
        in: formData
        name: conference_id
        type: integer
      - description: 单位名称/二维码名称-英文
        in: formData
        name: en_name
        type: string
      - description: 添加时不传修改时必传
        in: formData
        name: id
        type: integer
      - description: 二维码链接
        in: formData
        name: logo
        type: string
      - description: 排序
        in: formData
        name: sorting
        type: string
      - description: 数据类型（1主办单位，2承办单位，3二维码信息，4现场图片）
        in: formData
        name: type
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 新增或修改组织架构
      tags:
      - 管理后台-首页其他-组织架构
  /admin/organization/delete/id:
    post:
      consumes:
      - multipart/form-data
      description: 删除组织架构API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 组织架构ID
        in: formData
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 删除组织架构
      tags:
      - 管理后台-首页其他-组织架构
  /admin/organization/detail/id:
    get:
      consumes:
      - multipart/form-data
      description: 查询组织架构API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 组织架构ID
        in: formData
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.RespOrganizationInfo'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 组织架构详情
      tags:
      - 管理后台-首页其他-组织架构
  /admin/organization/list:
    get:
      consumes:
      - multipart/form-data
      description: 组织架构列表API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 展会ID
        in: query
        name: conference_id
        type: integer
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 分页大小
        in: query
        name: page_size
        type: integer
      - description: 数据类型（1主办单位，2承办单位，3二维码信息，4现场图片）
        in: query
        name: type
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResAdminConferenceOrganizationList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 组织架构列表
      tags:
      - 管理后台-首页其他-组织架构
  /admin/previous/add_edit:
    post:
      consumes:
      - multipart/form-data
      description: 新增或修改往届信息API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 名称中文
        in: formData
        name: cn_name
        required: true
        type: string
      - description: 往届PDF链接中文
        in: formData
        name: cn_pdf
        type: string
      - description: 链接中文
        in: formData
        name: cn_url
        required: true
        type: string
      - description: 关联展会ID
        in: formData
        name: conference_id
        required: true
        type: integer
      - description: 名称英文
        in: formData
        name: en_name
        type: string
      - description: 往届PDF链接英文
        in: formData
        name: en_pdf
        type: string
      - description: 链接英文
        in: formData
        name: en_url
        type: string
      - description: ID,大于0表示编辑;0表示新增
        in: formData
        minimum: 0
        name: id
        type: integer
      - description: 排序
        in: formData
        minimum: 0
        name: sort
        type: number
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 新增或修改往届信息
      tags:
      - 管理后台-往届
  /admin/previous/delete/:id:
    post:
      consumes:
      - multipart/form-data
      description: 删除往届信息API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: Path Params
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 删除往届信息
      tags:
      - 管理后台-往届
  /admin/previous/list:
    get:
      consumes:
      - multipart/form-data
      description: 查询往届列表API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 展会ID
        in: query
        name: conference_id
        required: true
        type: integer
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 分页大小
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResAdminPreviousList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 查询往届列表
      tags:
      - 管理后台-往届
  /admin/register/export:
    get:
      consumes:
      - multipart/form-data
      description: 导出报名信息列表API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 展会id
        in: query
        name: conference_id
        type: integer
      - description: 会议名称
        in: query
        name: conference_name
        type: string
      - description: 邮箱
        in: query
        name: email
        type: string
      - description: 提交时间-结束
        in: query
        name: end_time
        type: string
      - description: 企业类型
        in: query
        name: enterprise_type
        type: string
      - description: CnConferenceName string  `json:"conference_name" form:"conference_name"`
          //会议名称
        in: query
        name: first_name
        type: string
      - description: 渠道id
        in: query
        name: fromId
        type: string
      - description: 渠道名称
        in: query
        name: from_name
        type: string
      - description: 姓
        in: query
        name: last_name
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 分页大小
        in: query
        name: page_size
        type: integer
      - description: 来源id
        in: query
        name: source_id
        type: string
      - description: 提交时间-开始
        in: query
        name: start_time
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: excel文件
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 导出报名信息列表
      tags:
      - 管理后台-报名信息
  /admin/register/list:
    get:
      consumes:
      - multipart/form-data
      description: 查询报名信息列表API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 展会id
        in: query
        name: conference_id
        type: integer
      - description: 会议名称
        in: query
        name: conference_name
        type: string
      - description: 邮箱
        in: query
        name: email
        type: string
      - description: 提交时间-结束
        in: query
        name: end_time
        type: string
      - description: 企业类型
        in: query
        name: enterprise_type
        type: string
      - description: CnConferenceName string  `json:"conference_name" form:"conference_name"`
          //会议名称
        in: query
        name: first_name
        type: string
      - description: 渠道id
        in: query
        name: fromId
        type: string
      - description: 渠道名称
        in: query
        name: from_name
        type: string
      - description: 姓
        in: query
        name: last_name
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 分页大小
        in: query
        name: page_size
        type: integer
      - description: 来源id
        in: query
        name: source_id
        type: string
      - description: 提交时间-开始
        in: query
        name: start_time
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResAdminConferenceRegisterList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 查询报名信息列表
      tags:
      - 管理后台-报名信息
  /admin/register/source/list:
    get:
      consumes:
      - multipart/form-data
      description: 表单查询来源列表API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 展会id
        in: query
        name: conference_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResAdminConferenceChannelSourceList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 表单查询来源列表
      tags:
      - 管理后台-来源列表
  /admin/register/user/list:
    get:
      consumes:
      - multipart/form-data
      description: 查询报名-报名人信息列表API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 报名id
        in: query
        name: register_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResAdminConferenceRegisterUserList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 查询报名-报名人信息列表
      tags:
      - 管理后台-报名信息
  /admin/save/about/us:
    post:
      consumes:
      - multipart/form-data
      description: 保存关于我们
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 关于我们中文
        in: query
        name: cn_about_us
        required: true
        type: string
      - description: 展会ID
        in: query
        name: conference_id
        required: true
        type: integer
      - description: 关于我们英文
        in: query
        name: en_about_us
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 保存关于我们
      tags:
      - 管理后台-关于我们
  /admin/save/city/info:
    post:
      consumes:
      - multipart/form-data
      description: 保存城市概况
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文城市概况
        in: query
        name: cn_city_overview
        type: string
      - description: 展会id
        in: query
        name: conference_id
        type: integer
      - description: 英文城市概况
        in: query
        name: en_city_overview
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 保存城市概况
      tags:
      - 管理后台-城市概况
  /admin/save/contact/us:
    post:
      consumes:
      - multipart/form-data
      description: 保存联系我们
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 联系我们内容-中文
        in: query
        name: cn_contact
        type: string
      - description: 展会id
        in: query
        name: conference_id
        type: integer
      - description: 联系我们内容-英文
        in: query
        name: en_contact
        type: string
      - description: 添加时不传修改时必传                              //主键id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 保存联系我们
      tags:
      - 管理后台-关于我们
  /admin/save/hall/info:
    post:
      consumes:
      - multipart/form-data
      description: 保存展馆概况
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 展馆平面图url
        in: query
        name: cn_floor_graph
        type: string
      - description: 上标题-中文
        in: query
        name: cn_heading
        type: string
      - description: 下标题-中文
        in: query
        name: cn_headlining
        type: string
      - description: 展会id
        in: query
        name: conference_id
        type: integer
      - description: 英文展馆平面图url
        in: query
        name: en_floor_graph
        type: string
      - description: 上标题-英文
        in: query
        name: en_heading
        type: string
      - description: 下标题-英文
        in: query
        name: en_headlining
        type: string
      - description: 谷歌地图分享链接
        in: query
        name: google_url
        type: string
      - description: 展馆位置纬度
        in: query
        name: location_latitude
        type: string
      - description: 展馆位置经度
        in: query
        name: location_longitude
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 保存展馆概况
      tags:
      - 管理后台-展馆概况
  /admin/save/hotel:
    post:
      consumes:
      - multipart/form-data
      description: 保存酒店住宿页面数据
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文预定表单链接
        in: query
        name: cn_booking_url
        type: string
      - description: 中文酒店按钮
        in: query
        name: cn_hotel_button
        type: string
      - description: 中文酒店页面内容
        in: query
        name: cn_hotel_content
        type: string
      - description: 中文酒店表格
        in: query
        name: cn_hotel_excel
        type: string
      - description: 展会id
        in: query
        name: conference_id
        type: integer
      - description: 英文预定表单链接
        in: query
        name: en_booking_url
        type: string
      - description: 英文酒店按钮
        in: query
        name: en_hotel_button
        type: string
      - description: 英文酒店页面内容
        in: query
        name: en_hotel_content
        type: string
      - description: 中文酒店表格
        in: query
        name: en_hotel_excel
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 保存酒店住宿页面数据
      tags:
      - 管理后台-酒店住宿
  /admin/save/traffic/service:
    post:
      consumes:
      - multipart/form-data
      description: 保存交通信息
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文交通内容
        in: query
        name: cn_traffic_content
        type: string
      - description: 展会id
        in: query
        name: conference_id
        type: integer
      - description: 英文交通内容
        in: query
        name: en_traffic_content
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 保存交通信息
      tags:
      - 管理后台-交通服务
  /admin/schedule/add_edit:
    post:
      consumes:
      - multipart/form-data
      description: 新增或修改活动日程API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 日程描述--中文
        in: formData
        name: cn_content
        type: string
      - description: 展会日程名称--中文
        in: formData
        name: cn_title
        type: string
      - description: 展会id
        in: formData
        name: conference_id
        type: integer
      - description: 属于哪一天
        in: formData
        name: day_id
        type: integer
      - description: 日程描述--英文
        in: formData
        name: en_content
        type: string
      - description: 展会日程名称--英文
        in: formData
        name: en_title
        type: string
      - description: 活动ID
        in: formData
        name: event_id
        type: integer
      - description: 分论坛ID
        in: formData
        name: forum_id
        type: integer
      - description: 添加时不传修改时必传
        in: formData
        name: id
        type: integer
      - description: 日程类型（0议程、1非议程/餐食、2咖啡、3鸡尾酒）
        in: formData
        name: is_agenda
        type: integer
      - description: 时间-结束日期
        in: formData
        name: schedule_end
        type: string
      - description: 时间-开始日期
        in: formData
        name: schedule_start
        type: string
      - description: 排序
        in: formData
        name: sorting
        type: number
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 新增或修改活动日程
      tags:
      - 管理后台-活动日程
  /admin/schedule/date/add_edit:
    post:
      consumes:
      - multipart/form-data
      description: 修改活动日程日期显示名称和排序API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 日期名称
        in: formData
        name: cn_day_name
        type: string
      - description: 展会id
        in: formData
        name: conference_id
        type: integer
      - description: 日期名称
        in: formData
        name: en_day_name
        type: string
      - description: 活动ID
        in: formData
        name: event_id
        type: integer
      - description: 添加时不传修改时必传
        in: formData
        name: id
        type: integer
      - description: 排序
        in: formData
        name: sorting
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 修改活动日程日期显示名称和排序
      tags:
      - 管理后台-活动日程
  /admin/schedule/delete/id:
    post:
      consumes:
      - multipart/form-data
      description: 删除活动日程API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 日程ID
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 删除活动日程
      tags:
      - 管理后台-活动日程
  /admin/schedule/detail/id:
    get:
      consumes:
      - multipart/form-data
      description: 查询活动日程API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 日程ID
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.RespEventScheduleInfo'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 活动日程详情
      tags:
      - 管理后台-活动日程
  /admin/schedule/forum/add_edit:
    post:
      consumes:
      - multipart/form-data
      description: 新增或修改活动分论坛日程API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 分论坛名称-中文
        in: formData
        name: cn_name
        type: string
      - description: 展会id
        in: formData
        name: conference_id
        type: integer
      - description: 属于哪一天
        in: formData
        name: day_id
        type: integer
      - description: 分论坛名称-英文
        in: formData
        name: en_name
        type: string
      - description: 活动ID
        in: formData
        name: event_id
        type: integer
      - description: 添加时不传修改时必传
        in: formData
        name: id
        type: integer
      - in: formData
        name: sorting
        type: number
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 新增或修改活动分论坛日程
      tags:
      - 管理后台-活动日程-分论坛
  /admin/schedule/forum/delete/id:
    post:
      consumes:
      - multipart/form-data
      description: 删除活动分论坛日程API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 分论坛ID
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 删除活动分论坛日程
      tags:
      - 管理后台-活动日程-分论坛
  /admin/schedule/forum/detail/id:
    get:
      consumes:
      - multipart/form-data
      description: 查询活动分论坛日程API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 分论坛ID
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.RespEventScheduleInfo'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 活动分论坛日程详情
      tags:
      - 管理后台-活动日程-分论坛
  /admin/schedule/forum/list:
    get:
      consumes:
      - multipart/form-data
      description: 查询活动分论坛日程列表API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 日期ID
        in: query
        name: day_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResAdminEventScheduleForumList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 查询活动分论坛日程列表
      tags:
      - 管理后台-活动日程-分论坛
  /admin/schedule/guest/add_edit:
    post:
      consumes:
      - multipart/form-data
      description: 新增或修改活动日程嘉宾API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 称谓
        in: formData
        name: cn_appellation
        type: string
      - description: 公司
        in: formData
        name: cn_company
        type: string
      - description: 职位
        in: formData
        name: cn_position
        type: string
      - description: 展会id
        in: formData
        name: conference_id
        type: integer
      - description: 称谓
        in: formData
        name: en_appellation
        type: string
      - description: 公司
        in: formData
        name: en_company
        type: string
      - description: 职位
        in: formData
        name: en_position
        type: string
      - description: 活动ID
        in: formData
        name: event_id
        type: integer
      - description: 嘉宾身份
        in: formData
        name: guest_identity
        type: string
      - description: 添加时不传修改时必传
        in: formData
        name: id
        type: integer
      - description: 图片
        in: formData
        name: picture
        type: string
      - description: 日程ID
        in: formData
        name: schedule_id
        type: integer
      - description: 排序
        in: formData
        name: sorting
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 新增或修改活动日程嘉宾
      tags:
      - 管理后台-活动日程-嘉宾
  /admin/schedule/guest/delete/id:
    post:
      consumes:
      - multipart/form-data
      description: 删除活动日程嘉宾嘉宾API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 日程嘉宾ID
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 删除活动日程嘉宾嘉宾
      tags:
      - 管理后台-活动日程-嘉宾
  /admin/schedule/guest/detail/id:
    get:
      consumes:
      - multipart/form-data
      description: 查询活动日程嘉宾详情API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 日程嘉宾ID
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.RespEventScheduleGuestInfo'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 活动日程嘉宾详情
      tags:
      - 管理后台-活动日程-嘉宾
  /admin/schedule/guest/list:
    get:
      consumes:
      - multipart/form-data
      description: 查询活动日程嘉宾列表API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 称谓，职位，公司查询
        in: query
        name: name
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 分页大小
        in: query
        name: page_size
        type: integer
      - description: 日程ID
        in: query
        name: schedule_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResAdminEventScheduleGuestList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 查询活动日程嘉宾列表
      tags:
      - 管理后台-活动日程-嘉宾
  /admin/schedule/list:
    get:
      consumes:
      - multipart/form-data
      description: 查询活动日程列表API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 日期ID
        in: query
        name: day_id
        type: integer
      - description: 活动ID
        in: query
        name: event_id
        type: integer
      - description: 分论坛ID
        in: query
        name: forum_id
        type: integer
      - description: 查询
        in: query
        name: name
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 分页大小
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResAdminEventScheduleList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 查询活动日程列表
      tags:
      - 管理后台-活动日程
  /admin/short_map/add_edit:
    post:
      consumes:
      - multipart/form-data
      description: 新增或修改简称映射关系API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 关联展会ID
        in: formData
        name: conference_id
        required: true
        type: integer
      - description: 展会简称
        in: formData
        name: short_name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 新增或修改简称映射关系
      tags:
      - 管理后台-通用
  /admin/sponsor/add_edit:
    post:
      consumes:
      - multipart/form-data
      description: 新增或修改大会赞助API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 赞助按钮名称-中文
        in: formData
        name: cn_button
        type: string
      - description: 海报内容-中文
        in: formData
        name: cn_content
        type: string
      - description: 海报pdf-中文
        in: formData
        name: cn_pdf
        type: string
      - description: 赞助标题-中文
        in: formData
        name: cn_title
        type: string
      - description: 关联展会ID
        in: formData
        name: conference_id
        type: integer
      - description: 赞助按钮名称-英文
        in: formData
        name: en_button
        type: string
      - description: 海报内容-英文
        in: formData
        name: en_content
        type: string
      - description: 海报pdf-英文
        in: formData
        name: en_pdf
        type: string
      - description: 赞助标题-英文
        in: formData
        name: en_title
        type: string
      - description: 添加时不传修改时必传
        in: formData
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 新增或修改大会赞助
      tags:
      - 管理后台-赞助
  /admin/sponsor/detail/{id}:
    get:
      consumes:
      - multipart/form-data
      description: 查询大会赞助API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 展会id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.RespConferenceSponsorInfo'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 大会赞助详情
      tags:
      - 管理后台-赞助
  /admin/sponsor/export:
    get:
      consumes:
      - multipart/form-data
      description: 导出赞助信息列表
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 关联展会ID
        in: query
        name: conference_id
        type: integer
      - description: 展会名称
        in: query
        name: conference_name
        type: string
      - description: 结束时间
        in: query
        name: end_time
        type: string
      - description: 企业类型
        in: query
        name: enterprise_type
        type: string
      - description: 渠道
        in: query
        name: fromId
        type: string
      - description: 渠道名称
        in: query
        name: from_name
        type: string
      - description: 搜索词
        in: query
        name: keyword
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 分页大小
        in: query
        name: page_size
        type: integer
      - description: 来源id
        in: query
        name: source_id
        type: integer
      - description: 开始时间
        in: query
        name: start_time
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: excel文件
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 导出赞助信息列表
      tags:
      - 管理后台-赞助信息
  /admin/sponsor/list:
    get:
      consumes:
      - multipart/form-data
      description: 获取赞助信息列表
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 关联展会ID
        in: query
        name: conference_id
        type: integer
      - description: 展会名称
        in: query
        name: conference_name
        type: string
      - description: 结束时间
        in: query
        name: end_time
        type: string
      - description: 企业类型
        in: query
        name: enterprise_type
        type: string
      - description: 渠道
        in: query
        name: fromId
        type: string
      - description: 渠道名称
        in: query
        name: from_name
        type: string
      - description: 搜索词
        in: query
        name: keyword
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 分页大小
        in: query
        name: page_size
        type: integer
      - description: 来源id
        in: query
        name: source_id
        type: integer
      - description: 开始时间
        in: query
        name: start_time
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResConferenceExhibitionList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获取赞助信息列表
      tags:
      - 管理后台-赞助信息
  /admin/tickets/add_edit:
    post:
      consumes:
      - multipart/form-data
      description: 新增或修改票种API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 自定义按钮跳转链接-中文
        in: formData
        name: cn_button_link
        type: string
      - description: 自定义按钮名称-中文
        in: formData
        name: cn_button_name
        type: string
      - description: 是否显示购买按钮（1展示，0不展示）
        in: formData
        name: cn_is_displayed
        type: integer
      - description: 最大人数
        in: formData
        name: cn_maximum
        type: integer
      - description: 票种名称中文
        in: formData
        name: cn_name
        type: string
      - description: 报名页门票名称-中文
        in: formData
        name: cn_registration_page_name
        type: string
      - description: 中文服务ID
        in: formData
        name: cn_service_id
        type: string
      - description: 排序
        in: formData
        name: cn_sorting
        type: number
      - description: 展会ID
        in: formData
        name: conference_id
        type: integer
      - description: 自定义按钮跳转链接-英文
        in: formData
        name: en_button_link
        type: string
      - description: 自定义按钮名称-英文
        in: formData
        name: en_button_name
        type: string
      - description: 是否显示购买按钮（1展示，0不展示）
        in: formData
        name: en_is_displayed
        type: integer
      - description: 最大人数
        in: formData
        name: en_maximum
        type: integer
      - description: 票种名称英文
        in: formData
        name: en_name
        type: string
      - description: 报名页门票名称-英文
        in: formData
        name: en_registration_page_name
        type: string
      - description: 英文服务ID
        in: formData
        name: en_service_id
        type: string
      - description: 排序
        in: formData
        name: en_sorting
        type: number
      - description: 添加时不传修改时必传
        in: formData
        name: id
        type: integer
      - description: 费用类型（0人数金额模版，1票种权益模版）
        in: formData
        name: type
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 新增或修改票种
      tags:
      - 管理后台-票种
  /admin/tickets/config/add_edit:
    post:
      consumes:
      - multipart/form-data
      description: 新增或修改票种模版类型API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 价格模板 (0 人数金额模版,  1 票种权益表格模版)
        in: formData
        name: cn_price_template
        type: integer
      - description: 展会ID
        in: formData
        name: conference_id
        type: integer
      - description: 价格模板 (0 人数金额模版,  1 票种权益表格模版)
        in: formData
        name: en_price_template
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 新增或修改票种模版类型
      tags:
      - 管理后台-票种
  /admin/tickets/config/detail/id:
    get:
      consumes:
      - multipart/form-data
      description: 查询票种模版类型信息API
      parameters:
      - description: auth_token
        in: header
        name: SMM-ADMIN-TOKEN
        type: string
      - description: 展会ID
        in: formData
        name: conference_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResConferenceTicketConfigInfo'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 查询票种模版类型信息
      tags:
      - 管理后台-票种
  /admin/tickets/delete/id:
    post:
      consumes:
      - multipart/form-data
      description: 删除票种API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 权益id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 删除票种信息
      tags:
      - 管理后台-票种
  /admin/tickets/detail/id:
    get:
      consumes:
      - multipart/form-data
      description: 查询票种详情信息API
      parameters:
      - description: auth_token
        in: header
        name: SMM-ADMIN-TOKEN
        type: string
      - description: 票种id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.RespTicketPriceInfo'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 查询票种详情信息
      tags:
      - 管理后台-票种
  /admin/tickets/list:
    get:
      consumes:
      - multipart/form-data
      description: 查询票种列表API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 展会id
        in: query
        name: conference_id
        type: integer
      - description: 展会ID 0是中文，1是英文 默认中文
        in: query
        name: is_cn
        type: integer
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 分页大小
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResAdminTicketPriceList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 查询票种列表
      tags:
      - 管理后台-票种
  /admin/tickets/rights/add:
    post:
      consumes:
      - multipart/form-data
      description: 新增或修改票种权益绑定API
      parameters:
      - description: admin_token
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 展会id
        in: formData
        name: conference_id
        type: integer
      - description: 展会ID 0是中文，1是英文 默认中文
        in: formData
        name: is_cn
        type: integer
      - description: 权益ID多个英文逗号分割
        in: formData
        name: rights_id
        type: string
      - description: 票种ID
        in: formData
        name: ticket_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 新增或修改票种权益绑定
      tags:
      - 管理后台-票种权益绑定
  /admin/tickets/rights/list:
    get:
      consumes:
      - multipart/form-data
      description: 查询票种权益绑定列表API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 展会id
        in: query
        name: conference_id
        type: integer
      - description: 展会ID 0是中文，1是英文 默认中文
        in: query
        name: is_cn
        type: integer
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 分页大小
        in: query
        name: page_size
        type: integer
      - description: 票种ID
        in: query
        name: ticket_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResAdminRightsTicketList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 查询票种权益绑定列表
      tags:
      - 管理后台-票种权益绑定
  /admin/tickets/rights_interests/add_edit:
    post:
      consumes:
      - multipart/form-data
      description: 新增或修改票种权益API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 权益内容-中文
        in: formData
        name: cn_content
        type: string
      - description: 排序
        in: formData
        name: cn_sorting
        type: number
      - description: 展会ID
        in: formData
        name: conference_id
        type: integer
      - description: 权益内容-英文
        in: formData
        name: en_content
        type: string
      - description: 排序
        in: formData
        name: en_sorting
        type: number
      - description: 添加时不传修改时必传
        in: formData
        name: id
        type: integer
      - description: 模版类型 （0权益模版，1人数金额模版）
        in: formData
        name: type
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 新增或修改票种权益
      tags:
      - 管理后台-票种权益
  /admin/tickets/rights_interests/delete/id:
    post:
      consumes:
      - multipart/form-data
      description: 删除票种权益API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 权益id
        in: formData
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 删除票种权益信息
      tags:
      - 管理后台-票种权益
  /admin/tickets/rights_interests/detail/id:
    get:
      consumes:
      - multipart/form-data
      description: 查询票种权益详情信息API
      parameters:
      - description: auth_token
        in: header
        name: SMM-ADMIN-TOKEN
        type: string
      - description: 权益id
        in: formData
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResConferenceRightsInterestsInfo'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 查询票种权益详情信息
      tags:
      - 管理后台-票种权益
  /admin/tickets/rights_interests/en_list:
    get:
      consumes:
      - multipart/form-data
      description: 查询票种权益列表API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 展会id
        in: query
        name: conference_id
        type: integer
      - description: 展会ID 0是中文，1是英文 默认中文
        in: query
        name: is_cn
        type: integer
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 分页大小
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResAdminRightsInterestsList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 查询票种权益列表
      tags:
      - 管理后台-票种权益
  /admin/tickets/rights_interests/list:
    get:
      consumes:
      - multipart/form-data
      description: 查询票种权益列表API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 展会id
        in: query
        name: conference_id
        type: integer
      - description: 展会ID 0是中文，1是英文 默认中文
        in: query
        name: is_cn
        type: integer
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 分页大小
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResAdminRightsInterestsList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 查询票种权益列表
      tags:
      - 管理后台-票种权益
  /conference_register/upd:
    post:
      consumes:
      - multipart/form-data
      description: 订单购买状态回调API
      parameters:
      - description: SMM_auth_token
        in: header
        name: SMM_auth_token
        type: string
      - description: json body
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/protocol.ReqAdminUpdConferenceRegister'
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 订单购买状态回调
      tags:
      - 内网接口-订单回调
  /conference_ticket_price/update:
    post:
      consumes:
      - multipart/form-data
      description: 服务修改回调通知API
      parameters:
      - description: SMM_auth_token
        in: header
        name: SMM_auth_token
        type: string
      - description: json body
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/protocol.ReqAddConferenceRegister'
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 服务修改回调通知
      tags:
      - 内网接口-订单回调
  /production:
    get:
      consumes:
      - multipart/form-data
      description: 根据id获取产品信息
      parameters:
      - description: admin_token
        in: header
        name: AUTH-TOKEN
        required: true
        type: string
      - description: 产品信息id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ProductionManagement'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 根据id获取产品信息 （2.0新增）
      tags:
      - 后台-根据id获取产品信息
  /user/apply/purchase_booth/submit:
    post:
      consumes:
      - multipart/form-data
      description: 购买展览展位API
      parameters:
      - description: 百度跟踪ID
        in: formData
        name: bd_vid
        type: string
      - description: 电话
        in: formData
        name: cellphone
        required: true
        type: string
      - description: 0 中文 1 英文
        in: formData
        name: cn_or_en
        type: integer
      - description: 公司
        in: formData
        name: company
        required: true
        type: string
      - description: 关联展会ID
        in: formData
        name: conference_id
        required: true
        type: integer
      - description: 国家
        in: formData
        name: country
        type: string
      - description: 邮箱
        in: formData
        name: email
        type: string
      - description: 企业类型
        in: formData
        name: enterprise_type
        type: string
      - description: 感兴趣的展区
        in: formData
        name: exhibition_area
        type: string
      - in: formData
        name: first_name
        type: string
      - description: 渠道
        in: formData
        name: fromId
        type: string
      - description: 职位
        in: formData
        name: job_title
        required: true
        type: string
      - in: formData
        name: last_name
        type: string
      - description: 主推产品
        in: formData
        name: main_products
        type: string
      - description: 来源ID （跟报名/user/register/add接口传的一样）
        in: formData
        name: source_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 购买展览展位
      tags:
      - 表单-购买展览展位
  /user/apply/sponsor/submit:
    post:
      consumes:
      - multipart/form-data
      description: 会议赞助及广告机会API
      parameters:
      - description: 百度跟踪ID
        in: formData
        name: bd_vid
        type: string
      - description: 0 中文 1 英文
        in: formData
        name: cn_or_en
        type: integer
      - description: 公司
        in: formData
        name: company
        required: true
        type: string
      - description: 关联展会ID
        in: formData
        name: conference_id
        required: true
        type: integer
      - in: formData
        name: country
        type: string
      - description: 邮箱
        in: formData
        name: email
        type: string
      - description: 企业类型
        in: formData
        name: enterprise_type
        type: string
      - description: 感兴趣的展区
        in: formData
        name: exhibition_area
        type: string
      - in: formData
        name: first_name
        type: string
      - description: 渠道
        in: formData
        name: fromId
        type: string
      - description: 职位
        in: formData
        name: job_title
        type: string
      - in: formData
        name: last_name
        type: string
      - description: 主推产品
        in: formData
        name: main_products
        type: string
      - description: 手机号验证码
        in: formData
        name: phone_code
        type: string
      - description: 来源ID （跟报名/user/register/add接口传的一样）
        in: formData
        name: source_id
        type: string
      - description: 电话
        in: formData
        name: telephone
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 会议赞助及广告机会
      tags:
      - 表单-会议赞助及广告机会
  /user/apply/submit:
    post:
      consumes:
      - multipart/form-data
      description: 申请参展或赞助API
      parameters:
      - description: SMM_auth_token
        in: header
        name: SMM_auth_token
        type: string
      - description: 展位类型
        in: formData
        name: booth_type
        type: string
      - description: 电话
        in: formData
        name: cellphone
        required: true
        type: string
      - description: 公司
        in: formData
        name: company
        required: true
        type: string
      - description: 关联展会ID
        in: formData
        name: conference_id
        required: true
        type: integer
      - description: 国家
        in: formData
        name: country
        required: true
        type: string
      - description: 邮箱
        in: formData
        name: email
        required: true
        type: string
      - description: 名
        in: formData
        name: first_name
        required: true
        type: string
      - description: 渠道
        in: formData
        name: fromId
        type: string
      - description: 兴趣等级
        in: formData
        name: interest_level
        type: string
      - description: 职位
        in: formData
        name: job_title
        type: string
      - description: 姓 （中文可以不传，英文必填）
        in: formData
        name: last_name
        type: string
      - description: 来源ID （跟报名/user/register/add接口传的一样）
        in: formData
        name: source_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 申请参展或赞助
      tags:
      - 前台-通用
  /user/apply/visit/submit:
    post:
      consumes:
      - multipart/form-data
      description: 表单申请参观API
      parameters:
      - description: 电话
        in: formData
        name: cellphone
        required: true
        type: string
      - description: 公司
        in: formData
        name: company
        required: true
        type: string
      - description: 职位
        in: formData
        name: job_title
        required: true
        type: string
      - description: 姓名
        in: formData
        name: name
        required: true
        type: string
      - description: 手机号验证码
        in: formData
        name: phone_code
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 表单申请参观
      tags:
      - 表单-申请参观
  /user/audience/after/report/list:
    get:
      consumes:
      - multipart/form-data
      description: 前台获取展后报告列表
      parameters:
      - description: admin_token
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/protocol.AfterReport'
                  type: array
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 前台获取展后报告列表
      tags:
      - 前台-展后报告
  /user/audience/company/bearing:
    get:
      consumes:
      - multipart/form-data
      description: 根据id获取风采详情
      parameters:
      - description: admin_token
        in: header
        name: AUTH-TOKEN
        required: true
        type: string
      - description: 新闻id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.CompanyBearing'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 根据id获取风采详情（2.0修改 ，新增接口）
      tags:
      - 前台-展商
  /user/audience/company/bearing/list:
    get:
      consumes:
      - multipart/form-data
      description: 前台获取展商风采数据
      parameters:
      - description: admin_token
        in: header
        name: AUTH-TOKEN
        required: true
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 分页大小
        in: query
        name: page_size
        type: integer
      - description: 年度
        in: query
        name: year
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResCompanyBearingList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 前台获取展商风采数据（展商风采  （2.0修改 ，新增接口）
      tags:
      - 前台-展商
  /user/audience/company/directories:
    get:
      consumes:
      - multipart/form-data
      description: 前台获取展商名录
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: AUTH-TOKEN
        required: true
        type: string
      - description: 展会id
        in: query
        name: conference_id
        type: integer
      - description: 中文站请求
        in: query
        name: is_cn
        type: boolean
      - description: 关键字
        in: query
        name: key_word
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 分页大小
        in: query
        name: page_size
        type: integer
      - description: 场馆id
        in: query
        name: venue_id
        type: integer
      - description: 年度
        in: query
        name: year
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.CompanyDirectories'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 前台获取展商名录（2.0修改 ，接口传参、出参修改）
      tags:
      - 前台-展商名录
  /user/audience/floor/graph:
    get:
      consumes:
      - multipart/form-data
      description: 查询展览平面图
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResPageFloorGraph'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 查询展览平面图
      tags:
      - 前台-观众
  /user/audience/industry/info:
    get:
      consumes:
      - multipart/form-data
      description: 获取行业资讯
      parameters:
      - description: admin_token
        in: header
        name: AUTH-TOKEN
        required: true
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResIndustryInfo'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获取行业资讯
      tags:
      - 前台-首页
  /user/audience/paper:
    get:
      consumes:
      - multipart/form-data
      description: 获取问卷内容
      parameters:
      - description: SMM_auth_token
        in: header
        name: AUTH-TOKEN
        required: true
        type: string
      - description: 手机
        in: formData
        name: cellphone
        type: string
      - description: 关联展会ID
        in: formData
        name: conference_id
        required: true
        type: integer
      - description: 邮箱
        in: formData
        name: email
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.QuestionPaper'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获取问卷内容
      tags:
      - 前台-观众
  /user/audience/paper/collection:
    get:
      consumes:
      - multipart/form-data
      description: 前台获取论文征集数据
      parameters:
      - description: admin_token
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.PaperCollection'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 前台获取论文征集数据
      tags:
      - 前台-论文征集
  /user/audience/pre/register:
    get:
      consumes:
      - multipart/form-data
      description: 获取观众预登记记录api
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResAudiencePreRegister'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获取观众预登记记录
      tags:
      - 前台-观众预登记
  /user/audience/submit/free/visitor:
    post:
      consumes:
      - application/json
      description: 提交免费观众信息
      parameters:
      - description: admin_token
        in: header
        name: AUTH-TOKEN
        required: true
        type: string
      - description: Query Params
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/protocol.ReqSaveFreeVisitorInfo'
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResSubmitFreeVisitor'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 提交免费观众信息
      tags:
      - 前台-观众
  /user/audience/submit/paper:
    post:
      consumes:
      - application/json
      description: 提交问卷
      parameters:
      - description: admin_token
        in: header
        name: AUTH-TOKEN
        required: true
        type: string
      - description: Query Params
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/protocol.ReqSaveQuestionPaper'
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResSubmitQuestionPaper'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 提交问卷
      tags:
      - 前台-观众
  /user/cn/media_registration/add:
    post:
      consumes:
      - multipart/form-data
      description: 新增或修改大会介绍和介绍视频API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 大会介绍-页面内容-中文
        in: formData
        name: cn_content
        type: string
      - description: 展会ID
        in: formData
        name: conference_id
        type: integer
      - description: 大会介绍-页面内容-英文
        in: formData
        name: en_content
        type: string
      - description: 添加时不传修改时必传
        in: formData
        name: id
        type: integer
      - description: 会议介绍配图
        in: formData
        name: introduction_diagram
        type: string
      - description: 介绍视频
        in: formData
        name: introduction_video
        type: string
      - description: 视频封面
        in: formData
        name: video_cover
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 新增或修改大会介绍和介绍视频
      tags:
      - 管理后台-首页其他-大会介绍和介绍视频
  /user/column_information/list:
    get:
      consumes:
      - multipart/form-data
      description: 查询栏目信息列表API
      parameters:
      - description: SMM_auth_token
        in: header
        name: SMM_auth_token
        type: string
      - description: 展会id
        in: query
        name: conference_id
        type: integer
      - description: 是否是中文 false 中文，true 英文
        in: query
        name: is_en
        type: boolean
      - description: 模块类型（1赞助商，3合作商，4媒体）不传时返回除嘉宾外其他所有信息
        in: query
        name: type
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.RespWebColumnInformationList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 查询栏目信息列表
      tags:
      - 前台-通用模块
  /user/common/search:
    get:
      consumes:
      - multipart/form-data
      description: 搜索API
      parameters:
      - description: SMM_auth_token
        in: header
        name: SMM_auth_token
        type: string
      - description: 展会ID
        in: query
        name: conference_id
        required: true
        type: integer
      - description: 搜索内容
        in: query
        name: keyword
        required: true
        type: string
      - description: 对应语言
        enum:
        - cn
        - en
        in: query
        name: language
        required: true
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 分页大小
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResSearchDataList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 搜索
      tags:
      - 前台-通用
  /user/company/apply/conference:
    get:
      consumes:
      - multipart/form-data
      description: 获取展会价值数据
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ApplyConference'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获取展会价值数据 （2.0修改 ，之前页面叫申请参展）
      tags:
      - 前台-展商-展会价值
  /user/company/exhibition/success:
    get:
      consumes:
      - multipart/form-data
      description: 获取申请参展成功提示
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ExhibitionSuccess'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获取申请参展成功提示
      tags:
      - 前台-展商-申请参展
  /user/company/get/handbook/content:
    get:
      consumes:
      - multipart/form-data
      description: 前台获取展商手册页面内容
      parameters:
      - description: admin_token
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.HandbookContent'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 前台获取展商手册页面内容
      tags:
      - 前台-展商手册
  /user/company/get/invitation:
    get:
      consumes:
      - multipart/form-data
      description: 获取观展邀请函
      parameters:
      - description: SMM_auth_token
        in: header
        name: SMM_auth_token
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.Invitation'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获取观展邀请函
      tags:
      - 前台-展商观展邀请函
  /user/company/get/set/up:
    get:
      consumes:
      - multipart/form-data
      description: 获取现场搭建时间
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.SetUp'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获取现场搭建时间
      tags:
      - 前台-展商现场搭建
  /user/company/handbook/list:
    get:
      consumes:
      - multipart/form-data
      description: 获取展商手册列表
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResCompanyHandbookList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获取展商手册列表
      tags:
      - 前台-展商手册
  /user/company/online/register:
    get:
      consumes:
      - multipart/form-data
      description: 获取展商在线登记数据
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.OnlineRegister'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获取展商在线登记数据
      tags:
      - 前台-展商在线登记
  /user/company/qa/list:
    get:
      consumes:
      - multipart/form-data
      description: 获取展商常见问题
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResCommonQA'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获取展商常见问题
      tags:
      - 前台-展商常见问题
  /user/conference/detail/:id:
    get:
      consumes:
      - multipart/form-data
      description: 查询展会基本信息API
      parameters:
      - description: SMM_auth_token
        in: header
        name: SMM_auth_token
        type: string
      - description: Path Params
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResConferenceInfo'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 查询展会基本信息
      tags:
      - 前台-首页
  /user/conference/info:
    get:
      consumes:
      - multipart/form-data
      description: 查询展会基本信息API
      parameters:
      - description: SMM_auth_token
        in: header
        name: SMM_auth_token
        type: string
      - description: 会议编号
        in: formData
        name: meeting_no
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResConferenceInfo'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 查询展会基本信息
      tags:
      - 前台-首页
  /user/conference/mapping:
    get:
      consumes:
      - multipart/form-data
      description: 查询展会映射关系API
      parameters:
      - description: SMM_auth_token
        in: header
        name: SMM_auth_token
        type: string
      - description: Query Params
        in: query
        name: short_name
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/protocol.ResMapInfo'
                  type: array
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 查询展会映射关系
      tags:
      - 前台-首页
  /user/en/media_registration/add:
    post:
      consumes:
      - multipart/form-data
      description: 新增或修改大会介绍和介绍视频API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 大会介绍-页面内容-中文
        in: formData
        name: cn_content
        type: string
      - description: 展会ID
        in: formData
        name: conference_id
        type: integer
      - description: 大会介绍-页面内容-英文
        in: formData
        name: en_content
        type: string
      - description: 添加时不传修改时必传
        in: formData
        name: id
        type: integer
      - description: 会议介绍配图
        in: formData
        name: introduction_diagram
        type: string
      - description: 介绍视频
        in: formData
        name: introduction_video
        type: string
      - description: 视频封面
        in: formData
        name: video_cover
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 新增或修改大会介绍和介绍视频
      tags:
      - 管理后台-首页其他-大会介绍和介绍视频
  /user/enterprise_type/list:
    get:
      consumes:
      - multipart/form-data
      description: 企业类型下拉列表API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 展会ID
        in: query
        name: conference_id
        type: integer
      - description: 公司或嘉宾信息id
        in: query
        name: information_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResAdminEnterpriseTypeList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 企业类型下拉列表
      tags:
      - 管理后台-通用模块
  /user/event/annual_selection:
    get:
      consumes:
      - multipart/form-data
      description: 评选活动评选介绍和流程API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM_auth_token
        required: true
        type: string
      - description: 展会ID
        in: query
        name: conference_id
        type: integer
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 分页大小
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResAnnualSelectionList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 评选活动评选介绍和流程
      tags:
      - 前台-年度评选
  /user/event/expert/list:
    get:
      consumes:
      - multipart/form-data
      description: 评选活动评委列表公示API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM_auth_token
        required: true
        type: string
      - description: 展会ID
        in: query
        name: conference_id
        type: integer
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 分页大小
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResEventExpertInfoList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 评选活动评委列表公示
      tags:
      - 前台-年度评选
  /user/event/list:
    get:
      consumes:
      - multipart/form-data
      description: 查询活动列表API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM_auth_token
        required: true
        type: string
      - description: 展会ID
        in: query
        name: conference_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResUserConferenceEventList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 查询活动一览表列表 (2.0添加活动分类字段)
      tags:
      - 前台-活动一览表
  /user/forum_category/list:
    get:
      consumes:
      - multipart/form-data
      description: 活动类别关联导航菜单列表API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM_auth_token
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResForumCategoryConfigList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 活动类别关联导航菜单
      tags:
      - 前台-活动一览表
  /user/forum_config/list:
    get:
      consumes:
      - multipart/form-data
      description: 感兴趣的会议列表API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 展会id
        in: query
        name: conference_id
        type: integer
      - description: 0感兴趣的会议，1感兴趣的展区
        in: query
        name: type
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResUserForumConfigList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 感兴趣的会议列表
      tags:
      - 前台-票和报名
  /user/get/about/us:
    get:
      consumes:
      - multipart/form-data
      description: 获得关于我们
      parameters:
      - description: SMM_auth_token
        in: header
        name: SMM_auth_token
        type: string
      - description: Query Params
        in: query
        name: conference_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResAboutUs'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获得关于我们
      tags:
      - 前台-关于我们
  /user/get/apply/purchase_booth/list:
    get:
      consumes:
      - multipart/form-data
      description: 获取行业信息
      parameters:
      - description: admin_token
        in: header
        name: AUTH-TOKEN
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/protocol.IndustryFocus'
                  type: array
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获取行业信息
      tags:
      - 通用
  /user/get/apply/sponsor/list:
    get:
      consumes:
      - multipart/form-data
      description: 获取来源列表
      parameters:
      - description: admin_token
        in: header
        name: AUTH-TOKEN
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/protocol.IndustryFocus'
                  type: array
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获取来源列表
      tags:
      - 通用-获取来源列表
  /user/get/city/info:
    get:
      consumes:
      - multipart/form-data
      description: 前台获得城市概况
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.CityInfo'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 前台获得城市概况
      tags:
      - 前台-城市概况
  /user/get/contact/us:
    get:
      consumes:
      - multipart/form-data
      description: 获得联系我们
      parameters:
      - description: SMM_auth_token
        in: header
        name: SMM_auth_token
        type: string
      - description: Query Params
        in: query
        name: conference_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResContactUsList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获得联系我们
      tags:
      - 前台-关于我们
  /user/get/hall/info:
    get:
      consumes:
      - multipart/form-data
      description: 获取展馆概况
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.HallInfo'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获取展馆概况
      tags:
      - 前台-参会指南
  /user/get/hotel:
    get:
      consumes:
      - multipart/form-data
      description: 获取酒店住宿页面数据
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.HotelPage'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获取酒店住宿页面数据
      tags:
      - 前台-酒店住宿
  /user/get/industry/list:
    get:
      consumes:
      - multipart/form-data
      description: 获取行业信息
      parameters:
      - description: admin_token
        in: header
        name: AUTH-TOKEN
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/protocol.IndustryFocus'
                  type: array
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获取行业信息
      tags:
      - 通用
  /user/get/traffic/service:
    get:
      consumes:
      - multipart/form-data
      description: 前台获得交通服务信息
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.TrafficService'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 前台获得交通服务信息
      tags:
      - 前台-交通服务
  /user/get/venues:
    get:
      consumes:
      - multipart/form-data
      description: 获取场馆信息
      parameters:
      - description: admin_token
        in: header
        name: SMM_auth_token
        type: string
      - description: 中文 0 英文 1
        in: query
        name: cn_or_en
        type: integer
      - description: 展会id
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/protocol.Venue'
                  type: array
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 获取场馆信息
      tags:
      - 前台-通用
  /user/guest_information/detail/id:
    get:
      consumes:
      - multipart/form-data
      description: 查询嘉宾信息详情API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM_auth_token
        required: true
        type: string
      - description: 公司或嘉宾信息id
        in: query
        name: information_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.RespConferenceInformationInfo'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 查询嘉宾信息详情
      tags:
      - 前台-通用模块
  /user/guest_information/list:
    get:
      consumes:
      - multipart/form-data
      description: 查询嘉宾信息列表API
      parameters:
      - description: SMM_auth_token
        in: header
        name: SMM_auth_token
        type: string
      - description: 展会id
        in: query
        name: conference_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResWebGuestInformationList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 查询嘉宾信息列表
      tags:
      - 前台-通用模块
  /user/handbook/subscribe:
    post:
      consumes:
      - multipart/form-data
      description: 订阅展商手册API
      parameters:
      - description: SMM_auth_token
        in: header
        name: SMM_auth_token
        type: string
      - description: 手机
        in: formData
        name: cellphone
        type: string
      - description: 关联展会ID
        in: formData
        name: conference_id
        required: true
        type: integer
      - description: 邮箱
        in: formData
        name: email
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 订阅展商手册
      tags:
      - 前台-展商
  /user/introduction/list:
    get:
      consumes:
      - multipart/form-data
      description: 大会介绍信息列表API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM_auth_token
        required: true
        type: string
      - description: 展会ID
        in: query
        name: conference_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResWebConferenceIntroductionList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 大会介绍信息列表
      tags:
      - 前台-首页其他信息集合
  /user/previous/list:
    get:
      consumes:
      - multipart/form-data
      description: 查询往届展会API
      parameters:
      - description: SMM_auth_token
        in: header
        name: SMM_auth_token
        type: string
      - description: 关联展会ID
        in: formData
        name: conference_id
        required: true
        type: integer
      - description: 页码
        in: formData
        name: page
        type: integer
      - description: 分页大小
        in: formData
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResUserPreviousList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 查询往届展会
      tags:
      - 前台-通用
  /user/register/add:
    post:
      consumes:
      - multipart/form-data
      description: 用户填写报名信息API
      parameters:
      - description: SMM_auth_token
        in: header
        name: SMM_auth_token
        type: string
      - description: json body
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/protocol.ReqAddConferenceRegister'
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 用户填写报名信息 （2.0修改报名信息和参会人信息分开填写）
      tags:
      - 前台-票和报名
  /user/register/info:
    get:
      consumes:
      - multipart/form-data
      description: 用户填写报名信息详情API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 报名id
        in: query
        name: register_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResConferenceRegister'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 用户填写报名信息详情 （2.0新增 购买参会时第二步获取报名信息使用）
      tags:
      - 前台-报名信息详情
  /user/schedule/detail:
    get:
      consumes:
      - multipart/form-data
      description: 查询活动详情API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM_auth_token
        required: true
        type: string
      - description: 活动ID
        in: query
        name: event_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.RespUserConferenceEventInfo'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 活动日程详情
      tags:
      - 前台-活动一览表
  /user/schedule/list:
    get:
      consumes:
      - multipart/form-data
      description: 查询活动日程列表API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM_auth_token
        required: true
        type: string
      - description: 日期ID
        in: query
        name: day_id
        type: integer
      - description: 活动ID
        in: query
        name: event_id
        type: integer
      - description: 分论坛ID
        in: query
        name: forum_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResUserEventScheduleList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 查询活动日程列表
      tags:
      - 前台-活动一览表
  /user/schedule_forum/list:
    get:
      consumes:
      - multipart/form-data
      description: 查询活动日程分论坛列表API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM_auth_token
        required: true
        type: string
      - description: 日期ID
        in: query
        name: day_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResUserEventScheduleForumList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 查询活动日程分论坛列表
      tags:
      - 前台-活动一览表
  /user/send/code:
    post:
      consumes:
      - multipart/form-data
      description: 表单发送手机验证码API
      parameters:
      - description: 需要发送的手机号
        in: formData
        name: cellphone
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 表单发送手机验证码
      tags:
      - 表单-申请参观-发送验证码
  /user/send_sms:
    post:
      consumes:
      - multipart/form-data
      description: 发送验证码
      parameters:
      - description: admin_token
        in: header
        name: AUTH-TOKEN
        required: true
        type: string
      - description: 需要发送的手机号
        in: query
        name: cellphone
        type: string
      - in: query
        name: code_type
        type: string
      - in: query
        name: source
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            $ref: '#/definitions/protocol.Response'
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 发送验证码
      tags:
      - 前台-通用
  /user/sponsor/detail/{id}:
    get:
      consumes:
      - multipart/form-data
      description: 查询大会赞助API
      parameters:
      - description: SMM_auth_token
        in: header
        name: SMM_auth_token
        type: string
      - description: 展会id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.RespConferenceSponsorInfo'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 大会赞助详情
      tags:
      - 前台-赞助
  /user/tickets/list:
    get:
      consumes:
      - multipart/form-data
      description: 查询票种信息列表API
      parameters:
      - description: SMM-ADMIN-TOKEN
        in: header
        name: SMM-ADMIN-TOKEN
        required: true
        type: string
      - description: 展会ID
        in: query
        name: conference_id
        type: integer
      - description: 展会ID 0是中文，1是英文 默认中文
        in: query
        name: is_cn
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: ok，code = 0
          schema:
            allOf:
            - $ref: '#/definitions/protocol.Response'
            - properties:
                data:
                  $ref: '#/definitions/protocol.ResUserTicketPriceList'
              type: object
        "500":
          description: failed，code != 0
          schema:
            $ref: '#/definitions/protocol.Response'
      summary: 查询票种信息列表
      tags:
      - 前台-票和报名
securityDefinitions:
  JWT:
    description: Description for what is this security definition being used
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
