package service

import (
	"conferencecenter/internal/db"
	"conferencecenter/internal/model"
	"conferencecenter/internal/protocol"
	"gorm.io/gorm"
)

// todo test
func AdminAddEditConferenceSponsor(req protocol.ReqAdminSaveSponsor) error {
	sponsor := model.ConferenceSponsor{
		ID:           req.ID,
		ConferenceId: req.ConferenceId,
		CnTitle:      req.CnTitle,
		EnTitle:      req.EnTitle,
		CnButton:     req.CnButton,
		EnButton:     req.EnButton,
		CnPdf:        req.CnPdf,
		EnPdf:        req.EnPdf,
		CnContent:    req.CnContent,
		EnContent:    req.EnContent,
	}
	//id大于0视为编辑
	if req.ID > 0 {
		err := db.EditConferenceSponsor(nil, sponsor)
		return err
	}
	err := db.SaveConferenceSponsor(nil, &sponsor)
	return err
}

func QueryConferenceSponsorInfo(id string) (protocol.RespConferenceSponsorInfo, error) {
	info, err := db.GetConferenceSponsorById(nil, id)
	if err != nil && err != gorm.ErrRecordNotFound {
		return protocol.RespConferenceSponsorInfo{}, err
	}
	sponsorInfo := protocol.RespConferenceSponsorInfo{
		ID:           info.ID,
		CnTitle:      info.CnTitle,
		EnTitle:      info.EnTitle,
		CnButton:     info.CnButton,
		EnButton:     info.EnButton,
		CnPdf:        info.CnPdf,
		EnPdf:        info.EnPdf,
		CnContent:    info.CnContent,
		EnContent:    info.EnContent,
		ConferenceId: info.ConferenceId,
	}
	return sponsorInfo, nil
}
