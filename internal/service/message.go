package service

import (
	"conferencecenter/internal/constant"
	"conferencecenter/internal/corecontext"
	"conferencecenter/internal/db"
	"conferencecenter/internal/model"
	"conferencecenter/internal/pkg/utils"
	"fmt"
	"git.code.tencent.com/smmit/smmbase/logger"
	"git.code.tencent.com/smmit/smmbase/mailgun"
	"git.code.tencent.com/smmit/smmbase/sendcloud"
	"git.code.tencent.com/smmit/smmbase/sms"
	"github.com/pkg/errors"
	"github.com/ucloud/ucloud-sdk-go/services/usms"
	"github.com/ucloud/ucloud-sdk-go/ucloud/auth"
	uConfig "github.com/ucloud/ucloud-sdk-go/ucloud/config"
	"github.com/ucloud/ucloud-sdk-go/ucloud/log"
	"strings"
	"time"
)

// 发送UCloud短信
func SendUSMS(proId, sigCon, tempId string, receivers []string, tempParams ...string) (err error) {
	cfg := uConfig.NewConfig()
	cfg.LogLevel = log.WarnLevel
	cfg.ProjectId = proId

	credential := auth.NewCredential()
	credential.PrivateKey = "3bQDLEHWim57jVCyzgyIWqGDEeanwmSF5G4mzpjhoWvv"
	credential.PublicKey = "4eZBKjs4FSHJMmBiLE3pDkBB2Q74O1AW"

	client := usms.NewClient(&cfg, &credential)
	req := client.NewSendUSMSMessageRequest()
	req.SigContent = &sigCon
	req.TemplateId = &tempId
	req.PhoneNumbers = receivers
	req.TemplateParams = tempParams
	_, err = client.SendUSMSMessage(req)
	if err != nil {
		_ = logger.Error("SendUSMSMessage:Err->", err)
	}
	return err
}

func SendSEmail(email, subject, content string) (err error) {
	e := sendcloud.SendCloudUser{
		ApiKey:   sendcloud.API_KEY,
		ApiUser:  sendcloud.API_USER,
		LabelId:  72257,
		FromName: "上海有色网",
	}
	err = e.SendNormalEmailWithFiles(email, subject, content, &[]sendcloud.FormFile{})
	if err != nil {
		_ = logger.Error(email, subject, content)
	}
	return err
}

// 发送展商手册通知
func SendHandBookDataNotify(data model.KfkHandBookData) (err error) {
	var (
		sendStatus = constant.SendStatusFailure
		srvConf    = corecontext.Config()
	)
	sendLog := model.CompanyHandBookSendLog{
		SubscribeID:      data.SubscribeId,
		ConferenceID:     data.ConferenceId,
		HandbookID:       data.HandBookId,
		CnConferenceName: data.CnConferenceName,
		EnConferenceName: data.EnConferenceName,
		CnHandBookName:   data.CnHandBookName,
		EnHandBookName:   data.EnHandBookName,
		CnHandBookURL:    data.CnHandBookUrl,
		EnHandBookURL:    data.EnHandBookUrl,
		Cellphone:        data.Cellphone,
		Email:            data.Email,
		Status:           sendStatus,
		CreateTime:       time.Now(),
		UpdateTime:       time.Now(),
	}
	if data.Cellphone != "" {
		sendLog.SendType = constant.HandBookSendTypeCellphone
		sendLog.Content = fmt.Sprintf(`您好，您关注的%s的展商手册有更新，请打开%s，查看最新展商手册，拒收请回复R。`, sendLog.CnConferenceName, sendLog.CnHandBookURL)
		if !utils.CheckReceiverPhone(sendLog.Cellphone) {
			sendLog.Status = constant.SendStatusAbnormal
		}
	} else if data.Email != "" {
		sendLog.SendType = constant.HandBookSendTypeEmail
		sendLog.Content = fmt.Sprintf(`Hello, the exhibitor manual of %s you are following has been updated. Please open %s to view the latest exhibitor manual.`, sendLog.EnConferenceName, sendLog.EnHandBookURL)
		if sendLog.EnConferenceName == "" || sendLog.EnHandBookURL == "" {
			sendLog.Status = constant.SendStatusAbnormal
		}
	}
	err = db.SaveHandBookSendLog(nil, &sendLog)
	if err != nil {
		err = errors.WithMessage(err, "存储发送记录失败")
		return err
	}
	if sendLog.Status == constant.SendStatusAbnormal { // 数据异常时不再发送
		return
	}
	if sendLog.SendType == constant.HandBookSendTypeCellphone {
		_, cellphone := utils.GetCellphoneCountryCode(sendLog.Cellphone)
		if strings.HasPrefix(cellphone, "1000") == false {
			argsList := []string{sendLog.CnConferenceName, sendLog.CnHandBookURL}
			//发送短信
			err = sms.SendYtxSms(cellphone, srvConf.YtxConf.HandBookTemplateId, argsList...)
			if err != nil {
				_ = logger.Error(fmt.Sprintf("SendYtxSms:cellPhone->%s,content->%s,Err->", cellphone, sendLog.Content), err)
			} else {
				sendStatus = constant.SendStatusSuccess
			}
		} else {
			sendStatus = constant.SendStatusAbnormal
		}
	} else if sendLog.SendType == constant.HandBookSendTypeEmail {
		//发送邮件
		_, err = mailgun.SendMailGunEmail([]string{sendLog.Email}, "Exhibitor Mannual Update Reminder", sendLog.Content, &[]sendcloud.FormFile{}, "", "")
		if err != nil {
			_ = logger.Error(fmt.Sprintf("SendMailGunEmail:Email->%s,content->%s,Err->", sendLog.Email, sendLog.Content), err)
		} else {
			sendStatus = constant.SendStatusSuccess
		}
	}
	if sendStatus == constant.SendStatusAbnormal {
		return
	}
	err = db.UpdateHandBookSendLog(nil, sendLog.ID, sendStatus)
	if err != nil {
		err = errors.WithMessage(err, "更新发送状态失败")
	}
	return err
}
