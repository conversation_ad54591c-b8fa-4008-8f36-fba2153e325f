package service

import (
	"bytes"
	"conferencecenter/internal/acache"
	"conferencecenter/internal/constant"
	"conferencecenter/internal/corecontext"
	"conferencecenter/internal/db"
	"conferencecenter/internal/errcode"
	"conferencecenter/internal/memory"
	"conferencecenter/internal/model"
	"conferencecenter/internal/pkg/utils"
	"conferencecenter/internal/protocol"
	"conferencecenter/internal/rpc"
	"conferencecenter/internal/weChat"
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.code.tencent.com/smmit/smmbase/logger"
	"git.code.tencent.com/smmit/smmbase/webhook/feishu"
	"github.com/pkg/errors"
	"golang.org/x/sync/errgroup"
)

// 获取展会列表
func GetConferenceList(req protocol.ReqAdminConferenceList) (int64, []protocol.ResConferenceInfo, error) {
	var (
		infoLi = make([]protocol.ResConferenceInfo, 0)
	)
	buttonMap, err := GetCoverButtonMap(0)
	if err != nil {
		_ = logger.Error("GetCoverButtonMap:", err)
		return 0, infoLi, errors.WithStack(err)
	}
	total, dataLi, err := db.QueryConferenceList(nil, req.Keyword, true, req.Page, req.PageSize)
	if err != nil {
		_ = logger.Error("GetConferenceList:", err)
		return total, infoLi, errors.WithStack(err)
	}
	for _, data := range dataLi {
		buttonLi, ok := buttonMap[data.ID]
		if !ok {
			buttonLi = make([]protocol.ResButtonInfo, 0)
		}
		infoLi = append(infoLi, protocol.ResConferenceInfo{
			ID:             data.ID,
			CnName:         data.CnName,
			EnName:         data.EnName,
			CnShortName:    data.CnShortName,
			EnShortName:    data.EnShortName,
			CnLocation:     data.CnLocation,
			EnLocation:     data.EnLocation,
			StartTime:      data.StartTime.Format(constant.DateTime),
			EndTime:        data.EndTime.Format(constant.DateTime),
			Status:         data.Status,
			CnTopLeftLogo:  data.CnTopLeftLogo,
			EnTopLeftLogo:  data.EnTopLeftLogo,
			TopLogo:        data.TopLogo,
			TinyLogo:       data.TinyLogo,
			VideoBack:      data.VideoBack,
			PcBack:         data.PcBack,
			H5Back:         data.H5Back,
			CnShareGraph:   data.CnShareGraph,
			EnShareGraph:   data.EnShareGraph,
			CnShareTitle:   data.CnShareTitle,
			EnShareTitle:   data.EnShareTitle,
			CnShareContent: data.CnShareContent,
			EnShareContent: data.EnShareContent,
			MeetingSysId:   data.MeetingSysId,
			QwxUrl:         data.QwxUrl,
			TemplateType:   data.TemplateType,
			ButtonLi:       buttonLi,
			UpdateUser:     data.UpdateUser,
			CreateUser:     data.CreateUser,
			UpdateTime:     data.UpdateTime.Format(constant.DateTime),
			CreateTime:     data.CreateTime.Format(constant.DateTime),
		})
	}

	return total, infoLi, err
}

func GetConferenceNameByType(req protocol.ReqAdminConferenceNameList) (protocol.ResAdminConferenceNameList, error) {
	var (
		res = protocol.ResAdminConferenceNameList{
			List: make([]protocol.ConferenceNameItem, 0),
		}
		err error
	)

	switch req.Type {
	case 1:
		res.List, err = getAttendingConferenceNameItems()
	case 2:
		res.List, err = getSponsorChannelItems()
	case 3:
		res.List, err = getRegisterChannelItems()
	case 4:
		res.List, err = getCNFreeVisitorItems()
	case 5:
		res.List, err = getENFreeVisitorItems()
	case 6:
		res.List, err = getMediaRegistrationItems("en")
	case 7:
		res.List, err = getMediaRegistrationItems("cn")
	}

	if err != nil {
		return res, err
	}

	return res, nil
}

// getAttendingConferenceNameItems 获取参会渠道名称列表（包含会议ID和名称）
func getAttendingConferenceNameItems() ([]protocol.ConferenceNameItem, error) {
	_, dataLi, err := db.QueryConferenceClueList(nil, 0, 0, "", "", "", "", "", 0, 0, 0, false, nil, "", "")
	if err != nil {
		_ = logger.Error("GetConferenceList:", err)
		return nil, errors.WithStack(err)
	}

	// 使用map去重，key为会议ID_会议名称，value为ConferenceNameItem
	itemMap := make(map[string]protocol.ConferenceNameItem, 0)
	for _, data := range dataLi {
		if data.CnConferenceName != "" {
			key := fmt.Sprintf("%d_%s", data.ConferenceId, data.CnConferenceName)
			itemMap[key] = protocol.ConferenceNameItem{
				ConferenceID:   data.ConferenceId,
				ConferenceName: data.CnConferenceName,
			}
		}
	}

	// 转换为列表
	itemList := make([]protocol.ConferenceNameItem, 0, len(itemMap))
	for _, item := range itemMap {
		itemList = append(itemList, item)
	}

	return itemList, nil
}

// getSponsorChannelItems 获取赞助渠道名称列表（包含会议ID和名称）
func getSponsorChannelItems() ([]protocol.ConferenceNameItem, error) {
	_, dataLi, err := db.QueryConferenceClueList(nil, 0, 0, "", "", "", "", "", 1, 0, 0, false, nil, "", "")
	if err != nil {
		_ = logger.Error("GetConferenceList:", err)
		return nil, errors.WithStack(err)
	}

	// 使用map去重，key为会议ID_会议名称，value为ConferenceNameItem
	itemMap := make(map[string]protocol.ConferenceNameItem, 0)
	for _, data := range dataLi {
		if data.CnConferenceName != "" {
			key := fmt.Sprintf("%d_%s", data.ConferenceId, data.CnConferenceName)
			itemMap[key] = protocol.ConferenceNameItem{
				ConferenceID:   data.ConferenceId,
				ConferenceName: data.CnConferenceName,
			}
		}
	}

	// 转换为列表
	itemList := make([]protocol.ConferenceNameItem, 0, len(itemMap))
	for _, item := range itemMap {
		itemList = append(itemList, item)
	}

	return itemList, nil
}

// getRegisterChannelItems 获取注册渠道名称列表（包含会议ID和名称）
func getRegisterChannelItems() ([]protocol.ConferenceNameItem, error) {
	// 不再传入 conferenceId 参数
	req := protocol.ReqGetConferenceRegisterList{ReqPage: protocol.ReqPage{Page: 1, PageSize: 10000}}
	dataLi, _, err := db.QueryConferenceRegisterList(nil, req)
	if err != nil {
		return nil, err
	}

	// 使用map去重，key为会议ID_会议名称，value为ConferenceNameItem
	itemMap := make(map[string]protocol.ConferenceNameItem, 0)
	for _, data := range dataLi {
		if data.ConferenceName != "" {
			key := fmt.Sprintf("%d_%s", data.ConferenceId, data.ConferenceName)
			itemMap[key] = protocol.ConferenceNameItem{
				ConferenceID:   data.ConferenceId,
				ConferenceName: data.ConferenceName,
			}
		}
	}

	// 转换为列表
	itemList := make([]protocol.ConferenceNameItem, 0, len(itemMap))
	for _, item := range itemMap {
		itemList = append(itemList, item)
	}

	return itemList, nil
}

// getCNFreeVisitorItems 获取中文免费观众渠道名称列表（包含会议ID和名称）
func getCNFreeVisitorItems() ([]protocol.ConferenceNameItem, error) {
	// 不再传入 conferenceId 参数
	dataLi, _, err := db.GetCnFreeVisitorInfo(nil, 0, "", "", "", "", 0, "", "", "", 0, 0, "")
	if err != nil {
		logger.Error(fmt.Sprintf("获取中文免费观众信息失败：%v", err))
		return nil, err
	}

	// 使用map去重，key为会议ID_会议名称，value为ConferenceNameItem
	itemMap := make(map[string]protocol.ConferenceNameItem, 0)
	for _, data := range dataLi {
		if data.CnConferenceName != "" {
			key := fmt.Sprintf("%d_%s", data.ConferenceID, data.CnConferenceName)
			itemMap[key] = protocol.ConferenceNameItem{
				ConferenceID:   data.ConferenceID,
				ConferenceName: data.CnConferenceName,
			}
		}
	}

	// 转换为列表
	itemList := make([]protocol.ConferenceNameItem, 0, len(itemMap))
	for _, item := range itemMap {
		itemList = append(itemList, item)
	}

	return itemList, nil
}

// getENFreeVisitorItems 获取英文免费观众渠道名称列表（包含会议ID和名称）
func getENFreeVisitorItems() ([]protocol.ConferenceNameItem, error) {
	// 不再传入 conferenceId 参数
	dataLi, _, err := db.GetEnFreeVisitorInfo(nil, 0, "", "", "", "", 0, "", "", "", 0, 0, "")
	if err != nil {
		logger.Error(fmt.Sprintf("获取英文免费观众信息失败：%v", err))
		return nil, err
	}

	// 使用map去重，key为会议ID_会议名称，value为ConferenceNameItem
	itemMap := make(map[string]protocol.ConferenceNameItem, 0)
	for _, data := range dataLi {
		if data.EnConferenceName != "" {
			key := fmt.Sprintf("%d_%s", data.ConferenceId, data.EnConferenceName)
			itemMap[key] = protocol.ConferenceNameItem{
				ConferenceID:   data.ConferenceId,
				ConferenceName: data.EnConferenceName,
			}
		}
	}

	// 转换为列表
	itemList := make([]protocol.ConferenceNameItem, 0, len(itemMap))
	for _, item := range itemMap {
		itemList = append(itemList, item)
	}

	return itemList, nil
}

// getMediaRegistrationItems 获取媒体注册渠道名称列表（包含会议ID和名称）
func getMediaRegistrationItems(language string) ([]protocol.ConferenceNameItem, error) {
	// 不再传入 conferenceId 参数，只传入语言参数
	req := protocol.ReqGetMediaRegistrationList{
		Language: language,
		ReqPage: protocol.ReqPage{
			Page:     1,
			PageSize: 10000,
		},
	}
	dataLi, _, err := db.QueryMediaRegistrationList(nil, req)
	if err != nil {
		return nil, err
	}

	// 使用map去重，key为会议ID_会议名称，value为ConferenceNameItem
	itemMap := make(map[string]protocol.ConferenceNameItem, 0)
	for _, data := range dataLi {
		if data.ConferenceName != "" {
			key := fmt.Sprintf("%d_%s", data.ConferenceId, data.ConferenceName)
			itemMap[key] = protocol.ConferenceNameItem{
				ConferenceID:   data.ConferenceId,
				ConferenceName: data.ConferenceName,
			}
		}
	}

	// 转换为列表
	itemList := make([]protocol.ConferenceNameItem, 0, len(itemMap))
	for _, item := range itemMap {
		itemList = append(itemList, item)
	}

	return itemList, nil
}

func GetConferenceCoverButtons(confId int64) (int64, []protocol.ResButtonInfo, error) {
	var (
		buttonLi = make([]protocol.ResButtonInfo, 0)
	)
	total, dataLi, err := db.QueryConferenceCoverButtons(nil, confId, 0, 0)
	if err != nil {
		_ = logger.Error(fmt.Sprintf("QueryConferenceCoverButtons:%d,Err->", confId), err)
		return total, buttonLi, errors.WithStack(err)
	}
	for _, data := range dataLi {
		buttonLi = append(buttonLi, protocol.ResButtonInfo{
			ID:           data.ID,
			ConferenceID: data.ConferenceID,
			CnName:       data.CnName,
			EnName:       data.EnName,
			CnURL:        data.CnURL,
			EnURL:        data.EnURL,
			UpdateUser:   data.UpdateUser,
			CreateUser:   data.CreateUser,
			UpdateTime:   data.UpdateTime.Format(constant.DateTime),
			CreateTime:   data.CreateTime.Format(constant.DateTime),
		})
	}

	return total, buttonLi, err
}

func GetCoverButtonMap(confId int64) (map[int64][]protocol.ResButtonInfo, error) {
	var (
		buttonMap = make(map[int64][]protocol.ResButtonInfo)
	)
	_, dataLi, err := GetConferenceCoverButtons(confId)
	if err != nil {
		return nil, err
	}
	for _, button := range dataLi {
		buttonLi, ok := buttonMap[button.ConferenceID]
		if ok {
			buttonLi = append(buttonLi, button)
		} else {
			buttonLi = []protocol.ResButtonInfo{button}
		}
		buttonMap[button.ConferenceID] = buttonLi
	}

	return buttonMap, nil
}

// 设置展会状态
func UpdateConferenceStatus(confId int64, status int, opUser string) (string, error) {
	var (
		resMsg string
		err    error
	)
	// 分布式锁
	ok := acache.GetConferenceOpLock(corecontext.Cache(), confId)
	if !ok {
		return "当前有其他用户正在操作此展会，请稍后再试", nil
	}
	defer func() {
		acache.RemoveConferenceOpLock(corecontext.Cache(), confId)
	}()
	err = db.UpdateConferenceStatus(nil, confId, status, opUser)
	if err != nil {
		err = errors.WithStack(err)
	}
	return resMsg, err
}

// 设置展会日程模版类型
func UpdateConferenceTemplate(confId int64, status int, opUser string) (string, error) {
	var (
		resMsg string
		err    error
	)
	// 分布式锁
	ok := acache.GetConferenceOpLock(corecontext.Cache(), confId)
	if !ok {
		return "当前有其他用户正在操作此展会，请稍后再试", nil
	}
	defer func() {
		acache.RemoveConferenceOpLock(corecontext.Cache(), confId)
	}()
	err = db.UpdateConferenceTemplate(nil, confId, status, opUser)
	if err != nil {
		err = errors.WithStack(err)
	}
	return resMsg, err
}

// 添加或编辑展会
func AddEditConferenceInfo(req protocol.ReqAdminConferenceInfo) (resMsg string, err error) {
	var (
		confInfo model.Conference
		rawInfo  model.Conference
		buttonLi = make([]model.ConferenceCoverButton, 0)
		ok       bool
	)
	confInfo = model.Conference{
		ID:             req.ID,
		CnName:         req.CnName,
		EnName:         req.EnName,
		CnLocation:     req.CnLocation,
		EnLocation:     req.EnLocation,
		StartTime:      req.Start,
		EndTime:        req.End,
		Status:         constant.ConferenceStatusOnline,
		CnTopLeftLogo:  req.CnTopLeftLogo,
		EnTopLeftLogo:  req.EnTopLeftLogo,
		TopLogo:        req.TopLogo,
		TinyLogo:       req.TinyLogo,
		VideoBack:      req.VideoBack,
		PcBack:         req.PcBack,
		H5Back:         req.H5Back,
		CnShareGraph:   req.CnShareGraph,
		EnShareGraph:   req.EnShareGraph,
		CnShareTitle:   req.CnShareTitle,
		EnShareTitle:   req.EnShareTitle,
		CnShareContent: req.CnShareContent,
		EnShareContent: req.EnShareContent,
		MeetingSysId:   req.MeetingSysId,
		QwxUrl:         req.QwxUrl,
		UpdateUser:     req.OpUser,
		CreateUser:     req.OpUser,
		UpdateTime:     time.Now(),
		CreateTime:     time.Now(),
	}
	for _, button := range req.ButtonLi {
		buttonLi = append(buttonLi, model.ConferenceCoverButton{
			CnName:     button.CnName,
			EnName:     button.EnName,
			CnURL:      button.CnURL,
			EnURL:      button.EnURL,
			CreateUser: req.OpUser,
			UpdateUser: req.OpUser,
			UpdateTime: time.Now(),
			CreateTime: time.Now(),
		})
	}
	tx := corecontext.SrvContext.Orm.Begin()
	if tx == nil {
		return "", errors.New("创建事务失败")
	}
	defer func() {
		if !ok {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()
	if confInfo.ID > 0 { // 展会信息是否存在
		rawInfo, err = db.QueryConferenceInfo(tx, confInfo.ID)
		if err != nil {
			return "系统错误", errors.WithStack(err)
		} else if rawInfo.ID <= 0 {
			err = errors.New(fmt.Sprintf("展会信息不存在，confId：%d", confInfo.ID))
			return "展会信息不存在", err
		}
		confInfo.WorkWeChatChain = rawInfo.WorkWeChatChain
		confInfo.AutomaticMail = rawInfo.AutomaticMail
		confInfo.BdVidToken = rawInfo.BdVidToken
		confInfo.CreateUser = rawInfo.CreateUser
		confInfo.CreateTime = rawInfo.CreateTime
	}
	{
		err = db.SaveConferenceInfo(tx, &confInfo)
		if err != nil {
			return "系统错误", errors.WithStack(err)
		}
	}
	{
		err = db.DelCoverButton(tx, confInfo.ID, nil)
		if err != nil {
			return "系统错误", errors.WithStack(err)
		}
		for _, button := range buttonLi {
			button.ConferenceID = confInfo.ID
			err = db.CreateCoverButton(tx, &button)
			if err != nil {
				return "系统错误", errors.WithStack(err)
			}
		}
	}
	ok = true
	return
}

// 获取往届列表
func AdminGetPreviousList(req protocol.ReqAdminPreviousList) (int64, []protocol.AdminConferencePrevious, error) {
	var (
		infoLi = make([]protocol.AdminConferencePrevious, 0)
	)
	total, dataLi, err := db.QueryPreviousList(nil, req.ConferenceId, req.Page, req.PageSize)
	if err != nil {
		_ = logger.Error("QueryPreviousList:", err)
		return total, infoLi, errors.WithStack(err)
	}
	for _, data := range dataLi {
		infoLi = append(infoLi, protocol.AdminConferencePrevious{
			ID:           data.ID,
			ConferenceID: data.ConferenceID,
			CnName:       data.CnName,
			EnName:       data.EnName,
			CnURL:        data.CnURL,
			EnURL:        data.EnURL,
			CnPdf:        data.CnPdf,
			EnPdf:        data.EnPdf,
			Sort:         fmt.Sprintf("%.2f", data.Sort),
			UpdateUser:   data.UpdateUser,
			CreateUser:   data.CreateUser,
			UpdateTime:   data.UpdateTime.Format(constant.DateTime),
			CreateTime:   data.CreateTime.Format(constant.DateTime),
		})
	}

	return total, infoLi, err
}

// 删除往届展会
func DeletePreviousInfo(infoId int64, opUser string) (string, error) {
	var (
		resMsg string
		err    error
	)
	// 分布式锁
	ok := acache.GetPreviousOpLock(corecontext.Cache(), infoId)
	if !ok {
		return "当前有其他用户正在操作，请稍后再试", nil
	}
	defer func() {
		acache.RemovePreviousOpLock(corecontext.Cache(), infoId)
	}()
	err = db.UpdatePreviousStatus(nil, infoId, constant.PreviousStatusDeleted, opUser)
	if err != nil {
		err = errors.WithStack(err)
	}
	return resMsg, err
}

// 添加或编辑往届
func AddEditPreviousInfo(req protocol.ReqAdminPreviousInfo) (resMsg string, err error) {
	var (
		previousInfo model.ConferencePrevious
		ok           bool
	)
	previousInfo = model.ConferencePrevious{
		ID:           req.ID,
		ConferenceID: req.ConferenceID,
		CnName:       req.CnName,
		EnName:       req.EnName,
		CnURL:        req.CnURL,
		EnURL:        req.EnURL,
		CnPdf:        req.CnPdf,
		EnPdf:        req.EnPdf,
		Sort:         req.Sort,
		UpdateUser:   req.OpUser,
		CreateUser:   req.OpUser,
		UpdateTime:   time.Now(),
		CreateTime:   time.Now(),
	}
	tx := corecontext.SrvContext.Orm.Begin()
	if tx == nil {
		return "", errors.New("创建事务失败")
	}
	defer func() {
		if !ok {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()
	if previousInfo.ID > 0 { // 往届展会是否存在
		rawInfo, err := db.QueryPreviousInfo(tx, previousInfo.ID)
		if err != nil {
			return "系统错误", errors.WithStack(err)
		} else if rawInfo.ID <= 0 {
			err = errors.New(fmt.Sprintf("往届展会信息不存在，confId：%d", previousInfo.ID))
			return "往届展会信息不存在", err
		}
		previousInfo.CreateUser = rawInfo.CreateUser
		previousInfo.CreateTime = rawInfo.CreateTime
	}
	{
		err = db.SavePreviousInfo(tx, &previousInfo)
		if err != nil {
			return "系统错误", errors.WithStack(err)
		}
	}
	ok = true
	return
}

// 添加或编辑简称映射关系
func AddEditShortMap(req protocol.ReqAdminShortMap) (err error) {
	shortMap := model.ConferenceShortNameMap{
		ShortName:    req.ShortName,
		ConferenceId: req.ConferenceID,
		CreateUser:   req.OpUser,
		UpdateUser:   req.OpUser,
		CreateTime:   time.Now(),
		UpdateTime:   time.Now(),
	}
	return db.SaveShortNameMap(nil, &shortMap)
}

// 获取往届列表
func UserGetPreviousList(req protocol.ReqUserPreviousList) (int64, []protocol.UserPreviousInfo, error) {
	var (
		infoLi = make([]protocol.UserPreviousInfo, 0)
	)
	total, dataLi, err := db.QueryPreviousList(nil, req.ConferenceID, req.Page, req.PageSize)
	if err != nil {
		_ = logger.Error("QueryPreviousList:", err)
		return total, infoLi, errors.WithStack(err)
	}
	for _, data := range dataLi {
		infoLi = append(infoLi, protocol.UserPreviousInfo{
			ID:     data.ID,
			CnName: data.CnName,
			EnName: data.EnName,
			CnURL:  data.CnURL,
			URLEn:  data.EnURL,
			CnPdf:  data.CnPdf,
			EnPdf:  data.EnPdf,
			Sort:   data.Sort,
		})
	}

	return total, infoLi, err
}

// 获取展会基本信息
func GetConferenceInfo(confId int64) (*protocol.ResConferenceInfo, int, error) {
	buttonMap, err_ := GetCoverButtonMap(confId)
	if err_ != nil {
		_ = logger.Error(fmt.Sprintf("GetCoverButtonMap:%d,Err->", confId), err_)
		return nil, errcode.RESPONSE_CODE_SYSTEM_ERR, errors.WithStack(err_)
	}
	confInfo, err_ := db.QueryConferenceInfo(nil, confId)
	if err_ != nil {
		_ = logger.Error(fmt.Sprintf("QueryConferenceInfo:%d,Err->", confId), err_)
		return nil, errcode.RESPONSE_CODE_DB_ERR, errors.WithStack(err_)
	}
	guideline, err_ := db.GetExhibitionGuideline(nil, confId)
	if err_ != nil {
		return nil, errcode.RESPONSE_CODE_DB_ERR, errors.WithStack(err_)
	}

	if confInfo.ID <= 0 {
		return nil, errcode.RESPONSE_CODE_DATA_NOT_EXIST, nil
	}
	buttonLi, ok := buttonMap[confId]
	if !ok {
		buttonLi = make([]protocol.ResButtonInfo, 0)
	}

	reportList, err_ := db.GetAfterReportList(nil, confId)
	if err_ != nil {
		return nil, errcode.RESPONSE_CODE_DB_ERR, errors.WithStack(err_)
	}
	dataInfo := protocol.ResConferenceInfo{
		ID:             confInfo.ID,
		CnName:         confInfo.CnName,
		EnName:         confInfo.EnName,
		CnShortName:    confInfo.CnShortName,
		EnShortName:    confInfo.EnShortName,
		CnLocation:     confInfo.CnLocation,
		EnLocation:     confInfo.EnLocation,
		StartTime:      confInfo.StartTime.Format(constant.DateTime),
		EndTime:        confInfo.EndTime.Format(constant.DateTime),
		Status:         confInfo.Status,
		CnTopLeftLogo:  confInfo.CnTopLeftLogo,
		EnTopLeftLogo:  confInfo.EnTopLeftLogo,
		TopLogo:        confInfo.TopLogo,
		TinyLogo:       confInfo.TinyLogo,
		VideoBack:      confInfo.VideoBack,
		PcBack:         confInfo.PcBack,
		H5Back:         confInfo.H5Back,
		CnShareGraph:   confInfo.CnShareGraph,
		EnShareGraph:   confInfo.EnShareGraph,
		CnShareTitle:   confInfo.CnShareTitle,
		EnShareTitle:   confInfo.EnShareTitle,
		CnShareContent: confInfo.CnShareContent,
		EnShareContent: confInfo.EnShareContent,
		MeetingSysId:   confInfo.MeetingSysId,
		QwxUrl:         confInfo.QwxUrl,
		TemplateType:   confInfo.TemplateType,
		ButtonLi:       buttonLi,
		UpdateUser:     confInfo.UpdateUser,
		CreateUser:     confInfo.CreateUser,
		UpdateTime:     confInfo.UpdateTime.Format(constant.DateTime),
		CreateTime:     confInfo.CreateTime.Format(constant.DateTime),
	}
	if guideline.Id > 0 {
		dataInfo.CnFloorGraph = guideline.CnFloorGraph
		dataInfo.EnFloorGraph = guideline.EnFloorGraph
	}
	if len(reportList) > 0 {
		dataInfo.CnAfterReport = reportList[0].CnPdf
		dataInfo.EnAfterReport = reportList[0].EnPdf
	}
	return &dataInfo, 0, nil
}

// 获取展会基本信息
func GetConferenceByMeetingNo(meetingNo string) (*protocol.ResConferenceInfo, int, error) {

	confInfo, err_ := db.GetConferenceMeetingNo(nil, meetingNo)
	if err_ != nil {
		_ = logger.Error(fmt.Sprintf("QueryConferenceInfo:%d,Err->", meetingNo), err_)
		return nil, errcode.RESPONSE_CODE_DB_ERR, errors.WithStack(err_)
	}

	if confInfo.ID <= 0 {
		return nil, errcode.RESPONSE_CODE_DATA_NOT_EXIST, nil
	}

	dataInfo := protocol.ResConferenceInfo{
		ID:             confInfo.ID,
		CnName:         confInfo.CnName,
		EnName:         confInfo.EnName,
		CnShortName:    confInfo.CnShortName,
		EnShortName:    confInfo.EnShortName,
		CnLocation:     confInfo.CnLocation,
		EnLocation:     confInfo.EnLocation,
		StartTime:      confInfo.StartTime.Format(constant.DateTime),
		EndTime:        confInfo.EndTime.Format(constant.DateTime),
		Status:         confInfo.Status,
		CnTopLeftLogo:  confInfo.CnTopLeftLogo,
		EnTopLeftLogo:  confInfo.EnTopLeftLogo,
		TopLogo:        confInfo.TopLogo,
		TinyLogo:       confInfo.TinyLogo,
		VideoBack:      confInfo.VideoBack,
		PcBack:         confInfo.PcBack,
		H5Back:         confInfo.H5Back,
		CnShareGraph:   confInfo.CnShareGraph,
		EnShareGraph:   confInfo.EnShareGraph,
		CnShareTitle:   confInfo.CnShareTitle,
		EnShareTitle:   confInfo.EnShareTitle,
		CnShareContent: confInfo.CnShareContent,
		EnShareContent: confInfo.EnShareContent,
		MeetingSysId:   confInfo.MeetingSysId,
		QwxUrl:         confInfo.QwxUrl,
		TemplateType:   confInfo.TemplateType,
		ButtonLi:       []protocol.ResButtonInfo{},
		UpdateUser:     confInfo.UpdateUser,
		CreateUser:     confInfo.CreateUser,
		UpdateTime:     confInfo.UpdateTime.Format(constant.DateTime),
		CreateTime:     confInfo.CreateTime.Format(constant.DateTime),
	}
	dataInfo.CnFloorGraph = ""
	dataInfo.EnFloorGraph = ""
	dataInfo.CnAfterReport = ""
	dataInfo.EnAfterReport = ""

	return &dataInfo, 0, nil
}

// 查询简称映射关系
func GetConferenceMapping(shortName string) []protocol.ResMapInfo {
	var (
		infoLi = make([]protocol.ResMapInfo, 0)
	)
	if utils.IsEmpty(shortName) {
		infoLi = append(infoLi, protocol.ResMapInfo{
			ShortName:    "",
			ConferenceID: 0,
		})
		return infoLi
	}
	logger.Info(fmt.Sprintf("GetConferenceMapping:%s", shortName))
	nameMap := memory.GetConferenceShortNameMap(shortName, true)
	if nameMap != nil {
		infoLi = append(infoLi, protocol.ResMapInfo{
			ShortName:    nameMap.ShortName,
			ConferenceID: nameMap.ConferenceId,
		})
	} else {
		infoLi = append(infoLi, protocol.ResMapInfo{
			ShortName:    "",
			ConferenceID: 0,
		})

	}
	return infoLi
}

func GetConferenceExhibitionList(req protocol.ReqAdminClueList) (int64, []protocol.ConferenceExhibition, error) {
	var (
		infoLi = make([]protocol.ConferenceExhibition, 0)
	)

	total, dataLi, err := db.QueryConferenceClueList(nil, req.ConferenceID, req.SourceId, req.FromId, req.Keyword, req.StartTime, req.EndTime, req.EnterpriseType, req.Type, req.Page, req.PageSize, true, req.Ids, req.FromName, req.ConferenceName)
	if err != nil {
		_ = logger.Error("GetConferenceList:", err)
		return total, infoLi, errors.WithStack(err)
	}

	for _, data := range dataLi {

		infoLi = append(infoLi, protocol.ConferenceExhibition{
			Id:               data.Id,
			ConferenceId:     data.ConferenceId,
			CnConferenceName: data.CnConferenceName,
			EnConferenceName: data.EnConferenceName,
			CnShortName:      data.CnShortName,
			EnShortName:      data.EnShortName,
			UserId:           data.UserId,
			Name:             data.LastName + data.FirstName,
			CellPhone:        data.CellPhone,
			Email:            data.Email,
			Company:          data.Company,
			ExhibitionArea:   data.ExhibitionArea,
			SourceName:       data.SourceName,
			FromId:           data.FromId,
			FromName:         data.FromName,
			Country:          data.Country,
			Status:           data.Status,
			CreateTime:       data.CreateTime.Format(constant.DateTime),

			MainProducts:   data.MainProducts,
			EnterpriseType: data.EnterpriseType,
		})
	}

	return total, infoLi, err
}

func GetConferenceExhibitionExportList(req protocol.ReqAdminClueList) (int64, []protocol.ConferenceExhibitionExport, error) {
	var (
		infoLi = make([]protocol.ConferenceExhibitionExport, 0)
	)
	total, dataLi, err := db.QueryConferenceClueList(nil, req.ConferenceID, req.SourceId, req.FromId, req.Keyword, req.StartTime, req.EndTime, req.EnterpriseType, req.Type, req.Page, req.PageSize, true, req.Ids, req.FromName, req.ConferenceName)
	if err != nil {
		_ = logger.Error("GetConferenceList:", err)
		return total, infoLi, errors.WithStack(err)
	}
	statusNameMap := make(map[int]string)
	statusNameMap[0] = "未分配"
	statusNameMap[1] = "已分配"

	for _, data := range dataLi {

		infoLi = append(infoLi, protocol.ConferenceExhibitionExport{
			Id:               data.Id,
			ConferenceId:     data.ConferenceId,
			CnConferenceName: data.CnConferenceName,
			EnConferenceName: data.EnConferenceName,
			CnShortName:      data.CnShortName,
			EnShortName:      data.EnShortName,
			UserId:           data.UserId,
			Name:             data.LastName + data.FirstName,
			CellPhone:        data.CellPhone,
			Email:            data.Email,
			Company:          data.Company,
			ExhibitionArea:   data.ExhibitionArea,
			SourceName:       data.SourceName,
			FromId:           data.FromId,
			FromName:         data.FromName,
			MainProducts:     data.MainProducts,
			EnterpriseType:   data.EnterpriseType,
			Status:           statusNameMap[data.Status],
			CreateTime:       data.CreateTime.Format(constant.DateTime),
		})
	}

	return total, infoLi, err
}

func GetConferenceSponsorList(req protocol.ReqAdminClueList) (int64, []protocol.ConferenceExhibition, error) {
	var (
		infoLi = make([]protocol.ConferenceExhibition, 0)
	)
	total, dataLi, err := db.QueryConferenceClueList(nil, req.ConferenceID, req.SourceId, req.FromId, req.Keyword, req.StartTime, req.EndTime, req.EnterpriseType, req.Type, req.Page, req.PageSize, true, req.Ids, req.FromName, req.ConferenceName)
	if err != nil {
		_ = logger.Error("GetConferenceList:", err)
		return total, infoLi, errors.WithStack(err)
	}
	for _, data := range dataLi {
		infoLi = append(infoLi, protocol.ConferenceExhibition{
			Id:               data.Id,
			ConferenceId:     data.ConferenceId,
			CnConferenceName: data.CnConferenceName,
			EnConferenceName: data.EnConferenceName,
			CnShortName:      data.CnShortName,
			EnShortName:      data.EnShortName,
			UserId:           data.UserId,
			Name:             data.LastName + data.FirstName,
			CellPhone:        data.CellPhone,
			Email:            data.Email,
			Company:          data.Company,
			ExhibitionArea:   data.ExhibitionArea,
			SourceName:       data.SourceName,
			FromId:           data.FromId,
			FromName:         data.FromName,
			Status:           data.Status,
			CreateTime:       data.CreateTime.Format(constant.DateTime),
			Country:          data.Country,
			MainProducts:     data.MainProducts,
			EnterpriseType:   data.EnterpriseType,
		})
	}

	return total, infoLi, err
}

func GetConferenceClueList(req protocol.ReqAdminClueList) (int64, []protocol.ConferenceClue, error) {
	var (
		infoLi = make([]protocol.ConferenceClue, 0)
	)
	total, dataLi, err := db.QueryConferenceClueList(nil, req.ConferenceID, req.SourceId, req.FromId, req.Keyword, req.StartTime, req.EndTime, req.EnterpriseType, req.Type, req.Page, req.PageSize, true, req.Ids, req.FromName, req.ConferenceName)
	if err != nil {
		_ = logger.Error("GetConferenceList:", err)
		return total, infoLi, errors.WithStack(err)
	}

	for _, data := range dataLi {

		nameMap := memory.GetMeetingFromNameMap(strconv.FormatInt(data.ConferenceId, 10) + "_" + data.FromId)
		infoLi = append(infoLi, protocol.ConferenceClue{
			Id:               data.Id,
			ConferenceId:     data.ConferenceId,
			CnConferenceName: data.CnConferenceName,
			EnConferenceName: data.EnConferenceName,
			CnShortName:      data.CnShortName,
			EnShortName:      data.EnShortName,
			UserId:           data.UserId,
			Name:             data.LastName + data.FirstName,
			CellPhone:        data.CellPhone,
			Email:            data.Email,
			Company:          data.Company,
			JobTitle:         data.JobTitle,
			Country:          data.Country,
			ExhibitionArea:   data.ExhibitionArea,
			BoothType:        data.BoothType,
			SourceName:       data.SourceName,
			FromId:           nameMap.ChannelName,
			Status:           data.Status,
			CreateTime:       data.CreateTime.Format(constant.DateTime),
		})
	}

	return total, infoLi, err
}

// 导出展会线索列表
func ExportConferenceClueList(req protocol.ReqAdminClueList) (clueIds []int64, bReader *bytes.Reader, err error) {
	req.Page, req.PageSize = 0, 0 //重置分页数据
	_, dataLi, err_ := GetConferenceClueList(req)
	if err_ != nil {
		_ = logger.Error("GetConferenceClueList:", err)
		return nil, nil, err_
	}
	var listItems []interface{}
	for _, v := range dataLi {
		clueIds = append(clueIds, v.Id)
		listItems = append(listItems, v)
	}
	bReader, err = utils.GenListExcel(listItems, func(fieldName string, value interface{}) interface{} {
		if fieldName == "AllocateStatus" {
			stValue := value.(int)
			if stValue == constant.ClueStatusLocated {
				return "已分配"
			} else {
				return "未分配"
			}
		}
		return value
	}, protocol.ConferenceClue{})
	if err != nil {
		return nil, nil, err
	}
	return clueIds, bReader, nil
}

// 导出展会线索列表
func ExportConferenceExhibitionList(req protocol.ReqAdminClueList) (clueIds []int64, bReader *bytes.Reader, err error) {
	req.Page, req.PageSize = 0, 0 //重置分页数据
	_, dataLi, err_ := GetConferenceExhibitionExportList(req)
	if err_ != nil {
		_ = logger.Error("GetConferenceClueList:", err)
		return nil, nil, err_
	}
	var listItems []interface{}
	for _, v := range dataLi {
		clueIds = append(clueIds, v.Id)
		listItems = append(listItems, v)
	}
	bReader, err = utils.GenListExcel(listItems, func(fieldName string, value interface{}) interface{} {
		if fieldName == "AllocateStatus" {
			stValue := value.(int)
			if stValue == constant.ClueStatusLocated {
				return "已分配"
			} else {
				return "未分配"
			}
		}
		return value
	}, protocol.ConferenceClue{})
	if err != nil {
		return nil, nil, err
	}
	return clueIds, bReader, nil
}

// 导出展会线索列表
func ExportConferenceSponsorList(req protocol.ReqAdminClueList) (clueIds []int64, bReader *bytes.Reader, err error) {
	req.Page, req.PageSize = 0, 0 //重置分页数据
	_, dataLi, err_ := GetConferenceExhibitionExportList(req)
	if err_ != nil {
		_ = logger.Error("GetConferenceClueList:", err)
		return nil, nil, err_
	}
	var listItems []interface{}
	for _, v := range dataLi {
		clueIds = append(clueIds, v.Id)
		listItems = append(listItems, v)
	}
	bReader, err = utils.GenListExcel(listItems, func(fieldName string, value interface{}) interface{} {
		if fieldName == "AllocateStatus" {
			stValue := value.(int)
			if stValue == constant.ClueStatusLocated {
				return "已分配"
			} else {
				return "未分配"
			}
		}
		return value
	}, protocol.ConferenceClue{})
	if err != nil {
		return nil, nil, err
	}
	return clueIds, bReader, nil
}

// 保存用户申请记录(申请展商或申请赞助)
func SaveUserApply(req protocol.ReqUserApplySubmit) (err error) {
	var reqAPiSign protocol.ReqAddAPiSignAddEn

	nameMap := memory.GetChannelSourceNameMap(req.SourceID, true)

	if utils.NotEmpty(nameMap) {
		req.SourceName = nameMap
	}
	info, err := db.QueryConferenceInfo(nil, req.ConferenceID)
	if err != nil {
		_ = logger.Error("SaveUserApply:CreateConferenceClue,Err->", err)
		return errors.WithStack(err)
	}

	data := model.ConferenceClue{
		ConferenceId:     req.ConferenceID,
		CnConferenceName: info.CnName,
		UserId:           req.UserId,
		LastName:         req.LastName,
		FirstName:        req.FirstName,
		Email:            req.Email,
		CellPhone:        req.CellPhone,
		Company:          req.Company,
		JobTitle:         req.JobTitle,
		Country:          req.Country,
		Comment:          req.Comment,
		BoothType:        req.BoothType,
		InterestLevel:    req.InterestLevel,
		SourceID:         req.SourceID,
		SourceName:       req.SourceName,
		CreateUser:       "system",
		CreateTime:       time.Now(),
	}
	if utils.NotEmpty(req.InterestLevel) {
		data.Type = 1
	} else {
		data.Type = 0
	}
	err = db.CreateConferenceClue(nil, &data)
	if err != nil {
		_ = logger.Error("SaveUserApply:CreateConferenceClue,Err->", err)
		return errors.WithStack(err)
	}

	if utils.NotEmpty(info.MeetingSysId) {
		reqAPiSign.MeetingNo = info.MeetingSysId
		reqAPiSign.UserId = data.UserId
		reqAPiSign.FirstName = data.FirstName
		reqAPiSign.LastName = data.LastName
		reqAPiSign.Email = data.Email
		reqAPiSign.Mobile = data.CellPhone
		reqAPiSign.Company = data.Company
		reqAPiSign.Country = req.Country
		reqAPiSign.JobTitle = data.JobTitle
		reqAPiSign.FromId = data.FromId
		reqAPiSign.PlatformId = data.SourceID
		reqAPiSign.Identity = "2"
		if utils.NotEmpty(req.InterestLevel) {
			reqAPiSign.MeetingRemarks = req.InterestLevel
		} else {
			reqAPiSign.MeetingRemarks = req.BoothType
		}
		m, err := rpc.AddAPiSignUpAddEn(reqAPiSign)
		if err != nil {
			_ = logger.Error("SaveUserApply:AddAPiSignUpAddEn,Err->", err)
			return errors.WithStack(err)
		}
		if m.Msg != "success" {
			err = errors.New(m.Msg)
			return errors.New(m.Msg)
		}

		RespLog, err := json.Marshal(m)
		ReqLog, err := json.Marshal(reqAPiSign)

		RegisterLog := model.RegisterLog{
			RegisterId: data.Id,
			ReqLog:     string(ReqLog),
			RespLog:    string(RespLog),
			LogType:    2,
		}
		RegisterLog.CreateTime = time.Now()
		err = db.AddMeetingRegisterLog(nil, &RegisterLog)
		if err != nil {
			_ = logger.Error("SaveUserApply:AddAPiSignUpAddEn,Err->", err)
			return errors.WithStack(err)
		}
	} else {
		logger.Error(fmt.Sprintf("%s 会议申请错误，会议系统编号为空", info.EnName))
	}

	return
}

// 保存用户申请记录(申请展商展览展位)
func SaveUserPurchaseBooth(req protocol.ReqUserApplyPurchaseBoothSubmit) error {
	var (
		reqAPiSign protocol.ReqAddAPiSignAddEn
		WxUrl2     string
		registerId int64
		ReqLog     []byte
		RespLog    []byte
		m          *protocol.Message
	)

	defer func() {
		RegisterLog := model.RegisterLog{
			RegisterId: registerId,
			ReqLog:     string(ReqLog),
			RespLog:    string(RespLog),
			LogType:    2,
		}
		err := db.AddMeetingRegisterLog(nil, &RegisterLog)
		if err != nil {
			_ = logger.Error("SaveUserApply:AddAPiSignUpAddEn,Err->", err)
			return
		}
	}()
	if utils.IsEmpty(req.FromId) && utils.IsEmpty(req.SourceID) {
		req.SourceID = "32"
	}

	if utils.NotEmpty(req.SourceID) {
		nameMap := memory.GetChannelSourceNameMap(req.SourceID, true)

		if utils.NotEmpty(nameMap) {
			req.SourceName = nameMap
		}
	}

	info, err := db.QueryConferenceInfo(nil, req.ConferenceId)
	if err != nil {
		_ = logger.Error("SaveUserApply:CreateConferenceClue,Err->", err)
		return errors.WithStack(err)
	}

	if utils.NotEmpty(req.SourceID) {
		nameMap := memory.GetChannelSourceNameMap(req.SourceID, true)
		if utils.NotEmpty(nameMap) {
			req.SourceName = nameMap
		}
	}

	fromName := ""
	if utils.NotEmpty(req.FromId) {
		fromMap := memory.GetMeetingFromNameMap(strconv.FormatInt(req.ConferenceId, 10) + "_" + req.FromId)
		if utils.IsEmpty(fromMap.ChannelName) {
			req.FromId = ""
		} else {
			fromName = fromMap.ChannelName
		}
	}

	reqAPiSign.MeetingNo = info.MeetingSysId
	if utils.NotEmpty(info.QwxUrl) {
		WxUrl2 = info.QwxUrl
	} else if utils.IsTestEnv() {
		WxUrl2 = weChat.WxUrl
	} else {
		WxUrl2 = weChat.WxUrl2
	}

	if utils.NotEmpty(info.MeetingSysId) {
		reqAPiSign.PlatformId = req.SourceID

		if utils.IsEmpty(req.SourceName) {
			reqAPiSign.PlatformId = ""
		}

		reqAPiSign.MeetingNo = info.MeetingSysId
		reqAPiSign.UserId = req.UserId
		reqAPiSign.FirstName = req.FirstName
		reqAPiSign.LastName = req.LastName
		reqAPiSign.Email = req.Email
		reqAPiSign.Mobile = req.CellPhone
		reqAPiSign.Company = req.Company
		reqAPiSign.JobTitle = req.JobTitle
		reqAPiSign.Country = req.Country
		reqAPiSign.FromId = req.FromId
		reqAPiSign.Identity = "2"
		reqAPiSign.MeetingRemarks = req.ExhibitionArea
		reqAPiSign.CnOrEn = req.CnOrEn
		reqAPiSign.OrderStatus = "0"

		reqAPiSign.MainProduct = req.MainProducts
		reqAPiSign.CompanyType = req.EnterpriseType
		m, err = rpc.AddAPiSignUpAddEn(reqAPiSign)
		if err != nil {
			logger.Error(fmt.Sprintf("同步免费观众报名出错：Err->%v;Req->%+v", err, reqAPiSign))
			return err
		}
		if m.Msg != "success" {
			err = errors.New(m.Msg)
			return errors.New(m.Msg)
		}

		RespLog, err = json.Marshal(m)
		ReqLog, err = json.Marshal(reqAPiSign)

	} else {
		logger.Error(fmt.Sprintf("%s 会议申请错误，会议系统编号为空", info.EnName))
	}

	data := model.ConferenceClue{
		ConferenceId:     req.ConferenceId,
		CnConferenceName: info.CnName,
		UserId:           req.UserId,
		FirstName:        req.FirstName,
		LastName:         req.LastName,
		Email:            req.Email,
		CellPhone:        req.CellPhone,
		Company:          req.Company,
		JobTitle:         req.JobTitle,
		Country:          req.Country,
		ExhibitionArea:   req.ExhibitionArea,
		SourceID:         req.SourceID,
		SourceName:       req.SourceName,
		FromId:           req.FromId,
		FromName:         fromName,
		Type:             0,
		CreateUser:       "system",
		CreateTime:       time.Now(),
		MainProducts:     req.MainProducts,
		EnterpriseType:   req.EnterpriseType,
	}
	err = db.CreateConferenceClue(nil, &data)
	if err != nil {
		_ = logger.Error("SaveUserApply:CreateConferenceClue,Err->", err)
		return errors.WithStack(err)
	}
	registerId = data.Id

	markdown2 := fmt.Sprintf(`
		## 申请展商展览展位通知
		>会议名：%v
		>姓名：%s 
		>手机号：%v
		>邮箱：%v
		>公司：%v
		>渠道：%v
		>时间：%s`, info.CnName, req.FirstName, req.CellPhone, req.Email, req.Company, fromName, utils.GetCurDateTime())

	cardMsg := feishu.NewMdCardMsg("申请展商展览展位通知", "blue")
	cardMsg.AppendMd(`>会议名：%v
>姓名：%s 
>手机号：%v
>邮箱：%v
>公司：%v
>渠道：%v
>时间：%s`, info.CnName, req.FirstName, req.CellPhone, req.Email, req.Company, fromName, utils.GetCurDateTime())

	if strings.Contains(WxUrl2, "open.feishu.cn") {
		weChat.Send2(cardMsg, WxUrl2)
	} else {
		weChat.Send(markdown2, WxUrl2)
	}

	if utils.NotEmpty(req.BdVid) {
		rpc.UploadBdVid(req.BdVid, info.BdVidToken, 4)
	}

	return nil
}

// 保存用户申请记录(申请展商或申请赞助)
func SaveUserApplySponsor(req protocol.ReqUserApplySponsorSubmit) error {
	var (
		reqAPiSign protocol.ReqAddAPiSignAddEn
		WxUrl2     string
		registerId int64
		ReqLog     []byte
		RespLog    []byte
		m          *protocol.Message
	)
	defer func() {
		RegisterLog := model.RegisterLog{
			RegisterId: registerId,
			ReqLog:     string(ReqLog),
			RespLog:    string(RespLog),
			LogType:    2,
		}
		err := db.AddMeetingRegisterLog(nil, &RegisterLog)
		if err != nil {
			_ = logger.Error("SaveUserApply:AddAPiSignUpAddEn,Err->", err)
			return
		}
	}()

	if utils.NotEmpty(req.CellPhone) {
		m, err := rpc.CheckCode(protocol.ReqCheckCode{
			Cellphone: req.CellPhone,
			Code:      req.PhoneCode,
			CodeType:  "conference_from_phone",
		})
		if err != nil {
			return errors.WithStack(err)
		}
		if m.Code != errcode.RESPONSE_CODE_SUCCESS {
			return errors.WithStack(errors.New(m.Msg))
		}
	}

	if utils.IsEmpty(req.FromId) && utils.IsEmpty(req.SourceID) {
		req.SourceID = "32"
	}

	if utils.NotEmpty(req.SourceID) {
		nameMap := memory.GetChannelSourceNameMap(req.SourceID, true)

		if utils.NotEmpty(nameMap) {
			req.SourceName = nameMap
		}
	}

	info, err := db.QueryConferenceInfo(nil, req.ConferenceId)
	if err != nil {
		_ = logger.Error("SaveUserApply:CreateConferenceClue,Err->", err)
		return errors.WithStack(err)
	}

	if utils.NotEmpty(req.SourceID) {
		nameMap := memory.GetChannelSourceNameMap(req.SourceID, true)
		if utils.NotEmpty(nameMap) {
			req.SourceName = nameMap
		}
	}

	fromName := ""
	if utils.NotEmpty(req.FromId) {
		fromMap := memory.GetMeetingFromNameMap(strconv.FormatInt(req.ConferenceId, 10) + "_" + req.FromId)
		if utils.IsEmpty(fromMap.ChannelName) {
			req.FromId = ""
		} else {
			fromName = fromMap.ChannelName
		}
	}

	reqAPiSign.MeetingNo = info.MeetingSysId
	if utils.NotEmpty(info.QwxUrl) {
		WxUrl2 = info.QwxUrl
	} else if utils.IsTestEnv() {
		WxUrl2 = weChat.WxUrl
	} else {
		WxUrl2 = weChat.WxUrl2
	}

	if utils.NotEmpty(info.MeetingSysId) {

		reqAPiSign.PlatformId = req.SourceID

		if utils.IsEmpty(req.SourceName) {
			reqAPiSign.PlatformId = ""
		}

		reqAPiSign.MeetingNo = info.MeetingSysId
		reqAPiSign.UserId = req.UserId
		reqAPiSign.FirstName = req.FirstName
		reqAPiSign.LastName = req.LastName
		reqAPiSign.Email = req.Email
		reqAPiSign.Mobile = req.CellPhone
		reqAPiSign.Company = req.Company
		reqAPiSign.Country = req.Country
		reqAPiSign.JobTitle = req.JobTitle
		reqAPiSign.FromId = req.FromId
		reqAPiSign.CnOrEn = req.CnOrEn
		reqAPiSign.Identity = "2"
		reqAPiSign.MeetingRemarks = req.ExhibitionArea
		reqAPiSign.OrderStatus = "0"

		reqAPiSign.MainProduct = req.MainProducts
		reqAPiSign.CompanyType = req.EnterpriseType
		m, err = rpc.AddAPiSignUpAddEn(reqAPiSign)
		if err != nil {
			_ = logger.Error("SaveUserApply:AddAPiSignUpAddEn,Err->", err)
			return errors.WithStack(err)
		}
		if m.Msg != "success" {
			err = errors.New(m.Msg)
			return errors.New(m.Msg)
		}

		RespLog, err = json.Marshal(m)
		ReqLog, err = json.Marshal(reqAPiSign)

	} else {
		logger.Error(fmt.Sprintf("%s 会议申请错误，会议系统编号为空", info.EnName))
	}

	data := model.ConferenceClue{
		ConferenceId:     req.ConferenceId,
		CnConferenceName: info.CnName,
		UserId:           req.UserId,
		FirstName:        req.FirstName,
		LastName:         req.LastName,
		Email:            req.Email,
		CellPhone:        req.CellPhone,
		Company:          req.Company,
		JobTitle:         req.JobTitle,
		ExhibitionArea:   req.ExhibitionArea,
		SourceID:         req.SourceID,
		SourceName:       req.SourceName,
		FromId:           req.FromId,
		FromName:         fromName,
		Type:             1,
		CreateUser:       "system",
		CreateTime:       time.Now(),

		MainProducts:   req.MainProducts,
		EnterpriseType: req.EnterpriseType,
	}
	err = db.CreateConferenceClue(nil, &data)
	if err != nil {
		_ = logger.Error("SaveUserApply:CreateConferenceClue,Err->", err)
		return errors.WithStack(err)
	}
	registerId = data.Id

	markdown2 := fmt.Sprintf(`
				## 申请赞助及广告通知
				>会议名：%v
				>姓名：%s 
				>手机号：%v
				>邮箱：%v
				>公司：%v
				>渠道：%v
				>时间：%s`, info.CnName, req.FirstName, req.CellPhone, req.Email, req.Company, fromName, utils.GetCurDateTime())

	cardMsg := feishu.NewMdCardMsg("申请赞助及广告通知", "blue")
	cardMsg.AppendMd(`>会议名：%v
>姓名：%s 
>手机号：%v
>邮箱：%v
>公司：%v
>渠道：%v
>时间：%s`, info.CnName, req.FirstName, req.CellPhone, req.Email, req.Company, fromName, utils.GetCurDateTime())

	if strings.Contains(WxUrl2, "open.feishu.cn") {
		weChat.Send2(cardMsg, WxUrl2)
	} else {
		weChat.Send(markdown2, WxUrl2)
	}

	if utils.NotEmpty(req.BdVid) {
		rpc.UploadBdVid(req.BdVid, info.BdVidToken, 2)
	}

	return nil
}

// 批量更新线索状态
func UpdateConferenceClueStatus(clueIds []int64, status int, opUser string) (err error) {
	err = db.UpdateClueStatus(nil, clueIds, status, opUser)
	if err != nil {
		_ = logger.Error(fmt.Sprintf("批量更新线索状态失败,clueIds->%v,status->%d,opUser->%s,Err->", clueIds, status, opUser), err)
		err = errors.WithMessage(err, "批量更新线索状态失败")
	}
	return err
}

// 搜索数据
func SearchDataList(req protocol.ReqSearchData) (int, []protocol.ResSearchData, error) {
	var (
		dataLi    = make([]protocol.ResSearchData, 0)
		results   = make([]protocol.ResSearchData, 0)
		eventLi   = make([]model.EventData, 0)
		companyLi = make([]model.AudienceConferenceCompanyData, 0)
		guestLi   = make([]model.ConferenceGuestData, 0)
		//categoryLi      = make([]model.ConferenceEventCategory, 0)
		productionLi    = make([]model.ProductionData, 0)
		categoryMap     = make(map[int64]string)
		categoryTypeMap = make(map[int64]int)
		err             error
	)
	wg, _ := errgroup.WithContext(context.Background())
	// 活动分类

	// 搜索活动
	wg.Go(func() error {
		_, eventLi, err = db.QueryConferenceEvents(nil, req.ConferenceID, req.Keyword, req.Language, 0, 0)
		return err
	})
	// 搜索展商
	wg.Go(func() error {
		_, companyLi, err = db.QueryConferenceCompanyInfos(nil, req.ConferenceID, req.Keyword, req.Language, 0, 0)
		return err
	})
	// 搜索嘉宾
	wg.Go(func() error {
		_, guestLi, err = db.QueryConferenceGuests(nil, req.ConferenceID, req.Keyword, req.Language, 0, 0)
		return err
	})
	// 搜索展品
	wg.Go(func() error {
		_, productionLi, err = db.QueryProduction(nil, req.ConferenceID, req.Keyword, req.Language, 0, 0)
		return err
	})

	if err = wg.Wait(); err != nil {
		return 0, dataLi, err
	}

	//categoryLi, _, err = db.QueryConferenceEventCategoryList(nil, req.ConferenceID, 1, 100)
	//for _, category := range categoryLi {
	//	categoryTypeMap[category.Id] = category.Type
	//	if req.Language == "cn" {
	//		categoryMap[category.Id] = category.CnContent
	//	} else {
	//		categoryMap[category.Id] = category.EnContent
	//	}
	//}

	for _, item := range eventLi {
		dataLi = append(dataLi, protocol.ResSearchData{
			DataId:       item.ID,
			DataType:     constant.SearchDataTypeActivity,
			CategoryType: categoryTypeMap[item.CategoryType],
			Title:        item.Name,
			Content:      categoryMap[item.CategoryType],
		})
	}
	for _, item := range companyLi {
		dataLi = append(dataLi, protocol.ResSearchData{
			DataId:   item.Id,
			DataType: constant.SearchDataTypeCompany,
			Title:    item.ConferenceCompany,
			Content:  "",
		})
	}
	for _, item := range guestLi {
		dataLi = append(dataLi, protocol.ResSearchData{
			DataId:   item.Id,
			DataType: constant.SearchDataTypeGuest,
			Title:    item.Appellation,
			Content:  item.Content,
		})
	}
	for _, item := range productionLi {
		dataLi = append(dataLi, protocol.ResSearchData{
			DataId:   item.AudienceCompanyId,
			DataType: constant.SearchDataTypeCompany,
			Title:    item.Name,
			Content:  "",
		})
	}
	total := len(dataLi)
	// 分页
	if req.Page > 0 && req.PageSize > 0 {
		start := (req.Page - 1) * req.PageSize
		end := req.Page * req.PageSize
		if end <= total {
			results = dataLi[start:end]
		} else if start < total && end > total {
			results = dataLi[start:]
		}
	} else {
		results = dataLi
	}
	return total, results, nil
}

func GetMeetingFromName(meetingId int64, FromId string) string {

	meeting, err := db.QueryConferenceInfo(nil, meetingId)
	if err != nil {
		_ = logger.Error("GetEnMeetingsById:", err)
		return ""

	}
	if utils.IsEmpty(meeting.MeetingSysId) {
		return ""
	}

	dataList, err := rpc.GetMeetingChannelList(meeting.MeetingSysId)
	if err != nil {
		_ = logger.Error("GetMeetingChannelList:", err)

	}
	for _, v := range dataList {
		if FromId == v.ChannelId {
			return v.ChannelName
		}
	}
	return ""
}
