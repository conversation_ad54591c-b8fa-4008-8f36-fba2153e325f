package service

import (
	"bytes"
	"conferencecenter/internal/constant"
	"conferencecenter/internal/db"
	"conferencecenter/internal/model"
	"conferencecenter/internal/pkg/utils"
	"conferencecenter/internal/protocol"
	"fmt"

	"git.code.tencent.com/smmit/smmbase/logger"
	"github.com/pkg/errors"
	"gorm.io/gorm"
)

func GetApplyConference(conferenceId int64) (protocol.ApplyConference, error) {
	company, err := db.GetConferenceCompany(nil, conferenceId)
	if err != nil && gorm.ErrRecordNotFound == err {
		return protocol.ApplyConference{}, nil
	}
	applyConference := protocol.ApplyConference{
		ConferenceId:      company.ConferenceId,
		CnApplyConference: company.CnApplyConference,
		EnApplyConference: company.EnApplyConference,
	}
	return applyConference, nil
}

func SaveApplyConference(req protocol.ApplyConference) error {
	if utils.IsEmpty(req.CnApplyConference) {
		return errors.New("中文展会价值不能为空")
	}
	if req.CnApplyConference == "" {
		req.CnApplyConference = " "
	}
	if req.EnApplyConference == "" {
		req.EnApplyConference = " "
	}
	company := model.ConferenceCompany{
		CommonModel: model.CommonModel{
			CreateUser: req.OpUser,
		},
		ConferenceId:      req.ConferenceId,
		CnApplyConference: req.CnApplyConference,
		EnApplyConference: req.EnApplyConference,
	}
	return saveOrUpdateConferenceCompany(req.ConferenceId, company)
}

func GetExhibitionSuccess(conferenceId int64) (protocol.ExhibitionSuccess, error) {
	company, err := db.GetConferenceCompany(nil, conferenceId)
	if err != nil && gorm.ErrRecordNotFound == err {
		return protocol.ExhibitionSuccess{}, nil
	}
	applyConference := protocol.ExhibitionSuccess{
		ConferenceId:     company.ConferenceId,
		ExhibitionQrCode: company.ExhibitionQrCode,
		ExhibitionTips:   company.ExhibitionTips,
	}
	return applyConference, nil
}

func SaveExhibitionSuccess(req protocol.ExhibitionSuccess) error {

	if req.ExhibitionQrCode == "" {
		req.ExhibitionQrCode = " "
	}
	if req.ExhibitionTips == "" {
		req.ExhibitionTips = " "
	}
	company := model.ConferenceCompany{
		CommonModel: model.CommonModel{
			CreateUser: req.OpUser,
		},
		ConferenceId:     req.ConferenceId,
		ExhibitionQrCode: req.ExhibitionQrCode,
		ExhibitionTips:   req.ExhibitionTips,
	}
	return saveOrUpdateConferenceCompany(req.ConferenceId, company)
}

func SaveOnlineRegister(req protocol.OnlineRegister) error {
	if req.CnUrl == "" {
		req.CnUrl = " "
	}
	if req.EnUrl == "" {
		req.EnUrl = " "
	}
	if req.CnContent == "" {
		req.CnContent = " "
	}
	if req.EnContent == "" {
		req.EnContent = " "
	}
	company := model.ConferenceCompany{
		CommonModel: model.CommonModel{
			CreateUser: req.OpUser,
		},
		ConferenceId: req.ConferenceId,
		CnUrl:        req.CnUrl,
		EnUrl:        req.EnUrl,
		CnContent:    req.CnContent,
		EnContent:    req.EnContent,
	}
	conferenceId := req.ConferenceId
	return saveOrUpdateConferenceCompany(conferenceId, company)
}

func saveOrUpdateConferenceCompany(conferenceId int64, company model.ConferenceCompany) error {
	list, err := db.FindConferenceCompany(nil, conferenceId)
	if err != nil {
		return err
	}
	//不存在则插入
	if len(list) == 0 {
		if company.ConferenceId <= 0 {
			return nil
		}
		err = db.SaveConferenceCompany(nil, company)
		if err != nil {
			return err
		}
		return nil
	}
	//存在则更新
	company.Id = list[0].Id
	err = db.UpdateConferenceCompany(nil, company)
	return err
}

func GetOnlineRegister(conferenceId int64) (protocol.OnlineRegister, error) {
	company, err := db.GetConferenceCompany(nil, conferenceId)
	if err != nil {
		return protocol.OnlineRegister{}, err
	}
	onlineRegister := protocol.OnlineRegister{
		ConferenceId: company.ConferenceId,
		CnUrl:        company.CnUrl,
		EnUrl:        company.EnUrl,
		CnContent:    company.CnContent,
		EnContent:    company.EnContent,
	}
	return onlineRegister, nil
}

func AddCompanyHandbook(handbook protocol.CompanyHandbook) error {
	book := model.CompanyHandBook{
		CommonModel: model.CommonModel{
			CreateUser: handbook.OpUser,
		},
		ConferenceId: handbook.ConferenceId,
		CnName:       handbook.CnName,
		EnName:       handbook.EnName,
		CnPdf:        handbook.CnPdf,
		EnPdf:        handbook.EnPdf,
		Sort:         handbook.Sort,
	}
	err := db.SaveCompanyHandbook(nil, &book)
	if err != nil {
		return err
	}
	err = SendHandBookChgKfkNotify(book.Id)
	return err
}

func GetCompanyHandbookList(conferenceId int64) ([]protocol.ResCompanyHandbook, error) {
	list, err := db.GetCompanyHandbookList(nil, conferenceId)
	if err != nil {
		return nil, err
	}
	res := make([]protocol.ResCompanyHandbook, 0)
	for _, book := range list {
		handbook := protocol.ResCompanyHandbook{
			Id:           book.Id,
			ConferenceId: book.ConferenceId,
			CnName:       book.CnName,
			EnName:       book.EnName,
			CnPdf:        book.CnPdf,
			EnPdf:        book.EnPdf,
			Sort:         fmt.Sprintf("%.2f", book.Sort),
		}
		res = append(res, handbook)
	}
	return res, nil

}

func GetCompanyHandbook(handbookId int64) (protocol.CompanyHandbook, error) {
	handbook, err := db.GetCompanyHandbook(nil, handbookId)
	if err != nil {
		return protocol.CompanyHandbook{}, err
	}
	companyHandbook := protocol.CompanyHandbook{
		Id:           handbook.Id,
		ConferenceId: handbook.ConferenceId,
		CnName:       handbook.CnName,
		EnName:       handbook.EnName,
		CnPdf:        handbook.CnPdf,
		EnPdf:        handbook.EnPdf,
		Sort:         handbook.Sort,
	}
	return companyHandbook, nil
}

func DeleteCompanyHandbook(handbookId int64, user string) error {
	tx := db.OrmDB(nil).Set("user", user).Set("id", handbookId)
	err := db.DeleteCompanyHandbook(tx, handbookId)
	return err
}

func UpdateCompanyHandbook(handbook protocol.CompanyHandbook) error {
	book := model.CompanyHandBook{
		CommonModel: model.CommonModel{
			Id:         handbook.Id,
			UpdateUser: handbook.OpUser,
		},
		ConferenceId: handbook.ConferenceId,
		CnName:       handbook.CnName,
		EnName:       handbook.EnName,
		CnPdf:        handbook.CnPdf,
		EnPdf:        handbook.EnPdf,
		Sort:         handbook.Sort,
	}
	err := db.UpdateCompanyHandbook(nil, book)
	if err != nil {
		return err
	}
	err = SendHandBookChgKfkNotify(book.Id)
	return err
}

func SaveHandbookContent(content protocol.HandbookContent) error {
	if content.CnHandbookContent == "" {
		content.CnHandbookContent = " "
	}
	if content.EnHandbookContent == "" {
		content.EnHandbookContent = " "
	}
	if content.CnHandbookTitle == "" {
		content.CnHandbookTitle = " "
	}
	if content.EnHandbookContent == "" {
		content.EnHandbookContent = " "
	}
	company := model.ConferenceCompany{
		CommonModel: model.CommonModel{
			CreateUser: content.OpUser,
		},
		ConferenceId:      content.ConferenceId,
		CnHandbookTitle:   content.CnHandbookTitle,
		EnHandbookTitle:   content.EnHandbookTitle,
		CnHandbookContent: content.CnHandbookContent,
		EnHandbookContent: content.EnHandbookContent,
	}
	return saveOrUpdateConferenceCompany(content.ConferenceId, company)
}

func GetHandbookContent(conferenceId int64) (protocol.HandbookContent, error) {
	company, err := db.GetConferenceCompany(nil, conferenceId)
	if err != nil {
		return protocol.HandbookContent{}, err
	}
	content := protocol.HandbookContent{
		ConferenceId:      company.ConferenceId,
		CnHandbookTitle:   company.CnHandbookTitle,
		EnHandbookTitle:   company.EnHandbookTitle,
		CnHandbookContent: company.CnHandbookContent,
		EnHandbookContent: company.EnHandbookContent,
	}
	return content, nil
}

func SaveSetUp(setUp protocol.SetUp) error {
	if setUp.CnSetUp == "" {
		setUp.CnSetUp = " "
	}
	if setUp.EnSetUp == "" {
		setUp.EnSetUp = " "
	}
	company := model.ConferenceCompany{
		CommonModel: model.CommonModel{
			CreateUser: setUp.OpUser,
		},
		ConferenceId: setUp.ConferenceId,
		CnSetUp:      setUp.CnSetUp,
		EnSetUp:      setUp.EnSetUp,
	}
	return saveOrUpdateConferenceCompany(setUp.ConferenceId, company)
}

func GetSetUp(conferenceId int64) (protocol.SetUp, error) {
	company, err := db.GetConferenceCompany(nil, conferenceId)
	if err != nil {
		return protocol.SetUp{}, err
	}
	setUp := protocol.SetUp{
		ConferenceId: company.ConferenceId,
		CnSetUp:      company.CnSetUp,
		EnSetUp:      company.EnSetUp,
	}
	return setUp, nil
}

func SaveInvitation(invitation protocol.Invitation) error {
	if invitation.CnInvitation == "" {
		invitation.CnInvitation = " "
	}
	if invitation.EnInvitation == "" {
		invitation.EnInvitation = " "
	}
	company := model.ConferenceCompany{
		CommonModel: model.CommonModel{
			CreateUser: invitation.OpUser,
		},
		ConferenceId: invitation.ConferenceId,
		CnInvitation: invitation.CnInvitation,
		EnInvitation: invitation.EnInvitation,
	}
	return saveOrUpdateConferenceCompany(invitation.ConferenceId, company)
}

func GetInvitation(conferenceId int64) (protocol.Invitation, error) {
	company, err := db.GetConferenceCompany(nil, conferenceId)
	if err != nil {
		return protocol.Invitation{}, err
	}
	invitation := protocol.Invitation{
		ConferenceId: company.ConferenceId,
		CnInvitation: company.CnInvitation,
		EnInvitation: company.EnInvitation,
	}
	return invitation, nil
}

func SaveCompanyQuestionType(questionType protocol.CommonQuestionType) error {
	companyQuestionType := model.CompanyQuestionType{
		CommonModel: model.CommonModel{
			CreateUser: questionType.OpUser,
		},
		ConferenceId:   questionType.ConferenceId,
		CnQuestionType: questionType.CnQuestionType,
		EnQuestionType: questionType.EnQuestionType,
		Sort:           questionType.Sort,
	}
	err := db.SaveCompanyQuestionType(nil, &companyQuestionType)
	return err
}

func GetCompanyQuestionTypeList(conferenceId int64) ([]protocol.CommonQuestionType, error) {
	list, err := db.GetCompanyQuestionTypeList(nil, conferenceId)
	if err != nil {
		return nil, err
	}
	types := make([]protocol.CommonQuestionType, 0)
	for _, questionType := range list {
		companyQuestionType := protocol.CommonQuestionType{
			Id:             questionType.Id,
			ConferenceId:   questionType.ConferenceId,
			CnQuestionType: questionType.CnQuestionType,
			EnQuestionType: questionType.EnQuestionType,
			Sort:           questionType.Sort,
		}
		types = append(types, companyQuestionType)
	}
	return types, err
}

func GetCompanyQuestionType(typeId int64) (protocol.CommonQuestionType, error) {
	questionType, err := db.GetCompanyQuestionType(nil, typeId)
	if err != nil {
		return protocol.CommonQuestionType{}, err
	}
	commonQuestionType := protocol.CommonQuestionType{
		Id:             questionType.Id,
		ConferenceId:   questionType.ConferenceId,
		CnQuestionType: questionType.CnQuestionType,
		EnQuestionType: questionType.EnQuestionType,
		Sort:           questionType.Sort,
	}
	return commonQuestionType, nil
}

func DeleteCompanyQuestionType(req protocol.ReqQuestionTypeId) error {
	tx := db.OrmDB(nil).Set("user", req.OpUser).Set("id", req.Id)
	err := db.DeleteCompanyQuestionType(tx, req.Id)
	return err
}

func EditCompanyQuestionType(questionType protocol.ReqEditCompanyQuestionType) error {
	companyQuestionType := model.CompanyQuestionType{
		CommonModel: model.CommonModel{
			Id:         questionType.Id,
			UpdateUser: questionType.OpUser,
		},
		ConferenceId:   questionType.ConferenceId,
		CnQuestionType: questionType.CnQuestionType,
		EnQuestionType: questionType.EnQuestionType,
		Sort:           questionType.Sort,
	}
	err := db.EditCompanyQuestionType(nil, companyQuestionType)
	return err
}

func SaveCompanyQuestionContent(content protocol.QuestionContent) error {
	questionContent := model.CommonQuestionContent{
		CommonModel: model.CommonModel{
			CreateUser: content.OpUser,
		},
		QuestionTypeId:    content.QuestionTypeId,
		CnQuestionContent: content.CnQuestionContent,
		EnQuestionContent: content.EnQuestionContent,
		CnQuestionAnswer:  content.CnQuestionAnswer,
		EnQuestionAnswer:  content.EnQuestionAnswer,
		Sort:              content.Sort,
	}
	err := db.SaveCompanyQuestionContent(nil, &questionContent)
	return err
}

func GetCompanyQuestionAnswerList(questionTypeId int64) ([]protocol.CommonQuestionAnswer, error) {
	list, err := db.GetCompanyQuestionAnswerByTypeId(nil, questionTypeId)
	if err != nil {
		return nil, err
	}
	answers := make([]protocol.CommonQuestionAnswer, 0)
	for _, content := range list {
		answer := protocol.CommonQuestionAnswer{
			Id:                content.Id,
			CnQuestionContent: content.CnQuestionContent,
			CnQuestionAnswer:  content.CnQuestionAnswer,
			CnQuestionType:    content.CnQuestionTypeName,
			EnQuestionType:    content.EnQuestionTypeName,
			EnQuestionContent: content.EnQuestionContent,
			EnQuestionAnswer:  content.EnQuestionAnswer,
			Sort:              content.Sort,
		}
		answers = append(answers, answer)
	}
	return answers, err
}

func GetCompanyQuestionAnswer(answerId int64) (protocol.QuestionContent, error) {
	content, err := db.GetCompanyQuestionAnswerByAnswerId(nil, answerId)
	if err != nil {
		return protocol.QuestionContent{}, err
	}
	questionContent := protocol.QuestionContent{
		QuestionTypeId:    content.QuestionTypeId,
		CnQuestionContent: content.CnQuestionContent,
		EnQuestionContent: content.EnQuestionContent,
		CnQuestionAnswer:  content.CnQuestionAnswer,
		EnQuestionAnswer:  content.EnQuestionAnswer,
		Sort:              content.Sort,
	}
	return questionContent, nil
}

func EditCompanyQuestionContent(subscribe protocol.CommonQuestionAnswerUpdate) error {
	content := model.CommonQuestionContent{
		CommonModel: model.CommonModel{
			Id:         subscribe.Id,
			UpdateUser: subscribe.OpUser,
		},
		QuestionTypeId:    subscribe.QuestionTypeId,
		CnQuestionContent: subscribe.CnQuestionContent,
		EnQuestionContent: subscribe.EnQuestionContent,
		CnQuestionAnswer:  subscribe.CnQuestionAnswer,
		EnQuestionAnswer:  subscribe.EnQuestionAnswer,
		Sort:              subscribe.Sort,
	}
	err := db.EditCompanyQuestionContent(nil, content)
	return err
}

func DeleteCompanyQuestionContent(req protocol.ReqQuestionContentId) error {
	tx := db.OrmDB(nil).Set("user", req.OpUser).Set("id", req.Id)
	err := db.DeleteCompanyQuestionContent(tx, req.Id)
	return err
}

func GetCompanyCommonQA(conferenceId int64) ([]protocol.QuestionType, error) {
	list, err := db.GetCompanyCommonQuestionAnswer(nil, conferenceId)

	if err != nil {
		return nil, err
	}
	questionTypeList := make([]protocol.QuestionType, 0)
	m := make(map[int64][]model.CompanyQuestionContent)
	for _, content := range list {
		m[content.QuestionTypeId] = append(m[content.QuestionTypeId], content)
	}
	for k, v := range m {
		qas := make([]protocol.QA, 0)
		questionType := protocol.QuestionType{
			TypeId: k,
			CnType: "",
			EnType: "",
			QAs:    nil,
		}
		for _, content := range v {
			questionType.CnType = content.CnQuestionTypeName
			questionType.EnType = content.EnQuestionTypeName
			qa := protocol.QA{
				CnQuestion: content.CnQuestionContent,
				EnQuestion: content.EnQuestionContent,
				CnAnswer:   content.CnQuestionAnswer,
				EnAnswer:   content.EnQuestionAnswer,
			}
			qas = append(qas, qa)
		}
		questionType.QAs = qas
		questionTypeList = append(questionTypeList, questionType)
	}
	return questionTypeList, nil
}

// 获取展商手册订阅列表
func GetHandBookSubscribeList(req protocol.ReqAdminSubscribeList) (int64, []protocol.ResHandBookSubscribeInfo, error) {
	var (
		infoLi = make([]protocol.ResHandBookSubscribeInfo, 0)
	)
	total, dataLi, err := db.QueryHandBookSubscribeList(nil, 0, req.Keyword, req.StartTime, req.EndTime, req.Page, req.PageSize, true)
	if err != nil {
		_ = logger.Error("QueryHandBookSubscribeList:", err)
		return total, infoLi, errors.WithStack(err)
	}
	for _, data := range dataLi {
		infoLi = append(infoLi, protocol.ResHandBookSubscribeInfo{
			ID:               data.ID,
			ConferenceID:     data.ConferenceID,
			CnConferenceName: data.CnConferenceName,
			EnConferenceName: data.EnConferenceName,
			Cellphone:        data.Cellphone,
			Email:            data.Email,
			CreateTime:       data.CreateTime.Format(constant.DateTime),
		})
	}
	return total, infoLi, err
}

// 导出展商手册订阅列表
func ExportHandBookSubscribeList(req protocol.ReqAdminSubscribeList) (bReader *bytes.Reader, err error) {
	req.Page, req.PageSize = 0, 0 //重置分页数据
	_, dataLi, err_ := GetHandBookSubscribeList(req)
	if err_ != nil {
		_ = logger.Error("QueryHandBookSubscribeList:", err)
		return nil, err_
	}
	var listItems []interface{}
	for _, v := range dataLi {
		listItems = append(listItems, v)
	}
	bReader, err = utils.GenListExcel(listItems, func(fieldName string, value interface{}) interface{} {
		return value
	}, protocol.ResHandBookSubscribeInfo{})
	if err != nil {
		return nil, err
	}
	return
}

// 保存展商手册订阅记录
func SaveHandBookSubscribe(req protocol.ReqUserSubscribe) (err error) {
	info, err := db.QueryConferenceInfo(nil, req.ConferenceID)
	if err != nil {
		_ = logger.Error("SaveHandBookSubscribe:QueryConferenceInfo,Err->", err)
		return errors.WithStack(err)
	}
	data := model.CompanyHandbookSubscribe{
		ConferenceID:     req.ConferenceID,
		Cellphone:        req.Cellphone,
		Email:            req.Email,
		CnConferenceName: info.CnName,
		EnConferenceName: info.EnName,
	}
	err = db.CreateHandbookSubscribe(nil, &data)
	if err != nil {
		_ = logger.Error("SaveHandBookSubscribe:CreateHandbookSubscribe,Err->", err)
		return errors.WithStack(err)
	}
	return
}
