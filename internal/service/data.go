package service

import (
	"conferencecenter/internal/constant"
	"conferencecenter/internal/corecontext"
	"conferencecenter/internal/db"
	"conferencecenter/internal/model"
	"conferencecenter/internal/rpc"
	"fmt"
	"git.code.tencent.com/smmit/smmbase/logger"
	"time"
)

// 处理变更通知
func handBookDataProcess(kfkMsg model.KfkHandBookData) (procState string, err error) {
	var (
		content string
	)
	err = SendHandBookDataNotify(kfkMsg)
	if err != nil {
		_ = logger.Error(fmt.Sprintf("handBookDataProcess:SendHandBookDataNotify:kfkMsg->%+v,Err->", kfkMsg), err)
		content = fmt.Sprintf("发送展商手册变更通知异常")
	} else {
		content = fmt.Sprintf("发送展商手册变更通知完成")
	}
	content += fmt.Sprintf(";\n>详情:SubscribeId->%d,ConferenceID->%d,HandBookId->%d,Cellphone->%s,Email->%s", kfkMsg.SubscribeId, kfkMsg.ConferenceId, kfkMsg.HandBookId, kfkMsg.Cellphone, kfkMsg.Email)
	_ = rpc.SendWechatMsg(corecontext.Config().NotifyConf.WechatAddr, content, err)
	if err != nil {
		return "", err
	}

	return procState, err
}

// 保存操作日志
func SaveOpLog(modName, opType, content, ip, opEmail string) {
	opLog := model.OperationLog{
		AppName:    constant.AppConference,
		ModName:    modName,
		OpType:     opType,
		Content:    content,
		IP:         ip,
		Operator:   opEmail,
		CreateTime: time.Now().Unix(),
	}
	err := db.SaveOpLog(nil, &opLog)
	if err != nil {
		_ = logger.Error(fmt.Sprintf("SaveOpLog:value->%+v,Err->", opLog), err)
	}
	return
}

// 同步爬虫数据到企业库
func SyncComInfo(sId, eId int64) {

	return
}
