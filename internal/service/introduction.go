package service

import (
	"conferencecenter/internal/corecontext"
	"conferencecenter/internal/db"
	"conferencecenter/internal/model"
	"conferencecenter/internal/pkg/utils"
	"conferencecenter/internal/protocol"
	"fmt"
	"strconv"
	"time"

	"git.code.tencent.com/smmit/smmbase/logger"
	"github.com/pkg/errors"
)

func GetConferenceIntroductionInfo(id int64) (protocol.RespIntroductionInfo, error) {
	if id <= 0 {
		return protocol.RespIntroductionInfo{}, nil
	}
	company, err := db.QueryConferenceIntroductionInfo(nil, id)
	if err != nil {
		return protocol.RespIntroductionInfo{}, err
	}
	return protocol.RespIntroductionInfo{
		ID:                company.ID,
		ConferenceId:      company.ConferenceId,
		IntroductionVideo: company.IntroductionVideo,
		VideoCover:        company.VideoCover,
	}, err

}

// 添加或编辑海报
func AddEditConferenceIntroduction(req protocol.ReqAdminSaveIntroduction) (resMsg string, err error) {
	var (
		previousInfo model.ConferenceIntroduction
		ok           bool
	)
	previousInfo = model.ConferenceIntroduction{
		ID:           req.ConferenceId,
		ConferenceId: req.ConferenceId,
		UpdateTime:   time.Now(),
		CreateTime:   time.Now(),
	}

	company, err := db.QueryConferenceIntroductionInfo(nil, req.ConferenceId)
	if err != nil {
		return "系统错误", errors.WithStack(err)
	}

	if utils.NotEmpty(req.IntroductionVideo) {
		previousInfo.IntroductionVideo = req.IntroductionVideo
	} else if company.ID > 0 {
		previousInfo.IntroductionVideo = company.IntroductionVideo
	}
	if utils.NotEmpty(req.VideoCover) {
		previousInfo.VideoCover = req.VideoCover
	} else if company.ID > 0 {
		previousInfo.VideoCover = company.VideoCover
	}
	tx := corecontext.SrvContext.Orm.Begin()
	if tx == nil {
		return "", errors.New("创建事务失败")
	}
	defer func() {
		if !ok {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	err = db.SaveConferenceIntroduction(tx, &previousInfo)
	if err != nil {
		return "系统错误", errors.WithStack(err)
	}

	ok = true
	return
}

func QueryConferenceDataList(req protocol.ReqGetIntroductionList) (res []protocol.ConferenceData, total int64, err error) {
	list, total, err := db.QueryConferenceDataList(nil, req.ConferenceId, req.Page, req.PageSize)
	if err != nil {
		return nil, 0, err
	}
	for _, item := range list {
		res = append(res, protocol.ConferenceData{
			ID:           item.ID,
			ConferenceId: item.ConferenceId,
			CnDataName:   item.CnDataName,
			CnDataValue:  item.CnDataValue,
			EnDataName:   item.EnDataName,
			EnDataValue:  item.EnDataValue,
			DataImage:    item.DataImage,
			Sorting:      fmt.Sprintf("%.2f", item.Sorting),
			CreateTime:   item.CreateTime,
			UpdateTime:   item.UpdateTime,
			CreateAdmin:  item.CreateAdmin,
			UpdateAdmin:  item.UpdateAdmin,
		})
	}
	return res, total, err
}

func GetConferenceDataInfo(id int64) (protocol.RespIntroductionDataInfo, error) {
	if id <= 0 {
		return protocol.RespIntroductionDataInfo{}, nil
	}
	company, err := db.QueryConferenceDataInfo(nil, id)
	if err != nil {
		return protocol.RespIntroductionDataInfo{}, err
	}

	return protocol.RespIntroductionDataInfo{
		ID:           company.ID,
		ConferenceId: company.ConferenceId,
		CnDataName:   company.CnDataName,
		CnDataValue:  company.CnDataValue,
		EnDataName:   company.EnDataName,
		EnDataValue:  company.EnDataValue,
		DataImage:    company.DataImage,
		Sorting:      company.Sorting,
	}, err
}

// 添加或编辑海报
func AddEditConferenceData(req protocol.ReqAdminSaveIntroductionData) (resMsg string, err error) {
	var (
		previousInfo model.ConferenceData
		ok           bool
	)
	previousInfo = model.ConferenceData{
		ID:           req.ID,
		ConferenceId: req.ConferenceId,
		CnDataName:   req.CnDataName,
		CnDataValue:  req.CnDataValue,
		EnDataName:   req.EnDataName,
		EnDataValue:  req.EnDataValue,
		DataImage:    req.DataImage,
		Sorting:      req.Sorting,
		Deleted:      0,
		UpdateTime:   time.Now(),
		CreateTime:   time.Now(),
	}
	tx := corecontext.SrvContext.Orm.Begin()
	if tx == nil {
		return "", errors.New("创建事务失败")
	}
	defer func() {
		if !ok {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	err = db.SaveConferenceData(tx, &previousInfo)
	if err != nil {
		return "系统错误", errors.WithStack(err)
	}

	ok = true
	return
}

// 删除大会数据
func DeleteConferenceData(conferenceId int64) (resMsg string, err error) {
	var (
		ok bool
	)

	tx := corecontext.SrvContext.Orm.Begin()
	if tx == nil {
		return "", errors.New("创建事务失败")
	}
	defer func() {
		if !ok {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	err = db.UpdateConferenceData(tx, conferenceId, time.Now().Second(), "")
	if err != nil {
		return "系统错误", errors.WithStack(err)
	}

	ok = true
	return
}

func QueryConferenceOrganizationList(req protocol.ReqGetIntroductionOrganizationList) (res []model.ConferenceOrganization, total int64, err error) {
	list, total, err := db.QueryConferenceOrganizationList(nil, req.ConferenceId, req.Type, req.Page, req.PageSize)
	if err != nil {
		return nil, 0, err
	}
	return list, total, err
}

func GetConferenceOrganizationInfo(id int64) (protocol.RespOrganizationInfo, error) {
	if id <= 0 {
		return protocol.RespOrganizationInfo{}, nil
	}
	company, err := db.QueryConferenceOrganizationInfo(nil, id)
	if err != nil {
		return protocol.RespOrganizationInfo{}, err
	}

	return protocol.RespOrganizationInfo{
		ID:           company.ID,
		ConferenceId: company.ConferenceId,
		CnName:       company.CnName,
		EnName:       company.EnName,
		Logo:         company.Logo,
		Sorting:      company.Sorting,
		Type:         company.Type,
	}, err
}

// 添加或编辑
func AddEditConferenceOrganization(req protocol.ReqAdminSaveOrganization) (resMsg string, err error) {
	var (
		previousInfo model.ConferenceOrganization
		ok           bool
	)
	atoi, err := strconv.Atoi(req.Type)
	if err != nil {
		return "系统错误", errors.WithStack(err)
	}
	if atoi < 3 {
		info, err := db.QueryConferenceOrganizationByName(nil, req.ID, req.ConferenceId, req.Type, req.CnName, "")
		if err != nil {
			return "系统错误", errors.WithStack(err)
		}
		if info.ID > 0 {
			return "中文单位名称已存在", errors.WithStack(errors.New("中文单位名称已存在"))
		}
		info, err = db.QueryConferenceOrganizationByName(nil, req.ID, req.ConferenceId, req.Type, "", req.EnName)
		if err != nil {
			return "系统错误", errors.WithStack(err)
		}
		if info.ID > 0 {
			return "英文单位名称已存在", errors.WithStack(errors.New("英文单位名称已存在"))
		}
	}

	previousInfo = model.ConferenceOrganization{
		ID:           req.ID,
		ConferenceId: req.ConferenceId,
		CnName:       req.CnName,
		EnName:       req.EnName,
		Logo:         req.Logo,
		Type:         req.Type,
		Sorting:      req.Sorting,
		Deleted:      0,
		UpdateTime:   time.Now(),
		CreateTime:   time.Now(),
	}
	tx := corecontext.SrvContext.Orm.Begin()
	if tx == nil {
		return "", errors.New("创建事务失败")
	}
	defer func() {
		if !ok {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	err = db.SaveConferenceOrganization(tx, &previousInfo)
	if err != nil {
		return "系统错误", errors.WithStack(err)
	}

	ok = true
	return
}

// 删除大会数据
func DeleteConferenceOrganization(conferenceId int64) (resMsg string, err error) {
	var (
		ok bool
	)

	tx := corecontext.SrvContext.Orm.Begin()
	if tx == nil {
		return "", errors.New("创建事务失败")
	}
	defer func() {
		if !ok {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	err = db.UpdateConferenceOrganization(tx, conferenceId, time.Now().Second(), "")
	if err != nil {
		return "系统错误", errors.WithStack(err)
	}

	ok = true
	return
}

func QueryContactInformationList(req protocol.ReqGetIntroductionList) (res []model.ContactInformation, total int64, err error) {
	list, total, err := db.QueryContactInformationList(nil, req.ConferenceId, req.Page, req.PageSize, true)
	if err != nil {
		return nil, 0, err
	}
	return list, total, err
}

func GetContactInformationInfo(id int64) (protocol.RespContactInformationInfo, error) {
	if id <= 0 {
		return protocol.RespContactInformationInfo{}, nil
	}
	company, err := db.QueryContactInformationInfo(nil, id)
	if err != nil {
		return protocol.RespContactInformationInfo{}, err
	}

	return protocol.RespContactInformationInfo{
		ID:            company.ID,
		ConferenceId:  company.ConferenceId,
		CnTitle:       company.CnTitle,
		EnTitle:       company.EnTitle,
		CnContent:     company.CnContent,
		EnContent:     company.EnContent,
		CnIsDisplayed: company.CnIsDisplayed,
		EnIsDisplayed: company.EnIsDisplayed,
		Sorting:       company.Sorting,
	}, err
}

// 添加或编辑
func AddEditContactInformation(req protocol.ReqAdminSaveContactInformation) (resMsg string, err error) {
	var (
		previousInfo model.ContactInformation
		ok           bool
	)

	previousInfo = model.ContactInformation{
		ID:            req.ID,
		ConferenceId:  req.ConferenceId,
		CnTitle:       req.CnTitle,
		EnTitle:       req.EnTitle,
		CnContent:     req.CnContent,
		EnContent:     req.EnContent,
		CnIsDisplayed: req.CnIsDisplayed,
		EnIsDisplayed: req.EnIsDisplayed,
		Sorting:       req.Sorting,
		Deleted:       0,
		UpdateTime:    time.Now(),
		CreateTime:    time.Now(),
	}
	tx := corecontext.SrvContext.Orm.Begin()
	if tx == nil {
		return "", errors.New("创建事务失败")
	}
	defer func() {
		if !ok {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	err = db.SaveContactInformation(tx, &previousInfo)
	if err != nil {
		return "系统错误", errors.WithStack(err)
	}

	ok = true
	return
}

// 删除大会数据
func DeleteContactInformation(conferenceId int64) (resMsg string, err error) {
	var (
		ok bool
	)

	tx := corecontext.SrvContext.Orm.Begin()
	if tx == nil {
		return "", errors.New("创建事务失败")
	}
	defer func() {
		if !ok {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	err = db.UpdateContactInformation(tx, conferenceId, time.Now().Second(), "")
	if err != nil {
		return "系统错误", errors.WithStack(err)
	}

	ok = true
	return
}

func QueryConferenceBottomPageList(req protocol.ReqGetIntroductionList) (res []model.ConferenceBottomPage, total int64, err error) {
	list, total, err := db.QueryConferenceBottomPageList(nil, req.ConferenceId, 0, req.Page, req.PageSize)
	if err != nil {
		return nil, 0, err
	}
	return list, total, err
}

func GetConferenceBottomPageInfo(id int64) (protocol.RespBottomPageInfo, error) {
	if id <= 0 {
		return protocol.RespBottomPageInfo{}, nil
	}
	company, err := db.QueryConferenceBottomPageInfo(nil, id)
	if err != nil {
		return protocol.RespBottomPageInfo{}, err
	}

	return protocol.RespBottomPageInfo{
		ID:           company.ID,
		ConferenceId: company.ConferenceId,
		CnName:       company.CnName,
		EnName:       company.EnName,
		CnLogo:       company.CnLogo,
		EnLogo:       company.EnLogo,
		CnSorting:    company.CnSorting,
		EnSorting:    company.EnSorting,
	}, err
}

// 添加或编辑
func AddEditConferenceBottomPage(req protocol.ReqAdminSaveBottomPage) (resMsg string, err error) {
	var (
		previousInfo model.ConferenceBottomPage
		ok           bool
	)

	previousInfo = model.ConferenceBottomPage{
		ID:           req.ID,
		ConferenceId: req.ConferenceId,
		CnName:       req.CnName,
		EnName:       req.EnName,
		CnLogo:       req.CnLogo,
		EnLogo:       req.EnLogo,
		CnSorting:    req.CnSorting,
		EnSorting:    req.EnSorting,
		Deleted:      0,
		UpdateTime:   time.Now(),
		CreateTime:   time.Now(),
	}
	tx := corecontext.SrvContext.Orm.Begin()
	if tx == nil {
		return "", errors.New("创建事务失败")
	}
	defer func() {
		if !ok {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	err = db.SaveConferenceBottomPage(tx, &previousInfo)
	if err != nil {
		return "系统错误", errors.WithStack(err)
	}

	ok = true
	return
}

// 删除大会数据
func DeleteConferenceBottomPage(conferenceId int64) (resMsg string, err error) {
	var (
		ok bool
	)

	tx := corecontext.SrvContext.Orm.Begin()
	if tx == nil {
		return "", errors.New("创建事务失败")
	}
	defer func() {
		if !ok {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	err = db.UpdateConferenceBottomPage(tx, conferenceId, time.Now().Second(), "")
	if err != nil {
		return "系统错误", errors.WithStack(err)
	}

	ok = true
	return
}
func GetUserConferenceIntroductionList(req protocol.UserGetIntroductionList) (res protocol.ResWebConferenceIntroductionList, err error) {

	list, _, err := db.QueryConferenceDataList(nil, req.ConferenceId, 1, 1000)
	if err != nil {
		return res, err
	}

	organizationList, _, err := db.QueryConferenceOrganizationList(nil, req.ConferenceId, 0, 1, 1000)
	if err != nil {
		return res, err
	}
	cnBottomPageList, _, err := db.QueryConferenceBottomPageList(nil, req.ConferenceId, 0, 1, 1000)
	if err != nil {
		return res, err
	}
	enBottomPageList, _, err := db.QueryConferenceBottomPageList(nil, req.ConferenceId, 1, 1, 1000)
	if err != nil {
		return res, err
	}
	info, err := db.QueryConferenceIntroductionInfo(nil, req.ConferenceId)
	if err != nil {
		return res, err
	}
	if info == nil {
		_ = logger.Warnning("IntroductionInfo is nil")
		return res, errors.New("IntroductionInfo is nil")
	}

	contactList, _, err := db.QueryContactInformationList(nil, req.ConferenceId, 1, 1000, false)
	if err != nil {
		return res, err
	}

	return protocol.ResWebConferenceIntroductionList{
		DataList:               list,
		OrganizationList:       organizationList,
		CnBottomPageList:       cnBottomPageList,
		EnBottomPageList:       enBottomPageList,
		ContactInformationList: contactList,
		IntroductionVideo:      info.IntroductionVideo,
		VideoCover:             info.VideoCover,
		ConferenceId:           req.ConferenceId,
	}, err
}
