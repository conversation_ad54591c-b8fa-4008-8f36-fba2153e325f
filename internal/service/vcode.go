package service

import (
	"conferencecenter/internal/db"
	"conferencecenter/internal/errcode"
	"conferencecenter/internal/model"
	"conferencecenter/internal/pkg/utils"
	"conferencecenter/internal/protocol"
	"conferencecenter/internal/rpc"
	"conferencecenter/internal/weChat"
	"encoding/json"
	"fmt"
	"git.code.tencent.com/smmit/smmbase/logger"
	"git.code.tencent.com/smmit/smmbase/webhook/feishu"
	"github.com/pkg/errors"
	"strings"
	"time"
)

func SendSms(req protocol.ReqAddAPiSendSms) (m *protocol.Message, err error) {
	sms, err := rpc.SendSms(req)
	if err != nil {
		logger.Error(fmt.Sprintf("发送验证码失败:Err->%v;Req->%+v", err, req))
		return nil, err
	}
	return sms, nil
}

func CheckCode(req protocol.ReqCheckCode) (m *protocol.Message, err error) {
	code, err := rpc.CheckCode(req)
	if err != nil {
		logger.Error(fmt.Sprintf("校验验证码失败:Err->%v;Req->%+v", err, req))
		return nil, err
	}
	return code, nil
}

func SaveUserSendSms(req protocol.ReqUserAddAPiSendSms) error {
	m, err := rpc.SendSms(protocol.ReqAddAPiSendSms{
		Cellphone: req.Cellphone,
		Source:    "conference",
		CodeType:  "conference_from_phone",
	})
	if err != nil {
		_ = logger.Error("SaveUserApply:AddAPiSignUpAddEn,Err->", err)
		return errors.WithStack(err)
	}
	if m.Code != errcode.RESPONSE_CODE_SUCCESS {
		logger.Error("发送验证码失败原因：", m.Msg)
		return errors.WithStack(errors.New(m.Msg))
	}
	return nil
}

// 保存用户申请记录(申请展商或申请赞助)
func SaveUserApplyVisit(req protocol.ReqUserApplyVisitSubmit) (err error) {
	var (
		reqAPiSign protocol.ReqAddAPiSignUpInterestedParticipant
		WxUrl2     string
	)

	m, err := rpc.CheckCode(protocol.ReqCheckCode{
		Cellphone: req.CellPhone,
		Code:      req.PhoneCode,
		CodeType:  "conference_from_phone",
	})
	if m.Code != errcode.RESPONSE_CODE_SUCCESS {
		return errors.WithStack(err)
	}

	cnVisitor := model.CnFreeVisitor{
		ConferenceID: 1,
		CnName:       req.Name,
		CnCompany:    req.Company,
		CnJobTitle:   req.JobTitle,
		CnTelephone:  req.CellPhone,
		FromID:       "落地页",
	}
	err = db.CreateCnFreeVisitor(nil, &cnVisitor)
	if err != nil {
		logger.Error(fmt.Sprintf("查询"))
		return errors.WithStack(err)
	}

	info, err := db.QueryConferenceInfo(nil, 1)
	if err != nil {
		_ = logger.Error("SaveUserApply:CreateConferenceClue,Err->", err)
		return errors.WithStack(err)
	}

	if !utils.IsTestEnv() {
		WxUrl2 = weChat.LandingPageWxUrl2
	} else {
		WxUrl2 = weChat.LandingPageWxUrl
	}

	markdown2 := fmt.Sprintf(`
		## CLNB展览网站落地页报名通知
		>会议名：%v
		>姓名：%s 
		>公司：%s 
		>职位：%v
		>手机号：%v
		>时间：%s`, "CLNB 2024（第九届）中国国际新能源产业博览会", req.Name, req.Company, req.JobTitle, req.CellPhone, utils.GetCurDateTime())

	cardMsg := feishu.NewMdCardMsg("CLNB展览网站落地页报名通知", "blue")
	cardMsg.AppendMd(`>会议名：%v
>姓名：%s 
>公司：%s 
>职位：%v
>手机号：%v
>时间：%s`, "CLNB 2024（第九届）中国国际新能源产业博览会", req.Name, req.Company, req.JobTitle, req.CellPhone, utils.GetCurDateTime())

	if strings.Contains(WxUrl2, "open.feishu.cn") {
		weChat.Send2(cardMsg, WxUrl2)
	} else {
		weChat.Send(markdown2, WxUrl2)
	}

	if utils.NotEmpty(info.MeetingSysId) {
		reqAPiSign.MeetingNo = info.MeetingSysId
		reqAPiSign.Name = req.Name
		reqAPiSign.Cellphone = req.CellPhone
		reqAPiSign.CompanyName = req.Company
		reqAPiSign.Position = req.JobTitle

		m, err = rpc.AddAPiSignUpInterestedParticipant(reqAPiSign)
		if err != nil {
			_ = logger.Error("SaveUserApply:AddAPiSignUpAddEn,Err->", err)
			return errors.WithStack(err)
		}

		RespLog, err := json.Marshal(m)
		ReqLog, err := json.Marshal(reqAPiSign)

		RegisterLog := model.RegisterLog{
			RegisterId: cnVisitor.ID,
			ReqLog:     string(ReqLog),
			RespLog:    string(RespLog),
			LogType:    2,
			CreateTime: time.Now(),
		}
		err = db.AddMeetingRegisterLog(nil, &RegisterLog)
		if err != nil {
			_ = logger.Error("SaveUserApply:AddAPiSignUpAddEn,Err->", err)
			return errors.WithStack(err)
		}
	} else {
		logger.Error(fmt.Sprintf("%s 会议申请错误，会议系统编号为空", info.EnName))
	}

	return
}
