package service

import (
	"bytes"
	"conferencecenter/internal/constant"
	"conferencecenter/internal/corecontext"
	"conferencecenter/internal/db"
	"conferencecenter/internal/memory"
	"conferencecenter/internal/model"
	"conferencecenter/internal/pkg/utils"
	"conferencecenter/internal/protocol"
	"conferencecenter/internal/rpc"
	"conferencecenter/internal/weChat"
	"encoding/json"
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"time"

	"git.code.tencent.com/smmit/smmbase/logger"
	"git.code.tencent.com/smmit/smmbase/webhook/feishu"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
)

func QueryConferenceTicketPriceList(req protocol.ReqGetTicketPriceList) (res []protocol.ConferenceTicketPrice, total int64, err error) {
	list, total, err := db.QueryConferenceTicketPriceList(nil, req.ConferenceId, req.Page, req.PageSize)
	if err != nil {
		return nil, 0, err
	}
	for _, item := range list {
		res = append(res, protocol.ConferenceTicketPrice{
			Id:                     item.Id,
			ConferenceId:           item.ConferenceId,
			CnName:                 item.CnName,
			EnName:                 item.EnName,
			CnServiceId:            item.CnServiceId,
			EnServiceId:            item.EnServiceId,
			CnButtonName:           item.CnButtonName,
			EnButtonName:           item.EnButtonName,
			CnButtonLink:           item.CnButtonLink,
			EnButtonLink:           item.EnButtonLink,
			CnRegistrationPageName: item.CnRegistrationPageName,
			EnRegistrationPageName: item.EnRegistrationPageName,
			CnIsDisplayed:          item.CnIsDisplayed,
			CnMaximum:              item.CnMaximum,
			CnSorting:              fmt.Sprintf("%.2f", item.CnSorting),
			EnIsDisplayed:          item.EnIsDisplayed,
			EnMaximum:              item.EnMaximum,
			EnSorting:              fmt.Sprintf("%.2f", item.EnSorting),
			CnCurrencyUnit:         item.CnCurrencyUnit,
			EnCurrencyUnit:         item.EnCurrencyUnit,
			CnStandardFee:          item.CnStandardFee,
			EnStandardFee:          item.EnStandardFee,
			CreateAdmin:            item.CreateAdmin,
			CreateTime:             item.CreateTime,
			UpdateAdmin:            item.UpdateAdmin,
			UpdateTime:             item.UpdateTime,
			Type:                   item.Type,
			Deleted:                item.Deleted,
		})
	}
	return res, total, err
}
func QueryEnConferenceTicketPriceList(req protocol.ReqGetTicketPriceList) (res []protocol.ConferenceTicketPrice, total int64, err error) {
	list, total, err := db.QueryConferenceEnTicketPriceList(nil, req.ConferenceId, req.Page, req.PageSize)
	if err != nil {
		return nil, 0, err
	}
	for _, item := range list {
		res = append(res, protocol.ConferenceTicketPrice{
			Id:                     item.Id,
			ConferenceId:           item.ConferenceId,
			CnName:                 item.CnName,
			EnName:                 item.EnName,
			CnServiceId:            item.CnServiceId,
			EnServiceId:            item.EnServiceId,
			CnButtonName:           item.CnButtonName,
			EnButtonName:           item.EnButtonName,
			CnButtonLink:           item.CnButtonLink,
			EnButtonLink:           item.EnButtonLink,
			CnRegistrationPageName: item.CnRegistrationPageName,
			EnRegistrationPageName: item.EnRegistrationPageName,
			CnIsDisplayed:          item.CnIsDisplayed,
			CnMaximum:              item.CnMaximum,
			CnSorting:              fmt.Sprintf("%.2f", item.CnSorting),
			EnIsDisplayed:          item.EnIsDisplayed,
			EnMaximum:              item.EnMaximum,
			EnSorting:              fmt.Sprintf("%.2f", item.EnSorting),
			CnCurrencyUnit:         item.CnCurrencyUnit,
			EnCurrencyUnit:         item.EnCurrencyUnit,
			CnStandardFee:          item.CnStandardFee,
			EnStandardFee:          item.EnStandardFee,
			CreateAdmin:            item.CreateAdmin,
			CreateTime:             item.CreateTime,
			UpdateAdmin:            item.UpdateAdmin,
			UpdateTime:             item.UpdateTime,
			Type:                   item.Type,
			Deleted:                item.Deleted,
		})
	}
	return res, total, err
}

func GetConferenceTicketPriceInfo(id int64) (protocol.RespTicketPriceInfo, error) {
	if id <= 0 {
		return protocol.RespTicketPriceInfo{}, nil
	}
	company, err := db.QueryConferenceTicketPriceInfo(nil, id)
	if err != nil {
		return protocol.RespTicketPriceInfo{}, err
	}
	if company == nil {
		return protocol.RespTicketPriceInfo{}, errors.New("未找到票种信息")
	}

	return protocol.RespTicketPriceInfo{
		Id:                     company.Id,
		ConferenceId:           company.ConferenceId,
		CnName:                 company.CnName,
		EnName:                 company.EnName,
		CnServiceId:            company.CnServiceId,
		EnServiceId:            company.EnServiceId,
		CnButtonName:           company.CnButtonName,
		EnButtonName:           company.EnButtonName,
		CnButtonLink:           company.CnButtonLink,
		EnButtonLink:           company.EnButtonLink,
		CnRegistrationPageName: company.CnRegistrationPageName,
		EnRegistrationPageName: company.EnRegistrationPageName,
		CnIsDisplayed:          company.CnIsDisplayed,
		CnMaximum:              company.CnMaximum,
		CnSorting:              company.CnSorting,
		EnIsDisplayed:          company.EnIsDisplayed,
		EnMaximum:              company.EnMaximum,
		EnSorting:              company.EnSorting,
		CnCurrencyUnit:         company.CnCurrencyUnit,
		EnCurrencyUnit:         company.EnCurrencyUnit,
		CnStandardFee:          company.CnStandardFee,
		EnStandardFee:          company.EnStandardFee,
	}, err
}

// 添加或编辑海报
func AddEditConferenceTicketPrice(req protocol.ReqAdminSaveTicketPrice) (resMsg string, err error) {
	var (
		ok bool
	)
	tx := corecontext.SrvContext.Orm.Begin()
	if tx == nil {
		return "", errors.New("创建事务失败")
	}
	defer func() {
		if !ok {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()
	now := time.Now().In(time.Local)
	id := int64(0)
	if req.Id > 0 {
		// 编辑
		record, err := db.QueryConferenceTicketPriceInfo(nil, id)
		if err != nil {
			return resMsg, err
		}
		if record == nil {
			return "未找到票种信息", errors.New("未找到票种信息")
		}
		record.ConferenceId = req.ConferenceId
		record.CnName = req.CnName
		record.EnName = req.EnName
		record.CnServiceId = req.CnServiceId
		record.EnServiceId = req.EnServiceId
		record.CnButtonName = req.CnButtonName
		record.EnButtonName = req.EnButtonName
		record.CnButtonLink = req.CnButtonLink
		record.EnButtonLink = req.EnButtonLink
		record.CnRegistrationPageName = req.CnRegistrationPageName
		record.EnRegistrationPageName = req.EnRegistrationPageName
		record.CnIsDisplayed = req.CnIsDisplayed
		record.EnIsDisplayed = req.EnIsDisplayed
		if req.CnMaximum > 0 {
			record.CnMaximum = req.CnMaximum
		}
		if req.EnMaximum > 0 {
			record.EnMaximum = req.EnMaximum
		}
		record.CnSorting = req.CnSorting
		record.EnSorting = req.EnSorting
		record.UpdateAdmin = req.AdminEmail
		record.UpdateTime = now
		record.Id = req.Id
		if utils.NotEmpty(req.CnServiceId) {
			resp, err := GetServiceMeetingTicketPrice(req.CnServiceId)
			var standards []protocol.Standards

			if len(resp.ServiceList) > 0 {
				if resp.ServiceList[0].CurrencyUnit > 0 {
					record.CnCurrencyUnit = resp.ServiceList[0].CurrencyUnit
				}

				if len(resp.ServiceList[0].PriceTypeList) > 0 {
					standard := protocol.Standards{
						Delegates:    "1 人",
						EarlyBird:    resp.ServiceList[0].PriceTypeList[0].Price,
						CurrencyUnit: resp.ServiceList[0].CurrencyUnit,
					}
					standards = append(standards, standard)
				}
				if req.Type == 0 {
					kNum := -1
					for i := 2; i < req.CnMaximum+1; i++ {
						isTrue := false
						if len(resp.ServiceList[0].SalesStrategyList) > 0 {

							for k, salesStrategy := range resp.ServiceList[0].SalesStrategyList {

								if i == salesStrategy.RequireNum {

									price := decimal.NewFromFloat(resp.ServiceList[0].SalesStrategyList[k].UnitPrice)

									fromInt := decimal.NewFromInt(int64(i))

									sumPrice := price.Mul(fromInt)
									floatResult, _ := sumPrice.Float64()

									standard := protocol.Standards{
										Delegates:    strconv.Itoa(salesStrategy.RequireNum) + " 人",
										EarlyBird:    floatResult,
										CurrencyUnit: resp.ServiceList[0].CurrencyUnit,
									}
									standards = append(standards, standard)
									isTrue = true
									kNum = k
								}
							}

							if !isTrue {
								if i == 2 || kNum == -1 {
									price := decimal.NewFromFloat(resp.ServiceList[0].PriceTypeList[0].Price)

									fromInt := decimal.NewFromInt(int64(i))
									sumPrice := price.Mul(fromInt)
									floatResult, _ := sumPrice.Float64()

									standard := protocol.Standards{
										Delegates:    strconv.Itoa(i) + " 人",
										EarlyBird:    floatResult,
										CurrencyUnit: resp.ServiceList[0].CurrencyUnit,
									}
									standards = append(standards, standard)
								} else {

									price := decimal.NewFromFloat(resp.ServiceList[0].SalesStrategyList[kNum].UnitPrice)

									fromInt := decimal.NewFromInt(int64(i))

									sumPrice := price.Mul(fromInt)
									floatResult, _ := sumPrice.Float64()

									standard := protocol.Standards{
										Delegates:    strconv.Itoa(i) + " 人",
										EarlyBird:    floatResult,
										CurrencyUnit: resp.ServiceList[0].CurrencyUnit,
									}
									standards = append(standards, standard)
								}
							}

						} else {

							price := decimal.NewFromFloat(resp.ServiceList[0].PriceTypeList[0].Price)

							fromInt := decimal.NewFromInt(int64(i))
							sumPrice := price.Mul(fromInt)
							floatResult, _ := sumPrice.Float64()

							standard := protocol.Standards{
								Delegates:    strconv.Itoa(i) + " 人",
								EarlyBird:    floatResult,
								CurrencyUnit: resp.ServiceList[0].CurrencyUnit,
							}
							standards = append(standards, standard)
						}

					}

				} else {
					if len(resp.ServiceList[0].SalesStrategyList) > 0 {
						for k, salesStrategy := range resp.ServiceList[0].SalesStrategyList {

							if 3 == salesStrategy.RequireNum {
								standard := protocol.Standards{
									Delegates:    strconv.Itoa(salesStrategy.RequireNum) + " 人",
									EarlyBird:    resp.ServiceList[0].SalesStrategyList[k].UnitPrice,
									CurrencyUnit: resp.ServiceList[0].CurrencyUnit,
								}
								standards = append(standards, standard)
							}
						}
					}
				}

			}

			marshal, err := json.Marshal(standards)
			if err != nil {
				return "", errors.New(err.Error())
			}
			record.CnStandardFee = string(marshal)
			err = db.SaveConferenceTicketPriceInfo(nil, record)
			if err != nil {
				return "", errors.New(err.Error())
			}

		}
		if utils.NotEmpty(req.EnServiceId) {
			resp, err := GetServiceMeetingTicketPrice(req.EnServiceId)
			var standards []protocol.Standards

			if len(resp.ServiceList) > 0 {
				if resp.ServiceList[0].CurrencyUnit > 0 {
					record.EnCurrencyUnit = resp.ServiceList[0].CurrencyUnit
				}

				if len(resp.ServiceList[0].PriceTypeList) > 0 {
					standard := protocol.Standards{
						Delegates:    "1 Delegate",
						EarlyBird:    resp.ServiceList[0].PriceTypeList[0].Price,
						CurrencyUnit: resp.ServiceList[0].CurrencyUnit,
					}
					standards = append(standards, standard)
				}
				if req.Type == 0 {
					kNum := -1
					for i := 2; i < req.EnMaximum+1; i++ {
						isTrue := false
						if len(resp.ServiceList[0].SalesStrategyList) > 0 {

							for k, salesStrategy := range resp.ServiceList[0].SalesStrategyList {

								if i == salesStrategy.RequireNum {

									price := decimal.NewFromFloat(resp.ServiceList[0].SalesStrategyList[k].UnitPrice)

									fromInt := decimal.NewFromInt(int64(i))

									sumPrice := price.Mul(fromInt)
									floatResult, _ := sumPrice.Float64()

									standard := protocol.Standards{
										Delegates:    strconv.Itoa(salesStrategy.RequireNum) + " Delegates",
										EarlyBird:    floatResult,
										CurrencyUnit: resp.ServiceList[0].CurrencyUnit,
									}
									standards = append(standards, standard)
									isTrue = true
									kNum = k
								}
							}

							if !isTrue {
								if i == 2 || kNum == -1 {
									price := decimal.NewFromFloat(resp.ServiceList[0].PriceTypeList[0].Price)

									fromInt := decimal.NewFromInt(int64(i))
									sumPrice := price.Mul(fromInt)
									floatResult, _ := sumPrice.Float64()

									standard := protocol.Standards{
										Delegates:    strconv.Itoa(i) + " Delegates",
										EarlyBird:    floatResult,
										CurrencyUnit: resp.ServiceList[0].CurrencyUnit,
									}
									standards = append(standards, standard)
								} else {

									price := decimal.NewFromFloat(resp.ServiceList[0].SalesStrategyList[kNum].UnitPrice)

									fromInt := decimal.NewFromInt(int64(i))

									sumPrice := price.Mul(fromInt)
									floatResult, _ := sumPrice.Float64()

									standard := protocol.Standards{
										Delegates:    strconv.Itoa(i) + " Delegates",
										EarlyBird:    floatResult,
										CurrencyUnit: resp.ServiceList[0].CurrencyUnit,
									}
									standards = append(standards, standard)
								}
							}

						} else {

							price := decimal.NewFromFloat(resp.ServiceList[0].PriceTypeList[0].Price)

							fromInt := decimal.NewFromInt(int64(i))
							sumPrice := price.Mul(fromInt)
							floatResult, _ := sumPrice.Float64()

							standard := protocol.Standards{
								Delegates:    strconv.Itoa(i) + " Delegates",
								EarlyBird:    floatResult,
								CurrencyUnit: resp.ServiceList[0].CurrencyUnit,
							}
							standards = append(standards, standard)
						}

					}

				} else {
					if len(resp.ServiceList[0].SalesStrategyList) > 0 {
						for k, salesStrategy := range resp.ServiceList[0].SalesStrategyList {

							if 3 == salesStrategy.RequireNum {
								standard := protocol.Standards{
									Delegates:    strconv.Itoa(salesStrategy.RequireNum) + " Delegates",
									EarlyBird:    resp.ServiceList[0].SalesStrategyList[k].UnitPrice,
									CurrencyUnit: resp.ServiceList[0].CurrencyUnit,
								}
								standards = append(standards, standard)
							}
						}
					}
				}

			}

			marshal, err := json.Marshal(standards)
			if err != nil {
				return "", errors.New(err.Error())
			}
			record.EnStandardFee = string(marshal)
		}
		err = db.SaveConferenceTicketPriceInfo(nil, record)
		if err != nil {
			return "", errors.New(err.Error())
		}
		id = req.Id

	} else {

		// 创建专家
		rightsInterests := model.ConferenceTicketPrice{
			ConferenceId:           req.ConferenceId,
			CnName:                 req.CnName,
			EnName:                 req.EnName,
			CnServiceId:            req.CnServiceId,
			EnServiceId:            req.EnServiceId,
			CnButtonName:           req.CnButtonName,
			EnButtonName:           req.EnButtonName,
			CnButtonLink:           req.CnButtonLink,
			EnButtonLink:           req.EnButtonLink,
			CnRegistrationPageName: req.CnRegistrationPageName,
			EnRegistrationPageName: req.EnRegistrationPageName,
			CnIsDisplayed:          req.CnIsDisplayed,
			CnMaximum:              req.CnMaximum,
			CnSorting:              req.CnSorting,
			EnIsDisplayed:          req.EnIsDisplayed,
			EnMaximum:              req.EnMaximum,
			EnSorting:              req.EnSorting,
			Deleted:                0,
			CreateAdmin:            req.AdminEmail,
			UpdateAdmin:            req.AdminEmail,
			CreateTime:             now,
			UpdateTime:             now,
		}

		if utils.NotEmpty(req.CnServiceId) {
			resp, err := GetServiceMeetingTicketPrice(req.CnServiceId)
			standards := []protocol.Standards{}

			if len(resp.ServiceList) > 0 {
				if resp.ServiceList[0].CurrencyUnit > 0 {
					rightsInterests.CnCurrencyUnit = resp.ServiceList[0].CurrencyUnit
				}
				if len(resp.ServiceList[0].PriceTypeList) > 0 {
					standard := protocol.Standards{
						Delegates:    "1 人",
						EarlyBird:    resp.ServiceList[0].PriceTypeList[0].Price,
						CurrencyUnit: resp.ServiceList[0].CurrencyUnit,
					}
					standards = append(standards, standard)

				}
				if req.Type == 0 {
					for i := 2; i < req.CnMaximum+1; i++ {
						if len(resp.ServiceList[0].SalesStrategyList) > 0 {
							kNum := -1
							isTrue := false
							for k, salesStrategy := range resp.ServiceList[0].SalesStrategyList {

								if i == salesStrategy.RequireNum {

									price := decimal.NewFromFloat(resp.ServiceList[0].SalesStrategyList[k].UnitPrice)

									fromInt := decimal.NewFromInt(int64(i))

									sumPrice := price.Mul(fromInt)
									floatResult, _ := sumPrice.Float64()

									standard := protocol.Standards{
										Delegates:    strconv.Itoa(salesStrategy.RequireNum) + " 人",
										EarlyBird:    floatResult,
										CurrencyUnit: resp.ServiceList[0].CurrencyUnit,
									}
									standards = append(standards, standard)
									isTrue = true
									kNum = k
								}
							}

							if !isTrue {
								if i == 2 || kNum == -1 {

									price := decimal.NewFromFloat(resp.ServiceList[0].PriceTypeList[0].Price)

									fromInt := decimal.NewFromInt(int64(i))

									sumPrice := price.Mul(fromInt)
									floatResult, _ := sumPrice.Float64()

									standard := protocol.Standards{
										Delegates:    strconv.Itoa(i) + " 人",
										EarlyBird:    floatResult,
										CurrencyUnit: resp.ServiceList[0].CurrencyUnit,
									}
									standards = append(standards, standard)
								} else {
									price := decimal.NewFromFloat(resp.ServiceList[0].SalesStrategyList[kNum].UnitPrice)

									fromInt := decimal.NewFromInt(int64(i))

									sumPrice := price.Mul(fromInt)
									floatResult, _ := sumPrice.Float64()

									standard := protocol.Standards{
										Delegates:    strconv.Itoa(i) + " 人",
										EarlyBird:    floatResult,
										CurrencyUnit: resp.ServiceList[0].CurrencyUnit,
									}
									standards = append(standards, standard)
								}
							}

						} else {
							price := decimal.NewFromFloat(resp.ServiceList[0].PriceTypeList[0].Price)

							fromInt := decimal.NewFromInt(int64(i))
							sumPrice := price.Mul(fromInt)
							floatResult, _ := sumPrice.Float64()

							standard := protocol.Standards{
								Delegates:    strconv.Itoa(i) + " 人",
								EarlyBird:    floatResult,
								CurrencyUnit: resp.ServiceList[0].CurrencyUnit,
							}
							standards = append(standards, standard)
						}
					}
				} else {
					if len(resp.ServiceList[0].SalesStrategyList) > 0 {
						for k, salesStrategy := range resp.ServiceList[0].SalesStrategyList {
							if 3 == salesStrategy.RequireNum {
								standard := protocol.Standards{
									Delegates:    strconv.Itoa(salesStrategy.RequireNum) + " 人",
									EarlyBird:    resp.ServiceList[0].SalesStrategyList[k].UnitPrice,
									CurrencyUnit: resp.ServiceList[0].CurrencyUnit,
								}
								standards = append(standards, standard)
							}
						}
					}
				}
			}

			marshal, err := json.Marshal(standards)
			if err != nil {
				return "", errors.New(err.Error())
			}
			rightsInterests.CnStandardFee = string(marshal)
		}
		if utils.NotEmpty(req.EnServiceId) {
			resp, err := GetServiceMeetingTicketPrice(req.EnServiceId)
			standards := []protocol.Standards{}

			if len(resp.ServiceList) > 0 {
				if resp.ServiceList[0].CurrencyUnit > 0 {
					rightsInterests.EnCurrencyUnit = resp.ServiceList[0].CurrencyUnit
				}
				if len(resp.ServiceList[0].PriceTypeList) > 0 {
					standard := protocol.Standards{
						Delegates:    "1 Delegate",
						EarlyBird:    resp.ServiceList[0].PriceTypeList[0].Price,
						CurrencyUnit: resp.ServiceList[0].CurrencyUnit,
					}
					standards = append(standards, standard)

				}
				if req.Type == 0 {
					for i := 2; i < req.EnMaximum+1; i++ {
						if len(resp.ServiceList[0].SalesStrategyList) > 0 {
							kNum := -1
							isTrue := false
							for k, salesStrategy := range resp.ServiceList[0].SalesStrategyList {

								if i == salesStrategy.RequireNum {

									price := decimal.NewFromFloat(resp.ServiceList[0].SalesStrategyList[k].UnitPrice)

									fromInt := decimal.NewFromInt(int64(i))

									sumPrice := price.Mul(fromInt)
									floatResult, _ := sumPrice.Float64()

									standard := protocol.Standards{
										Delegates:    strconv.Itoa(salesStrategy.RequireNum) + " Delegates",
										EarlyBird:    floatResult,
										CurrencyUnit: resp.ServiceList[0].CurrencyUnit,
									}
									standards = append(standards, standard)
									isTrue = true
									kNum = k
								}
							}

							if !isTrue {
								if i == 2 || kNum == -1 {

									price := decimal.NewFromFloat(resp.ServiceList[0].PriceTypeList[0].Price)

									fromInt := decimal.NewFromInt(int64(i))

									sumPrice := price.Mul(fromInt)
									floatResult, _ := sumPrice.Float64()

									standard := protocol.Standards{
										Delegates:    strconv.Itoa(i) + " Delegates",
										EarlyBird:    floatResult,
										CurrencyUnit: resp.ServiceList[0].CurrencyUnit,
									}
									standards = append(standards, standard)
								} else {
									price := decimal.NewFromFloat(resp.ServiceList[0].SalesStrategyList[kNum].UnitPrice)

									fromInt := decimal.NewFromInt(int64(i))

									sumPrice := price.Mul(fromInt)
									floatResult, _ := sumPrice.Float64()

									standard := protocol.Standards{
										Delegates:    strconv.Itoa(i) + " Delegates",
										EarlyBird:    floatResult,
										CurrencyUnit: resp.ServiceList[0].CurrencyUnit,
									}
									standards = append(standards, standard)
								}
							}

						} else {
							price := decimal.NewFromFloat(resp.ServiceList[0].PriceTypeList[0].Price)

							fromInt := decimal.NewFromInt(int64(i))
							sumPrice := price.Mul(fromInt)
							floatResult, _ := sumPrice.Float64()

							standard := protocol.Standards{
								Delegates:    strconv.Itoa(i) + " Delegates",
								EarlyBird:    floatResult,
								CurrencyUnit: resp.ServiceList[0].CurrencyUnit,
							}
							standards = append(standards, standard)
						}
					}
				} else {
					if len(resp.ServiceList[0].SalesStrategyList) > 0 {
						for k, salesStrategy := range resp.ServiceList[0].SalesStrategyList {
							if 3 == salesStrategy.RequireNum {
								standard := protocol.Standards{
									Delegates:    strconv.Itoa(salesStrategy.RequireNum) + " Delegates",
									EarlyBird:    resp.ServiceList[0].SalesStrategyList[k].UnitPrice,
									CurrencyUnit: resp.ServiceList[0].CurrencyUnit,
								}
								standards = append(standards, standard)
							}
						}
					}
				}
			}

			marshal, err := json.Marshal(standards)
			if err != nil {
				return "", errors.New(err.Error())
			}
			rightsInterests.EnStandardFee = string(marshal)
		}
		err = db.SaveConferenceTicketPriceInfo(nil, &rightsInterests)
		if err != nil {
			return "", errors.New(err.Error())
		}

	}

	ok = true
	return
}

// 删除大会数据
func DeleteConferenceTicketPrice(conferenceId int64, AdminEmail string) (resMsg string, err error) {
	var (
		ok bool
	)

	tx := corecontext.SrvContext.Orm.Begin()
	if tx == nil {
		return "", errors.New("创建事务失败")
	}
	defer func() {
		if !ok {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	err = db.UpdateConferenceTicketPrice(tx, conferenceId, time.Now().Second(), AdminEmail)
	if err != nil {
		return "系统错误", errors.WithStack(err)
	}

	ok = true
	return
}

func QueryRightsInterestsList(req protocol.ReqGetRightsInterestsList) (res []model.ConferenceRightsInterests, total int64, err error) {
	list, total, err := db.QueryConferenceRightsInterestsList(nil, req.ConferenceId, req.Page, req.PageSize)
	if err != nil {
		return nil, 0, err
	}
	return list, total, err
}

func QueryEnRightsInterestsList(req protocol.ReqGetRightsInterestsList) (res []model.ConferenceRightsInterests, total int64, err error) {
	list, total, err := db.QueryConferenceEnRightsInterestsList(nil, req.ConferenceId, req.Page, req.PageSize)
	if err != nil {
		return nil, 0, err
	}
	return list, total, err
}

// 添加或编辑海报
func AddEditConferenceRightsInterests(req protocol.ReqAdminSaveRightInterests) (resMsg string, err error) {
	var (
		previousInfo model.ConferenceRightsInterests
		ok           bool
	)
	previousInfo = model.ConferenceRightsInterests{
		Id:           req.Id,
		ConferenceId: req.ConferenceId,
		CnContent:    req.CnContent,
		EnContent:    req.EnContent,
		Type:         req.Type,
		CnSorting:    req.CnSorting,
		EnSorting:    req.EnSorting,
		Deleted:      0,
		UpdateTime:   time.Now(),
		CreateTime:   time.Now(),
	}
	tx := corecontext.SrvContext.Orm.Begin()
	if tx == nil {
		return "", errors.New("创建事务失败")
	}
	defer func() {
		if !ok {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	err = db.SaveConferenceRightsInterests(tx, &previousInfo)
	if err != nil {
		return "系统错误", errors.WithStack(err)
	}

	ok = true
	return
}

func GetConferenceRightsInterestsInfo(id int64) (protocol.ResConferenceRightsInterestsInfo, error) {
	if id <= 0 {
		return protocol.ResConferenceRightsInterestsInfo{}, nil
	}
	company, err := db.QueryConferenceRightsInterestsInfo(nil, id)
	if err != nil {
		return protocol.ResConferenceRightsInterestsInfo{}, err
	}

	return protocol.ResConferenceRightsInterestsInfo{
		Id:           company.Id,
		ConferenceId: company.ConferenceId,
		CnContent:    company.CnContent,
		EnContent:    company.EnContent,
		CnSorting:    company.CnSorting,
		EnSorting:    company.EnSorting,
		Type:         company.Type,
	}, err
}

// 删除大会权益
func DeleteRightsInterests(conferenceId int64) (resMsg string, err error) {
	var (
		ok bool
	)

	tx := corecontext.SrvContext.Orm.Begin()
	if tx == nil {
		return "", errors.New("创建事务失败")
	}
	defer func() {
		if !ok {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	err = db.UpdateConferenceRightsInterests(tx, conferenceId, time.Now().Second(), "")
	if err != nil {
		return "系统错误", errors.WithStack(err)
	}

	ok = true
	return
}

func GetConferenceTicketConfigInfo(id int64) (protocol.ResConferenceTicketConfigInfo, error) {
	if id <= 0 {
		return protocol.ResConferenceTicketConfigInfo{}, nil
	}
	company, err := db.GetConferenceTicketConfigInfo(nil, id)
	if err != nil {
		return protocol.ResConferenceTicketConfigInfo{}, err
	}

	return protocol.ResConferenceTicketConfigInfo{
		Id:              company.Id,
		ConferenceId:    company.ConferenceId,
		CnPriceTemplate: company.CnPriceTemplate,
		EnPriceTemplate: company.EnPriceTemplate,
	}, err
}

// 添加或编辑海报
func AddEditConferenceTicketConfig(req protocol.ReqAdminSaveTicketConfig) (resMsg string, err error) {
	var (
		previousInfo model.ConferenceTicketConfig
		ok           bool
	)
	previousInfo = model.ConferenceTicketConfig{
		Id:              req.ConferenceId,
		ConferenceId:    req.ConferenceId,
		CnPriceTemplate: req.CnPriceTemplate,
		EnPriceTemplate: req.EnPriceTemplate,
		Deleted:         0,
	}
	tx := corecontext.SrvContext.Orm.Begin()
	if tx == nil {
		return "", errors.New("创建事务失败")
	}
	defer func() {
		if !ok {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	err = db.SaveConferenceTicketConfig(tx, &previousInfo)
	if err != nil {
		return "系统错误", errors.WithStack(err)
	}

	ok = true
	return
}

func QueryRightsTicketList(req protocol.ReqGetRightsTicketList) (res []protocol.ConferenceTicketsRightsInfo, total int64, err error) {
	var (
		list  []model.ConferenceRightsInterests
		list2 []model.ConferenceRightsTicket
	)
	if req.IsCn > 0 {
		list, total, err = db.QueryConferenceEnRightsInterestsList(nil, req.ConferenceId, 1, 1000)
		if err != nil {
			return nil, 0, err
		}
	} else {
		list, total, err = db.QueryConferenceRightsInterestsList(nil, req.ConferenceId, 1, 1000)
		if err != nil {
			return nil, 0, err
		}
	}

	list2, _, err = db.QueryConferenceRightsTicketList(nil, req.ConferenceId, req.TicketId)
	if err != nil {
		return nil, 0, err
	}

	var ticketsRightsList []protocol.ConferenceTicketsRightsInfo

	for _, interests := range list {
		var ticketsRights protocol.ConferenceTicketsRightsInfo
		ticketsRights.Id = interests.Id
		ticketsRights.ConferenceId = interests.ConferenceId
		ticketsRights.CnContent = interests.CnContent
		ticketsRights.EnContent = interests.EnContent
		ticketsRights.TicketId = req.TicketId

		for _, right := range list2 {
			if interests.Id == right.RightsId {
				ticketsRights.IsSelected = true
			}
		}
		ticketsRightsList = append(ticketsRightsList, ticketsRights)
	}

	return ticketsRightsList, total, err
}

func AdminSaveMeetingRightTicket(req protocol.ReqAdminSaveRightTicket) (resMsg string, err error) {
	if utils.NotEmpty(req.RightsId) {
		req.RightsIds = strings.Split(req.RightsId, ",")
	}
	err = db.AddMeetingTicketRightsList(nil, req.ConferenceId, req.IsCn, req.TicketId, req.RightsIds...)
	if err != nil {
		return "系统错误", errors.WithStack(err)
	}

	return "", err
}

func QueryQueryChannelSourceList(req protocol.ReqGetConferenceSourceList) (res []model.ChannelSource, err error) {
	dataList, err := rpc.GetMeetingChannelPlatformList()
	if err != nil {
		return nil, err
	}
	for _, data := range dataList {
		res = append(res, model.ChannelSource{
			Id:   data.PlatformId,
			Name: data.PlatformName,
		})
	}
	return res, err
}

func QueryConferenceRegisterList(req protocol.ReqGetConferenceRegisterList) (res []protocol.ResConferenceRegisterList, total int64, err error) {

	list, total, err := db.QueryConferenceRegisterList(nil, req)
	if err != nil {
		return nil, 0, err
	}
	var registerId []string
	for _, register := range list {
		registerId = append(registerId, strconv.FormatInt(register.Id, 10))
	}

	registerUserList, _, err := db.GetMeetingRegisterUserListIn(nil, registerId)
	if err != nil {
		return nil, 0, err
	}

	registerUserMap := make(map[int64]protocol.RespMeetingRegisterUserList) //用户行业信息
	for _, register := range registerUserList {
		registerUserMap[register.RegisterId] = register
	}

	registerList := []protocol.ResConferenceRegisterList{}
	for _, register := range list {
		fromMap := memory.GetMeetingFromNameMap(strconv.FormatInt(register.ConferenceId, 10) + "_" + register.FromId)

		registerId = append(registerId, strconv.FormatInt(register.Id, 10))
		registerInfo := protocol.ResConferenceRegisterList{
			ID:                 register.Id,
			ConferenceId:       register.ConferenceId,
			ConferenceName:     register.ConferenceName,
			FirstName:          register.FirstName,
			LastName:           register.LastName,
			Company:            register.Company,
			UserId:             register.UserId,
			Email:              register.Email,
			Mobile:             register.Mobile,
			InterestedMeetings: register.InterestedMeetings,
			OrderId:            register.OrderId,
			OrderStatus:        register.OrderStatus,
			OrderNum:           register.OrderNum,
			SourceID:           register.SourceID,
			SourceName:         register.SourceName,
			FromId:             fromMap.ChannelName,
			CreateTime:         register.CreateTime.Format(constant.DateTime),
			UserFirstName:      registerUserMap[register.Id].FirstName,
			UserLastName:       registerUserMap[register.Id].LastName,
			UserEmail:          registerUserMap[register.Id].Email,
			UserMobile:         registerUserMap[register.Id].Mobile,
			UserCompany:        registerUserMap[register.Id].Company,
			UserJobTitle:       registerUserMap[register.Id].JobTitle,

			MainProducts:   register.MainProducts,
			EnterpriseType: register.EnterpriseType,
		}
		registerList = append(registerList, registerInfo)
	}

	userCountList, _, err := db.GetMeetingRegisterUserCountListIn(nil, registerId)
	if err != nil {
		return nil, 0, err
	}

	registerNumMap := make(map[int64]int) //用户行业信息
	for _, registerUses := range userCountList {
		registerNumMap[registerUses.RegisterId] = registerUses.Count
	}

	for k, register := range registerList {
		registerList[k].RegisterNum = registerNumMap[register.ID]
	}

	return registerList, total, err
}

func QueryConferenceRegisterUserList(req protocol.ReqGetConferenceRegisterUserList) (res []model.ConferenceRegisterUser, total int64, err error) {
	list, total, err := db.QueryConferenceRegisterUserList(nil, req)
	if err != nil {
		return nil, 0, err
	}
	return list, total, err
}

// 导出报名信息列表
func ExportRegisterList(req protocol.ReqGetConferenceRegisterList) (bReader *bytes.Reader, err error) {

	req.Page, req.PageSize = 1, 10000 //重置分页数据
	list, _, err_ := db.QueryConferenceRegisterList(nil, req)
	if err_ != nil {
		_ = logger.Error("QueryHandBookSubscribeList:", err)
		return nil, err_
	}

	var registerId []string
	for _, register := range list {
		registerId = append(registerId, strconv.FormatInt(register.Id, 10))
	}

	var registerList []protocol.ResConferenceRegisterInfo
	for _, register := range list {
		orderStatus := ""
		if register.OrderStatus == 0 {
			orderStatus = "未创建订单"
		}
		if register.OrderStatus == 1 {
			orderStatus = "未支付"
		}
		if register.OrderStatus == 2 {
			orderStatus = "已完成"
		}

		registerInfo := protocol.ResConferenceRegisterInfo{
			ConferenceName: register.ConferenceName,
			SourceName:     register.SourceName,
			FromId:         register.FromId,
			FromName:       register.FromName,
			Name:           register.FirstName + " " + register.LastName,
			ConferenceID:   register.ConferenceId,
			UserId:         register.UserId,
			Email:          register.Email,
			Mobile:         register.Mobile,
			OrderId:        register.OrderId,
			OrderStatus:    orderStatus,
			OrderNum:       register.OrderNum,
			MainProducts:   register.MainProducts,
			EnterpriseType: register.EnterpriseType,
			CreateTime:     register.CreateTime.Format(constant.DateTime),
		}
		registerList = append(registerList, registerInfo)
	}

	var listItems []interface{}
	for _, v := range registerList {
		listItems = append(listItems, v)
	}
	bReader, err = utils.GenListExcel(listItems, func(fieldName string, value interface{}) interface{} {
		return value
	}, protocol.ResConferenceRegisterInfo{})
	if err != nil {
		return nil, err
	}
	return
}

func UserConferenceTicketPriceList(req protocol.ReqWebConferenceTicketList) (res protocol.ResUserTicketPriceList, err error) {

	var ticketsList []protocol.ConferenceTicketPriceInfo

	if req.IsCn > 0 {

		enList, err := db.UserConferenceTicketPriceList(nil, req.ConferenceId, 2)
		if err != nil {
			return res, errors.WithStack(err)
		}

		for _, ticket := range enList {
			ticketsList = append(ticketsList, protocol.ConferenceTicketPriceInfo{
				Id:                     ticket.Id,
				ConferenceId:           ticket.ConferenceId,
				CnName:                 ticket.CnName,
				EnName:                 ticket.EnName,
				EnServiceId:            ticket.EnServiceId,
				EnButtonName:           ticket.EnButtonName,
				EnButtonLink:           ticket.EnButtonLink,
				EnCurrencyUnit:         ticket.EnCurrencyUnit,
				EnRegistrationPageName: ticket.EnRegistrationPageName,
				EnIsDisplayed:          ticket.EnIsDisplayed,
				EnMaximum:              ticket.EnMaximum,
				EnSorting:              ticket.EnSorting,
				EnStandardFee:          ticket.EnStandardFee,
			})
		}
	} else {
		cnList, err := db.UserConferenceTicketPriceList(nil, req.ConferenceId, 1)
		if err != nil {
			return res, errors.WithStack(err)
		}

		for _, ticket := range cnList {
			ticketsList = append(ticketsList, protocol.ConferenceTicketPriceInfo{
				Id:                     ticket.Id,
				ConferenceId:           ticket.ConferenceId,
				CnName:                 ticket.CnName,
				EnName:                 ticket.EnName,
				CnServiceId:            ticket.CnServiceId,
				CnButtonName:           ticket.CnButtonName,
				CnButtonLink:           ticket.CnButtonLink,
				CnCurrencyUnit:         ticket.CnCurrencyUnit,
				CnRegistrationPageName: ticket.CnRegistrationPageName,
				CnIsDisplayed:          ticket.CnIsDisplayed,
				CnMaximum:              ticket.CnMaximum,
				CnSorting:              ticket.CnSorting,
				CnStandardFee:          ticket.CnStandardFee,
			})
		}
	}

	if req.IsCn > 0 {
		//if config.EnPriceTemplate == 0 {
		//	enRightsList, err := db.UserConferenceRightsInterestsList(nil, req.ConferenceId, 2)
		//	if err != nil {
		//		return res, errors.WithStack(err)
		//	}
		//
		//	if ticketsList != nil {
		//		return protocol.ResUserTicketPriceList{
		//			List:            ticketsList,
		//			EnRightsList:    enRightsList,
		//			CnPriceTemplate: config.CnPriceTemplate,
		//			EnPriceTemplate: config.EnPriceTemplate,
		//		}, nil
		//	} else {
		//		logger.Warnning("nil req:", req)
		//		return res, errors.WithStack(err)
		//	}
		//} else if config.EnPriceTemplate == 1 {
		interestsList, err := db.UserConferenceRightsInterestsList(nil, req.ConferenceId, 2)
		if err != nil {
			return res, errors.WithStack(err)
		}

		rightsList, err := db.UserConferenceRightsTicketList(nil, req.ConferenceId, 1)
		if err != nil {
			return res, errors.WithStack(err)
		}
		for k, ticket := range ticketsList {
			var rightsIds []string
			for _, rights := range rightsList {
				if ticket.Id == rights.TicketId {
					rightsIds = append(rightsIds, strconv.FormatInt(rights.RightsId, 10))
				}
			}
			join := strings.Join(rightsIds, ",")
			ticketsList[k].EnRightsIds = join
		}
		if ticketsList != nil {
			return protocol.ResUserTicketPriceList{
				List:            ticketsList,
				EnRightsList:    interestsList,
				CnPriceTemplate: 1,
				EnPriceTemplate: 1,
			}, nil
		} else {
			logger.Warnning("nil req:", req)
			return res, nil
		}
		//}
	} else {

		//if config.CnPriceTemplate == 0 {
		//	rightsList, err := db.UserConferenceRightsInterestsList(nil, req.ConferenceId, 1)
		//	if err != nil {
		//		return res, errors.WithStack(err)
		//	}
		//
		//	if ticketsList != nil {
		//		return protocol.ResUserTicketPriceList{
		//			List:            ticketsList,
		//			RightsList:      rightsList,
		//			CnPriceTemplate: config.CnPriceTemplate,
		//			EnPriceTemplate: config.EnPriceTemplate,
		//		}, nil
		//	} else {
		//		logger.Warnning("nil req:", req)
		//		return res, errors.WithStack(err)
		//	}
		//} else if config.CnPriceTemplate == 1 {
		interestsList, err := db.UserConferenceRightsInterestsList(nil, req.ConferenceId, 1)
		if err != nil {
			return res, errors.WithStack(err)
		}

		rightsList, err := db.UserConferenceRightsTicketList(nil, req.ConferenceId, 0)
		if err != nil {
			return res, errors.WithStack(err)
		}
		for k, ticket := range ticketsList {
			var rightsIds []string
			for _, rights := range rightsList {
				if ticket.Id == rights.TicketId {
					rightsIds = append(rightsIds, strconv.FormatInt(rights.RightsId, 10))
				}
			}
			join := strings.Join(rightsIds, ",")
			ticketsList[k].CnRightsIds = join
		}
		if ticketsList != nil {
			return protocol.ResUserTicketPriceList{
				List:            ticketsList,
				RightsList:      interestsList,
				CnPriceTemplate: 1,
				EnPriceTemplate: 1,
			}, nil
		} else {
			logger.Warnning("nil req:", req)
			return res, nil
		}
		//}

	}

	return res, err
}

// 用户报名
func UserAddConferenceRegister(req protocol.ReqAddConferenceRegister) (res protocol.RespConferenceRegisterInfo, resMsg string, err error) {
	var (
		previousInfo model.ConferenceRegister
		reqAPiSign   protocol.ReqAddAPiSignAddEn
		ok           bool
		WxUrl2       string
		ReqLog       []byte
		RespLog      []byte
	)

	tx := corecontext.SrvContext.Orm.Begin()
	if tx == nil {
		return res, "", errors.New("创建事务失败")
	}
	defer func() {
		if !ok {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	if utils.NotEmpty(req.SourceID) {
		decodedStr, _ := url.QueryUnescape(req.SourceID)
		if string(decodedStr) == "会展首页-会展列表" {
			req.SourceID = "32"
		}
	}
	if utils.IsEmpty(req.FromId) && utils.IsEmpty(req.SourceID) {
		req.SourceID = "32"
	}

	info, err := db.QueryConferenceInfo(nil, req.ConferenceId)
	if err != nil {
		return res, "系统错误", errors.WithStack(err)
	}
	req.ConferenceName = info.CnName

	if utils.NotEmpty(req.SourceID) {
		nameMap := memory.GetChannelSourceNameMap(req.SourceID, true)
		if utils.NotEmpty(nameMap) {
			req.SourceName = nameMap
		}
	}

	fromName := ""
	if utils.NotEmpty(req.FromId) {
		fromMap := memory.GetMeetingFromNameMap(strconv.FormatInt(req.ConferenceId, 10) + "_" + req.FromId)
		if utils.IsEmpty(fromMap.ChannelName) {
			req.FromId = ""
		} else {
			fromName = fromMap.ChannelName
		}
	}

	reqAPiSign.MeetingNo = info.MeetingSysId
	if utils.NotEmpty(info.QwxUrl) {
		WxUrl2 = info.QwxUrl
	} else if utils.IsTestEnv() {
		WxUrl2 = weChat.WxUrl
	} else {
		WxUrl2 = weChat.WxUrl2
	}

	if req.Id <= 0 {
		if utils.IsEmpty(req.Cellphone) {
			return res, "Required fields cannot be empty", errors.New("必填字段不能为空")
		}

		if utils.NotEmpty(reqAPiSign.MeetingNo) {

			reqAPiSign.PlatformId = req.SourceID

			if utils.IsEmpty(req.SourceName) {
				reqAPiSign.PlatformId = ""
			}

			logger.Info("++++++=======MeetingNo", reqAPiSign.MeetingNo)
			reqAPiSign.FirstName = req.FirstName
			reqAPiSign.LastName = req.LastName
			reqAPiSign.Email = req.Email
			reqAPiSign.Mobile = req.Cellphone
			reqAPiSign.Company = req.Company
			reqAPiSign.FromId = req.FromId
			reqAPiSign.CnOrEn = req.CnOrEn
			reqAPiSign.Identity = "1"
			reqAPiSign.OrderStatus = "0"

			reqAPiSign.MainProduct = req.MainProducts
			reqAPiSign.CompanyType = req.EnterpriseType
			m, err := rpc.AddAPiSignUpAddEn(reqAPiSign)
			if err != nil {
				return res, "系统错误", errors.WithStack(err)
			}
			if m.Msg != "success" {
				return res, m.Msg, errors.New(m.Msg)
			}
			ReqLog, err = json.Marshal(reqAPiSign)
			RespLog, err = json.Marshal(m)
			RegisterLog := model.RegisterLog{
				RegisterId: req.Id,
				ReqLog:     string(ReqLog),
				RespLog:    string(RespLog),
				LogType:    2,
				CreateTime: time.Now(),
			}

			err = db.AddMeetingRegisterLog(nil, &RegisterLog)
			if err != nil {
				return res, "系统错误", errors.WithStack(err)
			}
		}
		previousInfo = model.ConferenceRegister{
			Id:                 req.Id,
			ConferenceId:       req.ConferenceId,
			ConferenceName:     req.ConferenceName,
			FirstName:          req.FirstName,
			LastName:           req.LastName,
			Email:              req.Email,
			Company:            req.Company,
			Mobile:             req.Cellphone,
			InterestedMeetings: req.InterestMeetings,
			SourceName:         req.SourceName,
			CnOrEn:             req.CnOrEn,
			SourceID:           req.SourceID,
			FromId:             req.FromId,
			Deleted:            0,
			UpdateTime:         time.Now(),
			CreateTime:         time.Now(),
			MainProducts:       req.MainProducts,
			EnterpriseType:     req.EnterpriseType,
		}

		err = db.SaveConferenceRegister(tx, &previousInfo)
		if err != nil {
			return res, "系统错误", errors.WithStack(err)
		}

		logger.Info("++++++=======previousInfo", previousInfo.Id)

		err = db.AddConferenceRegisterUserList(tx, previousInfo.Id, append(req.RegisterUser, protocol.ReqConferenceRegisterUser{
			FirstName: req.FirstName,
			LastName:  req.LastName,
			Email:     req.Email,
			Mobile:    req.Cellphone,
			Company:   req.Company,
			JobTitle:  req.JobTitle,
		}))
		if err != nil {
			return res, "系统错误", errors.WithStack(err)
		}

		if utils.IsEmpty(req.LastName) {
			markdown2 := fmt.Sprintf(`
				## 申请参会报名通知
				>会议名：%v
				>购票人姓名：%s 
				>购票人手机号：%v
				>购票人邮箱：%v
				>购票人公司：%v
				>渠道：%v
				>时间：%s`, info.CnName, req.FirstName, req.Cellphone, req.Email, req.Company, fromName, utils.GetCurDateTime())

			cardMsg := feishu.NewMdCardMsg("申请参会报名通知", "blue")
			cardMsg.AppendMd(`>会议名：%s
>购票人姓名：%s 
>购票人手机号：%v
>购票人邮箱：%v
>购票人公司：%v
>渠道：%v
>时间: %s`, info.CnName, req.FirstName, req.Cellphone, req.Email, req.Company, fromName, utils.GetCurDateTime())

			if strings.Contains(WxUrl2, "open.feishu.cn") {
				weChat.Send2(cardMsg, WxUrl2)
			} else {
				weChat.Send(markdown2, WxUrl2)
			}

		} else {
			markdown2 := fmt.Sprintf(`
				## 申请参会报名通知
				>会议名：%s
				>购票人first name：%s 
				>购票人last name：%s 
				>购票人手机号：%v
				>购票人邮箱：%v
				>购票人公司：%v
				>渠道：%v
				>时间：%s`, info.CnName, req.FirstName, req.LastName, req.Cellphone, req.Email, req.Company, fromName, utils.GetCurDateTime())

			cardMsg := feishu.NewMdCardMsg("申请参会报名通知", "blue")
			cardMsg.AppendMd(`>会议名：%s
>购票人first name：%s 
>购票人last name：%s 
>购票人手机号：%v
>购票人邮箱：%v
>购票人公司：%v
>渠道：%v
>时间: %s`, info.CnName, req.FirstName, req.LastName, req.Cellphone, req.Email, req.Company, fromName, utils.GetCurDateTime())

			if strings.Contains(WxUrl2, "open.feishu.cn") {
				weChat.Send2(cardMsg, WxUrl2)
			} else {
				weChat.Send(markdown2, WxUrl2)
			}
		}

	} else {
		previousInfo, err = db.GetConferenceRegisterInfo(nil, req.Id)
		if err != nil {
			return res, "系统错误", errors.WithStack(err)
		}
		previousInfo.CnOrEn = req.CnOrEn
		err = db.SaveConferenceRegister(tx, &previousInfo)
		if err != nil {
			return res, "系统错误", errors.WithStack(err)
		}
	}

	if len(req.RegisterUser) > 0 {

		previousInfo.Id = req.Id

		if req.Id > 0 {
			_, total, err := db.GetMeetingRegisterUserList(nil, req.Id)
			if err != nil {
				return res, "系统错误", errors.WithStack(err)
			}
			if total > 0 {
				err = db.UpdateConferenceRegisterUser(tx, req.Id, constant.Yes, "")
				if err != nil {
					return res, "系统错误", errors.WithStack(err)
				}
			}
		}

		err = db.AddConferenceRegisterUserList(tx, req.Id, req.RegisterUser)
		if err != nil {
			return res, "系统错误", errors.WithStack(err)
		}

		if utils.NotEmpty(reqAPiSign.MeetingNo) {
			if utils.IsEmpty(req.FromId) {
				confInfo, _ := db.GetConferenceRegisterInfo(nil, req.Id)
				req.FromId = confInfo.FromId
			}

			for _, registerUser := range req.RegisterUser {

				logger.Info("++++++=======MeetingNo", reqAPiSign.MeetingNo)
				reqAPiSign.UserId = registerUser.UserId
				reqAPiSign.FirstName = registerUser.FirstName
				reqAPiSign.LastName = registerUser.LastName
				reqAPiSign.Email = registerUser.Email
				reqAPiSign.Mobile = registerUser.Mobile
				reqAPiSign.Company = registerUser.Company
				reqAPiSign.JobTitle = registerUser.JobTitle
				reqAPiSign.FromId = req.FromId
				reqAPiSign.PlatformId = req.SourceID
				reqAPiSign.Identity = "1"
				reqAPiSign.CnOrEn = req.CnOrEn
				reqAPiSign.OrderStatus = "0"

				reqAPiSign.MainProduct = req.MainProducts
				reqAPiSign.CompanyType = req.EnterpriseType
				m, err := rpc.AddAPiSignUpAddEn(reqAPiSign)
				if err != nil {
					return res, "系统错误", errors.WithStack(err)
				}
				if m.Msg != "success" {
					return res, m.Msg, errors.New(m.Msg)
				}

				ReqLog, err = json.Marshal(reqAPiSign)
				RespLog, err = json.Marshal(m)
				RegisterLog := model.RegisterLog{
					RegisterId: req.Id,
					ReqLog:     string(ReqLog),
					RespLog:    string(RespLog),
					LogType:    2,
					CreateTime: time.Now(),
				}

				err = db.AddMeetingRegisterLog(nil, &RegisterLog)
				if err != nil {
					return res, "系统错误", errors.WithStack(err)
				}
			}
		}

	}

	//if utils.NotEmpty(req.BdVid) {
	//	rpc.UploadBdVid(req.BdVid, info.BdVidToken, 3)
	//}

	ok = true

	res.Id = previousInfo.Id
	res.FirstName = previousInfo.FirstName
	res.LastName = previousInfo.LastName
	res.Email = previousInfo.Email

	return res, "", nil
}

func GetUserConferenceRegisterInfo(req protocol.ReqGetConferenceRegisterUserList) (protocol.ResConferenceRegister, error) {
	if req.RegisterId <= 0 {
		return protocol.ResConferenceRegister{}, nil
	}
	company, err := db.GetConferenceRegisterInfo(nil, req.RegisterId)
	if err != nil {
		return protocol.ResConferenceRegister{}, err
	}

	return protocol.ResConferenceRegister{
		ConferenceName: company.ConferenceName,
		SourceName:     company.SourceName,
		FirstName:      company.FirstName + " " + company.LastName,
		ConferenceId:   company.ConferenceId,
		UserId:         company.UserId,
		Company:        company.Company,
		Email:          company.Email,
		Mobile:         company.Mobile,
		CreateTime:     company.CreateTime.Format(constant.DateTime),
	}, err
}

func GetServiceMeetingTicketPrice(serviceId string) (resp protocol.RespService, err error) {

	var m protocol.Message

	url := corecontext.Config().RPC.OrderCenter + "/v2/service_list?app_name=&service_ids=" + serviceId
	err = utils.HttpGetCall(url, nil, nil, &m)
	if err != nil {
		return resp, err
	}

	if err = json.Unmarshal(*m.Data, &resp); err != nil {
		logger.Warnning(err.Error())
		return resp, err
	}
	if m.Code != 0 {
		return resp, err
	}
	return resp, nil
}

func UserForumConfigList(req protocol.ReqUserForumConfigList) (res protocol.ResUserForumConfigList, err error) {

	list, _, err := db.UserForumConfigList(nil, req.ConferenceId, req.Type)
	if err != nil {
		return res, err
	}

	res.List = list
	return res, nil
}

func GetConferenceFromConfigList(req protocol.ReqGetIntroductionInfo) (res protocol.ResConferenceFromConfigList, err error) {

	info, err := db.QueryConferenceInfo(nil, req.ConferenceId)
	if err != nil {
		return res, err
	}

	list, err := rpc.GetMeetingChannelList(info.MeetingSysId)
	if err != nil {
		return res, err
	}

	var channelList []model.ConferenceFromConfig
	for _, channe := range list {
		channelList = append(channelList, model.ConferenceFromConfig{
			FromId:   channe.ChannelId,
			FromName: channe.ChannelName,
		})
	}
	res.List = channelList

	return res, nil
}

func GetConferenceFromConfigListV2(req protocol.ReqGetIntroductionInfo) (res []model.ConferenceFromConfig, err error) {

	info, err := db.QueryConferenceInfo(nil, req.ConferenceId)
	if err != nil {
		return res, err
	}
	if info.ID == 0 {
		_ = logger.Error("不存在此展会")
		return nil, errors.New("不存在此展会")
	}

	switch req.Type {
	case 1:
		res, err = getAttendingChannelListV2(req.ConferenceName)
	case 2:
		res, err = getSponsorChannelListV2(req.ConferenceName)
	case 3:
		res, err = getRegisterChannelListV2(protocol.ReqGetConferenceRegisterList{ConferenceName: req.ConferenceName, ReqPage: protocol.ReqPage{
			Page:     1,
			PageSize: 10000,
		}})
	case 4:
		res, err = getCNFreeVisitorListV2(req.ConferenceName)
	case 5:
		res, err = getENFreeVisitorListV2(req.ConferenceName)
	case 6:
		res, err = getMediaRegistrationListV2(protocol.ReqGetMediaRegistrationList{ConferenceName: req.ConferenceName,
			Language: "en", ReqPage: protocol.ReqPage{
				Page:     1,
				PageSize: 10000,
			}})
	case 7:
		res, err = getMediaRegistrationListV2(protocol.ReqGetMediaRegistrationList{ConferenceName: req.ConferenceName,
			Language: "cn", ReqPage: protocol.ReqPage{
				Page:     1,
				PageSize: 10000,
			}})
	}

	// Remove empty entries
	filtered := make([]model.ConferenceFromConfig, 0)
	for _, channel := range res {
		if channel.FromId != "" && channel.FromName != "" {
			filtered = append(filtered, channel)
		}
	}

	return filtered, err
}

func getMediaRegistrationList(req protocol.ReqGetMediaRegistrationList) ([]string, error) {
	dataLi, _, err := db.QueryMediaRegistrationList(nil, req)
	if err != nil {
		return nil, err
	}
	fromMap := make(map[string]string, 0)
	for _, data := range dataLi {
		fromMap[data.FromName] = data.FromName
	}
	fromList := make([]string, 0)
	for k, _ := range fromMap {
		fromList = append(fromList, k)
	}
	return fromList, nil
}

func getMediaRegistrationListV2(req protocol.ReqGetMediaRegistrationList) ([]model.ConferenceFromConfig, error) {
	dataLi, _, err := db.QueryMediaRegistrationList(nil, req)
	if err != nil {
		return nil, err
	}
	fromMap := make(map[string]model.ConferenceFromConfig, 0)
	for _, data := range dataLi {
		key := data.FromId + "_" + data.FromName
		fromMap[key] = model.ConferenceFromConfig{
			Id:       data.FromId,
			FromId:   data.FromId,
			FromName: data.FromName,
		}
	}
	fromList := make([]model.ConferenceFromConfig, 0)
	for _, v := range fromMap {
		fromList = append(fromList, v)
	}
	return fromList, nil
}

func getENFreeVisitorList(conferenceId int64) ([]string, error) {
	dataLi, _, err := db.GetEnFreeVisitorInfo(nil, conferenceId, "", "", "", "", 0, "", "", "", 0, 0, "")
	if err != nil {
		logger.Error(fmt.Sprintf("获取英文免费观众信息失败：%v", err))
		return nil, err
	}
	fromMap := make(map[string]string, 0)
	for _, data := range dataLi {
		fromMap[data.FromName] = data.FromName
	}
	fromList := make([]string, 0)
	for k, _ := range fromMap {
		fromList = append(fromList, k)
	}
	return fromList, nil
}

func getENFreeVisitorListV2(confenreceName string) ([]model.ConferenceFromConfig, error) {
	dataLi, _, err := db.GetEnFreeVisitorInfo(nil, 0, "", "", "", "", 0, "", "", "", 0, 0, confenreceName)
	if err != nil {
		logger.Error(fmt.Sprintf("获取英文免费观众信息失败：%v", err))
		return nil, err
	}
	fromMap := make(map[string]model.ConferenceFromConfig, 0)
	for _, data := range dataLi {
		key := data.FromID + "_" + data.FromName
		fromMap[key] = model.ConferenceFromConfig{
			Id:       data.FromID,
			FromId:   data.FromID,
			FromName: data.FromName,
		}
	}
	fromList := make([]model.ConferenceFromConfig, 0)
	for _, v := range fromMap {
		fromList = append(fromList, v)
	}
	return fromList, nil
}

func getCNFreeVisitorList(conferenceId int64) ([]string, error) {
	dataLi, _, err := db.GetCnFreeVisitorInfo(nil, conferenceId, "", "", "", "", 0, "", "", "", 0, 0, "")
	if err != nil {
		logger.Error(fmt.Sprintf("获取中文免费观众信息失败：%v", err))
		return nil, err
	}
	fromMap := make(map[string]string, 0)
	for _, data := range dataLi {
		fromMap[data.FromName] = data.FromName
	}
	fromList := make([]string, 0)
	for k, _ := range fromMap {
		fromList = append(fromList, k)
	}
	return fromList, nil
}

func getCNFreeVisitorListV2(conferenceName string) ([]model.ConferenceFromConfig, error) {
	dataLi, _, err := db.GetCnFreeVisitorInfo(nil, 0, "", "", "", "", 0, "", "", "", 0, 0, conferenceName)
	if err != nil {
		logger.Error(fmt.Sprintf("获取中文免费观众信息失败：%v", err))
		return nil, err
	}
	fromMap := make(map[string]model.ConferenceFromConfig, 0)
	for _, data := range dataLi {
		key := data.FromID + "_" + data.FromName
		fromMap[key] = model.ConferenceFromConfig{
			Id:       data.FromID,
			FromId:   data.FromID,
			FromName: data.FromName,
		}
	}
	fromList := make([]model.ConferenceFromConfig, 0)
	for _, v := range fromMap {
		fromList = append(fromList, v)
	}
	return fromList, nil
}

func getRegisterChannelList(req protocol.ReqGetConferenceRegisterList) ([]string, error) {
	dataLi, _, err := db.QueryConferenceRegisterList(nil, req)
	if err != nil {
		return nil, err
	}
	fromMap := make(map[string]string, 0)
	for _, data := range dataLi {
		fromMap[data.FromName] = data.FromName
	}
	fromList := make([]string, 0)
	for k, _ := range fromMap {
		fromList = append(fromList, k)
	}
	return fromList, nil
}

func getSponsorChannelList(conferenceId int64) ([]string, error) {
	_, dataLi, err := db.QueryConferenceClueList(nil, conferenceId, 0, "", "", "", "", "", 1, 0, 0, false, nil, "", "")
	if err != nil {
		_ = logger.Error("GetConferenceList:", err)
		return nil, errors.WithStack(err)
	}
	fromMap := make(map[string]string, 0)
	for _, data := range dataLi {
		fromMap[data.FromName] = data.FromName
	}
	fromList := make([]string, 0)
	for k, _ := range fromMap {
		fromList = append(fromList, k)
	}
	return fromList, nil
}

func getSponsorChannelListV2(conferenceName string) ([]model.ConferenceFromConfig, error) {
	_, dataLi, err := db.QueryConferenceClueList(nil, 0, 0, "", "", "", "", "", 1, 0, 0, false, nil, "", conferenceName)
	if err != nil {
		_ = logger.Error("GetConferenceList:", err)
		return nil, errors.WithStack(err)
	}
	fromMap := make(map[string]model.ConferenceFromConfig, 0)
	for _, data := range dataLi {
		key := data.FromId + "_" + data.FromName
		fromMap[key] = model.ConferenceFromConfig{
			Id:       data.FromId,
			FromId:   data.FromId,
			FromName: data.FromName,
		}
	}
	fromList := make([]model.ConferenceFromConfig, 0)
	for _, v := range fromMap {
		fromList = append(fromList, v)
	}
	return fromList, nil
}

func getRegisterChannelListV2(req protocol.ReqGetConferenceRegisterList) ([]model.ConferenceFromConfig, error) {
	dataLi, _, err := db.QueryConferenceRegisterList(nil, req)
	if err != nil {
		return nil, err
	}
	fromMap := make(map[string]model.ConferenceFromConfig, 0)
	for _, data := range dataLi {
		key := data.FromId + "_" + data.FromName
		fromMap[key] = model.ConferenceFromConfig{
			Id:       data.FromId,
			FromId:   data.FromId,
			FromName: data.FromName,
		}
	}
	fromList := make([]model.ConferenceFromConfig, 0)
	for _, v := range fromMap {
		fromList = append(fromList, v)
	}
	return fromList, nil
}

func getAttendingChannelList(conferenceId int64) ([]string, error) {
	_, dataLi, err := db.QueryConferenceClueList(nil, conferenceId, 0, "", "", "", "", "", 0, 0, 0, false, nil, "", "")
	if err != nil {
		_ = logger.Error("GetConferenceList:", err)
		return nil, errors.WithStack(err)
	}

	fromMap := make(map[string]string, 0)
	for _, data := range dataLi {
		fromMap[data.FromName] = data.FromName
	}
	fromList := make([]string, 0)
	for k, _ := range fromMap {
		fromList = append(fromList, k)
	}
	return fromList, nil
}

func getAttendingChannelListV2(conferenceName string) ([]model.ConferenceFromConfig, error) {
	_, dataLi, err := db.QueryConferenceClueList(nil, 0, 0, "", "", "", "", "", 0, 0, 0, false, nil, "", conferenceName)
	if err != nil {
		_ = logger.Error("GetConferenceList:", err)
		return nil, errors.WithStack(err)
	}

	fromMap := make(map[string]model.ConferenceFromConfig, 0)
	for _, data := range dataLi {
		key := data.FromId + "_" + data.FromName
		fromMap[key] = model.ConferenceFromConfig{
			Id:       data.FromId,
			FromId:   data.FromId,
			FromName: data.FromName,
		}
	}
	fromList := make([]model.ConferenceFromConfig, 0)
	for _, v := range fromMap {
		fromList = append(fromList, v)
	}
	return fromList, nil
}
