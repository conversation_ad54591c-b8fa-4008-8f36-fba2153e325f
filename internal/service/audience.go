package service

import (
	"bytes"
	"conferencecenter/internal/constant"
	"conferencecenter/internal/db"
	"conferencecenter/internal/memory"
	"conferencecenter/internal/model"
	"conferencecenter/internal/pkg/utils"
	"conferencecenter/internal/protocol"
	"conferencecenter/internal/rpc"
	"conferencecenter/internal/weChat"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"os"
	"sort"
	"strconv"
	"strings"
	"time"

	"git.code.tencent.com/smmit/smmbase/excel"
	"git.code.tencent.com/smmit/smmbase/exception"
	"git.code.tencent.com/smmit/smmbase/logger"
	"git.code.tencent.com/smmit/smmbase/qiniu"
	"git.code.tencent.com/smmit/smmbase/webhook/feishu"
	"github.com/pkg/errors"
	"github.com/skip2/go-qrcode"
	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
)

func RecordAudiencePreRegister(req protocol.ReqAudiencePreRegister) error {
	register, err := db.GetAudiencePreRegister(nil, req.ConferenceId)
	if err != nil && err != gorm.ErrRecordNotFound {
		return err
	}

	if register.Id > 0 {
		register.ConferenceId = req.ConferenceId
		register.CnTitle = req.CnTitle
		register.EnTitle = req.EnTitle
		register.CnHandbookName = req.CnHandbookName
		register.EnHandbookName = req.EnHandbookName
		register.CnUrl = req.CnUrl
		register.EnUrl = req.EnUrl
		register.UpdateUser = req.OpUser
		err = db.CreateAudiencePreRegister(nil, register)
		if err != nil {
			return err
		}
	} else {
		data := model.AudiencePreRegistry{
			CommonModel: model.CommonModel{
				CreateUser: req.OpUser,
				UpdateUser: req.OpUser,
			},
			Id:             req.Id,
			ConferenceId:   req.ConferenceId,
			CnTitle:        req.CnTitle,
			EnTitle:        req.EnTitle,
			CnHandbookName: req.CnHandbookName,
			EnHandbookName: req.EnHandbookName,
			CnUrl:          req.CnUrl,
			EnUrl:          req.EnUrl,
		}
		err = db.CreateAudiencePreRegister(nil, data)
		if err != nil {
			return err
		}
	}

	return nil
}

func RecordAudienceVisitingValue(req protocol.ReqAudienceVisitingValue) error {

	if utils.IsEmpty(req.CnVisitingValue) {
		return errors.New("中文参观价值不能为空")
	}

	registry, err := db.GetAudiencePreRegister(nil, req.ConferenceId)
	if err != nil && err != gorm.ErrRecordNotFound {
		return err
	}
	registry.ConferenceId = req.ConferenceId
	registry.CnVisitingValue = req.CnVisitingValue
	registry.EnVisitingValue = req.EnVisitingValue

	err = db.CreateAudiencePreRegister(nil, registry)
	if err != nil {
		return err
	}
	return nil
}

func GetAudiencePreRegister(conferenceId int64) (res protocol.ResAudiencePreRegister, err error) {
	registry, err := db.GetAudiencePreRegister(nil, conferenceId)
	if err != nil && err != gorm.ErrRecordNotFound {
		return res, err
	}
	return protocol.ResAudiencePreRegister{
		ConferenceId:    registry.ConferenceId,
		CnTitle:         registry.CnTitle,
		EnTitle:         registry.EnTitle,
		CnHandbookName:  registry.CnHandbookName,
		EnHandbookName:  registry.EnHandbookName,
		CnVisitingValue: registry.CnVisitingValue,
		EnVisitingValue: registry.EnVisitingValue,
		CnUrl:           registry.CnUrl,
		EnUrl:           registry.EnUrl,
	}, nil
}

func ResAudienceVisitingInfo(conferenceId int64) (res protocol.ResAudienceVisitingInfo, err error) {
	registry, err := db.GetAudiencePreRegister(nil, conferenceId)
	if err != nil && err != gorm.ErrRecordNotFound {
		return res, err
	}
	return protocol.ResAudienceVisitingInfo{
		ConferenceId:    registry.ConferenceId,
		CnVisitingValue: registry.CnVisitingValue,
		EnVisitingValue: registry.EnVisitingValue,
	}, nil
}

func RecordCompany(management protocol.ReqCompanyManagement) error {
	company := model.AudienceConferenceCompany{
		CommonModel: model.CommonModel{
			CreateUser: management.OpUser,
		},
		ConferenceId:            management.ConferenceId,
		CnConferenceCompany:     management.CnConferenceCompany,
		EnConferenceCompany:     management.EnConferenceCompany,
		CnBoothNumber:           management.CnBoothNumber,
		EnBoothNumber:           management.EnBoothNumber,
		CnUrl:                   management.CnUrl,
		EnUrl:                   management.EnUrl,
		Logo:                    management.Logo,
		CnExhibitorVideoLink:    management.CnExhibitorVideoLink,
		CnExhibitorVideoCover:   management.CnExhibitorVideoCover,
		CnExhibitorNewsID:       management.CnExhibitorNewsID,
		CnExhibitorIntroduction: management.CnExhibitorIntroduction,
		CnExhibitorVenueId:      management.CnExhibitorVenueID,
		Sort:                    management.Sort,
		Year:                    management.Year,
	}
	tx := db.OrmDB(nil)
	err := tx.Transaction(func(tx *gorm.DB) error {

		err := db.RecordAudienceConferenceCompany(tx, &company)
		if err != nil {
			return err
		}
		return nil
		//return recordFirstLetter(tx, management, company.ID)
	})
	if err != nil {
		return err
	}
	return nil
}

func recordFirstLetter(tx *gorm.DB, management protocol.ReqCompanyManagement, companyId int64) error {
	CnLetterList := utils.TransStringFirstString(management.CnConferenceCompany)
	EnLetterList := utils.TransStringFirstString(management.EnConferenceCompany)
	letterList := make([]model.ConferenceCompanyFirstLetter, 0)
	for _, letter := range CnLetterList {
		firstLetter := model.ConferenceCompanyFirstLetter{
			CommonModel: model.CommonModel{
				CreateUser: management.OpUser,
			},
			AudienceCompanyId: companyId,
			FirstLetter:       letter,
			IsCn:              true,
		}
		letterList = append(letterList, firstLetter)
	}
	for _, letter := range EnLetterList {
		firstLetter := model.ConferenceCompanyFirstLetter{
			CommonModel:       model.CommonModel{},
			AudienceCompanyId: companyId,
			FirstLetter:       letter,
			IsCn:              false,
		}
		letterList = append(letterList, firstLetter)
	}
	err := db.RecordFirstLetter(tx, letterList)
	if err != nil {
		return err
	}
	return nil
}

func GetCompanyManagement(req protocol.PageReqConferenceId) (companyList []protocol.CompanyManagement, total int64, err error) {
	dbCompanyList, total, err := db.GetAudienceConferenceCompany(nil, req.Id, req.Year, req.Page, req.PageSize)
	if err != nil {
		return
	}
	for _, dbCompany := range dbCompanyList {
		companyManagement := protocol.CompanyManagement{
			Id:                      dbCompany.Id,
			ConferenceId:            dbCompany.ConferenceId,
			CnConferenceCompany:     dbCompany.CnConferenceCompany,
			EnConferenceCompany:     dbCompany.EnConferenceCompany,
			CnBoothNumber:           dbCompany.CnBoothNumber,
			EnBoothNumber:           dbCompany.EnBoothNumber,
			CnUrl:                   dbCompany.CnUrl,
			EnUrl:                   dbCompany.EnUrl,
			Logo:                    dbCompany.Logo,
			CnExhibitorVideoLink:    dbCompany.CnExhibitorVideoLink,
			CnExhibitorVideoCover:   dbCompany.CnExhibitorVideoCover,
			CnExhibitorNewsID:       dbCompany.CnExhibitorNewsID,
			CnExhibitorIntroduction: dbCompany.CnExhibitorIntroduction,
			CnExhibitorVenueId:      dbCompany.CnExhibitorVenueId,
			Sort:                    fmt.Sprintf("%.2f", dbCompany.Sort),
			Year:                    dbCompany.Year,
		}
		companyList = append(companyList, companyManagement)
	}
	return
}

func EditAudienceConferenceCompany(req protocol.ReqCompanyManagement) error {
	if req.Id <= 0 {
		return errors.New("展商id错误")
	}
	tx := db.OrmDB(nil)
	err := tx.Transaction(func(tx *gorm.DB) error {
		err := db.DeleteFirstLetter(tx, req.Id)
		if err != nil {
			return err
		}
		company := model.AudienceConferenceCompany{
			CommonModel: model.CommonModel{
				Id:         req.Id,
				UpdateUser: req.OpUser,
			},
			ConferenceId:            req.ConferenceId,
			CnConferenceCompany:     req.CnConferenceCompany,
			EnConferenceCompany:     req.EnConferenceCompany,
			CnBoothNumber:           req.CnBoothNumber,
			EnBoothNumber:           req.EnBoothNumber,
			CnUrl:                   req.CnUrl,
			EnUrl:                   req.EnUrl,
			Logo:                    req.Logo,
			CnExhibitorVideoLink:    req.CnExhibitorVideoLink,
			CnExhibitorVideoCover:   req.CnExhibitorVideoCover,
			CnExhibitorNewsID:       req.CnExhibitorNewsID,
			CnExhibitorIntroduction: req.CnExhibitorIntroduction,
			CnExhibitorVenueId:      req.CnExhibitorVenueID,
			Sort:                    req.Sort,
			Year:                    req.Year,
		}
		err = db.EditAudienceConferenceCompany(tx, company)
		if err != nil {
			return err
		}
		err = recordFirstLetter(tx, req, req.Id)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}

	return nil
}

func DeleteAudienceConferenceCompany(req protocol.ReqCompanyId, user string) error {
	tx := db.OrmDB(nil).Set("user", user).Set("id", req.Id)
	err := db.DeleteAudienceConferenceCompany(tx, req.Id)
	return err
}

func GetSingleAudienceConferenceCompany(id int64) (protocol.CompanyManagement, error) {
	if id <= 0 {
		return protocol.CompanyManagement{}, nil
	}
	company, err := db.GetSingleAudienceConferenceCompany(nil, id)
	if err != nil && gorm.ErrRecordNotFound == err {
		return protocol.CompanyManagement{}, nil
	}
	return protocol.CompanyManagement{
		Id:                      company.Id,
		ConferenceId:            company.ConferenceId,
		CnConferenceCompany:     company.CnConferenceCompany,
		EnConferenceCompany:     company.EnConferenceCompany,
		CnBoothNumber:           company.CnBoothNumber,
		EnBoothNumber:           company.EnBoothNumber,
		CnUrl:                   company.CnUrl,
		EnUrl:                   company.EnUrl,
		Logo:                    company.Logo,
		CnExhibitorVideoLink:    company.CnExhibitorVideoLink,
		CnExhibitorVideoCover:   company.CnExhibitorVideoCover,
		CnExhibitorNewsID:       company.CnExhibitorNewsID,
		CnExhibitorIntroduction: company.CnExhibitorIntroduction,
		CnExhibitorVenueId:      company.CnExhibitorVenueId,
		Sort:                    company.Sort,
		Year:                    company.Year,
	}, err
}

func GetCompanyDirectories(req protocol.ReqCompanyDirectories) (directories []protocol.CompanyDirectories, total int64, err error) {
	if req.VenueId == 0 {
		venueIds, err := db.GetVenueIdByKeyWord(nil, req.KeyWord, req.IsCn, req.ConferenceId)
		if err != nil {
			logger.Error(fmt.Sprintf("查询场馆id数量出错：Err->%v", err))
			return nil, 0, err
		}
		if len(venueIds) > 1 {
			req.VenueId = venueIds[0]
		}
	}

	list, total, err := db.GetAudienceConferenceCompanyByKeyWordFirstLetter(nil, req.KeyWord, req.IsCn, req.Page, req.PageSize, req.ConferenceId, req.VenueId, req.Year)
	if len(list) == 0 {
		return nil, 0, nil
	}
	companyIdList := make([]int64, 0)
	for _, dto := range list {
		companyIdList = append(companyIdList, dto.Id)
	}
	productionModelList, _, err := db.GetProductionByCompanyId(nil, companyIdList, 0, 0)
	if err != nil {
		logger.Error(fmt.Sprintf("查询产品信息失败：Err->%v;companyIdList->%v", err, companyIdList))
		return nil, 0, err
	}
	companyProductionMap := make(map[int64][]protocol.ProductionManagement)
	for _, production := range productionModelList {
		proList := companyProductionMap[production.AudienceCompanyId]
		p := protocol.ProductionManagement{
			ID:                production.Id,
			AudienceCompanyId: production.AudienceCompanyId,
			CnName:            production.CnName,
			EnName:            production.EnName,
			Logo:              production.Logo,
			Sorting:           production.Sorting,
		}
		proList = append(proList, p)
		sort.Slice(proList, func(i, j int) bool {
			return proList[i].Sorting < proList[j].Sorting
		})
		companyProductionMap[production.AudienceCompanyId] = proList
	}
	for _, company := range list {
		productionList := companyProductionMap[company.Id]
		directory := protocol.CompanyDirectories{
			Id:                      company.Id,
			CnConferenceCompany:     company.CnConferenceCompany,
			EnConferenceCompany:     company.EnConferenceCompany,
			CnBoothNumber:           company.CnBoothNumber,
			EnBoothNumber:           company.EnBoothNumber,
			CnUrl:                   company.CnUrl,
			EnUrl:                   company.EnUrl,
			CnVenueName:             company.CnVenueName,
			EnVenueName:             company.EnVenueName,
			CnExhibitorIntroduction: company.CnExhibitorIntroduction,
			ProductionList:          productionList,
			Logo:                    company.Logo,
			Year:                    company.Year,
			VenueId:                 int64(company.CnExhibitorVenueId),
		}
		directories = append(directories, directory)
	}
	if err != nil {
		return
	}

	return
}

func GetIndustryInfo(req protocol.ReqConferenceId) (res protocol.ResIndustryInfo, err error) {
	//中文才需要
	epoType := "1"

	if req.CnOrEn == constant.Cn {
		companyList, _, err := db.GetAudienceConferenceCompanyHaveNews(nil, req.Id, 1, 2)
		if err != nil {
			logger.Error(fmt.Sprintf("查询配置有新闻的展商出错：Err->%v;ConferenceId->%d", err, req.Id))
			return protocol.ResIndustryInfo{}, err
		}

		setId := 0

		newsSet, err := db.GetConferenceNewsSet(nil, req.Id)
		if err != nil {
			logger.Error(fmt.Sprintf("查询配置有新闻的展商出错：Err->%v;ConferenceId->%d", err, req.Id))
			return protocol.ResIndustryInfo{}, err
		}
		if &newsSet != nil {
			if newsSet.SetId > 0 {
				setId = newsSet.SetId
			}
			if utils.NotEmpty(epoType) {
				epoType = newsSet.ExpoNews
			}
		}

		companyNewsList, _, err := rpc.GetNewsList(rpc.ReqGetNews{
			ID: setId,
		})
		if err != nil {
			logger.Error(fmt.Sprintf("查询展商新闻失败"))
			return protocol.ResIndustryInfo{}, err
		}
		venueMap, err := db.GetVenueMapByConferenceID(nil, req.Id)
		if err != nil {
			logger.Error(fmt.Sprintf("查询场馆信息出错：Err->%v;ConferenceID->%d", err, req.Id))
			return protocol.ResIndustryInfo{}, err
		}
		newsIdMap := make(map[int64]rpc.News)
		for _, news := range companyNewsList {
			newsIdMap[news.NewsID] = news
		}
		for _, company := range companyList {
			news := newsIdMap[company.CnExhibitorNewsID]
			bearing := protocol.CompanyBearing{
				ConferenceId:          company.ConferenceId,
				CnExhibitorVideoLink:  company.CnExhibitorVideoLink,
				CnExhibitorVideoCover: company.CnExhibitorVideoCover,
				CnExhibitorNewsID:     company.CnExhibitorNewsID,
				CnNews:                news.Profile,
				CnBoothNumber:         company.CnBoothNumber,
				EnBoothNumber:         company.EnBoothNumber,
				CnExhibitorVenueID:    company.CnExhibitorVenueId,
				CnExhibitorVenueName:  venueMap[int64(company.CnExhibitorVenueId)].CnVenueName,
				PubTime:               time.Unix(int64(news.PubDate), 0).Format(constant.DateOnly),
				CnNewsTitle:           news.Title,
				CnNewsURL:             news.NewsURL,
				Source:                news.Source,
				Logo:                  company.Logo,
			}
			res.CompanyBearingList = append(res.CompanyBearingList, bearing)
		}
		for i, news := range companyNewsList {
			if i < 2 {

				conferenceNews := protocol.ConferenceNews{
					CnNews:            news.Profile,
					CnNewsTitle:       news.Title,
					PubTime:           time.Unix(int64(news.PubDate), 0).Format(constant.DateOnly),
					Thumb:             news.Thumb,
					CnNewsURL:         news.NewsURL,
					Source:            news.Source,
					CnExhibitorNewsID: news.NewsID,
				}
				res.ConferenceNewsList = append(res.ConferenceNewsList, conferenceNews)
			}
		}
	}

	//默认中文
	language := "cn"
	page := protocol.ReqPage{
		Page:     1,
		PageSize: 2,
	}
	if req.CnOrEn == constant.En {
		language = "en"
		page.PageSize = 6
	}
	expoNews, _, err := rpc.GetExpoNewsList(rpc.ReqGetExpoNews{
		ExpoType: epoType,
		Language: language,
		ReqPage:  page,
	})
	if err != nil {
		logger.Error(fmt.Sprintf("查询行业新闻失败：Err->%v", err))
		return protocol.ResIndustryInfo{}, err
	}
	for _, news := range expoNews {

		industryNews := protocol.IndustryNews{
			CnNews:      news.Profile,
			CnNewsTitle: news.Title,
			PubTime:     time.Unix(int64(news.PubDate), 0).Format(constant.DateOnly),
			Thumb:       news.Thumb,
			CnNewsURL:   news.NewsUrl,
			Source:      news.Source,
			NewsID:      news.NewsId,
		}
		if req.CnOrEn == constant.En {
			industryNews.PubTime = utils.TransDateTimeToEn(industryNews.PubTime)
		}
		res.IndustryNewsList = append(res.IndustryNewsList, industryNews)
	}
	if utils.IsTestEnv() {
		if req.Id == 11 {
			res.CompanyBearingList = nil
			res.IndustryNewsList = nil
		}
	} else {
		if req.Id == 2 {
			res.CompanyBearingList = nil
			res.IndustryNewsList = nil
		}
	}

	return res, nil
}

func GetCompanyBearing(req protocol.PageReqConferenceId) ([]protocol.CompanyBearing, int64, error) {
	//companyList, _, err2 := db.GetAudienceConferenceCompany(nil, req.ReqConferenceId.ID, req.Page, req.PageSize)
	//if err2 != nil {
	//	logger.Error(fmt.Sprintf("查询展商信息失败:Err->%v", err2))
	//	return protocol.ResCompanyBearingList{}, err2
	//}

	setId := 0
	newsSet, err := db.GetConferenceNewsSet(nil, req.Id)
	if err != nil {
		logger.Error(fmt.Sprintf("查询配置有新闻的展商出错：Err->%v;ConferenceId->%d", err, req.Id))
		return nil, 0, err
	}
	if &newsSet != nil {
		if newsSet.SetId > 0 {
			setId = newsSet.SetId
		}
	}
	list, _, err := rpc.GetNewsList(rpc.ReqGetNews{
		// 写死id
		ID: setId,
	})
	if err != nil {
		return nil, 0, err
	}

	newsCompanyMap := make(map[int64]rpc.News)

	for _, news := range list {
		newsCompanyMap[news.NewsID] = news
	}
	audienceCompanyList, total, err := db.GetAudienceConferenceCompany2(nil, req.Page, req.PageSize, req.Id)
	if err != nil {
		logger.Error(fmt.Sprintf("展商风采查询出错：Err->%v", err))
		return nil, 0, err
	}
	venueMap, err := db.GetVenueMapByConferenceID(nil, req.ReqConferenceId.Id)
	if err != nil {
		logger.Error(fmt.Sprintf("查询场馆信息出错：Err->%v;ConferenceID->%d", err, req.ReqConferenceId.Id))
		return nil, 0, err
	}
	companyBearingList := make([]protocol.CompanyBearing, 0)
	for _, company := range audienceCompanyList {
		//绑定了的才展示
		news := newsCompanyMap[company.CnExhibitorNewsID]
		if company.Id > 0 {
			bearing := protocol.CompanyBearing{
				ConferenceId:          company.ConferenceId,
				CnExhibitorVideoLink:  company.CnExhibitorVideoLink,
				CnExhibitorVideoCover: company.CnExhibitorVideoCover,
				CnExhibitorNewsID:     company.CnExhibitorNewsID,
				CnNews:                news.Profile,
				CnBoothNumber:         company.CnBoothNumber,
				EnBoothNumber:         company.EnBoothNumber,
				CnExhibitorVenueID:    company.CnExhibitorVenueId,
				CnExhibitorVenueName:  venueMap[int64(company.CnExhibitorVenueId)].CnVenueName,
				PubTime:               time.Unix(int64(news.PubDate), 0).Format(constant.DateOnly),
				CnNewsTitle:           news.Title,
				CnNewsURL:             news.NewsURL,
				Source:                news.Source,
				Logo:                  company.Logo,
			}
			companyBearingList = append(companyBearingList, bearing)
		}
	}

	return companyBearingList, total, nil
}

func GetCompanyBearingById(req protocol.ReqCompanyBearingID) (bearing protocol.CompanyBearing, err error) {

	audienceCompanyList, _, err := db.GetAudienceConferenceCompanyByNewsId(nil, []int64{req.ID}, 0, 0)
	if err != nil {
		logger.Error(fmt.Sprintf("展商风采查询出错：Err->%v", err))
		return
	}
	if len(audienceCompanyList) == 0 {
		return protocol.CompanyBearing{}, nil
	}
	setId := 0
	newsSet, err := db.GetConferenceNewsSet(nil, audienceCompanyList[0].ConferenceId)
	if err != nil {
		logger.Error(fmt.Sprintf("查询配置有新闻的展商出错：Err->%v;ConferenceId->%d", err, req.ID))
		return
	}
	if &newsSet != nil {
		if newsSet.SetId > 0 {
			setId = newsSet.SetId
		}
	}

	// 写死CLNB大会
	list, _, err := rpc.GetNewsList(rpc.ReqGetNews{
		// 写死id
		ID: setId,
	})
	if err != nil {
		return
	}

	newsIDs := make([]int64, 0)
	newsIdMap := make(map[int64]rpc.News)
	for _, news := range list {
		newsIDs = append(newsIDs, news.NewsID)
		newsIdMap[news.NewsID] = news
	}
	company := audienceCompanyList[0]
	news := newsIdMap[company.CnExhibitorNewsID]
	venueMap, err := db.GetVenueMapByConferenceID(nil, company.Id)
	if err != nil {
		logger.Error(fmt.Sprintf("查询场馆信息出错：Err->%v;ConferenceID->%d", err, req.ID))
		return
	}
	content, err := rpc.GetNewsContent(company.CnExhibitorNewsID)
	if err != nil {
		logger.Error(fmt.Sprintf("查询新闻正文错误：Err->%v", err))
		return protocol.CompanyBearing{}, err
	}
	bearing = protocol.CompanyBearing{
		ConferenceId:          company.ConferenceId,
		CnExhibitorVideoLink:  company.CnExhibitorVideoLink,
		CnExhibitorVideoCover: company.CnExhibitorVideoCover,
		CnExhibitorNewsID:     company.CnExhibitorNewsID,
		CnNews:                news.Profile,
		CnBoothNumber:         company.CnBoothNumber,
		EnBoothNumber:         company.EnBoothNumber,
		CnExhibitorVenueID:    company.CnExhibitorVenueId,
		CnExhibitorVenueName:  venueMap[int64(company.CnExhibitorVenueId)].CnVenueName,
		PubTime:               time.Unix(int64(news.PubDate), 0).Format(constant.DateOnly),
		CnNewsTitle:           news.Title,
		CnNewsURL:             news.NewsURL,
		Source:                news.Source,
		CnContent:             content,
		Logo:                  company.Logo,
	}
	return bearing, nil
}

func RecordFloorGraph(graph protocol.ReqFloorGraph) error {
	floorGraph := model.ConferenceFloorGraph{
		CommonModel: model.CommonModel{
			CreateUser: graph.OpUser,
		},
		ConferenceId: graph.ConferenceId,
		CnGraphUrl:   graph.CnGraphUrl,
		EnGraphUrl:   graph.EnGraphUrl,
		CnGraphName:  graph.CnGraphName,
		EnGraphName:  graph.EnGraphName,
		Sort:         graph.Sort,
	}
	err := db.RecordFloorGraph(nil, &floorGraph)
	if err != nil {
		return err
	}
	return nil
}

func GetPageFloorGraphList(id int64, page int, size int) ([]protocol.FloorGraph, int64, error) {
	list, total, err := db.GetPagedFloorGraphListByConferenceId(nil, id, page, size)
	if err != nil {
		return nil, 0, err
	}
	graphs := make([]protocol.FloorGraph, 0)
	for _, graph := range list {
		floorGraph := protocol.FloorGraph{
			Id:          graph.Id,
			CnGraphUrl:  graph.CnGraphUrl,
			EnGraphUrl:  graph.EnGraphUrl,
			CnGraphName: graph.CnGraphName,
			EnGraphName: graph.EnGraphName,
			Sort:        fmt.Sprintf("%.2f", graph.Sort),
		}
		graphs = append(graphs, floorGraph)
	}
	return graphs, total, nil
}

func GetFloorGraphList(id int64) ([]protocol.FloorGraph, error) {
	list, err := db.GetFloorGraphListByConferenceId(nil, id)
	if err != nil {
		return nil, err
	}
	graphs := make([]protocol.FloorGraph, 0)
	for _, graph := range list {
		floorGraph := protocol.FloorGraph{
			Id:          graph.Id,
			CnGraphUrl:  graph.CnGraphUrl,
			EnGraphUrl:  graph.EnGraphUrl,
			CnGraphName: graph.CnGraphName,
			EnGraphName: graph.EnGraphName,
			Sort:        fmt.Sprintf("%.2f", graph.Sort),
		}
		graphs = append(graphs, floorGraph)
	}
	return graphs, nil
}

func GetSingleFloorGraph(req protocol.ReqFloorGraphID) (protocol.FloorGraph, error) {
	graph, err := db.GetSingleFloorGraph(nil, req.ID)
	if err != nil && err != gorm.ErrRecordNotFound {
		return protocol.FloorGraph{}, err
	}
	floorGraph := protocol.FloorGraph{
		Id:          graph.Id,
		CnGraphUrl:  graph.CnGraphUrl,
		EnGraphUrl:  graph.EnGraphUrl,
		CnGraphName: graph.CnGraphName,
		EnGraphName: graph.EnGraphName,
		Sort:        fmt.Sprintf("%.2f", graph.Sort),
	}
	return floorGraph, err
}

func UpdateFloorGraph(graph protocol.ReqUpdateFloorGraph) error {
	floorGraph := model.ConferenceFloorGraph{
		CommonModel: model.CommonModel{
			Id:         graph.Id,
			UpdateUser: graph.OpUser,
		},
		ConferenceId: graph.ConferenceId,
		CnGraphUrl:   graph.CnGraphUrl,
		EnGraphUrl:   graph.EnGraphUrl,
		CnGraphName:  graph.CnGraphName,
		EnGraphName:  graph.EnGraphName,
		Sort:         graph.Sort,
	}
	err := db.UpdateFloorGraph(nil, floorGraph)
	return err
}

func DeleteFloorGraph(req protocol.ReqFloorGraphID) error {
	tx := db.OrmDB(nil).Set("user", req.OpUser).Set("id", req.ID)
	err := db.DeleteFloorGraph(tx, req.ID)
	if err != nil {
		return err
	}
	return nil
}

func RecordCommonQuestionType(questionType protocol.ReqCommonQuestionType) error {
	audienceQuestionType := model.AudienceQuestionType{
		CommonModel: model.CommonModel{
			CreateUser: questionType.OpUser,
		},
		ConferenceId:   questionType.ConferenceId,
		EnQuestionType: questionType.EnQuestionType,
		CnQuestionType: questionType.CnQuestionType,
		Sort:           questionType.Sort,
	}
	err := db.RecordAudienceQuestionType(nil, audienceQuestionType)
	return err
}

func GetCommonQuestionType(req protocol.ReqConferenceId) ([]protocol.CommonQuestionType, error) {
	list, err := db.GetCommonQuestionType(nil, req.Id)
	if err != nil {
		return nil, err
	}
	typeList := make([]protocol.CommonQuestionType, 0)
	for _, questionType := range list {
		commonType := protocol.CommonQuestionType{
			Id:             questionType.Id,
			ConferenceId:   questionType.ConferenceId,
			CnQuestionType: questionType.CnQuestionType,
			EnQuestionType: questionType.EnQuestionType,
			Sort:           questionType.Sort,
		}
		typeList = append(typeList, commonType)
	}
	return typeList, err
}

func UpdateAudienceCommonQuestionType(questionType protocol.ReqUpdateQuestionType) error {
	err := db.UpdateAudienceCommonQuestionType(nil, questionType)
	return err
}

func DeleteAudienceCommonQuestionType(req protocol.ReqQuestionTypeId) error {
	tx := db.OrmDB(nil).Set("user", req.OpUser).Set("id", req.Id)
	return db.DeleteAudienceCommonQuestionType(tx, req.Id)
}

func GetCommonQuestionTypeById(req protocol.ReqQuestionTypeId) (protocol.CommonQuestionType, error) {
	questionType, err := db.GetCommonQuestionTypeById(nil, req.Id)
	if err != nil {
		return protocol.CommonQuestionType{}, err
	}
	companyQuestionType := protocol.CommonQuestionType{
		Id:             questionType.Id,
		ConferenceId:   questionType.ConferenceId,
		CnQuestionType: questionType.CnQuestionType,
		EnQuestionType: questionType.EnQuestionType,
		Sort:           questionType.Sort,
	}
	return companyQuestionType, nil
}

func GetCommonQuestionAnswerById(req protocol.ReqQuestionContentId) (protocol.QuestionContent, error) {
	answer, err := db.GetCommonQuestionAnswerById(nil, req.Id)
	if err != nil {
		return protocol.QuestionContent{}, err
	}
	questionAnswer := protocol.QuestionContent{
		Id:                answer.Id,
		QuestionTypeId:    answer.QuestionTypeId,
		CnQuestionContent: answer.CnQuestionContent,
		EnQuestionContent: answer.EnQuestionContent,
		CnQuestionAnswer:  answer.CnQuestionAnswer,
		EnQuestionAnswer:  answer.EnQuestionAnswer,
		Sort:              answer.Sort,
	}
	return questionAnswer, nil
}

func RecordCommonQuestionAnswer(req protocol.CommonQuestionAnswerAdd) error {
	content := model.CommonQuestionContent{
		CommonModel: model.CommonModel{
			CreateUser: req.OpUser,
		},
		QuestionTypeId:    req.QuestionTypeId,
		CnQuestionContent: req.CnQuestionContent,
		EnQuestionContent: req.EnQuestionContent,
		CnQuestionAnswer:  req.CnQuestionAnswer,
		EnQuestionAnswer:  req.EnQuestionAnswer,
		Sort:              req.Sort,
	}
	return db.RecordCommonQuestionAnswer(nil, content)
}

func GetCommonQuestionAnswer(typeId int64) ([]protocol.CommonQuestionAnswer, error) {
	list, err := db.GetAudienceCommonQuestionAnswerByTypeId(nil, typeId)
	if err != nil {
		return nil, err
	}
	answers := make([]protocol.CommonQuestionAnswer, 0)
	for _, content := range list {
		answer := protocol.CommonQuestionAnswer{
			Id:                content.Id,
			CnQuestionContent: content.CnQuestionContent,
			CnQuestionAnswer:  content.CnQuestionAnswer,
			CnQuestionType:    content.CnQuestionTypeName,
			EnQuestionType:    content.EnQuestionTypeName,
			EnQuestionContent: content.EnQuestionContent,
			EnQuestionAnswer:  content.EnQuestionAnswer,
			Sort:              content.Sort,
		}
		answers = append(answers, answer)
	}
	return answers, err
}

func UpdateCommonQuestionAnswer(update protocol.CommonQuestionAnswerUpdate) error {
	content := model.CompanyQuestionContent{
		CommonModel: model.CommonModel{
			Id:         update.Id,
			UpdateUser: update.OpUser,
		},
		CnQuestionContent: update.CnQuestionContent,
		EnQuestionContent: update.EnQuestionContent,
		CnQuestionAnswer:  update.CnQuestionAnswer,
		EnQuestionAnswer:  update.EnQuestionAnswer,
		Sort:              update.Sort,
	}
	err := db.UpdateAudienceQuestionAnswer(nil, content)
	return err
}

func DeleteCommonQuestionAnswer(req protocol.ReqAudienceQuestionContentId) error {
	tx := db.OrmDB(nil).Set("user", req.OpUser).Set("id", req.Id)
	return db.DeleteAudienceCommonQuestionAnswer(tx, req.Id)
}

func FrontGetCommonQA(conferenceId int64) ([]protocol.QuestionType, error) {
	list, err := db.GetAudienceCommonQuestionAnswer(nil, conferenceId)
	if err != nil {
		return nil, err
	}
	questionTypeList := make([]protocol.QuestionType, 0)
	m := make(map[int64][]model.AudienceQuestionContent)
	for _, content := range list {
		m[content.QuestionTypeId] = append(m[content.QuestionTypeId], content)
	}
	for k, v := range m {
		qas := make([]protocol.QA, 0)
		questionType := protocol.QuestionType{
			TypeId: k,
			CnType: "",
			EnType: "",
			QAs:    nil,
		}
		for _, content := range v {
			questionType.CnType = content.CnQuestionTypeName
			questionType.EnType = content.EnQuestionTypeName
			qa := protocol.QA{
				Id:         content.Id,
				CnQuestion: content.CnQuestionContent,
				EnQuestion: content.EnQuestionContent,
				CnAnswer:   content.CnQuestionAnswer,
				EnAnswer:   content.EnQuestionAnswer,
			}
			qas = append(qas, qa)
		}
		questionType.QAs = qas
		questionTypeList = append(questionTypeList, questionType)
	}
	return questionTypeList, nil
}

func SaveAfterReport(report protocol.ReqSaveAfterReport) error {
	afterReport := model.AudienceAfterReport{
		CommonModel: model.CommonModel{
			Id: report.Id,
		},
		ConferenceId: report.ConferenceId,
		CnName:       report.CnName,
		EnName:       report.EnName,
		CnPdf:        report.CnPdf,
		EnPdf:        report.EnPdf,
		Sort:         report.Sort,
	}
	err := db.SaveAfterReport(nil, &afterReport)
	return err
}

func GetAfterReportById(req protocol.ReqAfterReportID) (protocol.AfterReport, error) {
	report, err := db.GetAfterReportById(nil, req.ID)
	if err != nil {
		return protocol.AfterReport{}, err
	}
	return protocol.AfterReport{
		Id:           report.Id,
		ConferenceId: report.ConferenceId,
		CnName:       report.CnName,
		EnName:       report.EnName,
		CnPdf:        report.CnPdf,
		EnPdf:        report.EnPdf,
		Sort:         fmt.Sprintf("%.2f", report.Sort),
	}, nil
}

func GetAfterReportList(req protocol.ReqConferenceId) (list []protocol.AfterReport, err error) {
	reportList, err := db.GetAfterReportList(nil, req.Id)
	for _, report := range reportList {
		afterReport := protocol.AfterReport{
			Id:           report.Id,
			ConferenceId: report.ConferenceId,
			CnName:       report.CnName,
			EnName:       report.EnName,
			CnPdf:        report.CnPdf,
			EnPdf:        report.EnPdf,
			Sort:         fmt.Sprintf("%.2f", report.Sort),
		}
		list = append(list, afterReport)
	}
	return
}

func DeleteAfterReport(req protocol.ReqAfterReportID) error {
	err := db.DeleteAfterReport(nil, req.ID)
	if err != nil {
		logger.Debug(err)
	}
	return err
}

func SavePaperCollection(collection protocol.ReqSavePaperCollection) error {
	paperCollection := model.PaperCollection{
		ID:           collection.ID,
		ConferenceId: collection.ConferenceID,
		CnFormUrl:    collection.CnFormURL,
		EnFormUrl:    collection.EnFormURL,
		CnContent:    collection.CnContent,
		EnContent:    collection.EnContent,
		CnButtonName: collection.CnButtonName,
		EnButtonName: collection.EnButtonName,
		CnAboveName:  collection.CnAboveName,
		EnAboveName:  collection.EnAboveName,
	}
	err := db.SavePaperCollection(nil, paperCollection)
	return err
}

func GetPaperCollection(conferenceId int64) (protocol.PaperCollection, error) {
	collection, err := db.GetPaperCollection(nil, conferenceId)
	if err != nil {
		return protocol.PaperCollection{}, err
	}
	return protocol.PaperCollection{
		ID:           collection.ID,
		ConferenceId: collection.ConferenceId,
		CnFormUrl:    collection.CnFormUrl,
		EnFormUrl:    collection.EnFormUrl,
		CnContent:    collection.CnContent,
		EnContent:    collection.EnContent,
		CnButtonName: collection.CnButtonName,
		EnButtonName: collection.EnButtonName,
		CnAboveName:  collection.CnAboveName,
		EnAboveName:  collection.EnAboveName,
	}, nil
}

func GetProductionManagement(req protocol.ReqCompanyId) ([]protocol.ProductionManagement, int64, error) {
	productionList, total, err := db.GetProductionByCompanyId(nil, []int64{req.Id}, req.Page, req.PageSize)
	if err != nil {
		logger.Error("根据观众展商查询产品失败：Err->%v;companyId->%d", err, req.Id)
		return nil, 0, err
	}
	list := make([]protocol.ProductionManagement, 0)
	for _, management := range productionList {
		product := protocol.ProductionManagement{
			ID:                management.Id,
			AudienceCompanyId: management.AudienceCompanyId,
			CnName:            management.CnName,
			EnName:            management.EnName,
			Logo:              management.Logo,
			Sorting:           management.Sorting,
		}
		list = append(list, product)
	}
	return list, total, err
}

func SaveProduction(req protocol.ReqSaveProductionManagement) (res string, err error) {
	p := model.ProductionManagement{
		AdminCommonModel: model.AdminCommonModel{
			Id:          req.ID,
			UpdateAdmin: req.UserName,
		},
		AudienceCompanyId: req.AudienceCompanyId,
		CnName:            req.CnName,
		EnName:            req.EnName,
		Logo:              req.Logo,
		Sorting:           req.Sorting,
	}
	//编辑
	if req.ID > 0 {
		productionById, err := db.GetProductionById(nil, req.ID)
		if err != nil {
			return "编辑产品信息出错", err
		}
		if productionById.Id == 0 {
			return "不存在此产品信息，请确认", nil
		}
		p.CreateAdmin = productionById.CreateAdmin
		err = db.SaveProduction(nil, &p)
		if err != nil {
			logger.Error(fmt.Sprintf("编辑产品信息出错:%v;值：%+v", err, req))
			return "编辑产品信息出错", err
		}
	} else {
		p.CreateAdmin = req.UserName
		err := db.SaveProduction(nil, &p)
		if err != nil {
			logger.Error(fmt.Sprintf("保存产品信息出错:%v;值：%+v", err, req))
			return "保存产品信息出错", err
		}
	}
	return "", err
}

func DeleteProduction(req protocol.ReqProductionId) (res string, err error) {
	//p, err := db.GetProductionById(nil, req.ID)
	//if err != nil {
	//	logger.Error(fmt.Sprintf("查询产品信息出错：Err->%v;ID->%d", err, req.ID))
	//	return "", err
	//}
	err = db.DeleteProduction(nil, req.Id)
	return "", err
}

func GetProductionById(req protocol.ReqProductionId) (production protocol.ProductionManagement, err error) {
	p, err := db.GetProductionById(nil, req.Id)
	if err != nil {
		logger.Error(fmt.Sprintf("查询产品信息出错：Err->%v;ID->%d", err, req.Id))
		return protocol.ProductionManagement{}, err
	}
	return protocol.ProductionManagement{
		ID:                0,
		AudienceCompanyId: p.AudienceCompanyId,
		CnName:            p.CnName,
		EnName:            p.EnName,
		Logo:              p.Logo,
		Sorting:           p.Sorting,
	}, err
}

func GetQuestionPaper(conferenceId int64) (paper protocol.QuestionPaper, err error) {
	paperUrl, err := db.GetQuestionPaperExcel(nil, conferenceId)
	if err != nil {
		logger.Error(fmt.Sprintf("查询问卷失败：%v", err))
	}
	resp, err := http.Get(paperUrl.Url)
	if err != nil {
		logger.Error(fmt.Sprintf("error downloading excel file: %v", err))
		return
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logger.Error(fmt.Sprintf("error reading excel file: %v", err))
		return
	}
	tmpFile, err := ioutil.TempFile("", "question_paper.xlsx")
	if err != nil {
		logger.Error(fmt.Sprintf("error creating temp file: %v", err))
		return
	}
	defer os.Remove(tmpFile.Name())
	if _, err := tmpFile.Write(body); err != nil {
		logger.Error(fmt.Sprintf("error writing to temp file: %v", err))
		return paper, err
	}
	f, err := excelize.OpenFile(tmpFile.Name())
	if err != nil {
		logger.Error(fmt.Sprintf("error opening excel file: %v", err))
		return
	}
	questionCodeContentMap := make(map[string]*protocol.QuestionAndChoice)
	questionRows, err := f.GetRows("Sheet1")
	if err != nil {
		logger.Error(fmt.Sprintf("获取问题页行数失败: %v", err))
		return
	}
	for i, row := range questionRows {
		if i == 0 {
			continue
		}
		questionCode := row[0]
		questionType := 0
		if row[1] == "单选题" {
			questionType = 1
		} else if row[1] == "多选题" {
			questionType = 2
		} else if row[1] == "填空题" {
			questionType = 3
		} else {
			logger.Error("问题类型填写出错：%v", row[1])
			return protocol.QuestionPaper{}, errors.New("请填写正确的问题类型")
		}
		limit, err := strconv.Atoi(row[3])
		if err != nil {
			logger.Error(fmt.Sprintf("答案数量限制填写出错：%v;值->%v", err, row[3]))
			return protocol.QuestionPaper{}, err
		}
		sorting, err := strconv.Atoi(row[4])
		if err != nil {
			logger.Error(fmt.Sprintf("排序填写出错：%v;值->%v", err, row[4]))
			return protocol.QuestionPaper{}, err
		}
		needAnswer := false
		if row[5] == "是" {
			needAnswer = true
		} else if row[5] == "否" {
			needAnswer = false
		} else {
			return protocol.QuestionPaper{}, errors.New("是否必答请填写是或否")
		}
		choiceList := make([]protocol.Option, 0)
		questionAndChoice := &protocol.QuestionAndChoice{
			Number:       limit,
			Question:     row[2],
			Sorting:      float32(sorting),
			NeedAnswer:   needAnswer,
			QuestionType: questionType,
			Choice:       choiceList,
		}
		questionCodeContentMap[questionCode] = questionAndChoice
	}
	answerRows, err := f.GetRows("Sheet2")
	if err != nil {
		logger.Error(fmt.Sprintf("获取答案页行数失败: %v", err))
		return
	}
	for i, row := range answerRows {
		if i == 0 {
			continue
		}
		questionCode := row[2]
		needNote := false
		if row[4] == "是" {
			needNote = true
		} else if row[4] == "否" {
			needNote = false
		} else {
			return protocol.QuestionPaper{}, errors.New("是否需要填写补充说明请填写是或否")
		}
		sorting, err := strconv.Atoi(row[3])
		if err != nil {
			logger.Error(fmt.Sprintf("排序填写出错：%v;值->%v", err, row[4]))
			return protocol.QuestionPaper{}, err
		}
		option := protocol.Option{
			Choice:   row[1],
			NeedNote: needNote,
			Sorting:  float32(sorting),
		}
		content := questionCodeContentMap[questionCode]
		contentChoiceList := content.Choice
		contentChoiceList = append(contentChoiceList, option)
		sort.Slice(contentChoiceList, func(i, j int) bool {
			return contentChoiceList[i].Sorting < contentChoiceList[j].Sorting
		})
		content.Choice = contentChoiceList
	}
	for _, value := range questionCodeContentMap {
		choice := protocol.QuestionAndChoice{
			Number:       value.Number,
			Question:     value.Question,
			Sorting:      value.Sorting,
			NeedAnswer:   value.NeedAnswer,
			QuestionType: value.QuestionType,
			Choice:       value.Choice,
		}
		paper.QuestionAndChoiceList = append(paper.QuestionAndChoiceList, choice)
	}
	sort.Slice(paper.QuestionAndChoiceList, func(i, j int) bool {
		return paper.QuestionAndChoiceList[i].Sorting < paper.QuestionAndChoiceList[j].Sorting
	})
	return
}

func SubmitQuestionPaper(req protocol.ReqSaveQuestionPaper) (res protocol.ResSubmitQuestionPaper, err error) {
	visitor, err := db.GetCnFreeVisitorById(nil, req.Id)
	if err != nil {
		logger.Error("查询中文免费观众出错")
		return protocol.ResSubmitQuestionPaper{}, err
	}
	info, err := db.QueryConferenceInfo(nil, visitor.ConferenceID)
	if err != nil {
		logger.Error(fmt.Sprintf("获取会议信息出错：Err->%v", err))
		return protocol.ResSubmitQuestionPaper{}, err
	}
	bytes, err := json.Marshal(req)
	if err != nil {
		logger.Error(fmt.Sprintf("序列化失败：Err->%v;req->%+v", err, req))
		return protocol.ResSubmitQuestionPaper{}, err
	}
	paper := string(bytes)
	data := make(map[string]interface{})
	for _, answer := range req.Content {
		if answer.Choice.NeedShow {
			data["willing"] = answer.Choice.Choice
		}
	}
	data["paper_answer"] = paper
	err = db.UpdateQuestionPaperAnswer(nil, req.Id, data)
	if err != nil {
		logger.Error(fmt.Sprintf("更新问卷回答失败:%v;paper->%s", err, paper))
		return protocol.ResSubmitQuestionPaper{}, err
	}
	//codeString, err := rpc.SignUpViewer(reqSignUp, constant.Cn)
	//if err != nil {
	//	logger.Error(fmt.Sprintf("同步免费观众报名出错：Err->%v;Req->%+v", err, reqSignUp))
	//	return protocol.ResSubmitQuestionPaper{ErrorString: codeString}, err
	//}
	return protocol.ResSubmitQuestionPaper{
		Company:   visitor.CnCompany,
		Name:      visitor.CnName,
		StartTime: info.StartTime.Format(constant.DateOnly),
		EndTime:   info.EndTime.Format(constant.DateOnly),
		Location:  info.CnLocation,
		CodePhoto: visitor.CodePhoto,
	}, nil
}

func GetAllFromID() ([]string, error) {
	cnFromIDs, err := db.GetCnFreeVisitorFromId()
	if err != nil {
		logger.Error(fmt.Sprintf("查询Cn渠道数据出错：Err->%v", err))
		return nil, err
	}
	enFromIDs, err := db.GetEnFreeVisitorFromId()
	if err != nil {
		logger.Error(fmt.Sprintf("查询En渠道数据出错：Err->%v", err))
		return nil, err
	}
	list := make([]string, 0)
	list = append(list, cnFromIDs...)
	list = append(list, enFromIDs...)
	return list, nil
}

func SubmitFreeVisitor(req protocol.ReqSaveFreeVisitorInfo) (res protocol.ResSubmitFreeVisitor, err error) {
	var WxUrl2 string

	if utils.NotEmpty(req.SourceId) {
		decodedStr, _ := url.QueryUnescape(req.SourceId)
		if string(decodedStr) == "会展首页-会展列表" {
			req.SourceId = "32"
		}
	}
	id := int64(0)
	if utils.IsEmpty(req.FromID) && utils.IsEmpty(req.SourceId) {
		req.SourceId = "32"
	}
	info, err := db.QueryConferenceInfo(nil, req.ConferenceID)
	if err != nil {
		logger.Error(fmt.Sprintf("查询展览失败：%v", err))
	}

	if !utils.IsTestEnv() {
		if info.ID <= 0 {
			WxUrl2 = weChat.WxUrl2
		} else {
			if utils.NotEmpty(info.QwxUrl) {
				WxUrl2 = info.QwxUrl
			} else {
				WxUrl2 = weChat.WxUrl2
			}

		}
	} else {
		if info.ID <= 0 || utils.IsEmpty(info.QwxUrl) {
			WxUrl2 = weChat.WxUrl
		} else {
			WxUrl2 = info.QwxUrl
		}
	}

	fromMap := memory.GetMeetingFromNameMap(strconv.FormatInt(info.ID, 10) + "_" + req.FromID)

	fromName := ""
	if utils.NotEmpty(fromMap.ChannelName) {
		fromName = fromMap.ChannelName
	}

	unescape, err := url.QueryUnescape(req.SourceId)
	if err != nil {
		logger.Error("解码失败：Err->%v", err)
		return protocol.ResSubmitFreeVisitor{}, err
	}
	if req.CnOrEn == constant.Cn {
		m, err := rpc.CheckCode(protocol.ReqCheckCode{
			Code:      req.CnVisitor.VerificationCode,
			Cellphone: req.CnVisitor.Telephone,
			CodeType:  "conference_from_phone",
		})
		if err != nil {
			logger.Error(fmt.Sprintf("校验验证码失败：Err->%v;Req->%+v", err, req))
			return protocol.ResSubmitFreeVisitor{}, err
		}
		if m.Code != 0 {
			return protocol.ResSubmitFreeVisitor{}, errors.New("验证码错误")
		}
		visitor := req.CnVisitor
		if utils.IsEmpty(info.MeetingSysId) {
			cardMsg := feishu.NewMdCardMsg("观众报名未绑定会议系统", "blue")
			cardMsg.AppendMd(`>会议名：%s
>姓名：%s 
>手机号：%v
>邮箱：%v
>公司：%s 
>职位：%v
>渠道：%v
>时间: %s`, info.CnName, visitor.Name, visitor.Telephone, visitor.Email, visitor.Company, visitor.JobTitle, fromName, utils.GetCurDateTime())
			return protocol.ResSubmitFreeVisitor{}, errors.New("未绑定会议系统")
		}

		if utils.IsEmpty(visitor.JobTitle) {
			visitor.JobTitle = visitor.Department
		}
		reqSignUp := rpc.ViewerSignUp{
			MeetingNo:      info.MeetingSysId,
			Name:           visitor.Name,
			CompanyName:    visitor.Company,
			Position:       visitor.JobTitle,
			CellPhone:      visitor.Telephone,
			Email:          visitor.Email,
			FromId:         req.FromID,
			PlatformId:     req.SourceId,
			MainProduct:    visitor.MainProducts,
			CompanyType:    visitor.EnterpriseType,
			PurchaseDemand: visitor.ProcurementItems,
			PurchaseVolume: visitor.OrderQuantity,
		}
		logger.Info(fmt.Sprintf("同步免费观众报名：Req->%+v", reqSignUp))
		codeString, err := rpc.SignUpViewer(reqSignUp, constant.Cn)
		if err != nil {
			if err.Error() == "手机号已存在" || err.Error() == "邮箱已存在" {
				logger.Warnning(fmt.Sprintf("同步免费观众报名出错：Err->%v;Req->%+v", err, reqSignUp))
				return protocol.ResSubmitFreeVisitor{ErrorString: codeString}, err
			} else {
				logger.Error(fmt.Sprintf("同步免费观众报名出错：Err->%v;Req->%+v", err, reqSignUp))
				return protocol.ResSubmitFreeVisitor{ErrorString: codeString}, err
			}
		}
		markdown2 := fmt.Sprintf(`
				## 观众报名通知
				>会议名：%v
				>姓名：%s 
				>手机号：%v
				>邮箱：%v
				>公司：%s 
				>职位：%v
				>渠道：%v
				>时间：%s`, info.CnName, visitor.Name, visitor.Telephone, visitor.Email, visitor.Company, visitor.JobTitle, fromMap.ChannelName, utils.GetCurDateTime())

		cardMsg := feishu.NewMdCardMsg("观众报名通知", "blue")
		cardMsg.AppendMd(`>会议名：%s
>姓名：%s 
>手机号：%v
>邮箱：%v
>公司：%s 
>职位：%v
>渠道：%v
>时间: %s`, info.CnName, visitor.Name, visitor.Telephone, visitor.Email, visitor.Company, visitor.JobTitle, fromName, utils.GetCurDateTime())

		if strings.Contains(WxUrl2, "open.feishu.cn") {
			weChat.Send2(cardMsg, WxUrl2)
		} else {
			weChat.Send(markdown2, WxUrl2)
		}

		cnVisitor := model.CnFreeVisitor{
			ConferenceID:     req.ConferenceID,
			CnConferenceName: info.CnName,
			CnName:           req.CnVisitor.Name,
			CnCompany:        req.CnVisitor.Company,
			CnDepartment:     req.CnVisitor.Department,
			CnJobTitle:       req.CnVisitor.JobTitle,
			CnTelephone:      req.CnVisitor.Telephone,
			CnEmail:          req.CnVisitor.Email,
			IdNo:             req.CnVisitor.IdNo,
			Source:           unescape,
			FromID:           req.FromID,
			FromName:         fromName,
			CodePhoto:        codeString,
			MainProducts:     visitor.MainProducts,
			EnterpriseType:   visitor.EnterpriseType,
			ProcurementItems: visitor.ProcurementItems,
			OrderQuantity:    visitor.OrderQuantity,
		}
		err = db.CreateCnFreeVisitor(nil, &cnVisitor)
		if err != nil {
			logger.Error(fmt.Sprintf("查询"))
			return protocol.ResSubmitFreeVisitor{}, err
		}
		id = cnVisitor.ID

		if utils.NotEmpty(info.WorkWeChatChain) {
			dateRange := ""
			if utils.NotEmpty(info.StartTime.Format(constant.DateOnly)) {
				dateRange, err = utils.FormatDateRange(info.StartTime.Format(constant.DateOnly), info.EndTime.Format(constant.DateOnly))
				if err != nil {
					logger.Error(fmt.Sprintf("格式化日期范围出错：Err->%v;Req->%+v", err, req))
				}
			}

			var args []string
			args = append(args, req.CnVisitor.Name)
			args = append(args, info.CnName)
			args = append(args, dateRange)
			args = append(args, info.CnLocation)
			args = append(args, info.WorkWeChatChain)

			data, err := json.Marshal(args) //将切片进行序列化操作
			if err != nil {
				logger.Warnning("序列化错误", err)
			}

			msg := SendMessage(req.CnVisitor.Telephone, "conference_cn_visitor", "conference_center", string(data))
			if msg != "" {
				logger.Error(fmt.Sprintf("发送手机号：%v短信失败：%v", req.CnVisitor.Telephone, msg))
			}
		}

	} else if req.CnOrEn == constant.En {

		reqEnvisitor := req.EnVisitor

		if utils.IsEmpty(info.MeetingSysId) {
			cardMsg := feishu.NewMdCardMsg("英文观众报名未绑定会议系统通知", "blue")
			cardMsg.AppendMd(`>会议名称：%v
>Email:%s,
>First Name：%s
>Last Name：%s
>Company / Organisation：%s
>Job Title：%s
>Country of Residence：%s
>City：%s
>Phone：%s
>Mobile Number：%s
>Nationality：%s
>Main Focus：%s
>Channel：%s
>Time:%s `, info.CnName, reqEnvisitor.Email, reqEnvisitor.FirstName, reqEnvisitor.LastName, reqEnvisitor.Company,
				reqEnvisitor.JobTitle, reqEnvisitor.CountryResidence, reqEnvisitor.City, reqEnvisitor.Phone, reqEnvisitor.Telephone,
				reqEnvisitor.Nationality, reqEnvisitor.MainFocus, fromName, utils.GetCurDateTime())
			return protocol.ResSubmitFreeVisitor{}, errors.New("未绑定会议系统")
		}

		enVisitor := model.EnFreeVisitor{
			ConferenceId:       req.ConferenceID,
			EnConferenceName:   info.EnName,
			EnEmail:            reqEnvisitor.Email,
			EnFirstName:        reqEnvisitor.FirstName,
			EnLastName:         reqEnvisitor.LastName,
			EnCompany:          reqEnvisitor.Company,
			EnJobTitle:         reqEnvisitor.JobTitle,
			EnCountryResidence: reqEnvisitor.CountryResidence,
			EnCity:             reqEnvisitor.City,
			EnPhone:            reqEnvisitor.Phone,
			EnTelephone:        reqEnvisitor.Telephone,
			EnNationality:      reqEnvisitor.Nationality,
			EnMainFocus:        reqEnvisitor.MainFocus,
			Source:             unescape,
			FromID:             req.FromID,
			FromName:           fromName,
			MainProducts:       reqEnvisitor.MainProducts,
			EnterpriseType:     reqEnvisitor.EnterpriseType,
			ProcurementItems:   reqEnvisitor.ProcurementItems,
			OrderQuantity:      reqEnvisitor.OrderQuantity,
		}
		reqSignUp := rpc.ViewerSignUp{
			MeetingNo:      info.MeetingSysId,
			Name:           reqEnvisitor.FirstName + " " + reqEnvisitor.LastName,
			CompanyName:    reqEnvisitor.Company,
			Position:       reqEnvisitor.JobTitle,
			CellPhone:      reqEnvisitor.Telephone,
			Email:          reqEnvisitor.Email,
			FromId:         req.FromID,
			PlatformId:     req.SourceId,
			Country:        reqEnvisitor.Nationality,
			MainProduct:    reqEnvisitor.MainProducts,
			CompanyType:    reqEnvisitor.EnterpriseType,
			PurchaseDemand: reqEnvisitor.ProcurementItems,
			PurchaseVolume: reqEnvisitor.OrderQuantity,
		}
		logger.Info(fmt.Sprintf("同步免费观众报名：Req->%+v", reqSignUp))
		codeString, err := rpc.SignUpViewer(reqSignUp, req.CnOrEn)
		if err != nil {
			logger.Error(fmt.Sprintf("同步免费观众报名出错：Err->%v;Req->%+v", err, reqSignUp))
			return protocol.ResSubmitFreeVisitor{ErrorString: codeString}, err
		}
		// 生成二维码字节流
		qrCode, err := qrcode.Encode(codeString, qrcode.Medium, 256) // 256 为二维码尺寸
		if err != nil {
			fmt.Printf("Error generating QR Code: %v\n", err)
			return protocol.ResSubmitFreeVisitor{}, err
		}
		imageUrl, err := qiniu.NewSmmQiniuUpload().QiniuUploadImage(qiniu.CLNB_QR_CODE, qrCode, 0, "")
		if err != nil {
			return protocol.ResSubmitFreeVisitor{}, err
		}
		logger.Info(fmt.Sprintf("生成二维码：%v", imageUrl))
		err = db.CreateEnFreeVisitor(nil, &enVisitor)
		if err != nil {
			logger.Error(fmt.Sprintf("记录海外观众失败"))
			return protocol.ResSubmitFreeVisitor{}, err
		}
		if utils.NotEmpty(info.AutomaticMail) {
			templateVars := map[string]string{"name": reqEnvisitor.FirstName + " " + reqEnvisitor.LastName, "imgurl": imageUrl}
			msg := SendEmail(reqEnvisitor.Email, info.AutomaticMail, "conference_center", templateVars)
			if msg != "" {
				markdown := fmt.Sprintf(`发送邮箱失败
							>First Name：%v
							>Last Name：%s
							>Company / Organisation：%s
							>Job Title：%v
							>Country of Residence：%v
							>City：%v
							>Phone：%v
							>Mobile Number：%v
							>Nationality：%v
							>Main Focus：%v`, reqEnvisitor.FirstName, reqEnvisitor.LastName, reqEnvisitor.Company,
					reqEnvisitor.JobTitle, reqEnvisitor.CountryResidence, reqEnvisitor.City, reqEnvisitor.Phone,
					reqEnvisitor.Telephone, reqEnvisitor.Nationality, reqEnvisitor.MainFocus)
				weChat.Send(markdown, "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3292d962-98c2-4849-b66a-f5466fcd46ed")
			}
		}

		markdown2 := fmt.Sprintf(`
				## 英文观众报名通知
				>会议名：%s
				>Email:%s,
				>First Name：%s
				>Last Name：%s
				>Company / Organisation：%s
				>Job Title：%s
				>Country of Residence：%s
				>City：%s
				>Phone：%s
				>Mobile Number：%s
				>Nationality：%s
				>Main Focus：%s
				>Channel：%s
				>Time:%s `, info.CnName, reqEnvisitor.Email, reqEnvisitor.FirstName, reqEnvisitor.LastName, reqEnvisitor.Company,
			reqEnvisitor.JobTitle, reqEnvisitor.CountryResidence, reqEnvisitor.City, reqEnvisitor.Phone, reqEnvisitor.Telephone,
			reqEnvisitor.Nationality, reqEnvisitor.MainFocus, fromName, utils.GetCurDateTime())

		cardMsg := feishu.NewMdCardMsg("英文观众报名通知", "blue")
		cardMsg.AppendMd(`>会议名称：%v
>Email:%s,
>First Name：%s
>Last Name：%s
>Company / Organisation：%s
>Job Title：%s
>Country of Residence：%s
>City：%s
>Phone：%s
>Mobile Number：%s
>Nationality：%s
>Main Focus：%s
>Channel：%s
>Time:%s `, info.CnName, reqEnvisitor.Email, reqEnvisitor.FirstName, reqEnvisitor.LastName, reqEnvisitor.Company,
			reqEnvisitor.JobTitle, reqEnvisitor.CountryResidence, reqEnvisitor.City, reqEnvisitor.Phone, reqEnvisitor.Telephone,
			reqEnvisitor.Nationality, reqEnvisitor.MainFocus, fromName, utils.GetCurDateTime())

		if strings.Contains(WxUrl2, "open.feishu.cn") {
			weChat.Send2(cardMsg, WxUrl2)
		} else {
			weChat.Send(markdown2, WxUrl2)
		}

		id = enVisitor.ID
		res.Company = reqEnvisitor.Company
		res.StartTime = info.StartTime.Format(constant.DateOnly)
		res.EndTime = info.EndTime.Format(constant.DateOnly)
		res.Location = info.EnLocation
		res.CodePhoto = codeString
		res.Name = reqEnvisitor.FirstName + " " + reqEnvisitor.LastName
	} else {
		return protocol.ResSubmitFreeVisitor{}, errors.New("中英文参数错误")
	}

	//添加百度广告跟踪
	if utils.NotEmpty(req.BdVid) {
		rpc.UploadBdVid(req.BdVid, info.BdVidToken, 1)
	}
	res.ID = id
	return res, nil
}

func GetFreeVisitorInfo(req protocol.ReqVisitorID) (res []protocol.FreeVisitor, total int64, err error) {
	platformList, err := rpc.GetMeetingChannelPlatformList()
	if err != nil {
		return nil, 0, err
	}
	sourceMap := make(map[string]string)
	for _, source := range platformList {
		sourceMap[source.PlatformId] = source.PlatformName
	}

	if req.StartTime != "" {
		req.StartTime = req.StartTime + " 00:00:00"
	}
	if req.EndTime != "" {
		req.EndTime = req.EndTime + " 23:59:59"
	}

	if req.CnOrEn == constant.Cn {
		list, t, err := db.GetCnFreeVisitorInfo(nil, req.ConferenceId, req.Email, req.FullName, req.FromID, req.FromName, req.SourceID, req.StartTime, req.EndTime, req.EnterpriseType, req.Page, req.PageSize, req.ConferenceName)
		if err != nil {
			logger.Error(fmt.Sprintf("获取中文免费观众信息失败：%v", err))
			return nil, 0, err
		}
		for _, visitor := range list {
			s := sourceMap[visitor.Source]
			if s == "" {
				s = visitor.Source
			}
			freeVisitor := protocol.FreeVisitor{
				CnVisitor: protocol.CnVisitor{
					Name:             visitor.CnName,
					Company:          visitor.CnCompany,
					Department:       visitor.CnDepartment,
					JobTitle:         visitor.CnJobTitle,
					Telephone:        visitor.CnTelephone,
					Email:            visitor.CnEmail,
					PaperAnswer:      visitor.PaperAnswer,
					CnConferenceName: visitor.CnConferenceName,
					Willing:          visitor.Willing,
					SubmitTime:       visitor.SubmitTime,
					Source:           s,
					FromID:           visitor.FromID,
					FromName:         visitor.FromName,
					MainProducts:     visitor.MainProducts,
					EnterpriseType:   visitor.EnterpriseType,
					ProcurementItems: visitor.ProcurementItems,
					OrderQuantity:    visitor.OrderQuantity,
				},
			}
			res = append(res, freeVisitor)
		}
		return res, t, nil
	}
	if req.CnOrEn == constant.En {
		list, t, err := db.GetEnFreeVisitorInfo(nil, req.ConferenceId, req.Email, req.FullName, req.FromID, req.FromName, req.SourceID, req.StartTime, req.EndTime, req.EnterpriseType, req.Page, req.PageSize, req.ConferenceName)
		if err != nil {
			logger.Error(fmt.Sprintf("获取英文免费观众信息失败：%v", err))
			return nil, 0, err
		}
		for _, visitor := range list {
			s := sourceMap[visitor.Source]
			if s == "" {
				s = visitor.Source
			}
			freeVisitor := protocol.FreeVisitor{EnVisitor: protocol.EnVisitor{
				Email:            visitor.EnEmail,
				FirstName:        visitor.EnFirstName,
				LastName:         visitor.EnLastName,
				Company:          visitor.EnCompany,
				JobTitle:         visitor.EnJobTitle,
				CountryResidence: visitor.EnCountryResidence,
				City:             visitor.EnCity,
				Phone:            visitor.EnPhone,
				Telephone:        visitor.EnTelephone,
				Nationality:      visitor.EnNationality,
				MainFocus:        visitor.EnMainFocus,
				FullName:         visitor.EnFirstName + " " + visitor.EnLastName,
				SubmitTime:       visitor.SubmitTime,
				EnConferenceName: visitor.EnConferenceName,
				Source:           s,
				FromID:           visitor.FromID,
				FromName:         visitor.FromName,

				MainProducts:     visitor.MainProducts,
				EnterpriseType:   visitor.EnterpriseType,
				ProcurementItems: visitor.ProcurementItems,
				OrderQuantity:    visitor.OrderQuantity,
			}}
			res = append(res, freeVisitor)
		}
		return res, t, nil
	}
	return nil, 0, errors.New("请选择英文或者中文观众")
}

func ExportFreeVisitorInfo(req protocol.ReqVisitorID) (bReader *bytes.Reader, err error) {
	req.Page = 0
	req.PageSize = 0
	list, _, err := GetFreeVisitorInfo(req)
	if err != nil {
		logger.Error(fmt.Sprintf("导出免费观众信息查询出错：Err->%v", err))
		return nil, err
	}
	listItems := make([]interface{}, 0)
	if req.CnOrEn == constant.Cn {
		for _, visitor := range list {
			listItems = append(listItems, visitor.CnVisitor)
		}
		bReader, err = utils.GenListExcel(listItems, func(fieldName string, value interface{}) interface{} {
			return value
		}, protocol.CnVisitor{})
	}
	if req.CnOrEn == constant.En {
		for _, visitor := range list {
			listItems = append(listItems, visitor.EnVisitor)
		}
		bReader, err = utils.GenListExcel(listItems, func(fieldName string, value interface{}) interface{} {
			return value
		}, protocol.EnVisitor{})
	}
	return

}

func GetIndustryList() ([]protocol.IndustryFocus, error) {
	list, err := db.GetIndustryList(nil)
	if err != nil {
		return nil, err
	}
	industryList := make([]protocol.IndustryFocus, 0)
	for _, industry := range list {
		industryList = append(industryList, protocol.IndustryFocus{
			ID:           industry.ID,
			IndustryName: industry.IndustryName,
		})
	}
	return industryList, nil
}

func GetGetApplyPurchaseBoothList() ([]protocol.IndustryFocus, error) {
	list, err := db.GetIndustryList(nil)
	if err != nil {
		return nil, err
	}
	industryList := make([]protocol.IndustryFocus, 0)
	for _, industry := range list {
		industryList = append(industryList, protocol.IndustryFocus{
			ID:           industry.ID,
			IndustryName: industry.IndustryName,
		})
	}
	return industryList, nil
}

func GetApplySponsorList() ([]protocol.IndustryFocus, error) {
	list, err := db.GetIndustryList(nil)
	if err != nil {
		return nil, err
	}
	industryList := make([]protocol.IndustryFocus, 0)
	for _, industry := range list {
		industryList = append(industryList, protocol.IndustryFocus{
			ID:           industry.ID,
			IndustryName: industry.IndustryName,
		})
	}
	return industryList, nil
}

func AdminAudienceImport(req *protocol.ReqBatchImportAudience) (string, error) {

	var (
		emailList []string
		resp      string
	)

	fileContent, err := ioutil.ReadAll(req.File)
	if err != nil {
		return "文件解析错误", exception.NewSysErrorException(err.Error())
	}
	dataList, err := excel.ParseXLSX(fileContent)
	if err != nil {
		return "文件解析错误", exception.NewSysErrorException(err.Error())
	}

	for rownum, row := range dataList {
		if rownum <= 0 {
			continue
		}
		if len(row) == 0 {
			break
		}
		email := strings.TrimSpace(row[0])
		if utils.IsEmpty(email) {
			continue
		}

		firstName := strings.TrimSpace(row[1])

		lastName := strings.TrimSpace(row[2])

		company := strings.TrimSpace(row[3])

		templateVars := map[string]string{"name": firstName + " " + lastName, "company": company}

		msg := SendEmail(email, "clnb_visitor_from", "conference_center", templateVars)
		if utils.NotEmpty(msg) {
			logger.Warnning("SendMailGunEmail error: ", msg)
		}
		if utils.NotEmpty(msg) {
			emailList = append(emailList, email)
		}

		time.Sleep(time.Millisecond * 100)
	}
	if len(emailList) > 0 {
		resp = strings.Join(emailList, ",") + " 邮箱发送失败"
	}

	return resp, nil
}

//var CarGalleryMap []model.NewsGallery
//
//// 加载新闻随机图库
//func LoadCarGallery() {
//	var err error
//	CarGalleryMap, err = db.GetConferenceNewsGallery(nil)
//	if err != nil {
//		logger.Error(err)
//		return
//	}
//}
