package service

import (
	"conferencecenter/internal/conf"
	"conferencecenter/internal/constant"
	"conferencecenter/internal/corecontext"
	"conferencecenter/internal/db"
	"conferencecenter/internal/model"
	"conferencecenter/internal/msgqueue"
	"encoding/json"
	"fmt"
	"git.code.tencent.com/smmit/smmbase/logger"
	"github.com/Shopify/sarama"
	jsoniter "github.com/json-iterator/go"
	"github.com/pkg/errors"
	"time"
)

/**
 * 消费端处理开关
 */
func GetConferenceProcessSwitch(kfkConf conf.KafkaConfSub, topic string) bool {
	pSwitchConf := kfkConf.ProcessSwitch
	if len(kfkConf.ProcessSwitch) == 1 { //总开关
		if topic == constant.TopicHandBookData {
			return corecontext.IsMainNode()
		}
		return true
	}
	switch topic {
	case constant.TopicHandBookData:
		if len(pSwitchConf) >= 1 {
			return pSwitchConf[0:1] == "1" && corecontext.IsMainNode()
		}
	}
	return false
}

func HandBookDataProcess(kfkMsg *sarama.ConsumerMessage) (proc string, err error) {
	msgContent := model.KfkHandBookData{}
	err = jsoniter.Unmarshal(kfkMsg.Value, &msgContent)
	if err != nil {
		_ = logger.Error("HandBookDataProcess:Err->", err)
		return
	}
	proc, err = handBookDataProcess(msgContent)
	if err != nil {
		_ = logger.Error("HandBookDataProcess:handBookDataProcess:Err->", err)
		return
	}
	return proc, err
}

// 发送展商手册变更通知
func SendHandBookChgKfkNotify(id int64) error {
	var (
		page         = 1
		pageSize     = 100
		subscribeMap = make(map[string]int, 0)
	)
	//查询展商手册
	handBook, err := db.QueryHandBookInfo(nil, id)
	if err != nil {
		return errors.WithMessage(err, "获取展商手册失败")
	}
	if handBook.Id <= 0 {
		err = errors.New(fmt.Sprintf("展商手册不存在，ID->%d", id))
		_ = logger.Error(err)
		return err
	}
	//查询订阅用户
	for {
		_, dataLi, err_ := db.QueryHandBookSubscribeList(nil, handBook.ConferenceId, "", "", "", page, pageSize, false)
		if err_ != nil {
			err = errors.WithMessage(err_, "获取订阅用户失败")
			return err
		}
		if len(dataLi) <= 0 {
			break
		}
		for _, data := range dataLi {
			if _, ok := subscribeMap[data.Cellphone]; ok {
				continue
			}
			if _, ok := subscribeMap[data.Email]; ok {
				continue
			}
			kfkData := model.KfkHandBookData{
				SubscribeId:      data.ID,
				ConferenceId:     data.ConferenceID,
				CnConferenceName: data.CnConferenceName,
				EnConferenceName: data.EnConferenceName,
				HandBookId:       handBook.Id,
				CnHandBookName:   handBook.CnName,
				EnHandBookName:   handBook.EnName,
				CnHandBookUrl:    handBook.CnPdf,
				EnHandBookUrl:    handBook.EnPdf,
				Cellphone:        data.Cellphone,
				Email:            data.Email,
			}
			msg, err_ := json.Marshal(kfkData)
			if err_ != nil {
				_ = logger.Error(fmt.Sprintf("SendHandBookChgKfkNotify:Marshal,kfkData->%+v,Err->", kfkData), err_)
				continue
			}
			err_ = msgqueue.KafkaNotify(constant.TopicHandBookData, msg)
			if err_ != nil {
				_ = logger.Error(fmt.Sprintf("SendHandBookChgKfkNotify:KafkaNotify,kfkData->%+v,Err->", kfkData), err_)
				continue
			}
			if data.Cellphone != "" {
				subscribeMap[data.Cellphone] = 1
			}
			if data.Email != "" {
				subscribeMap[data.Email] = 1
			}
		}
		time.Sleep(time.Millisecond * 200)
		page++
	}

	return nil
}
