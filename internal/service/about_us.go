package service

import (
	"conferencecenter/internal/constant"
	"conferencecenter/internal/corecontext"
	"conferencecenter/internal/db"
	"conferencecenter/internal/model"
	"conferencecenter/internal/protocol"
	"fmt"
	"git.code.tencent.com/smmit/smmbase/logger"
	"github.com/pkg/errors"
	"time"
)

func SaveAboutUs(req protocol.ReqAboutUs) (err error) {
	aboutUs := model.AboutUs{
		ConferenceId: req.ConferenceId,
		CnAboutUs:    req.CnAboutUs,
		EnAboutUs:    req.EnAboutUs,
		CreateUser:   req.OpUser,
		UpdateUser:   req.OpUser,
		CreateTime:   time.Now(),
		UpdateTime:   time.Now(),
	}
	err = db.UpsertAboutUs(nil, &aboutUs)
	if err != nil {
		err = errors.WithMessage(err, "存储关于我们失败")
	}
	return err
}

func AdminGetAboutUs(conferenceId int64) (*protocol.ResAboutUs, error) {
	data, err_ := db.GetAboutUs(nil, conferenceId)
	if err_ != nil {
		err := errors.WithMessage(err_, "数据库查询失败")
		return nil, err
	}
	if data.Id <= 0 {
		return nil, nil
	}
	aboutUs := protocol.ResAboutUs{
		ConferenceID: data.ConferenceId,
		CnAboutUs:    data.CnAboutUs,
		EnAboutUs:    data.EnAboutUs,
		CreateUser:   data.CreateUser,
		UpdateUser:   data.UpdateUser,
		CreateTime:   data.CreateTime.Format(constant.DateTime),
		UpdateTime:   data.UpdateTime.Format(constant.DateTime),
	}
	return &aboutUs, nil
}

func UserGetAboutUs(conferenceId int64) (*protocol.ResAboutUs, error) {
	data, err_ := db.GetAboutUs(nil, conferenceId)
	if err_ != nil {
		return nil, err_
	}
	if data.Id <= 0 {
		return nil, nil
	}
	aboutUs := protocol.ResAboutUs{
		ConferenceID: data.ConferenceId,
		CnAboutUs:    data.CnAboutUs,
		EnAboutUs:    data.EnAboutUs,
	}
	return &aboutUs, nil
}

func AddEditContactUs(req protocol.ReqContactUs) (resMsg string, err error) {
	var (
		ok bool
	)
	contactUs := model.ContactUs{
		ID:           req.ID,
		ConferenceId: req.ConferenceId,
		CnContact:    req.CnContact,
		EnContact:    req.EnContact,
		CreateUser:   req.OpUser,
		UpdateUser:   req.OpUser,
		CreateTime:   time.Now(),
		UpdateTime:   time.Now(),
	}
	tx := corecontext.SrvContext.Orm.Begin()
	if tx == nil {
		return "", errors.New("创建事务失败")
	}
	defer func() {
		if !ok {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	if contactUs.ID <= 0 && contactUs.ConferenceId > 0 {
		_, dataLi, err := db.QueryContactUsList(nil, req.ConferenceId, true)
		if err != nil {
			_ = logger.Error("AdminGetContactUsList:", err)
			return "联系人信息不存在", errors.WithStack(err)
		}
		if len(dataLi) > 0 {
			contactUs.ID = dataLi[0].ID
		}
	}
	if contactUs.ID > 0 { // 联系人信息是否存在
		rawInfo, err := db.QueryContactUsInfo(tx, contactUs.ID)
		if err != nil {
			return "系统错误", errors.WithStack(err)
		} else if rawInfo.ID <= 0 {
			err = errors.New(fmt.Sprintf("联系人信息不存在，id：%d", contactUs.ID))
			return "联系人信息不存在", err
		}
		contactUs.IsDeleted = rawInfo.IsDeleted
		contactUs.CreateUser = rawInfo.CreateUser
		contactUs.CreateTime = rawInfo.CreateTime
	}
	{
		err = db.SaveContactUsInfo(tx, &contactUs)
		if err != nil {
			return "系统错误", errors.WithStack(err)
		}
	}
	ok = true
	return resMsg, err
}

func AdminGetContactUsList(req protocol.ReqContactUsList) (protocol.ResContactUs, error) {
	var (
		infoLi = protocol.ResContactUs{}
	)
	_, dataLi, err := db.QueryContactUsList(nil, req.ConferenceId, true)
	if err != nil {
		_ = logger.Error("AdminGetContactUsList:", err)
		return infoLi, errors.WithStack(err)
	}
	for _, data := range dataLi {
		infoLi = protocol.ResContactUs{
			Id:           data.ID,
			ConferenceId: data.ConferenceId,
			CnContact:    data.CnContact,
			EnContact:    data.EnContact,
			UpdateUser:   data.UpdateUser,
			CreateUser:   data.CreateUser,
			UpdateTime:   data.UpdateTime.Format(constant.DateTime),
			CreateTime:   data.CreateTime.Format(constant.DateTime),
		}
	}

	return infoLi, err
}

func UserGetContactUsList(req protocol.ReqContactUsList) (protocol.ResContactUs, error) {
	var (
		infoLi = protocol.ResContactUs{}
	)
	_, dataLi, err := db.QueryContactUsList(nil, req.ConferenceId, true)
	if err != nil {
		_ = logger.Error(fmt.Sprintf("UserGetContactUsList:conferenceId->%d,Err->", req.ConferenceId), err)
		return infoLi, err
	}
	for _, data := range dataLi {
		infoLi = protocol.ResContactUs{
			Id:           data.ID,
			ConferenceId: data.ConferenceId,
			CnContact:    data.CnContact,
			EnContact:    data.EnContact,
		}
	}
	return infoLi, err
}
