package service

import (
	"conferencecenter/internal/corecontext"
	"conferencecenter/internal/pkg/utils"
	"encoding/json"
	"fmt"
	"git.code.tencent.com/smmit/smmbase/logger"
	"git.code.tencent.com/smmit/smmbase/sendcloud"
	mailgun "gopkg.in/mailgun/mailgun-go.v1"
	"net/url"
)

func SendSendCloudEmail(email string, templateName string, vars map[string]string, fromName string) error {
	params := map[string]string{}
	for k, v := range vars {
		params["%"+k+"%"] = v
	}

	send := sendcloud.SendCloudUser{
		ApiUser:  sendcloud.API_USER,
		ApiKey:   sendcloud.API_KEY,
		FromName: fromName,
		LabelId:  553964,
	}
	logger.Debug(email, params)
	err := send.SendEmailWithMap(email, params, templateName)
	if err != nil {
		logger.Error(err, "SendSendCloudEmail", templateName, vars)
	}
	return err
}

func SendMailGunEmailWithHtml(email string, title, content string, fromName string, params map[string]interface{}) error {
	mg := mailgun.NewMailgun("overseamail.smm.cn", corecontext.Config().MailGun.Secret, corecontext.Config().MailGun.Key)
	message := mg.NewMessage(
		fmt.Sprintf("%s <no-reply@%s>", fromName, corecontext.Config().MailGun.Domain), //,
		title, //"Reset Your SMM Account Password",
		"",
		email)

	message.SetHtml(utils.RenderHtmlText(content, params))
	_, _, err := mg.Send(message)
	if err != nil {
		logger.Error(" SendMailGunEmail ", email, title, err)
	}
	return err
}

const (
	VCODE_SEND_EMAIL = "/v3/send_notify_email"

	VCODE_SEND_SMS = "/v3/send_notify_sms"
)

// 发送邮件
func SendEmail(email, codeType, source string, params map[string]string) (msg string) {
	var (
		urlStr   = corecontext.Config().RPC.VcodeCenterInner + VCODE_SEND_EMAIL
		dataByte []byte
		dataStr  string
	)

	if params != nil && len(params) > 0 {
		dataByte, _ = json.Marshal(params)
		dataStr = string(dataByte)
	}
	data := url.Values{
		"email":     {email},
		"code_type": {codeType},
		"source":    {source},
		"params":    {dataStr},
	}
	response, err := utils.NativeSendHttpRequestPost(urlStr, data)
	if err != nil {
		logger.Warnning("SendHttpRequestPost failed :", err.Error())
		return err.Error()
	}
	myJSONStruct := CommonResponse{}
	err = json.Unmarshal(response, &myJSONStruct)
	if err != nil {
		logger.Warnning("json Unmarshal failed :", err.Error())
		return err.Error()
	}
	if myJSONStruct.Code != 0 {
		logger.Warnning(myJSONStruct.Msg)
		return
	}
	logger.Debug("发送海外通知邮件成功")
	return ""
}

// 发送短信
func SendMessage(email, codeType, source string, dataStr string) (msg string) {
	var (
		urlStr = corecontext.Config().RPC.VcodeCenterInner + VCODE_SEND_SMS
	)

	data := url.Values{
		"cellphone": {email},
		"code_type": {codeType},
		"args":      {dataStr},
		"source":    {source},
	}
	response, err := utils.NativeSendHttpRequestPost(urlStr, data)
	if err != nil {
		logger.Warnning("SendHttpRequestPost failed :", err.Error())
		return err.Error()
	}
	myJSONStruct := CommonResponse{}
	err = json.Unmarshal(response, &myJSONStruct)
	if err != nil {
		logger.Warnning("json Unmarshal failed :", err.Error())
		return err.Error()
	}
	if myJSONStruct.Code != 0 {
		logger.Warnning(myJSONStruct.Msg)
		return
	}
	logger.Debug("发送短信成功")
	return ""
}

type CommonResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data string `json:"data"`
}
