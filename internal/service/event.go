package service

import (
	"conferencecenter/internal/constant"
	"conferencecenter/internal/corecontext"
	"conferencecenter/internal/db"
	"conferencecenter/internal/model"
	"conferencecenter/internal/pkg/utils"
	"conferencecenter/internal/protocol"
	"fmt"
	"io"
	"sort"
	"strconv"
	"strings"
	"time"

	"regexp"

	"github.com/pkg/errors"
	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
)

// 活动分类列表
func QueryConferenceEventCategoryList(req protocol.ReqGetConferenceEventCategoryList) (res []protocol.ConferenceEventCategoryInfo, total int64, err error) {
	list, total, err := db.QueryConferenceEventCategoryList(nil, req.ConferenceId, req.Page, req.PageSize)
	if err != nil {
		return nil, 0, err
	}

	var eventList []protocol.ConferenceEventCategoryInfo
	for _, event := range list {
		eventInfo := protocol.ConferenceEventCategoryInfo{
			Id:           event.Id,
			ConferenceId: event.ConferenceId,
			StartTime:    event.StartTime,
			EndTime:      event.EndTime,
			EntryType:    event.EntryType,
			CnName:       event.CnName,
			EnName:       event.EnName,
			CnPlace:      event.CnPlace,
			EnPlace:      event.EnPlace,
			CnTitle:      event.CnTitle,
			EnTitle:      event.EnTitle,
			CnContent:    event.CnContent,
			EnContent:    event.EnContent,
			CnButtonLink: event.CnButtonLink,
			EnButtonLink: event.EnButtonLink,
			Picture:      event.Picture,
			Type:         event.Type,
			Sorting:      fmt.Sprintf("%.2f", event.Sorting),
		}
		eventList = append(eventList, eventInfo)

	}
	return eventList, total, err
}

func GetConferenceEventCategoryInfo(id int64) (protocol.RespAdminConferenceEventCategoryInfo, error) {
	if id <= 0 {
		return protocol.RespAdminConferenceEventCategoryInfo{}, nil
	}
	event, err := db.QueryConferenceEventCategoryInfo(nil, id)
	if err != nil {
		return protocol.RespAdminConferenceEventCategoryInfo{}, err
	}

	return protocol.RespAdminConferenceEventCategoryInfo{
		ID:           event.Id,
		ConferenceId: event.ConferenceId,
		StartTime:    event.StartTime,
		EndTime:      event.EndTime,
		EntryType:    event.EntryType,
		CnName:       event.CnName,
		EnName:       event.EnName,
		CnPlace:      event.CnPlace,
		EnPlace:      event.EnPlace,
		CnTitle:      event.CnTitle,
		EnTitle:      event.EnTitle,
		CnContent:    event.CnContent,
		EnContent:    event.EnContent,
		CnButtonLink: event.CnButtonLink,
		EnButtonLink: event.EnButtonLink,
		Picture:      event.Picture,
		Type:         event.Type,
		Sorting:      fmt.Sprintf("%.2f", event.Sorting),
	}, err
}

// 添加或编辑活动分类
func AddEditConferenceEventCategory(req protocol.ReqAdminSaveConferenceEventCategory) (resMsg string, err error) {
	var (
		previousInfo model.ConferenceEventCategory
		ok           bool
	)
	if req.ID > 0 {

		previousInfo, err = db.QueryConferenceEventCategoryInfo(nil, req.ID)
		if err != nil {
			return "系统错误", errors.WithStack(err)
		}
		previousInfo.StartTime = req.StartTime
		previousInfo.EndTime = req.EndTime
		previousInfo.EntryType = req.EntryType
		previousInfo.CnName = req.CnName
		previousInfo.EnName = req.EnName
		previousInfo.CnPlace = req.CnPlace
		previousInfo.EnPlace = req.EnPlace
		previousInfo.CnTitle = req.CnTitle
		previousInfo.EnTitle = req.EnTitle
		previousInfo.CnContent = req.CnContent
		previousInfo.EnContent = req.EnContent
		previousInfo.CnButtonLink = req.CnButtonLink
		previousInfo.EnButtonLink = req.EnButtonLink
		previousInfo.Picture = req.Picture
		previousInfo.Type = req.Type
		previousInfo.Sorting = req.Sorting
		previousInfo.UpdateTime = time.Now().Format(constant.DateTime)
	} else {

		previousInfo = model.ConferenceEventCategory{
			Id:           req.ID,
			ConferenceId: req.ConferenceId,
			StartTime:    req.StartTime,
			EndTime:      req.EndTime,
			EntryType:    req.EntryType,
			CnName:       req.CnName,
			EnName:       req.EnName,
			CnPlace:      req.CnPlace,
			EnPlace:      req.EnPlace,
			CnTitle:      req.CnTitle,
			EnTitle:      req.EnTitle,
			CnContent:    req.CnContent,
			EnContent:    req.EnContent,
			Picture:      req.Picture,
			CnButtonLink: req.CnButtonLink,
			EnButtonLink: req.EnButtonLink,
			Type:         req.Type,
			Sorting:      req.Sorting,
			Deleted:      0,
			UpdateTime:   time.Now().Format(constant.DateTime),
			CreateTime:   time.Now().Format(constant.DateTime),
		}
	}
	tx := corecontext.SrvContext.Orm.Begin()
	if tx == nil {
		return "", errors.New("创建事务失败")
	}
	defer func() {
		if !ok {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	err = db.SaveConferenceEventCategoryInfo(tx, &previousInfo)
	if err != nil {
		return "系统错误", errors.WithStack(err)
	}

	if len(req.StartTime) > 0 {
		dateList, _, err := db.QueryEventScheduleDateList(nil, req.ConferenceId, previousInfo.Id, 1, 1000)
		if err != nil {
			return "系统错误", errors.WithStack(err)
		}
		err = db.UpdateConferenceEventDate(nil, previousInfo.Id, time.Now().Unix(), "")
		if err != nil {
			return "系统错误", errors.WithStack(err)
		}

		startDate := strings.Split(req.StartTime, " ")

		if len(startDate) > 1 {
			parse, err := time.Parse(constant.DateOnly, startDate[0])
			if err != nil {
				return "系统错误", errors.WithStack(err)
			}

			dateId := int64(0)

			for k, eventDate := range dateList {
				if eventDate.Date == parse.Format(constant.DateOnly) {
					dateId = dateList[k].ID
				}
			}
			scheduleDate := model.EventScheduleDate{
				ID:            dateId,
				ConferenceId:  req.ConferenceId,
				EventId:       previousInfo.Id,
				CnDayName:     parse.Format(constant.DateOnly),
				EnDayName:     parse.Format(constant.DateOnly),
				Date:          parse.Format(constant.DateOnly),
				Time:          startDate[1],
				StartDateTime: startDate[0],
				EndDateTime:   startDate[0],
				Sorting:       strconv.FormatFloat(float64(req.Sorting), 'f', 2, 64),
				Deleted:       0,
				UpdateTime:    time.Now().Format(constant.DateTime),
				CreateTime:    time.Now().Format(constant.DateTime),
			}
			err = db.SaveEventScheduleDateInfo(nil, &scheduleDate)
			if err != nil {
				return "系统错误", errors.WithStack(err)
			}
		}

	}

	ok = true
	return
}

// 删除大会活动分类
func DeleteConferenceEventCategory(id int64) (resMsg string, err error) {

	info, err := db.QueryConferenceEventCategoryInfo(nil, id)
	if err != nil {
		return "系统错误", errors.WithStack(err)
	}
	if info.ConferenceId <= 0 {
		return "活动分类查询错误", errors.New("活动分类查询错误")
	}

	_, total, err := db.QueryConferenceEventList(nil, info.ConferenceId, "", 1, 10)
	if err != nil {
		return "系统错误", errors.WithStack(err)
	}
	if total > 0 {
		return "删除分类会导致分类下的活动无法显示", errors.New("删除分类会导致分类下的活动无法显示")
	}

	err = db.UpdateConferenceEventCategory(nil, id, time.Now().Second(), "")
	if err != nil {
		return "系统错误", errors.WithStack(err)
	}

	return
}

func QueryConferenceEventList(req protocol.ReqGetConferenceEventList) (res []protocol.ConferenceEventInfo, total int64, err error) {
	list, total, err := db.QueryConferenceEventList(nil, req.ConferenceId, req.NamePlace, req.Page, req.PageSize)
	if err != nil {
		return nil, 0, err
	}
	var eventList []protocol.ConferenceEventInfo
	for _, event := range list {
		eventInfo := protocol.ConferenceEventInfo{
			Id:                 event.Id,
			ConferenceId:       event.ConferenceId,
			CnName:             event.CnName,
			EnName:             event.EnName,
			CategoryType:       event.CategoryType,
			ScheduleIsForum:    event.ScheduleIsForum,
			IsSubSection:       event.IsSubSection,
			IsDisplayPage:      event.IsDisplayPage,
			EventTime:          event.EventTime,
			CnSponsors:         event.CnSponsors,
			EnSponsors:         event.EnSponsors,
			CnSponsorsLogo:     event.CnSponsorsLogo,
			EnSponsorsLogo:     event.EnSponsorsLogo,
			CnSponsorshipType:  event.CnSponsorshipType,
			EnSponsorshipType:  event.EnSponsorshipType,
			CnSponsors2:        event.CnSponsors2,
			EnSponsors2:        event.EnSponsors2,
			CnSponsorsLogo2:    event.CnSponsorsLogo2,
			EnSponsorsLogo2:    event.EnSponsorsLogo2,
			CnSponsorshipType2: event.CnSponsorshipType2,
			EnSponsorshipType2: event.EnSponsorshipType2,
			CreateTime:         event.CreateTime,
			Sorting:            event.Sorting,
		}

		eventList = append(eventList, eventInfo)
	}
	return eventList, total, err
}

func AdminEnterpriseTypeDownList(req protocol.ReqGetConferenceInformationEventList) (res []model.ConferenceEnterpriseType, total int64, err error) {
	list, total, err := db.AdminEnterpriseTypeDownList(nil, req.ConferenceId, 1, 1000)
	if err != nil {
		return nil, 0, err
	}

	return list, total, err
}

func GetConferenceEventInfo(id int64) (protocol.RespAdminConferenceEventInfo, error) {
	if id <= 0 {
		return protocol.RespAdminConferenceEventInfo{}, nil
	}
	company, err := db.QueryConferenceEventInfo(nil, id)
	if err != nil {
		return protocol.RespAdminConferenceEventInfo{}, err
	}

	return protocol.RespAdminConferenceEventInfo{
		ID:                 company.Id,
		ConferenceId:       company.ConferenceId,
		CnName:             company.CnName,
		EnName:             company.EnName,
		CategoryType:       company.CategoryType,
		ScheduleIsForum:    company.ScheduleIsForum,
		IsDisplayPage:      company.IsDisplayPage,
		Sorting:            company.Sorting,
		EventTime:          company.EventTime,
		CnSponsors:         company.CnSponsors,
		EnSponsors:         company.EnSponsors,
		CnSponsorsLogo:     company.CnSponsorsLogo,
		EnSponsorsLogo:     company.EnSponsorsLogo,
		CnSponsorshipType:  company.CnSponsorshipType,
		EnSponsorshipType:  company.EnSponsorshipType,
		CnSponsors2:        company.CnSponsors2,
		EnSponsors2:        company.EnSponsors2,
		CnSponsorsLogo2:    company.CnSponsorsLogo2,
		EnSponsorsLogo2:    company.EnSponsorsLogo2,
		CnSponsorshipType2: company.CnSponsorshipType2,
		EnSponsorshipType2: company.EnSponsorshipType2,
		ScheduleDateList:   []model.EventScheduleDate{},
	}, err
}

// 添加或编辑海报
func AddEditConferenceEvent(req protocol.ReqAdminSaveConferenceEvent) (resMsg string, err error) {
	var (
		previousInfo model.ConferenceEvent
		ok           bool
	)
	if req.ID > 0 {
		previousInfo, err = db.QueryConferenceEventInfo(nil, req.ID)
		if err != nil {
			return "系统错误", errors.WithStack(err)
		}
		if utils.NotEmpty(req.ScheduleIsForum) {
			previousInfo.ScheduleIsForum = req.ScheduleIsForum
		} else {
			previousInfo.CnName = req.CnName
			previousInfo.EnName = req.EnName
			previousInfo.CategoryType = req.CategoryType
			previousInfo.EventTime = req.StartTime
			previousInfo.IsDisplayPage = req.IsDisplayPage
			previousInfo.Sorting = req.Sorting
			previousInfo.UpdateTime = time.Now().Format(constant.DateTime)
			previousInfo.CnSponsors = req.CnSponsors
			previousInfo.EnSponsors = req.EnSponsors
			previousInfo.CnSponsorsLogo = req.CnSponsorsLogo
			previousInfo.EnSponsorsLogo = req.EnSponsorsLogo
			previousInfo.CnSponsorshipType = req.CnSponsorshipType
			previousInfo.EnSponsorshipType = req.EnSponsorshipType
			previousInfo.CnSponsors2 = req.CnSponsors2
			previousInfo.EnSponsors2 = req.EnSponsors2
			previousInfo.CnSponsorsLogo2 = req.CnSponsorsLogo2
			previousInfo.EnSponsorsLogo2 = req.EnSponsorsLogo2
			previousInfo.CnSponsorshipType2 = req.CnSponsorshipType2
			previousInfo.EnSponsorshipType2 = req.EnSponsorshipType2
		}
	} else {
		previousInfo = model.ConferenceEvent{
			Id:                 req.ID,
			ConferenceId:       req.ConferenceId,
			CnName:             req.CnName,
			EnName:             req.EnName,
			EventTime:          req.StartTime,
			IsDisplayPage:      req.IsDisplayPage,
			CategoryType:       req.CategoryType,
			CnSponsors:         req.CnSponsors,
			EnSponsors:         req.EnSponsors,
			CnSponsorsLogo:     req.CnSponsorsLogo,
			EnSponsorsLogo:     req.EnSponsorsLogo,
			CnSponsorshipType:  req.CnSponsorshipType,
			EnSponsorshipType:  req.EnSponsorshipType,
			CnSponsors2:        req.CnSponsors2,
			EnSponsors2:        req.EnSponsors2,
			CnSponsorsLogo2:    req.CnSponsorsLogo2,
			EnSponsorsLogo2:    req.EnSponsorsLogo2,
			CnSponsorshipType2: req.CnSponsorshipType2,
			EnSponsorshipType2: req.EnSponsorshipType2,
			Sorting:            req.Sorting,
			Deleted:            0,
			UpdateTime:         time.Now().Format(constant.DateTime),
			CreateTime:         time.Now().Format(constant.DateTime),
		}
	}
	tx := corecontext.SrvContext.Orm.Begin()
	if tx == nil {
		return "", errors.New("创建事务失败")
	}
	defer func() {
		if !ok {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	err = db.SaveConferenceEventInfo(tx, &previousInfo)
	if err != nil {
		return "系统错误", errors.WithStack(err)
	}

	ok = true
	return
}

func daysBetween(start, end string) (int, error) {
	layout := "2006-01-02"
	t1, err := time.Parse(layout, start)
	if err != nil {
		return 0, err
	}

	t2, err := time.Parse(layout, end)
	if err != nil {
		return 0, err
	}

	duration := t2.Sub(t1)
	return int(duration.Hours() / 24), nil
}

// 删除大会活动
func DeleteConferenceEvent(EventId int64) (resMsg string, err error) {
	var (
		ok bool
	)

	tx := corecontext.SrvContext.Orm.Begin()
	if tx == nil {
		return "", errors.New("创建事务失败")
	}
	defer func() {
		if !ok {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	err = db.UpdateConferenceEvent(tx, EventId, time.Now().Second(), "")
	if err != nil {
		return "系统错误", errors.WithStack(err)
	}
	err = db.UpdateConferenceEventDate(nil, EventId, time.Now().Unix(), "")
	if err != nil {
		return "系统错误", errors.WithStack(err)
	}

	ok = true
	return
}

// 添加或编辑海报
func AddEditEventScheduleDate(req protocol.ReqAdminSaveEventScheduleDate) (resMsg string, err error) {
	var (
		previousInfo model.EventScheduleDate
		ok           bool
	)
	if req.ID > 0 {
		previousInfo, err = db.QueryEventScheduleDateInfo(nil, req.ID)
		if err != nil {
			return "系统错误", errors.WithStack(err)
		}
		if utils.NotEmpty(req.CnDayName) {
			previousInfo.CnDayName = req.CnDayName
		}
		if utils.NotEmpty(req.EnDayName) {
			previousInfo.EnDayName = req.EnDayName
		}
		if utils.NotEmpty(req.Sorting) {
			previousInfo.Sorting = req.Sorting
		}
		previousInfo.UpdateTime = time.Now().Format(constant.DateTime)
	}
	tx := corecontext.SrvContext.Orm.Begin()
	if tx == nil {
		return "", errors.New("创建事务失败")
	}
	defer func() {
		if !ok {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	err = db.SaveEventScheduleDateInfo(tx, &previousInfo)
	if err != nil {
		return "系统错误", errors.WithStack(err)
	}

	ok = true
	return
}

func QueryEventScheduleForumList(req protocol.ReqGetConferenceForumList) (res []model.EventScheduleForum, total int64, err error) {
	list, total, err := db.QueryEventScheduleForumList(nil, req.DayId)
	if err != nil {
		return nil, 0, err
	}
	return list, total, err
}

func GetEventScheduleForumInfo(id int64) (protocol.RespEventScheduleForumInfo, error) {
	if id <= 0 {
		return protocol.RespEventScheduleForumInfo{}, nil
	}
	company, err := db.QueryEventScheduleForumInfo(nil, id)
	if err != nil {
		return protocol.RespEventScheduleForumInfo{}, err
	}

	return protocol.RespEventScheduleForumInfo{
		ID:           company.ID,
		ConferenceId: company.ConferenceId,
		DayId:        company.DayId,
		CnName:       company.CnName,
		EnName:       company.EnName,
		Sorting:      company.Sorting,
	}, err
}

// 添加或编辑海报
func AddEditEventScheduleForum(req protocol.ReqAdminSaveEventScheduleForum) (resMsg string, err error) {
	var (
		previousInfo model.EventScheduleForum
		ok           bool
	)
	previousInfo = model.EventScheduleForum{
		ID:           req.ID,
		ConferenceId: req.ConferenceId,
		EventId:      req.EventId,
		DayId:        req.DayId,
		CnName:       req.CnName,
		EnName:       req.EnName,
		Sorting:      req.Sorting,
		Deleted:      0,
		UpdateTime:   time.Now(),
		CreateTime:   time.Now(),
	}
	tx := corecontext.SrvContext.Orm.Begin()
	if tx == nil {
		return "", errors.New("创建事务失败")
	}
	defer func() {
		if !ok {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	err = db.SaveEventScheduleForum(tx, &previousInfo)
	if err != nil {
		return "系统错误", errors.WithStack(err)
	}

	ok = true
	return
}

// 删除展会分论坛信息
func DeleteEventScheduleForum(conferenceId int64) (resMsg string, err error) {
	var (
		ok bool
	)

	tx := corecontext.SrvContext.Orm.Begin()
	if tx == nil {
		return "", errors.New("创建事务失败")
	}
	defer func() {
		if !ok {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	err = db.UpdateEventScheduleForum(tx, conferenceId, time.Now().Second(), "")
	if err != nil {
		return "系统错误", errors.WithStack(err)
	}

	ok = true
	return
}

func QueryEventScheduleList(req protocol.ReqGetEventScheduleList) (res []protocol.EventSchedule, total int64, err error) {
	list, total, err := db.QueryEventScheduleList(nil, req.EventId, req.DayId, req.ForumId, req.Name, req.Page, req.PageSize)
	if err != nil {
		return nil, 0, err
	}
	var eventScheduleList []protocol.EventSchedule
	for _, eventSchedule := range list {
		eventScheduleInfo := protocol.EventSchedule{
			ID:            eventSchedule.ID,
			ConferenceId:  eventSchedule.ConferenceId,
			EventId:       eventSchedule.EventId,
			DayId:         eventSchedule.DayId,
			ForumId:       eventSchedule.ForumId,
			CnTitle:       eventSchedule.CnTitle,
			EnTitle:       eventSchedule.EnTitle,
			ScheduleStart: eventSchedule.ScheduleStart,
			ScheduleEnd:   eventSchedule.ScheduleEnd,
			Sorting:       fmt.Sprintf("%.2f", eventSchedule.Sorting),
		}
		eventScheduleList = append(eventScheduleList, eventScheduleInfo)
	}
	return eventScheduleList, total, err
}

func GetEventScheduleInfo(id int64) (protocol.RespEventScheduleInfo, error) {
	if id <= 0 {
		return protocol.RespEventScheduleInfo{}, nil
	}
	company, err := db.QueryEventScheduleInfo(nil, id)
	if err != nil {
		return protocol.RespEventScheduleInfo{}, err
	}

	return protocol.RespEventScheduleInfo{
		ID:            company.ID,
		ConferenceId:  company.ConferenceId,
		EventId:       company.EventId,
		DayId:         company.DayId,
		ForumId:       company.ForumId,
		CnTitle:       company.CnTitle,
		EnTitle:       company.EnTitle,
		ScheduleStart: company.ScheduleStart,
		ScheduleEnd:   company.ScheduleEnd,
		CnContent:     company.CnContent,
		EnContent:     company.EnContent,
		IsAgenda:      company.IsAgenda,
		Sorting:       company.Sorting,
	}, err
}

// 添加或编辑海报
func AddEditEventSchedule(req protocol.ReqAdminSaveEventSchedule) (resMsg string, err error) {
	var (
		previousInfo model.EventSchedule
		ok           bool
	)
	previousInfo = model.EventSchedule{
		ID:            req.ID,
		ConferenceId:  req.ConferenceId,
		EventId:       req.EventId,
		DayId:         req.DayId,
		ForumId:       req.ForumId,
		CnTitle:       req.CnTitle,
		EnTitle:       req.EnTitle,
		ScheduleStart: req.ScheduleStart,
		ScheduleEnd:   req.ScheduleEnd,
		CnContent:     req.CnContent,
		EnContent:     req.EnContent,
		Sorting:       req.Sorting,
		IsAgenda:      req.IsAgenda,
		Deleted:       0,
		UpdateTime:    time.Now(),
		CreateTime:    time.Now(),
	}
	tx := corecontext.SrvContext.Orm.Begin()
	if tx == nil {
		return "", errors.New("创建事务失败")
	}
	defer func() {
		if !ok {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	err = db.SaveEventSchedule(tx, &previousInfo)
	if err != nil {
		return "系统错误", errors.WithStack(err)
	}

	ok = true
	return
}

// 删除大会数据
func DeleteEventSchedule(conferenceId int64) (resMsg string, err error) {
	var (
		ok bool
	)

	tx := corecontext.SrvContext.Orm.Begin()
	if tx == nil {
		return "", errors.New("创建事务失败")
	}
	defer func() {
		if !ok {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	err = db.UpdateEventSchedule(tx, conferenceId, time.Now().Second(), "")
	if err != nil {
		return "系统错误", errors.WithStack(err)
	}

	ok = true
	return
}

func QueryEventScheduleGuestList(req protocol.ReqGetEventScheduleGuestList) (res []model.EventScheduleGuest, total int64, err error) {
	list, total, err := db.QueryEventScheduleGuestList(nil, 0, req.ScheduleId, req.Name, req.Page, req.PageSize)
	if err != nil {
		return nil, 0, err
	}
	return list, total, err
}

func GetEventScheduleGuestInfo(id int64) (protocol.RespEventScheduleGuestInfo, error) {
	if id <= 0 {
		return protocol.RespEventScheduleGuestInfo{}, nil
	}
	company, err := db.QueryEventScheduleGuestInfo(nil, id)
	if err != nil {
		return protocol.RespEventScheduleGuestInfo{}, err
	}

	return protocol.RespEventScheduleGuestInfo{
		ID:            company.ID,
		ConferenceId:  company.ConferenceId,
		ScheduleId:    company.ScheduleId,
		CnCompany:     company.CnCompany,
		EnCompany:     company.EnCompany,
		EnPosition:    company.EnPosition,
		CnPosition:    company.CnPosition,
		CnAppellation: company.CnAppellation,
		EnAppellation: company.EnAppellation,
		Picture:       company.Picture,
		GuestIdentity: company.GuestIdentity,
		Sorting:       company.Sorting,
	}, err
}

// 添加或编辑海报
func AddEditEventScheduleGuest(req protocol.ReqAdminSaveEventScheduleGuest) (resMsg string, err error) {
	var (
		previousInfo model.EventScheduleGuest
		ok           bool
	)
	previousInfo = model.EventScheduleGuest{
		ID:            req.ID,
		ConferenceId:  req.ConferenceId,
		EventId:       req.EventId,
		ScheduleId:    req.ScheduleId,
		CnCompany:     req.CnCompany,
		EnCompany:     req.EnCompany,
		EnPosition:    req.EnPosition,
		CnPosition:    req.CnPosition,
		CnAppellation: req.CnAppellation,
		EnAppellation: req.EnAppellation,
		Picture:       req.Picture,
		GuestIdentity: req.GuestIdentity,
		Sorting:       req.Sorting,
		UpdateTime:    time.Now(),
		CreateTime:    time.Now(),
	}
	tx := corecontext.SrvContext.Orm.Begin()
	if tx == nil {
		return "", errors.New("创建事务失败")
	}
	defer func() {
		if !ok {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	err = db.SaveEventScheduleGuest(tx, &previousInfo)
	if err != nil {
		return "系统错误", errors.WithStack(err)
	}

	ok = true
	return
}

// 删除大会数据
func DeleteEventScheduleGuest(conferenceId int64) (resMsg string, err error) {
	var (
		ok bool
	)

	tx := corecontext.SrvContext.Orm.Begin()
	if tx == nil {
		return "", errors.New("创建事务失败")
	}
	defer func() {
		if !ok {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	err = db.UpdateEventScheduleGuest(tx, conferenceId, time.Now().Second(), "")
	if err != nil {
		return "系统错误", errors.WithStack(err)
	}

	ok = true
	return
}

func checkDuplicates(arr []int64) bool {
	duplicates := make(map[int64]bool)

	for _, item := range arr {
		if _, ok := duplicates[item]; ok {
			// If item is found in map, it means it's a duplicate
			return true
		}

		// Add item to map
		duplicates[item] = true
	}

	return false
}

func UserConferenceEventList(req protocol.ReqWebConferenceList) (res protocol.ResUserConferenceEventList, err error) {

	//info, err := db.QueryConferenceEventCategory(nil, req.ConferenceId, req.CategoryType)
	//if info.Id <= 0 {
	//	return res, nil
	//}

	//res.Category = protocol.ConferenceEventCategory{
	//	ConferenceId: info.ConferenceId,
	//	EntryType:    info.EntryType,
	//	CnName:       info.CnName,
	//	EnName:       info.EnName,
	//	CnPlace:      info.CnPlace,
	//	EnPlace:      info.EnPlace,
	//	CnTitle:      info.CnTitle,
	//	EnTitle:      info.EnTitle,
	//	CnContent:    info.CnContent,
	//	EnContent:    info.EnContent,
	//	CnButtonLink: info.CnButtonLink,
	//	EnButtonLink: info.EnButtonLink,
	//	Type:         info.Type,
	//	Picture:      info.Picture,
	//	CnLanguage:   "普通话/英文",
	//	EnLanguage:   "Mandarin/English",
	//}
	//if utils.NotEmpty(info.StartTime) && utils.NotEmpty(info.EndTime) {
	//
	//	start, err := time.Parse(constant.DateOnly, info.StartTime) // 解析字符串为日期格式
	//	if err != nil {
	//		fmt.Println("解析日期出错：", err)
	//	}
	//	end, err := time.Parse(constant.DateOnly, info.EndTime) // 解析字符串为日期格式
	//	if err != nil {
	//		fmt.Println("解析日期出错：", err)
	//	}
	//	timeRange := formatTimeRange(start, end)
	//	res.Category.CnDate = timeRange
	//
	//	englishTimeS := start.Format("Jan 2, 2006")
	//	englishTimeE := end.Format("Jan 2, 2006")
	//	res.Category.EnDate = englishTimeS + "-" + englishTimeE
	//}
	//if utils.NotEmpty(info.EntryType) {
	//	res.Category.CnEntryType = info.EntryType
	//	if info.EntryType == "免费预约" {
	//		res.Category.EnEntryType = "Free of charge"
	//	} else {
	//		res.Category.EnEntryType = "Buy tickets"
	//	}
	//}

	categoryList, _, err := db.QueryConferenceEventCategoryList(nil, req.ConferenceId, 1, 100)
	if err != nil {
		return res, err
	}

	list, _, err := db.QueryConferenceEventList(nil, req.ConferenceId, "", 1, 1000)
	if err != nil {
		return res, err
	}

	var resEventList []protocol.ResConferenceEvent

	for _, event := range list {

		var eventList []protocol.ConferenceEventInfo

		eventInfo := protocol.ConferenceEventInfo{
			Id:              event.Id,
			ConferenceId:    event.ConferenceId,
			CnName:          event.CnName,
			EnName:          event.EnName,
			CategoryType:    event.CategoryType,
			ScheduleIsForum: event.ScheduleIsForum,
			IsSubSection:    event.IsSubSection,
			IsDisplayPage:   event.IsDisplayPage,
			CnSponsors:      event.CnSponsors,
			EnSponsors:      event.EnSponsors,
			CnSponsorsLogo:  event.CnSponsorsLogo,
			EnSponsorsLogo:  event.EnSponsorsLogo,

			CnSponsorshipType:  event.CnSponsorshipType,
			EnSponsorshipType:  event.EnSponsorshipType,
			CnSponsors2:        event.CnSponsors2,
			EnSponsors2:        event.EnSponsors2,
			CnSponsorsLogo2:    event.CnSponsorsLogo2,
			EnSponsorsLogo2:    event.EnSponsorsLogo2,
			CnSponsorshipType2: event.CnSponsorshipType2,
			EnSponsorshipType2: event.EnSponsorshipType2,

			CreateTime: event.CreateTime,
			Sorting:    event.Sorting,
			Times:      event.EventTime,
		}

		for _, category := range categoryList {
			if category.Id == int64(event.CategoryType) {
				eventInfo.CnCategoryName = category.CnName
				eventInfo.EnCategoryName = category.EnName
				eventInfo.CnButtonLink = category.CnButtonLink
				eventInfo.EnButtonLink = category.EnButtonLink
			}
		}

		eventList = append(eventList, eventInfo)
		sortSlice(eventList)

		eventDate, err := time.Parse(constant.DateOnly, event.EventTime) // 解析字符串为日期格式
		if err != nil {
			fmt.Println("解析日期出错：", err)
		}
		resEvent := protocol.ResConferenceEvent{
			Id:     event.Id,
			Date:   event.EventTime,
			CnDate: eventDate.Format("1月2日"),
			EnDate: eventDate.Format("Jan 2, 2006"),
		}
		resEvent.List = eventList
		resEventList = append(resEventList, resEvent)
	}
	res.List = resEventList
	return res, err
}

func sortSlice(people []protocol.ConferenceEventInfo) []protocol.ConferenceEventInfo {
	sort.Slice(people, func(i, j int) bool {
		return people[i].Sorting < people[j].Sorting
	})
	return people
}

func GetUserConferenceEventInfo(id int64) (protocol.RespUserConferenceEventInfo, error) {
	if id <= 0 {
		return protocol.RespUserConferenceEventInfo{}, nil
	}
	company, err := db.QueryConferenceEventInfo(nil, id)
	if err != nil {
		return protocol.RespUserConferenceEventInfo{}, err
	}
	if company.ConferenceId <= 0 {
		return protocol.RespUserConferenceEventInfo{}, nil
	}
	dateList, err := db.UserEventScheduleDateList(nil, id)
	if err != nil {
		return protocol.RespUserConferenceEventInfo{}, err
	}
	var eventDateList []protocol.RespEventDateInfo
	for _, eventDate := range dateList {
		eventDateList = append(eventDateList, protocol.RespEventDateInfo{
			Date: eventDate.Date,
			Time: eventDate.Time,
		})
	}

	//informationIdList, err := db.UserConferenceInformationEventList(nil, 0, id)
	//if err != nil {
	//	return protocol.RespUserConferenceEventInfo{}, err
	//}
	//var informationIds []int64
	//if len(informationIdList) > 0 {
	//	for _, eventId := range informationIdList {
	//		informationIds = append(informationIds, eventId.InformationId)
	//	}
	//}

	guestList, err := db.UserConferenceInformationEventList(nil, 0, 2, true)
	if err != nil {
		return protocol.RespUserConferenceEventInfo{}, err
	}

	informationList, err := db.UserConferenceInformationEventList(nil, 0, 0, true)
	if err != nil {
		return protocol.RespUserConferenceEventInfo{}, err
	}

	columnList, err := db.UserConferenceColumnList(nil, company.ConferenceId, 0, 0)
	if err != nil {
		return protocol.RespUserConferenceEventInfo{}, err
	}

	subColumnList, err := db.UserConferenceColumnList(nil, company.ConferenceId, -1, 0)
	if err != nil {
		return protocol.RespUserConferenceEventInfo{}, err
	}

	var conferenceColumnList []protocol.ConferenceColumnInfo
	if len(informationList) > 0 {
		for _, column := range columnList {
			conferenceColumn := protocol.ConferenceColumnInfo{
				Id:           column.Id,
				ConferenceId: column.ConferenceId,
				CnName:       column.CnName,
				EnName:       column.EnName,
				IsSubSection: column.IsSubSection,
				SubSectionId: column.SubSectionId,
			}
			if column.IsSubSection == 0 {
				var respInformationList []model.ConferenceInformation
				for _, information := range informationList {
					if column.Id == information.ColumnId {
						respInformationList = append(respInformationList, information)
					}
				}
				if len(respInformationList) > 0 {
					conferenceColumn.ConferenceInformationList = respInformationList
					if column.SubSectionId == 0 {
						if conferenceColumn.ConferenceInformationList != nil {
							conferenceColumnList = append(conferenceColumnList, conferenceColumn)
						}
					}
				}
			} else {
				var respSubColumnList []protocol.ConferenceColumnInfo
				for _, subColumn := range subColumnList {

					if column.Id == subColumn.SubSectionId {
						var respInformationList []model.ConferenceInformation

						subColumnInfo := protocol.ConferenceColumnInfo{
							Id:           subColumn.Id,
							ConferenceId: subColumn.ConferenceId,
							CnName:       subColumn.CnName,
							EnName:       subColumn.EnName,
							IsSubSection: subColumn.IsSubSection,
							SubSectionId: subColumn.SubSectionId,
						}

						for _, information := range informationList {
							if subColumn.Id == information.ColumnId {
								respInformationList = append(respInformationList, information)
							}
						}
						if len(respInformationList) > 0 {
							subColumnInfo.ConferenceInformationList = respInformationList
							respSubColumnList = append(respSubColumnList, subColumnInfo)
						}
					}
				}
				if len(respSubColumnList) > 0 {
					conferenceColumn.ConferenceColumnList = respSubColumnList
					conferenceColumnList = append(conferenceColumnList, conferenceColumn)
				}
			}

		}
	}

	return protocol.RespUserConferenceEventInfo{
		ID:               company.Id,
		ConferenceId:     company.ConferenceId,
		CnName:           company.CnName,
		EnName:           company.EnName,
		CategoryType:     company.CategoryType,
		ScheduleIsForum:  company.ScheduleIsForum,
		IsDisplayPage:    company.IsDisplayPage,
		EventDateList:    eventDateList,
		GuestList:        guestList,
		InformationList:  conferenceColumnList,
		ScheduleDateList: dateList,

		CnSponsors:         company.CnSponsors,
		EnSponsors:         company.EnSponsors,
		CnSponsorsLogo:     company.CnSponsorsLogo,
		EnSponsorsLogo:     company.EnSponsorsLogo,
		CnSponsorshipType:  company.CnSponsorshipType,
		EnSponsorshipType:  company.EnSponsorshipType,
		CnSponsors2:        company.CnSponsors2,
		EnSponsors2:        company.EnSponsors2,
		CnSponsorsLogo2:    company.CnSponsorsLogo2,
		EnSponsorsLogo2:    company.EnSponsorsLogo2,
		CnSponsorshipType2: company.CnSponsorshipType2,
		EnSponsorshipType2: company.EnSponsorshipType2,
	}, err
}

func UserEventScheduleList(req protocol.ReqGetEventScheduleList) (res protocol.ResUserEventScheduleList, err error) {

	info, err := db.QueryConferenceEventInfo(nil, req.EventId)
	if err != nil {
		return res, err
	}
	res.CnName = info.CnName
	res.EnName = info.EnName
	res.Category = info.CategoryType
	res.CnSponsors = info.CnSponsors
	res.EnSponsors = info.EnSponsors
	res.CnSponsorsLogo = info.CnSponsorsLogo
	res.EnSponsorsLogo = info.EnSponsorsLogo
	res.CnSponsorshipType = info.CnSponsorshipType
	res.EnSponsorshipType = info.EnSponsorshipType
	res.CnSponsors2 = info.CnSponsors2
	res.EnSponsors2 = info.EnSponsors2
	res.CnSponsorsLogo2 = info.CnSponsorsLogo2
	res.EnSponsorsLogo2 = info.EnSponsorsLogo2
	res.CnSponsorshipType2 = info.CnSponsorshipType2
	res.EnSponsorshipType2 = info.EnSponsorshipType2

	conference, err := db.QueryConferenceInfo(nil, info.ConferenceId)
	if err != nil {
		return res, err
	}
	res.TemplateType = conference.TemplateType

	confInfo, err := db.QueryConferenceEventCategoryInfo(nil, int64(info.CategoryType))
	if err != nil {
		return res, err
	}
	res.CnButtonLink = confInfo.CnButtonLink
	res.EnButtonLink = confInfo.EnButtonLink

	list, _, err := db.QueryEventScheduleList(nil, req.EventId, req.DayId, req.ForumId, req.Name, 1, 1000)
	if err != nil {
		return res, err
	}

	guestList, _, err := db.QueryEventScheduleGuestList(nil, req.EventId, 0, "", 1, 1000)
	if err != nil {
		return res, err
	}
	var scheduleInfoList []protocol.EventScheduleInfo
	for _, company := range list {
		scheduleInfo := protocol.EventScheduleInfo{
			ID:            company.ID,
			ConferenceId:  company.ConferenceId,
			EventId:       company.EventId,
			DayId:         company.DayId,
			ForumId:       company.ForumId,
			CnTitle:       company.CnTitle,
			EnTitle:       company.EnTitle,
			ScheduleStart: company.ScheduleStart,
			ScheduleEnd:   company.ScheduleEnd,
			CnContent:     company.CnContent,
			EnContent:     company.EnContent,
			IsAgenda:      company.IsAgenda,
			Sorting:       company.Sorting,
			//	CnButtonLink:  confInfo.CnButtonLink,
			//	EnButtonLink:  confInfo.EnButtonLink,
		}
		for _, guest := range guestList {
			if company.ID == guest.ScheduleId {
				scheduleInfo.GuestList = append(scheduleInfo.GuestList, guest)
			}
		}

		scheduleInfoList = append(scheduleInfoList, scheduleInfo)
	}
	res.List = scheduleInfoList
	return res, err
}

func formatTimeRange(start time.Time, end time.Time) string {
	// 判断是否同年同月
	if start.Year() == end.Year() && start.Month() == end.Month() {
		// 同年同月，只需显示年月和日期范围
		return fmt.Sprintf("%d年%d月%d日-%d日", start.Year(), start.Month(), start.Day(), end.Day())
	}
	// 不同年或者不同月，需要显示年月日
	return fmt.Sprintf("%d年%d月%d日-%d年%d月%d日", start.Year(), start.Month(), start.Day(), end.Year(), end.Month(), end.Day())
}

func main() {
	start := time.Date(2016, time.January, 10, 0, 0, 0, 0, time.UTC)
	end := time.Date(2016, time.January, 15, 0, 0, 0, 0, time.UTC)
	fmt.Println(formatTimeRange(start, end))
}

func UserForumCategoryConfigList() (list []model.ForumCategoryConfig, total int64, err error) {

	list, total, err = db.QueryForumCategoryConfigList(nil)
	if err != nil {
		return nil, 0, err
	}
	return list, total, err
}

func UserQueryExpertInfoV3List(req protocol.ReqGetConferenceEventCategoryList) (list []model.EventExpertInfo, total int64, err error) {

	list, total, err = db.QueryExpertInfoV3List(nil, req.Page, req.PageSize)
	if err != nil {
		return nil, 0, err
	}
	return list, total, err
}

func UserAnnualSelectionList(req protocol.ReqGetConferenceEventCategoryList) (list []model.AnnualSelection, total int64, err error) {

	list, total, err = db.QueryAnnualSelection(nil, req.Page, req.PageSize)
	if err != nil {
		return nil, 0, err
	}
	return list, total, err
}

// AdminImportEventSchedule 批量导入活动日程（Excel）
func AdminImportEventSchedule(fileReader io.Reader, fileName string, conferenceId int64) (result interface{}, err error) {
	// 1. 读取Excel内容
	f, err := excelize.OpenReader(fileReader)
	if err != nil {
		return nil, fmt.Errorf("Excel文件解析失败: %v", err)
	}
	sheetName := f.GetSheetName(0)
	rows, err := f.GetRows(sheetName)
	if err != nil {
		return nil, fmt.Errorf("读取Excel内容失败: %v", err)
	}
	if len(rows) < 2 {
		return nil, errors.New("Excel无有效数据")
	}

	// 2. 解析表头，映射字段
	headers := rows[0]
	var dataRows []protocol.EventScheduleImportRow
	errRows := make([]string, 0)
	rowMap := make(map[string]int)
	for i, h := range headers {
		h = strings.TrimSpace(h)
		rowMap[h] = i
	}

	// 3. 逐行解析、校验
	for rowIdx, row := range rows[1:] {
		var item protocol.EventScheduleImportRow
		get := func(col string) string {
			idx, ok := rowMap[col]
			if !ok || idx >= len(row) {
				return ""
			}
			return strings.TrimSpace(row[idx])
		}
		item.EventCode = get("活动编号（必填）")
		item.CategoryName = get("活动分类（必填）")
		item.EventName = get("活动名称（必填）")
		item.EventNameEn = get("活动名称英文")
		item.EventDate = get("活动日期（必填）")
		item.ShowEventDetail = get("是否展示活动详情")
		item.ScheduleTitle = get("日程标题（必填）")
		item.ScheduleTitleEn = get("标题英文")
		item.ScheduleType = get("日程类型")
		item.ScheduleTime = get("日程时间（必填）")
		item.ScheduleDesc = get("日程描述")
		item.ScheduleDescEn = get("描述英文")
		item.GuestName = get("嘉宾称谓")
		item.GuestNameEn = get("称谓英文")
		item.GuestPosition = get("嘉宾职位")
		item.GuestPositionEn = get("职位英文")
		item.GuestCompany = get("嘉宾公司")
		item.GuestCompanyEn = get("公司英文")
		item.GuestIdentity = get("嘉宾身份")
		item.ConferenceId = conferenceId
		// 必填项校验（逐项校验并打印缺失字段）
		missingFields := make([]string, 0)
		if item.EventCode == "" {
			missingFields = append(missingFields, "活动编号")
		}
		if item.CategoryName == "" {
			missingFields = append(missingFields, "活动分类")
		}
		if item.EventName == "" {
			missingFields = append(missingFields, "活动名称")
		}
		if item.EventDate == "" {
			missingFields = append(missingFields, "活动日期")
		}
		if item.ScheduleTitle == "" {
			missingFields = append(missingFields, "日程标题")
		}
		if item.ScheduleTime == "" {
			missingFields = append(missingFields, "日程时间")
		}
		if len(missingFields) > 0 {
			errRows = append(errRows, fmt.Sprintf("第%d行: 必填项缺失 [%s]", rowIdx+2, strings.Join(missingFields, ", ")))
			continue
		}
		// 日期格式校验，允许YYYY-M-D、YYYY-MM-DD等多种写法，自动补零为YYYY-MM-DD
		datePattern := regexp.MustCompile(`^(\d{4})-(\d{1,2})-(\d{1,2})$`)
		matches := datePattern.FindStringSubmatch(item.EventDate)
		if matches == nil {
			errRows = append(errRows, fmt.Sprintf("第%d行: 活动日期格式错误，应为YYYY-MM-DD", rowIdx+2))
			continue
		}
		// 自动补零，标准化为YYYY-MM-DD
		year, month, day := matches[1], matches[2], matches[3]
		item.EventDate = fmt.Sprintf("%s-%02s-%02s", year, month, day)
		// 日程时间格式校验
		if !regexp.MustCompile(`^\d{2}:\d{2}-\d{2}:\d{2}$`).MatchString(item.ScheduleTime) {
			errRows = append(errRows, fmt.Sprintf("第%d行: 日程时间格式错误，应为hh:mm-hh:mm", rowIdx+2))
			continue
		}
		// 是否展示活动详情
		if item.ShowEventDetail == "" {
			item.ShowEventDetail = "是"
		}
		if item.ShowEventDetail != "是" && item.ShowEventDetail != "否" {
			errRows = append(errRows, fmt.Sprintf("第%d行: 是否展示活动详情只能为'是'或'否'", rowIdx+2))
			continue
		}
		// 日程类型
		if item.ScheduleType == "" {
			item.ScheduleType = "议程"
		}
		if item.ScheduleType != "议程" && item.ScheduleType != "非议程" {
			errRows = append(errRows, fmt.Sprintf("第%d行: 日程类型只能为'议程'或'非议程'", rowIdx+2))
			continue
		}
		// 嘉宾身份
		if item.GuestIdentity != "" && item.GuestIdentity != "Panelist" && item.GuestIdentity != "Moderator" {
			errRows = append(errRows, fmt.Sprintf("第%d行: 嘉宾身份只能为'Panelist'或'Moderator'", rowIdx+2))
			continue
		}
		dataRows = append(dataRows, item)
	}
	if len(errRows) > 0 {
		return errRows, errors.New("数据校验未通过")
	}

	// 4. 校验活动分类、活动编号唯一性
	dbConn := db.OrmDB(nil)
	// 查询所有活动分类，限定当前conference_id
	var allCategories []model.ConferenceEventCategory
	dbConn.Table("conference_event_category").Where("deleted=0 AND conference_id = ?", conferenceId).Find(&allCategories)
	categoryMap := make(map[string]model.ConferenceEventCategory)
	for _, c := range allCategories {
		categoryMap[c.CnName] = c
	}

	// 校验活动编号是否存在（对于需要修改的活动）
	eventCodeMap := make(map[string]bool)
	for i, row := range dataRows {
		if eventCodeMap[row.EventCode] {
			errRows = append(errRows, fmt.Sprintf("第%d行: 活动编号'%s'在Excel中重复", i+2, row.EventCode))
			continue
		}
		eventCodeMap[row.EventCode] = true

		// 检查活动编号是否在数据库中存在
		_, err := db.QueryConferenceEventByCode(nil, conferenceId, row.EventCode)
		if err != nil && err != gorm.ErrRecordNotFound {
			errRows = append(errRows, fmt.Sprintf("第%d行: 查询活动编号'%s'时发生错误: %v", i+2, row.EventCode, err))
			continue
		}
		// 如果活动不存在，记录为需要新建
		if err == gorm.ErrRecordNotFound {
			// 活动不存在，将创建新活动
		}
	}

	if len(errRows) > 0 {
		return errRows, errors.New("数据校验未通过")
	}

	// 按活动编号分组数据
	eventGroups := make(map[string][]protocol.EventScheduleImportRow)
	for _, row := range dataRows {
		eventGroups[row.EventCode] = append(eventGroups[row.EventCode], row)
	}

	// 事务批量入库
	err = dbConn.Transaction(func(tx *gorm.DB) error {
		processedEvents := make(map[string]bool) // 记录已处理的活动编号

		for eventCode, eventRows := range eventGroups {
			if processedEvents[eventCode] {
				continue
			}
			processedEvents[eventCode] = true

			// 获取第一行作为活动基本信息
			firstRow := eventRows[0]

			// 校验活动分类
			cat, ok := categoryMap[firstRow.CategoryName]
			if !ok {
				return fmt.Errorf("活动编号'%s': 当前conference_id=%d下未找到活动分类'%s'，请先创建该分类", eventCode, conferenceId, firstRow.CategoryName)
			}

			// 查询或创建活动
			existEvent, err := db.QueryConferenceEventByCode(tx, conferenceId, eventCode)
			var currentEvent model.ConferenceEvent

			if err == gorm.ErrRecordNotFound {
				// 创建新活动
				currentEvent = model.ConferenceEvent{
					ConferenceId:  conferenceId,
					EventCode:     eventCode,
					CnName:        firstRow.EventName,
					EnName:        firstRow.EventNameEn,
					EventTime:     firstRow.EventDate,
					CategoryType:  int(cat.Id),
					IsDisplayPage: mapShowEventDetail(firstRow.ShowEventDetail),
					Deleted:       0,
					Sorting:       "1.00",
					CreateTime:    time.Now().Format("2006-01-02 15:04:05"),
				}
				if err := tx.Table("conference_event").Create(&currentEvent).Error; err != nil {
					return fmt.Errorf("活动编号'%s': 创建活动失败: %v", eventCode, err)
				}
			} else if err != nil {
				return fmt.Errorf("活动编号'%s': 查询活动失败: %v", eventCode, err)
			} else {
				// 更新现有活动，保留ID和创建时间
				currentEvent = existEvent
				currentEvent.CnName = firstRow.EventName
				currentEvent.EnName = firstRow.EventNameEn
				currentEvent.EventTime = firstRow.EventDate
				currentEvent.CategoryType = int(cat.Id)
				currentEvent.IsDisplayPage = mapShowEventDetail(firstRow.ShowEventDetail)
				currentEvent.UpdateTime = time.Now().Format("2006-01-02 15:04:05")

				if err := tx.Save(&currentEvent).Error; err != nil {
					return fmt.Errorf("活动编号'%s': 更新活动失败: %v", eventCode, err)
				}

				// 软删除现有的日程和嘉宾
				if err := db.SoftDeleteEventSchedulesByEventId(tx, currentEvent.Id, conferenceId); err != nil {
					return fmt.Errorf("活动编号'%s': 软删除现有日程失败: %v", eventCode, err)
				}
				if err := db.SoftDeleteEventScheduleGuestsByEventId(tx, currentEvent.Id, conferenceId); err != nil {
					return fmt.Errorf("活动编号'%s': 软删除现有嘉宾失败: %v", eventCode, err)
				}
			}
			// 创建日程和嘉宾
			scheduleSort := 1
			guestSort := 1

			for _, row := range eventRows {
				// 创建日程
				start, end := parseScheduleTime(row.ScheduleTime)
				schedule := model.EventSchedule{
					ConferenceId:  conferenceId,
					EventId:       currentEvent.Id,
					CnTitle:       row.ScheduleTitle,
					EnTitle:       row.ScheduleTitleEn,
					ScheduleStart: start,
					ScheduleEnd:   end,
					CnContent:     row.ScheduleDesc,
					EnContent:     row.ScheduleDescEn,
					IsAgenda:      mapScheduleType(row.ScheduleType),
					Deleted:       0,
					Sorting:       float32(scheduleSort),
					CreateTime:    time.Now(),
				}

				if err := tx.Create(&schedule).Error; err != nil {
					return fmt.Errorf("活动编号'%s': 创建日程'%s'失败: %v", eventCode, row.ScheduleTitle, err)
				}
				scheduleSort++

				// 创建嘉宾（如果有）
				if row.GuestName != "" {
					guest := model.EventScheduleGuest{
						ConferenceId:  conferenceId,
						EventId:       currentEvent.Id,
						ScheduleId:    schedule.ID,
						CnAppellation: row.GuestName,       // 嘉宾称谓-中文
						EnAppellation: row.GuestNameEn,     // 嘉宾称谓-英文
						CnPosition:    row.GuestPosition,   // 嘉宾职位-中文
						EnPosition:    row.GuestPositionEn, // 嘉宾职位-英文
						CnCompany:     row.GuestCompany,    // 嘉宾公司-中文
						EnCompany:     row.GuestCompanyEn,  // 嘉宾公司-英文
						GuestIdentity: row.GuestIdentity,   // 嘉宾身份
						Deleted:       "0",
						Sorting:       fmt.Sprintf("%.2f", float64(guestSort)),
						CreateTime:    time.Now(),
					}

					if err := tx.Create(&guest).Error; err != nil {
						return fmt.Errorf("活动编号'%s': 创建嘉宾'%s'失败: %v", eventCode, row.GuestName, err)
					}
					guestSort++
				}
			}
		}
		return nil
	})
	if err != nil {
		return nil, err
	}

	// 统计处理结果
	totalEvents := len(eventGroups)
	totalSchedules := len(dataRows)
	guestCount := 0
	for _, row := range dataRows {
		if row.GuestName != "" {
			guestCount++
		}
	}

	return fmt.Sprintf("成功处理%d个活动，%d个日程，%d个嘉宾", totalEvents, totalSchedules, guestCount), nil
}

// mapShowEventDetail "是"->"0"，"否"->"1"
func mapShowEventDetail(val string) string {
	if val == "是" {
		return "0"
	}
	return "1"
}

// mapScheduleType "议程"->0，"非议程"->1
func mapScheduleType(val string) int64 {
	if val == "议程" {
		return 0
	}
	return 1
}

// parseScheduleTime "hh:mm-hh:mm" -> (start, end)
func parseScheduleTime(val string) (string, string) {
	parts := strings.Split(val, "-")
	if len(parts) == 2 {
		return parts[0], parts[1]
	}
	return "", ""
}
