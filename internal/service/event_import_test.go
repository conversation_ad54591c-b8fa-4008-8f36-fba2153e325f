package service

import (
	"conferencecenter/internal/model"
	"conferencecenter/internal/protocol"
	"testing"
	"time"

	"github.com/xuri/excelize/v2"
)

// TestEventScheduleImportRow_NewFields 测试新增的活动编号字段
func TestEventScheduleImportRow_NewFields(t *testing.T) {
	row := protocol.EventScheduleImportRow{
		EventCode:       "EVENT001",
		CategoryName:    "主论坛",
		EventName:       "开幕式",
		EventNameEn:     "Opening Ceremony",
		EventDate:       "2024-03-15",
		ShowEventDetail: "是",
		ScheduleTitle:   "欢迎致辞",
		ScheduleTitleEn: "Welcome Speech",
		ScheduleType:    "议程",
		ScheduleTime:    "09:00-09:30",
		ScheduleDesc:    "大会开幕致辞",
		ScheduleDescEn:  "Opening remarks",
		GuestName:       "张三",
		GuestNameEn:     "Zhang San",
		GuestPosition:   "总裁",
		GuestPositionEn: "President",
		GuestCompany:    "ABC公司",
		GuestCompanyEn:  "ABC Company",
		GuestIdentity:   "Moderator",
		ConferenceId:    1,
	}

	if row.EventCode != "EVENT001" {
		t.<PERSON><PERSON><PERSON>("Expected EventCode to be 'EVENT001', got '%s'", row.EventCode)
	}
}

// TestConferenceEvent_EventCodeField 测试ConferenceEvent模型的新字段
func TestConferenceEvent_EventCodeField(t *testing.T) {
	event := model.ConferenceEvent{
		Id:           1,
		ConferenceId: 1,
		EventCode:    "EVENT001",
		CnName:       "开幕式",
		EnName:       "Opening Ceremony",
		EventTime:    "2024-03-15",
		CategoryType: 1,
		Deleted:      0,
		CreateTime:   time.Now().Format("2006-01-02 15:04:05"),
	}

	if event.EventCode != "EVENT001" {
		t.Errorf("Expected EventCode to be 'EVENT001', got '%s'", event.EventCode)
	}
}

// TestCreateExcelWithEventCode 测试创建包含活动编号的Excel文件
func TestCreateExcelWithEventCode(t *testing.T) {
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			t.Errorf("Failed to close Excel file: %v", err)
		}
	}()

	// 设置表头
	headers := []string{
		"活动编号（必填）", "活动分类（必填）", "活动名称（必填）", "活动名称英文", "活动日期（必填）",
		"是否展示活动详情", "日程标题（必填）", "标题英文", "日程类型", "日程时间（必填）",
		"日程描述", "描述英文", "嘉宾称谓", "称谓英文", "嘉宾职位", "职位英文",
		"嘉宾公司", "公司英文", "嘉宾身份",
	}

	for i, header := range headers {
		cell, _ := excelize.CoordinatesToCellName(i+1, 1)
		f.SetCellValue("Sheet1", cell, header)
	}

	// 添加测试数据
	testData := [][]interface{}{
		{"EVENT001", "主论坛", "开幕式", "Opening Ceremony", "2024-03-15", "是", "欢迎致辞", "Welcome Speech", "议程", "09:00-09:30", "大会开幕致辞", "Opening remarks", "张三", "Zhang San", "总裁", "President", "ABC公司", "ABC Company", "Moderator"},
		{"EVENT001", "主论坛", "开幕式", "Opening Ceremony", "2024-03-15", "是", "主题演讲", "Keynote Speech", "议程", "09:30-10:30", "行业发展趋势", "Industry trends", "李四", "Li Si", "专家", "Expert", "XYZ研究院", "XYZ Institute", "Panelist"},
	}

	for rowIdx, rowData := range testData {
		for colIdx, value := range rowData {
			cell, _ := excelize.CoordinatesToCellName(colIdx+1, rowIdx+2)
			f.SetCellValue("Sheet1", cell, value)
		}
	}

	// 验证Excel内容
	rows, err := f.GetRows("Sheet1")
	if err != nil {
		t.Errorf("Failed to get rows: %v", err)
		return
	}

	if len(rows) != 3 { // 1 header + 2 data rows
		t.Errorf("Expected 3 rows, got %d", len(rows))
	}

	// 验证表头
	if rows[0][0] != "活动编号（必填）" {
		t.Errorf("Expected first header to be '活动编号（必填）', got '%s'", rows[0][0])
	}

	// 验证数据
	if rows[1][0] != "EVENT001" {
		t.Errorf("Expected first data row event code to be 'EVENT001', got '%s'", rows[1][0])
	}
}

// TestParseScheduleTime 测试时间解析函数
func TestParseScheduleTime(t *testing.T) {
	testCases := []struct {
		input    string
		expected []string
	}{
		{"09:00-10:30", []string{"09:00", "10:30"}},
		{"14:15-15:45", []string{"14:15", "15:45"}},
		{"invalid", []string{"", ""}},
	}

	for _, tc := range testCases {
		start, end := parseScheduleTime(tc.input)
		if start != tc.expected[0] || end != tc.expected[1] {
			t.Errorf("parseScheduleTime(%s) = (%s, %s), expected (%s, %s)",
				tc.input, start, end, tc.expected[0], tc.expected[1])
		}
	}
}

// TestMapShowEventDetail 测试展示详情映射函数
func TestMapShowEventDetail(t *testing.T) {
	testCases := []struct {
		input    string
		expected string
	}{
		{"是", "0"},
		{"否", "1"},
		{"其他", "1"},
	}

	for _, tc := range testCases {
		result := mapShowEventDetail(tc.input)
		if result != tc.expected {
			t.Errorf("mapShowEventDetail(%s) = %s, expected %s", tc.input, result, tc.expected)
		}
	}
}

// TestMapScheduleType 测试日程类型映射函数
func TestMapScheduleType(t *testing.T) {
	testCases := []struct {
		input    string
		expected int64
	}{
		{"议程", 0},
		{"非议程", 1},
		{"其他", 1},
	}

	for _, tc := range testCases {
		result := mapScheduleType(tc.input)
		if result != tc.expected {
			t.Errorf("mapScheduleType(%s) = %d, expected %d", tc.input, result, tc.expected)
		}
	}
}
