package service

import (
	"bytes"
	"conferencecenter/internal/constant"
	"conferencecenter/internal/db"
	"conferencecenter/internal/errcode"
	"conferencecenter/internal/memory"
	"conferencecenter/internal/model"
	"conferencecenter/internal/pkg/utils"
	"conferencecenter/internal/protocol"
	"conferencecenter/internal/rpc"
	"encoding/json"
	"fmt"
	"git.code.tencent.com/smmit/smmbase/logger"
	"github.com/pkg/errors"
	"strconv"
	"time"
)

// 添加或编辑媒体注册配置
func AddEditMediaRegistrationConfig(req protocol.ReqAdminSaveMediaRegistrationConfig) (resMsg string, err error) {
	var (
		previousInfo model.MediaRegistrationConfig
	)

	company, err := db.QueryMediaRegistrationConfigInfo(nil, req.ConferenceId)
	if err != nil {
		return "系统错误", errors.WithStack(err)
	}
	if company != nil && company.Id > 0 {
		if utils.NotEmpty(req.CnContent) {
			if req.CnContent == "-" {
				company.CnContent = ""
			} else {
				company.CnContent = req.CnContent
			}
		}
		if utils.NotEmpty(req.EnContent) {
			if req.EnContent == "-" {
				company.EnContent = ""
			} else {
				company.EnContent = req.EnContent
			}
		}
		if req.CnNavigationDisplay > 0 {
			company.CnNavigationDisplay = req.CnNavigationDisplay
		}
		if req.EnNavigationDisplay > 0 {
			company.EnNavigationDisplay = req.EnNavigationDisplay
		}
		err = db.SaveMediaRegistrationConfig(nil, company)
		if err != nil {
			return "系统错误", errors.WithStack(err)
		}
		return
	}
	previousInfo = model.MediaRegistrationConfig{
		Id:                  req.ConferenceId,
		ConferenceId:        req.ConferenceId,
		CnContent:           req.CnContent,
		EnContent:           req.EnContent,
		CnNavigationDisplay: req.CnNavigationDisplay,
		EnNavigationDisplay: req.EnNavigationDisplay,
		UpdateTime:          time.Now(),
		CreateTime:          time.Now(),
	}
	err = db.SaveMediaRegistrationConfig(nil, &previousInfo)
	if err != nil {
		return "系统错误", errors.WithStack(err)
	}
	return
}

func GetMediaRegistrationConfigInfo(id int64) (protocol.RespMediaRegistrationConfigInfo, error) {
	if id <= 0 {
		return protocol.RespMediaRegistrationConfigInfo{}, nil
	}
	company, err := db.QueryMediaRegistrationConfigInfo(nil, id)
	if err != nil {
		return protocol.RespMediaRegistrationConfigInfo{}, err
	}
	return protocol.RespMediaRegistrationConfigInfo{
		ID:                  company.Id,
		ConferenceId:        company.ConferenceId,
		CnNavigationDisplay: company.CnNavigationDisplay,
		EnNavigationDisplay: company.EnNavigationDisplay,
		CnContent:           company.CnContent,
		EnContent:           company.EnContent,
	}, err

}

func CnUserAddMediaRegistration(req protocol.ReqAdminSaveMediaRegistration) (resMsg string, err error) {
	var (
		previousInfo model.MediaRegistration
		reqAPiSign   protocol.ReqAddApiMediaSignUp
	)

	if utils.NotEmpty(req.Phone) {
		m, err := rpc.CheckCode(protocol.ReqCheckCode{
			Cellphone: req.Phone,
			Code:      req.PhoneCode,
			CodeType:  "conference_from_phone",
		})
		if err != nil {
			logger.Error(fmt.Sprintf("CheckCode:%s ,m:%v", err, m))
			return "验证码错误", errors.WithStack(err)
		}
		if m.Code != errcode.RESPONSE_CODE_SUCCESS {
			logger.Error(fmt.Sprintf("CheckCode:%s ,m:%v", err, m))
			return "验证码错误", errors.New("验证码错误")
		}
	}
	info, err := db.QueryConferenceInfo(nil, req.ConferenceId)
	if err != nil {
		logger.Error(fmt.Sprintf("QueryConferenceInfo:%s ,info:%v", err, info))
	}
	fromName := ""
	if utils.NotEmpty(req.FromId) {
		fromMap := memory.GetMeetingFromNameMap(strconv.FormatInt(req.ConferenceId, 10) + "_" + req.FromId)
		if utils.IsEmpty(fromMap.ChannelName) {
			req.FromId = ""
		} else {
			fromName = fromMap.ChannelName
		}
	}
	previousInfo = model.MediaRegistration{
		ConferenceId:   req.ConferenceId,
		ConferenceName: info.CnName,
		Name:           req.Name,
		Organisation:   req.Organisation,
		Phone:          req.Phone,
		JobTitle:       req.JobTitle,
		Email:          req.Email,
		FromId:         req.FromId,
		FromName:       fromName,
		SourceID:       req.SourceID,
		Language:       "cn",
		UpdateTime:     time.Now(),
		CreateTime:     time.Now(),
	}
	err = db.SaveMediaRegistration(nil, &previousInfo)
	if err != nil {
		return "系统错误", errors.WithStack(err)
	}

	if utils.NotEmpty(info.MeetingSysId) {
		reqAPiSign.MeetingNo = info.MeetingSysId
		reqAPiSign.UserId = req.UserId
		reqAPiSign.CustomerName = req.Name
		reqAPiSign.MediaName = req.Organisation
		reqAPiSign.Email = req.Email
		reqAPiSign.Cellphone = req.Phone
		reqAPiSign.Position = req.JobTitle
		reqAPiSign.Channel = req.FromId
		reqAPiSign.Platform = req.SourceID
		m, err := rpc.AddApiMediaSignUp(reqAPiSign)
		if err != nil {
			logger.Error(fmt.Sprintf("AddApiMediaSignUp:%s ,m:%v", err, m))
			return "系统错误", errors.WithStack(err)
		}

		ReqLog, err := json.Marshal(reqAPiSign)
		RespLog, err := json.Marshal(m)
		RegisterLog := model.RegisterLog{
			RegisterId: req.Id,
			ReqLog:     string(ReqLog),
			RespLog:    string(RespLog),
			LogType:    3,
			CreateTime: time.Now(),
		}

		err = db.AddMeetingRegisterLog(nil, &RegisterLog)
		if err != nil {
			logger.Error(fmt.Sprintf("AddMeetingRegisterLog:%s ,m:%v", err, m))
		}

	}

	return
}

func EnUserAddMediaRegistration(req protocol.ReqAdminSaveMediaRegistration) (resMsg string, err error) {
	var (
		reqAPiSign   protocol.ReqAddApiMediaSignUp
		previousInfo model.MediaRegistration
	)

	info, err := db.QueryConferenceInfo(nil, req.ConferenceId)
	if err != nil {
		logger.Error(fmt.Sprintf("QueryConferenceInfo:%s ,info:%v", err, info))
	}
	fromName := ""
	if utils.NotEmpty(req.FromId) {
		fromMap := memory.GetMeetingFromNameMap(strconv.FormatInt(req.ConferenceId, 10) + "_" + req.FromId)
		if utils.IsEmpty(fromMap.ChannelName) {
			req.FromId = ""
		} else {
			fromName = fromMap.ChannelName
		}
	}
	previousInfo = model.MediaRegistration{
		ConferenceId:       req.ConferenceId,
		ConferenceName:     info.EnName,
		FirstName:          req.FirstName,
		LastName:           req.LastName,
		Email:              req.Email,
		Phone:              req.Phone,
		Organisation:       req.Organisation,
		JobTitle:           req.JobTitle,
		Country:            req.Country,
		IsAgreeSendMessage: req.IsAgreeSendMessage,
		FromId:             req.FromId,
		FromName:           fromName,
		SourceID:           req.SourceID,
		Language:           "en",
		UpdateTime:         time.Now(),
		CreateTime:         time.Now(),
	}
	err = db.SaveMediaRegistration(nil, &previousInfo)
	if err != nil {
		return "系统错误", errors.WithStack(err)
	}

	if utils.NotEmpty(info.MeetingSysId) {
		reqAPiSign.MeetingNo = info.MeetingSysId
		reqAPiSign.UserId = req.UserId
		reqAPiSign.CustomerName = req.FirstName + " " + req.LastName
		reqAPiSign.MediaName = req.Organisation
		reqAPiSign.Email = req.Email
		reqAPiSign.Cellphone = req.Phone
		reqAPiSign.Position = req.JobTitle
		reqAPiSign.Country = req.Country
		reqAPiSign.Channel = req.FromId
		reqAPiSign.Platform = req.SourceID
		m, err := rpc.AddApiMediaSignUp(reqAPiSign)
		if err != nil {
			logger.Error(fmt.Sprintf("AddApiMediaSignUp:%s ,m:%v", err, m))
			return "系统错误", errors.WithStack(err)
		}

		ReqLog, err := json.Marshal(reqAPiSign)
		RespLog, err := json.Marshal(m)
		RegisterLog := model.RegisterLog{
			RegisterId: req.Id,
			ReqLog:     string(ReqLog),
			RespLog:    string(RespLog),
			LogType:    3,
			CreateTime: time.Now(),
		}

		err = db.AddMeetingRegisterLog(nil, &RegisterLog)
		if err != nil {
			logger.Error(fmt.Sprintf("AddMeetingRegisterLog:%s ,m:%v", err, m))
		}
	}

	return
}

func QueryConferenceMediaRegistrationList(req protocol.ReqGetMediaRegistrationList) (res []model.MediaRegistration, total int64, err error) {
	list, total, err := db.QueryMediaRegistrationList(nil, req)
	if err != nil {
		return nil, 0, err
	}
	return list, total, err
}

// 导出报名信息列表
func CnExportMediaRegistrationList(req protocol.ReqGetMediaRegistrationList) (bReader *bytes.Reader, err error) {

	req.Page, req.PageSize = 1, 10000 //重置分页数据
	list, _, err_ := db.QueryMediaRegistrationList(nil, req)
	if err_ != nil {
		_ = logger.Error("QueryHandBookSubscribeList:", err)
		return nil, err_
	}

	var registerId []string
	for _, register := range list {
		registerId = append(registerId, strconv.FormatInt(register.Id, 10))
	}

	var registerList []protocol.CnResMediaRegistrationInfo

	for _, register := range list {

		conferenceName := memory.GetConferenceNameMap(register.ConferenceId, false)

		name := ""

		name = register.Name
		registerInfo := protocol.CnResMediaRegistrationInfo{
			ConferenceName: conferenceName,
			FromName:       register.FromName,
			Name:           name,
			ConferenceID:   register.ConferenceId,
			UserId:         register.UserId,
			Email:          register.Email,
			Mobile:         register.Phone,
			Organisation:   register.Organisation,
			JobTitle:       register.JobTitle,
			CreateTime:     register.CreateTime.Format(constant.DateTime),
		}
		registerList = append(registerList, registerInfo)

	}

	var listItems []interface{}
	for _, v := range registerList {
		listItems = append(listItems, v)
	}

	bReader, err = utils.GenListExcel(listItems, func(fieldName string, value interface{}) interface{} {
		return value
	}, protocol.ResConferenceRegisterInfo{})
	if err != nil {
		return nil, err
	}
	return
}

// 导出报名信息列表
func EnExportMediaRegistrationList(req protocol.ReqGetMediaRegistrationList) (bReader *bytes.Reader, err error) {

	req.Page, req.PageSize = 1, 10000 //重置分页数据
	list, _, err_ := db.QueryMediaRegistrationList(nil, req)
	if err_ != nil {
		_ = logger.Error("QueryHandBookSubscribeList:", err)
		return nil, err_
	}

	var registerId []string
	for _, register := range list {
		registerId = append(registerId, strconv.FormatInt(register.Id, 10))
	}

	var enRegisterList []protocol.EnResMediaRegistrationInfo

	for _, register := range list {

		conferenceName := memory.GetConferenceNameMap(register.ConferenceId, false)
		name := ""
		name = register.LastName + " " + register.FirstName

		registerInfo := protocol.EnResMediaRegistrationInfo{
			ConferenceName: conferenceName,
			FromName:       register.FromName,
			Name:           name,
			ConferenceID:   register.ConferenceId,
			UserId:         register.UserId,
			Email:          register.Email,
			Mobile:         register.Phone,
			Organisation:   register.Organisation,
			JobTitle:       register.JobTitle,
			Country:        register.Country,
			CreateTime:     register.CreateTime.Format(constant.DateTime),
		}
		enRegisterList = append(enRegisterList, registerInfo)

	}

	var listItems []interface{}

	for _, v := range enRegisterList {
		listItems = append(listItems, v)
	}

	bReader, err = utils.GenListExcel(listItems, func(fieldName string, value interface{}) interface{} {
		return value
	}, protocol.ResConferenceRegisterInfo{})
	if err != nil {
		return nil, err
	}
	return
}
