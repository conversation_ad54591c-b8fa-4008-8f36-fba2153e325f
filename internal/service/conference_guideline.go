package service

import (
	"conferencecenter/internal/db"
	"conferencecenter/internal/model"
	"conferencecenter/internal/protocol"
	"fmt"

	"git.code.tencent.com/smmit/smmbase/logger"
)

func SaveHallInfo(info protocol.HallInfo) (err error) {
	if info.LocationLongitude == "" {
		info.LocationLongitude = " "
	}
	if info.LocationLatitude == "" {
		info.LocationLatitude = " "
	}
	if info.CnFloorGraph == "" {
		info.CnFloorGraph = " "
	}
	if info.EnFloorGraph == "" {
		info.EnFloorGraph = " "
	}
	if info.GoogleUrl == "" {
		info.GoogleUrl = " "
	}
	if info.CnHeading == "" {
		info.CnHeading = " "
	}
	if info.EnHeading == "" {
		info.EnHeading = " "
	}
	if info.CnHeadlining == "" {
		info.CnHeadlining = " "
	}
	if info.EnHeadlining == "" {
		info.EnHeadlining = " "
	}
	list, err := db.FindExhibitionGuideline(nil, info.ConferenceId)
	if err != nil {
		return err
	}

	guideline := model.ExhibitionGuideline{
		CommonModel: model.CommonModel{
			CreateUser: info.OpUser,
		},
		ConferenceId:      info.ConferenceId,
		LocationLongitude: info.LocationLongitude,
		LocationLatitude:  info.LocationLatitude,
		CnFloorGraph:      info.CnFloorGraph,
		EnFloorGraph:      info.EnFloorGraph,
		GoogleUrl:         info.GoogleUrl,
		CnHeading:         info.CnHeading,
		EnHeading:         info.EnHeading,
		CnHeadlining:      info.CnHeadlining,
		EnHeadlining:      info.EnHeadlining,
	}

	//不存在就插入
	if len(list) == 0 {
		if guideline.ConferenceId <= 0 {
			return nil
		}
		err = db.SaveExhibitionGuideline(nil, guideline)
	} else {
		guideline = list[0]

		guideline.CreateUser = info.OpUser
		guideline.ConferenceId = info.ConferenceId
		guideline.LocationLongitude = info.LocationLongitude
		guideline.LocationLatitude = info.LocationLatitude
		guideline.CnFloorGraph = info.CnFloorGraph
		guideline.EnFloorGraph = info.EnFloorGraph
		guideline.GoogleUrl = info.GoogleUrl
		guideline.CnHeading = info.CnHeading
		guideline.EnHeading = info.EnHeading
		guideline.CnHeadlining = info.CnHeadlining
		guideline.EnHeadlining = info.EnHeadlining

		err = saveOrUpdateExhibitionGuideline(guideline)
	}

	return err
}

func saveOrUpdateExhibitionGuideline(guideline model.ExhibitionGuideline) error {

	//存在则更新
	err := db.UpdateExhibitionGuideline(nil, guideline)

	return err
}

func GetHallInfo(conferenceId int64) (protocol.HallInfo, error) {
	guideline, err := db.GetExhibitionGuideline(nil, conferenceId)
	if err != nil {
		return protocol.HallInfo{}, err
	}
	info := protocol.HallInfo{
		ConferenceId:      guideline.ConferenceId,
		LocationLongitude: guideline.LocationLongitude,
		LocationLatitude:  guideline.LocationLatitude,
		GoogleUrl:         guideline.GoogleUrl,
		CnFloorGraph:      guideline.CnFloorGraph,
		EnFloorGraph:      guideline.EnFloorGraph,
		CnHeading:         guideline.CnHeading,
		EnHeading:         guideline.EnHeading,
		CnHeadlining:      guideline.CnHeadlining,
		EnHeadlining:      guideline.EnHeadlining,
	}
	return info, nil
}

func SaveHotelPage(page protocol.HotelPage) error {
	if page.CnHotelContent == "-" {
		page.CnHotelContent = ""
	}
	if page.EnHotelContent == "-" {
		page.EnHotelContent = ""
	}
	if page.CnBookingUrl == "-" {
		page.CnBookingUrl = ""
	}
	if page.EnBookingUrl == "-" {
		page.EnBookingUrl = ""
	}

	if page.CnHotelExcel == "-" {
		page.CnHotelExcel = ""
	}
	if page.EnHotelExcel == "-" {
		page.EnHotelExcel = ""
	}
	if page.CnHotelButton == "-" {
		page.CnHotelButton = ""
	}
	if page.EnHotelButton == "-" {
		page.EnHotelButton = ""
	}

	list, err := db.FindExhibitionGuideline(nil, page.ConferenceId)
	if err != nil {
		return err
	}

	exhibitionGuideline := model.ExhibitionGuideline{
		CommonModel: model.CommonModel{
			CreateUser: page.OpUser,
		},
		ConferenceId:   page.ConferenceId,
		CnHotelExcel:   page.CnHotelExcel,
		EnHotelExcel:   page.EnHotelExcel,
		CnHotelButton:  page.CnHotelButton,
		EnHotelButton:  page.EnHotelButton,
		CnBookingUrl:   page.CnBookingUrl,
		EnBookingUrl:   page.EnBookingUrl,
		CnHotelContent: page.CnHotelContent,
		EnHotelContent: page.EnHotelContent,
	}

	//不存在就插入
	if len(list) == 0 {
		if exhibitionGuideline.ConferenceId <= 0 {
			return nil
		}
		err = db.SaveExhibitionGuideline(nil, exhibitionGuideline)
	} else {
		exhibitionGuideline = list[0]

		exhibitionGuideline.CreateUser = page.OpUser
		exhibitionGuideline.ConferenceId = page.ConferenceId
		exhibitionGuideline.CnHotelExcel = page.CnHotelExcel
		exhibitionGuideline.EnHotelExcel = page.EnHotelExcel
		exhibitionGuideline.CnHotelButton = page.CnHotelButton
		exhibitionGuideline.EnHotelButton = page.EnHotelButton
		exhibitionGuideline.CnBookingUrl = page.CnBookingUrl
		exhibitionGuideline.EnBookingUrl = page.EnBookingUrl
		exhibitionGuideline.CnHotelContent = page.CnHotelContent
		exhibitionGuideline.EnHotelContent = page.EnHotelContent

		err = saveOrUpdateExhibitionGuideline(exhibitionGuideline)
	}

	return err
}

func GetHotelPage(conferenceId int64) (protocol.HotelPage, error) {
	guideline, err := db.GetExhibitionGuideline(nil, conferenceId)
	if err != nil {
		return protocol.HotelPage{}, err
	}
	page := protocol.HotelPage{
		ConferenceId:   guideline.ConferenceId,
		CnHotelExcel:   guideline.CnHotelExcel,
		EnHotelExcel:   guideline.EnHotelExcel,
		CnHotelButton:  guideline.CnHotelButton,
		EnHotelButton:  guideline.EnHotelButton,
		CnBookingUrl:   guideline.CnBookingUrl,
		EnBookingUrl:   guideline.EnBookingUrl,
		CnHotelContent: guideline.CnHotelContent,
		EnHotelContent: guideline.EnHotelContent,
	}
	return page, nil
}

func SaveTrafficService(service protocol.TrafficService) error {
	if service.CnTrafficContent == "" {
		service.CnTrafficContent = " "
	}
	if service.EnTrafficContent == "" {
		service.EnTrafficContent = " "
	}

	list, err := db.FindExhibitionGuideline(nil, service.ConferenceId)
	if err != nil {
		return err
	}

	guideline := model.ExhibitionGuideline{
		CommonModel: model.CommonModel{
			CreateUser: service.OpUser,
		},
		ConferenceId:     service.ConferenceId,
		CnTrafficContent: service.CnTrafficContent,
		EnTrafficContent: service.EnTrafficContent,
	}
	//不存在就插入
	if len(list) == 0 {
		if service.ConferenceId <= 0 {
			return nil
		}
		err = db.SaveExhibitionGuideline(nil, guideline)
	} else {
		guideline = list[0]
		guideline.CreateUser = service.OpUser
		guideline.ConferenceId = service.ConferenceId
		guideline.CnTrafficContent = service.CnTrafficContent
		guideline.EnTrafficContent = service.EnTrafficContent

		err = saveOrUpdateExhibitionGuideline(guideline)
	}

	return err
}

func GetTrafficService(conferenceId int64) (protocol.TrafficService, error) {
	guideline, err := db.GetExhibitionGuideline(nil, conferenceId)
	if err != nil {
		return protocol.TrafficService{}, err
	}
	traffic := protocol.TrafficService{
		ConferenceId:     guideline.ConferenceId,
		CnTrafficContent: guideline.CnTrafficContent,
		EnTrafficContent: guideline.EnTrafficContent,
	}
	return traffic, nil
}

func SaveCityInfo(info protocol.CityInfo) error {
	if info.CnCityOverview == "" {
		info.CnCityOverview = " "
	}
	if info.EnCityOverview == "" {
		info.EnCityOverview = " "
	}

	list, err := db.FindExhibitionGuideline(nil, info.ConferenceId)
	if err != nil {
		return err
	}

	guideline := model.ExhibitionGuideline{
		CommonModel: model.CommonModel{
			CreateUser: info.OpUser,
		},
		ConferenceId:   info.ConferenceId,
		CnCityOverview: info.CnCityOverview,
		EnCityOverview: info.EnCityOverview,
	}
	//不存在就插入
	if len(list) == 0 {
		if info.ConferenceId <= 0 {
			return nil
		}
		err = db.SaveExhibitionGuideline(nil, guideline)
	} else {
		guideline = list[0]
		guideline.CreateUser = info.OpUser
		guideline.ConferenceId = info.ConferenceId
		guideline.CnCityOverview = info.CnCityOverview
		guideline.EnCityOverview = info.EnCityOverview

		err = saveOrUpdateExhibitionGuideline(guideline)
	}

	return err
}

func GetCityInfo(conferenceId int64) (protocol.CityInfo, error) {
	guideline, err := db.GetExhibitionGuideline(nil, conferenceId)
	if err != nil {
		return protocol.CityInfo{}, err
	}
	info := protocol.CityInfo{
		ConferenceId:   guideline.ConferenceId,
		CnCityOverview: guideline.CnCityOverview,
		EnCityOverview: guideline.EnCityOverview,
	}
	return info, nil
}

func GetVenues(conferenceId int64) (list []protocol.Venue, err error) {
	models, err := db.GetVenueByConferenceId(nil, conferenceId)
	if err != nil {
		return nil, err
	}
	for _, venue := range models {
		p := protocol.Venue{
			ID:           venue.Id,
			ConferenceID: venue.ConferenceId,
			CnVenueName:  venue.CnVenueName,
			EnVenueName:  venue.EnVenueName,
			Sorting:      fmt.Sprintf("%.2f", venue.Sorting),
		}
		list = append(list, p)
	}
	return list, nil
}

func SaveVenue(req protocol.ReqSaveVenue) (res string, err error) {
	v := model.Venue{
		AdminCommonModel: model.AdminCommonModel{
			Id: req.ID,
		},
		ConferenceId: req.ConferenceId,
		CnVenueName:  req.CnVenueName,
		EnVenueName:  req.EnVenueName,
		Sorting:      req.Sorting,
	}
	// 编辑
	if req.ID > 0 {
		venue, err := db.GetVenueById(nil, req.ID)
		if err != nil {
			return "", err
		}
		if venue.Id == 0 {
			return "不存在此场馆，请确认", err
		}
		v.CreateAdmin = venue.CreateAdmin
		v.UpdateAdmin = req.UserName
	} else {
		v.CreateAdmin = req.UserName
		v.UpdateAdmin = req.UserName
	}
	err = db.SaveVenue(nil, &v)
	if err != nil {
		logger.Error(fmt.Sprintf("保存场馆信息失败：Err->%v;model->%+v", err, v))
		return "", err
	}
	return "", nil
}

func DeleteVenue(id int64) (res string, err error) {
	err = db.DeleteVenue(nil, id)
	return "", err
}
