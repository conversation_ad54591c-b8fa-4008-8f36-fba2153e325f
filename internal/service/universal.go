package service

import (
	"conferencecenter/internal/corecontext"
	"conferencecenter/internal/db"
	"conferencecenter/internal/model"
	"conferencecenter/internal/protocol"
	"fmt"
	"time"

	"github.com/pkg/errors"
)

func QueryConferenceColumnList(req protocol.ReqColumnList) (res []protocol.ConferenceColumn, total int64, err error) {
	list, total, err := db.QueryConferenceColumnList(nil, req)
	if err != nil {
		return nil, 0, err
	}
	for _, item := range list {
		res = append(res, protocol.ConferenceColumn{
			Id:           item.Id,
			ConferenceId: item.ConferenceId,
			CnName:       item.CnName,
			EnName:       item.EnName,
			SubSectionId: item.SubSectionId,
			IsSubSection: item.IsSubSection,
			Sorting:      fmt.Sprintf("%.2f", item.Sorting),
			Type:         item.Type,
			Deleted:      item.Deleted,
			CreateTime:   item.CreateTime,
			UpdateTime:   item.UpdateTime,
			CreateAdmin:  item.CreateAdmin,
			UpdateAdmin:  item.UpdateAdmin,
		})
	}
	return res, total, err
}

func GetConferenceColumnInfo(id int64) (protocol.RespConferenceColumnInfo, error) {
	if id <= 0 {
		return protocol.RespConferenceColumnInfo{}, nil
	}
	company, err := db.QueryConferenceColumnInfo(nil, id)
	if err != nil {
		return protocol.RespConferenceColumnInfo{}, err
	}

	return protocol.RespConferenceColumnInfo{
		Id:           company.Id,
		ConferenceId: company.ConferenceId,
		CnName:       company.CnName,
		EnName:       company.EnName,
		IsSubSection: company.IsSubSection,
	}, err
}

// 添加或编辑通用栏目
func AddEditConferenceColumn(req protocol.ReqAdminSaveColumn) (resMsg string, err error) {
	var (
		previousInfo model.ConferenceColumn
		ok           bool
	)

	previousInfo = model.ConferenceColumn{
		Id:           req.Id,
		ConferenceId: req.ConferenceId,
		CnName:       req.CnName,
		EnName:       req.EnName,
		SubSectionId: req.SubSectionId,
		IsSubSection: req.IsSubSection,
		Type:         req.Type,
		Sorting:      req.Sorting,
		Deleted:      0,
		UpdateTime:   time.Now(),
		CreateTime:   time.Now(),
		UpdateAdmin:  req.AdminEmail,
		CreateAdmin:  req.AdminEmail,
	}
	tx := corecontext.SrvContext.Orm.Begin()
	if tx == nil {
		return "", errors.New("创建事务失败")
	}
	defer func() {
		if !ok {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	err = db.SaveConferenceColumnInfo(tx, &previousInfo)
	if err != nil {
		return "系统错误", errors.WithStack(err)
	}

	ok = true
	return
}

// 删除大会通用栏目
func DeleteConferenceColumn(conferenceId int64) (resMsg string, err error) {
	var (
		ok bool
	)

	tx := corecontext.SrvContext.Orm.Begin()
	if tx == nil {
		return "", errors.New("创建事务失败")
	}
	defer func() {
		if !ok {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	err = db.UpdateConferenceColumn(tx, conferenceId, time.Now().Second(), "")
	if err != nil {
		return "系统错误", errors.WithStack(err)
	}

	ok = true
	return
}

func QueryConferenceInformationList(req protocol.ReqColumnInformationList) (res []protocol.ConferenceInformation, total int64, err error) {
	list, total, err := db.QueryConferenceInformationList(nil, req)
	if err != nil {
		return nil, 0, err
	}
	for _, item := range list {
		res = append(res, protocol.ConferenceInformation{
			Id:            item.Id,
			ConferenceId:  item.ConferenceId,
			ColumnId:      item.ColumnId,
			CnCompany:     item.CnCompany,
			EnCompany:     item.EnCompany,
			CnLink:        item.CnLink,
			EnLink:        item.EnLink,
			CnPicture:     item.CnPicture,
			EnPicture:     item.EnPicture,
			Sorting:       fmt.Sprintf("%.2f", item.Sorting),
			CnSorting:     fmt.Sprintf("%.2f", item.CnSorting),
			EnSorting:     fmt.Sprintf("%.2f", item.EnSorting),
			CnAppellation: item.CnAppellation,
			EnAppellation: item.EnAppellation,
			CnPosition:    item.CnPosition,
			EnPosition:    item.EnPosition,
			CnContent:     item.CnContent,
			EnContent:     item.EnContent,
			Type:          item.Type,
			Deleted:       item.Deleted,
			CreateTime:    item.CreateTime,
			UpdateTime:    item.UpdateTime,
			CreateAdmin:   item.CreateAdmin,
			UpdateAdmin:   item.UpdateAdmin,
		})
	}
	return res, total, err
}

func GetConferenceInformationInfo(id int64) (protocol.RespConferenceInformationInfo, error) {
	if id <= 0 {
		return protocol.RespConferenceInformationInfo{}, nil
	}
	company, err := db.QueryConferenceInformationInfo(nil, id)
	if err != nil {
		return protocol.RespConferenceInformationInfo{}, err
	}

	return protocol.RespConferenceInformationInfo{
		Id:            company.Id,
		ConferenceId:  company.ConferenceId,
		ColumnId:      company.ColumnId,
		CnCompany:     company.CnCompany,
		EnCompany:     company.EnCompany,
		CnLink:        company.CnLink,
		EnLink:        company.EnLink,
		CnPicture:     company.CnPicture,
		EnPicture:     company.EnPicture,
		Sorting:       company.Sorting,
		CnSorting:     company.CnSorting,
		EnSorting:     company.EnSorting,
		CnAppellation: company.CnAppellation,
		EnAppellation: company.EnAppellation,
		CnPosition:    company.CnPosition,
		EnPosition:    company.EnPosition,
		CnContent:     company.CnContent,
		EnContent:     company.EnContent,
		Type:          company.Type,
	}, err
}

// 添加或编辑通用栏目
func AddEditConferenceInformation(req protocol.ReqAdminSaveInformation) (resMsg string, err error) {
	var (
		previousInfo model.ConferenceInformation
		ok           bool
	)
	if req.ColumnId > 0 {
		info, err := db.QueryConferenceColumnInfo(nil, req.ColumnId)
		if err != nil {
			return "系统错误", errors.WithStack(err)
		}
		if info.SubSectionId > 0 {
			info, err = db.QueryConferenceColumnInfo(nil, info.SubSectionId)
			if err != nil {
				return "系统错误", errors.WithStack(err)
			}
		}
		if info.Type > 0 {
			req.Type = info.Type
		}
	} else {
		req.Type = 2
	}

	previousInfo = model.ConferenceInformation{
		Id:            req.Id,
		ConferenceId:  req.ConferenceId,
		ColumnId:      req.ColumnId,
		CnCompany:     req.CnCompany,
		EnCompany:     req.EnCompany,
		CnLink:        req.CnLink,
		EnLink:        req.EnLink,
		CnPicture:     req.CnPicture,
		EnPicture:     req.EnPicture,
		CnAppellation: req.CnAppellation,
		EnAppellation: req.EnAppellation,
		CnPosition:    req.CnPosition,
		EnPosition:    req.EnPosition,
		CnContent:     req.CnContent,
		EnContent:     req.EnContent,
		Type:          req.Type,
		Sorting:       req.Sorting,
		CnSorting:     req.CnSorting,
		EnSorting:     req.EnSorting,
		Deleted:       0,
		UpdateTime:    time.Now(),
		CreateTime:    time.Now(),
		UpdateAdmin:   req.AdminEmail,
		CreateAdmin:   req.AdminEmail,
	}
	tx := corecontext.SrvContext.Orm.Begin()
	if tx == nil {
		return "", errors.New("创建事务失败")
	}
	defer func() {
		if !ok {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	err = db.SaveConferenceInformation(tx, &previousInfo)
	if err != nil {
		return "系统错误", errors.WithStack(err)
	}

	ok = true
	return
}

// 删除大会通用栏目
func DeleteConferenceInformation(conferenceId int64) (resMsg string, err error) {
	var (
		ok bool
	)

	tx := corecontext.SrvContext.Orm.Begin()
	if tx == nil {
		return "", errors.New("创建事务失败")
	}
	defer func() {
		if !ok {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	err = db.UpdateConferenceInformation(tx, conferenceId, time.Now().Second(), "")
	if err != nil {
		return "系统错误", errors.WithStack(err)
	}

	ok = true
	return
}

//// 添加或编辑通用栏目
//func AddEditConferenceInformationEvent(req protocol.ReqAdminSaveInformationEvent) (resMsg string, err error) {
//
//	if utils.IsEmpty(req.EventId) {
//		return "活动ID参数错误", errors.WithStack(err)
//	}
//
//	split := strings.Split(req.EventId, ",")
//	err = db.AddConferenceInformationEventList(nil, req.ConferenceId, req.InformationId, split...)
//	if err != nil {
//		return "系统错误", errors.WithStack(err)
//	}
//	return
//}

func UserConferenceInformationList(req protocol.ReqUserColumnInformationList) (res protocol.RespWebColumnInformationList, err error) {

	columnList, err := db.UserConferenceColumnList(nil, req.ConferenceId, 0, req.Type)
	if err != nil {
		return res, err
	}

	subColumnList, err := db.UserConferenceColumnList(nil, req.ConferenceId, -1, 0)
	if err != nil {
		return res, err
	}

	//informationIdList, err := db.UserConferenceInformationEventList(nil, req.ConferenceId, 0)
	//if err != nil {
	//	return res, err
	//}
	//var informationIds []int64
	//if len(informationIdList) > 0 {
	//	for _, eventId := range informationIdList {
	//		informationIds = append(informationIds, eventId.InformationId)
	//	}
	//}
	informationList, err := db.UserConferenceInformationList(nil, req.ConferenceId, req.Type, req.IsEn)
	if err != nil {
		return res, err
	}

	var conferenceColumnList []protocol.ConferenceColumnInfo
	if len(informationList) > 0 {
		for _, column := range columnList {
			conferenceColumn := protocol.ConferenceColumnInfo{
				Id:           column.Id,
				ConferenceId: column.ConferenceId,
				CnName:       column.CnName,
				EnName:       column.EnName,
				IsSubSection: column.IsSubSection,
				SubSectionId: column.SubSectionId,
			}
			if column.IsSubSection == 0 {
				var respInformationList []model.ConferenceInformation
				for _, information := range informationList {
					if column.Id == information.ColumnId {
						respInformationList = append(respInformationList, information)
					}
				}
				if len(respInformationList) > 0 {
					conferenceColumn.ConferenceInformationList = respInformationList
					if column.SubSectionId == 0 {
						if conferenceColumn.ConferenceInformationList != nil {
							conferenceColumnList = append(conferenceColumnList, conferenceColumn)
						}
					}
				}
			} else {
				var respSubColumnList []protocol.ConferenceColumnInfo
				for _, subColumn := range subColumnList {

					if column.Id == subColumn.SubSectionId {
						var respInformationList []model.ConferenceInformation

						subColumnInfo := protocol.ConferenceColumnInfo{
							Id:           subColumn.Id,
							ConferenceId: subColumn.ConferenceId,
							CnName:       subColumn.CnName,
							EnName:       subColumn.EnName,
							IsSubSection: subColumn.IsSubSection,
							SubSectionId: subColumn.SubSectionId,
						}

						for _, information := range informationList {
							if subColumn.Id == information.ColumnId {
								respInformationList = append(respInformationList, information)
							}
						}
						if len(respInformationList) > 0 {
							subColumnInfo.ConferenceInformationList = respInformationList
							respSubColumnList = append(respSubColumnList, subColumnInfo)
						}
					}
				}
				if len(respSubColumnList) > 0 {
					conferenceColumn.ConferenceColumnList = respSubColumnList
					conferenceColumnList = append(conferenceColumnList, conferenceColumn)
				}
			}

		}
	}

	res.List = conferenceColumnList
	return res, err
}
