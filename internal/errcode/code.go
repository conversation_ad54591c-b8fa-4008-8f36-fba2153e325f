package errcode

const (
	RESPONSE_CODE_SUCCESS               = 0
	RESPONSE_CODE_FAILED                = 10000
	RESPONSE_CODE_SYSTEM_ERR            = 10001
	RESPONSE_CODE_PARAM_ERR             = 10002
	RESPONSE_CODE_TOKEN_ERR             = 10003
	RESPONSE_CODE_TOKEN_EXPIRED         = 10004
	RESPONSE_CODE_RPC_ERR               = 10005
	RESPONSE_CODE_USER_NOT_EXIST        = 10006
	RESPONSE_CODE_USER_STATUS_ERR       = 10007
	RESPONSE_CODE_DB_ERR                = 10008
	RESPONSE_CODE_CACHE_ERR             = 10009
	RESPONSE_CODE_FETCHDATA_ERR         = 10100
	RESPONSE_CODE_RESTRICTED_DOMAIN_ERR = 10101
	RESPONSE_CODE_PRODUCT_CHANGED_ERR   = 10102
	RESPONSE_CODE_CLICK_PERMISSION_ERR  = 10103
	RESPONSE_CODE_OUT_OF_PERMISSION     = 10104
	RESPONSE_CODE_CODE_INVALID          = 10105
	RESPONSE_CODE_CELLPHONE_EXISTS      = 10106
	RESPONSE_CODE_DATA_NOT_EXIST        = 10107

	RESPONSE_CODE_UNKNOWN        = 99999
	RESPONSE_CODE_PERMISSION_ERR = 100106
)

var CodeMsg = map[int]string{
	RESPONSE_CODE_SUCCESS:               "成功",
	RESPONSE_CODE_FAILED:                "操作失败",
	RESPONSE_CODE_SYSTEM_ERR:            "系统错误",
	RESPONSE_CODE_PARAM_ERR:             "参数错误",
	RESPONSE_CODE_TOKEN_ERR:             "用户认证失败，请重新登录",
	RESPONSE_CODE_TOKEN_EXPIRED:         "权限已过期，请重新登录",
	RESPONSE_CODE_USER_NOT_EXIST:        "用户不存在",
	RESPONSE_CODE_USER_STATUS_ERR:       "用户状态异常",
	RESPONSE_CODE_RPC_ERR:               "远程服务错误",
	RESPONSE_CODE_DB_ERR:                "数据库错误",
	RESPONSE_CODE_CACHE_ERR:             "缓存错误",
	RESPONSE_CODE_FETCHDATA_ERR:         "获取数据错误",
	RESPONSE_CODE_RESTRICTED_DOMAIN_ERR: "受限制的域名",
	RESPONSE_CODE_PRODUCT_CHANGED_ERR:   "选择的产品信息已经发生变动",
	RESPONSE_CODE_CLICK_PERMISSION_ERR:  "访问次数超限",
	RESPONSE_CODE_UNKNOWN:               "未知错误",
	RESPONSE_CODE_OUT_OF_PERMISSION:     "超出用户角色访问权限",
	RESPONSE_CODE_CODE_INVALID:          "验证码错误",
	RESPONSE_CODE_CELLPHONE_EXISTS:      "手机号/邮箱已存在，请直接登陆",
	RESPONSE_CODE_PERMISSION_ERR:        "权限错误",
	RESPONSE_CODE_DATA_NOT_EXIST:        "数据不存在",
}
