package utils

import (
	"database/sql"
	"gorm.io/gorm"
	"reflect"
	"strings"
)

func PageOffset(page, pageSize int) (limit, offset int) {
	limit = pageSize
	offset = 0
	if page > 0 {
		offset = (page - 1) * pageSize
	}
	return
}

// 判断接口是否为nil
func IsNil(i interface{}) bool {
	vi := reflect.ValueOf(i)
	if vi.Kind() == reflect.Ptr {
		return vi.IsNil()
	}
	return false
}

func IsSqlNoRows(err error) bool {
	return err == sql.ErrNoRows || err == gorm.ErrRecordNotFound
}

func IsNotSqlNoRows(err error) bool {
	return err != sql.ErrNoRows && err != gorm.ErrRecordNotFound
}

func SplitStringToStringList(s string, sep string) ([]string, error) {

	itemList := []string{}
	list := strings.Split(s, sep)
	for _, i := range list {
		tmp := strings.Trim(i, " ")
		itemList = append(itemList, tmp)

	}
	return itemList, nil
}
