package utils

import (
	"errors"
	"fmt"
	"git.code.tencent.com/smmit/smmbase/logger"
	"github.com/levigross/grequests"
	"io"
	"io/ioutil"
	"net"
	"net/http"
	"net/url"
	"time"
)

type FilePost struct {
	FileReader io.Reader
	FileName   string
	FieldName  string
}

func NewRequestOptions() *grequests.RequestOptions {
	return &grequests.RequestOptions{
		DialTimeout:         15 * time.Second,
		TLSHandshakeTimeout: 15 * time.Second,
		RequestTimeout:      15 * time.Second,
		InsecureSkipVerify:  true,
	}
}

func HttpGetCall(url string, params map[string]string, cooikes []*http.Cookie, res interface{}) error {
	op := NewRequestOptions()
	op.Params = params
	op.Cookies = cooikes
	resp, err := grequests.Get(url, op)
	if resp != nil {
		defer resp.Close()
	}
	if err != nil {
		return err
	}

	if !resp.Ok {
		return fmt.Errorf("response failed with status code: %d", resp.StatusCode)
	}

	if res != nil {
		return resp.JSON(res)
	}
	return nil
}

func HttpPostCall(url string, params map[string]string, res interface{}) error {
	op := NewRequestOptions()
	op.Data = params

	logger.Warnning(op)
	resp, err := grequests.Post(url, op)
	if resp != nil {
		defer resp.Close()
	}
	if err != nil {
		return err
	}

	if !resp.Ok {
		return fmt.Errorf("Response failed with status code: %d.", resp.StatusCode)
	}

	if res != nil {
		return resp.JSON(res)
	}
	return nil
}

func HttpCustomCall(url string, method string, params, data, headers map[string]string, requestBody io.Reader, cooikes []*http.Cookie, files []FilePost) ([]byte, error) {
	op := NewRequestOptions()
	op.Data = data
	op.Params = params
	op.Cookies = cooikes
	op.Headers = headers
	op.RequestBody = requestBody

	var fs []grequests.FileUpload
	for _, file := range files {
		fs = append(fs, grequests.FileUpload{
			FileName:     file.FileName,
			FieldName:    file.FieldName,
			FileContents: ioutil.NopCloser(file.FileReader),
		})
	}
	op.Files = fs

	var resp *grequests.Response
	var err error

	if method == "GET" {
		resp, err = grequests.Get(url, op)
	} else {
		resp, err = grequests.Post(url, op)
	}
	if resp != nil {
		defer resp.Close()
	}
	if err != nil {
		return nil, err
	}

	if !resp.Ok {
		return nil, fmt.Errorf("Response failed with status code: %d.", resp.StatusCode)
	}

	return resp.Bytes(), nil
}

func JsonPost(url string, host string, headers map[string]string, jsonForm interface{}) ([]byte, bool) {
	resp, err := grequests.Post(url, &grequests.RequestOptions{
		JSON:           jsonForm,
		Host:           host,
		Headers:        headers,
		DialTimeout:    3 * time.Second,
		RequestTimeout: 5 * time.Second})
	if err != nil || !resp.Ok {
		logger.Error("POST failed: "+url, " Err: ", err, "ret:", resp)
		return nil, false
	}

	defer resp.Close()

	return resp.Bytes(), true
}

// net/http:发送HTTP请求---POST
func NativeSendHttpRequestPost(url string, data url.Values) (response []byte, err error) {
	client := &http.Client{
		Transport: &http.Transport{
			Dial: func(netw, addr string) (net.Conn, error) {
				client, err := net.DialTimeout(netw, addr, time.Second*3)
				if err != nil {
					return nil, err
				}
				client.SetDeadline(time.Now().Add(time.Second * 5))
				return client, nil
			},
			ResponseHeaderTimeout: time.Second * 3,
		},
	}
	//	queryParameter := DealQueryParameter(queryStruct)
	resp, err := client.PostForm(url, data)
	if err != nil {
		logger.Error("client PostForm failed :", err.Error())
		return
	}

	defer resp.Body.Close()
	if resp.StatusCode != 200 {
		logger.Error("client Post failed :", resp.StatusCode)
		err = errors.New("client Get failed")
		return
	}
	response, err = ioutil.ReadAll(resp.Body)
	if err != nil {
		logger.Error("ioutil ReadAll failed :", err.Error())
		return
	}

	return
}
