package utils

import (
	"bytes"
	"conferencecenter/internal/constant"
	"conferencecenter/internal/corecontext"
	"fmt"
	"github.com/jinzhu/now"
	"github.com/pkg/errors"
	"github.com/xuri/excelize/v2"
	"io/ioutil"
	"log"
	"reflect"
	"strconv"
	"strings"
	"time"
)

/**
 * 生成excel列表，使用默认Sheet1名称
 * excelHeader 在itemList为空的情况可以指定一个空对象（根据空对象生成包含excel表头的空白表格）
 */
func GenListExcel(itemList []interface{}, valueParse func(fieldName string, value interface{}) interface{}, excelHeader ...interface{}) (rd *bytes.Reader, er error) {
	if len(excelHeader) > 0 {
		return GenListExcelWithSheetName("", itemList, valueParse, excelHeader[0])
	} else {
		return GenListExcelWithSheetName("", itemList, valueParse)
	}
}

/**
 * 生成excel列表，可以指定sheet名称，默认是Sheet1
 */
func GenListExcelWithSheetName(sheetName string, itemList []interface{}, valueParse func(fieldName string, value interface{}) interface{}, excelHeader ...interface{}) (rd *bytes.Reader, er error) {
	var err error
	var hdInfo interface{}
	if itemList == nil || len(itemList) <= 0 { //无数据的情况可以指定表头导出
		if excelHeader != nil && len(excelHeader) > 0 {
			hdInfo = excelHeader[0]
		} else {
			return nil, errors.New("no any data")
		}
	} else {
		hdInfo = itemList[0]
	}
	itemHeadMap, itemColumnIndexMap, formatColumnMap, err := GetExcelHeadMapWithTag(reflect.TypeOf(hdInfo))
	if err != nil {
		corecontext.Log().Error(err)
		return nil, err
	}
	if len(itemHeadMap) <= 0 {
		er = errors.New("无有效数据")
		return
	}
	var titleRow = 1
	f := excelize.NewFile() // Create a new sheet.
	if len(sheetName) == 0 {
		sheetName = f.GetSheetName(f.GetActiveSheetIndex())
	} else {
		err = f.SetSheetName("Sheet1", sheetName)
		if err != nil {
			er = err
			return
		}
	}
	if sheetName == "" {
		er = errors.New("can't get the sheet name")
		return
	}
	{
		//写入标题
		for headName, columnIndex := range itemHeadMap {
			err = f.SetCellValue(sheetName, columnIndex+strconv.Itoa(titleRow), headName)
			if err != nil {
				return nil, err
			}
		}
	}
	rowIndex := titleRow + 1 //从第二行起始
	for _, rowVal := range itemList {
		rfRow := reflect.ValueOf(rowVal)
		for fieldName, columnIndex := range itemColumnIndexMap {
			pValue := rfRow.FieldByName(fieldName).Interface()

			//处理字段格式：
			format, ok := formatColumnMap[fieldName]
			if ok {
				switch pvv := pValue.(type) {
				case float64, float32:
					if !strings.HasPrefix(format, "%") && !strings.HasSuffix(format, "f") {
						log.Panicf("float format %s is not supported", format)
					}
					pValue = fmt.Sprintf(format, pvv) //浮点数的格式 %.2f
				case time.Time:
					if pvv.IsZero() {
						pValue = ""
					} else {
						switch format {
						case "timestamp":
							pValue = pvv.Unix()
						case "time":
							pValue = pvv.Format(constant.TimeOnly)
						case "date":
							pValue = pvv.Format(constant.DateOnly)
						case "datetime":
							pValue = pvv.Format(constant.DateTime)
						default:
							pValue = pvv.Format(constant.DateTime)
						}
					}

				case string:
					switch format {
					case "timestamp", "time", "date", "datetime":
						t, err := now.ParseInLocation(time.Local, pvv)
						if err != nil {
							log.Printf("%v 时间解析失败", pvv)
						}
						switch format {
						case "timestamp":
							pValue = t.Unix()
						case "time":
							pValue = t.Format(constant.TimeOnly)
						case "date":
							pValue = t.Format(constant.DateOnly)
						case "datetime":
							pValue = t.Format(constant.DateTime)
						}
					}
				}

			} else {
				//处理时间格式
				pvv, ok := pValue.(time.Time)
				if ok {
					if pvv.IsZero() {
						pValue = ""
					} else {
						pValue = pvv.Format(constant.DateTime)
					}
				}
			}

			if valueParse != nil { //对数据值做加工处理
				pValue = valueParse(fieldName, pValue)
			}
			err = f.SetCellValue(sheetName, columnIndex+strconv.Itoa(rowIndex), pValue)
			if err != nil {
				er = err
				return
			}
		}
		rowIndex++
	}
	{
		buf, err := f.WriteToBuffer()
		if err != nil {
			er = err
			return
		}
		//转换成字节流
		bufBytes, err := ioutil.ReadAll(buf)
		if err != nil {
			er = err
			return
		}
		rd := bytes.NewReader(bufBytes)
		if rd != nil {
			return rd, nil
		}
	}
	return
}

/**
 * 生成表头map
 */
func GetExcelHeadMap(souMap map[string]string, rfType reflect.Type, withTag bool) (headMap, excelColumnMap, formatColumnMap map[string]string, er error) {
	headMap, excelColumnMap, formatColumnMap = map[string]string{}, map[string]string{}, map[string]string{} //标题，excel列索引, 列格式
	//获取表头映射关系
	if souMap != nil && len(souMap) > 0 {
		for fldName, val := range souMap {
			if val == "" {
				continue
			}
			v1 := strings.Split(val, ",")
			if len(v1) >= 2 {
				headMap[v1[0]] = v1[1]
				excelColumnMap[fldName] = v1[1]
			} else {
				headMap[fldName] = val //直接使用实体名称作为列名
				excelColumnMap[fldName] = val
			}
		}
	} else if rfType != nil {
		rf := rfType
		idx, cIndx := 0, 0
		if withTag {
			cIndexSet := map[string]int{}
			for idx < rf.NumField() {
				rxTag := rf.Field(idx).Tag
				rxName := rf.Field(idx).Name
				if tgVal, ok := rxTag.Lookup("excel_smm"); ok { //读取tag信息 tag format eg："数据项名称,A,format:.2f"
					if tgVal != "" {
						tList := strings.Split(tgVal, ",")
						for i := range tList {
							tList[i] = strings.TrimSpace(tList[i])
						}
						if len(tList) >= 2 {
							_, ok = cIndexSet[tList[1]]
							if ok {
								return nil, nil, nil, errors.Errorf("字段'%s'tag'%s' 与其他字段的冲突", rxName, tgVal)
							} else {
								cIndexSet[tList[1]] = cIndx
							}
							headMap[tList[0]] = tList[1]      //列头名称
							excelColumnMap[rxName] = tList[1] //excel 列索引

							if len(tList) >= 3 {
								formatColumnMap[rxName] = strings.TrimPrefix(tList[2], "format:") //字段格式
							}
						} else {
							columnIndex := ExcelAlikeColumn(cIndx + 1)
							headMap[tgVal] = columnIndex         //key:列头名称==>value:索引
							excelColumnMap[rxName] = columnIndex //key:实体field名称==>value:索引
						}
						cIndx++
					}
				}
				idx++
			}
		} else {
			for idx < rf.NumField() {
				rxName := rf.Field(idx).Name
				rxTag := rf.Field(idx).Tag
				if _, ok := rxTag.Lookup("excel_ignore"); !ok {
					excelColumn := ExcelAlikeColumn(cIndx + 1)
					headMap[rxName] = excelColumn //rf.Name() //直接使用实体名称作为列名
					excelColumnMap[rxName] = excelColumn
					cIndx++
				}
				idx++
			}
		}

	} else {
		return nil, nil, nil, errors.New("没有任何需要导出的数据")
	}
	return
}

/**
 * 根据tag生成
 */
func GetExcelHeadMapWithTag(rfType reflect.Type) (headMap, excelColumnMap, formatColumnMap map[string]string, er error) {
	return GetExcelHeadMap(nil, rfType, true)
}

/**
 * 生成excel 列索引（字母）
 */
func ExcelAlikeColumn(iColumn int) string {
	aColumn := ""
	columnNumber := iColumn
	for columnNumber > 0 {
		cColumnNum := (columnNumber - 1) % 26
		aColumn = string(rune(cColumnNum+65)) + aColumn
		columnNumber = (columnNumber - (cColumnNum + 1)) / 26
	}
	return aColumn
}
