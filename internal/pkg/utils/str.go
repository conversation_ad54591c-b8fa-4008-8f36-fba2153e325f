package utils

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/pkg/errors"
	"html/template"
	"io/ioutil"
	"math/rand"
	"regexp"
	"strconv"
	"strings"
	"time"
	"unicode"
	"unsafe"

	"git.code.tencent.com/smmit/smmbase/logger"
)

func Split(s, sep string) []string {
	t := strings.TrimSpace(s)
	if len(t) == 0 {
		return nil
	}

	var arr []string
	for _, v := range strings.Split(t, sep) {
		if len(v) == 0 {
			continue
		}

		arr = append(arr, v)
	}

	if len(arr) == 0 {
		return nil
	}

	return arr
}

type UniqueEntry interface {
	Get() string
}

func Unique(a []UniqueEntry) (ret []UniqueEntry) {
	var filter = make(map[string]struct{})
	for _, v := range a {
		_, exist := filter[v.Get()]
		if exist {
			continue
		} else {
			filter[v.Get()] = struct{}{}

			ret = append(ret, v)
		}
	}

	return
}

func RemDuplicate(a []string) (ret []string) {
	var filter = make(map[string]struct{})
	for _, v := range a {
		_, exist := filter[v]
		if exist {
			continue
		} else {
			filter[v] = struct{}{}

			ret = append(ret, v)
		}
	}

	return
}

func Cut(raw []string, s string) []string {
	var newStr []string

	for _, v := range raw {
		if v != s {
			newStr = append(newStr, v)
		}
	}

	return newStr
}

// ToString 把[]byte转成string
func ToString(b []byte) string {
	return *(*string)(unsafe.Pointer(&b))
}

func ToBytes(s string) []byte {
	return *(*[]byte)(unsafe.Pointer(&s))
}

// 切片去重
func RemoveDuplicate(slis *[]string) {
	found := make(map[string]bool)
	j := 0
	for i, val := range *slis {
		if _, ok := found[val]; !ok {
			found[val] = true
			(*slis)[j] = (*slis)[i]
			j++
		}
	}
	*slis = (*slis)[:j]
}

// 切片的每一个元素移除字符串
func RemoveStr(slis *[]string, removeStr ...string) {
	for i, _ := range *slis {
		temp := (*slis)[i]
		for _, rem := range removeStr {
			temp = strings.ReplaceAll(temp, rem, "")
		}
		(*slis)[i] = temp
	}
}

const (
	KC_RAND_KIND_NUM   = iota // 纯数字
	KC_RAND_KIND_LOWER        // 小写字母
	KC_RAND_KIND_UPPER        // 大写字母
	KC_RAND_KIND_ALL          // 数字、大小写字母
)

// 随机字符串
func NonceStr(size int, kind int) []byte {
	ikind, kinds, result := kind, [][]int{[]int{10, 48}, []int{26, 97}, []int{26, 65}}, make([]byte, size)
	is_all := kind > 2 || kind < 0
	rand.Seed(time.Now().UnixNano())
	for i := 0; i < size; i++ {
		if is_all { // random ikind
			ikind = rand.Intn(3)
		}
		scope, base := kinds[ikind][0], kinds[ikind][1]
		result[i] = uint8(base + rand.Intn(scope))
	}
	return result
}

func StrIn(val string, args ...string) bool {
	for _, a := range args {
		if val == a {
			return true
		}
	}
	return false
}

func IsChinese(str string) bool {
	var count int
	for _, v := range str {
		if unicode.Is(unicode.Han, v) {
			count++
			break
		}
	}
	return count > 0
}

func MarshalStringOnly(o interface{}) string {
	jsVal, err := json.Marshal(o)
	if err != nil {
		return ""
	}
	return string(jsVal)
}

func Str2Int(str []string) ([]int, error) {
	s := make([]int, 0, len(str))
	for _, e := range str {
		i, err := strconv.Atoi(e)
		if err != nil {
			logger.Debug(e, err)
			return s, errors.New("数据处理异常")
		}
		s = append(s, i)
	}
	return s, nil
}

func TransStr2Int(str string) (int, bool) {
	num, err := strconv.Atoi(str)
	if err != nil {
		_ = logger.Warnning(str, err)
		return 0, false
	}
	return num, true
}

/**
 * 利用正则提取手机号(1开头共11位)
 */
func ParseCellPhoneNum(content string) string {
	reg := regexp.MustCompile(`^1[0-9]{10}$`) //匹配手机号
	if reg != nil {
		return reg.FindString(content)
	}
	return ""
}

func CheckReceiverPhone(rawPhone string) bool {
	newPhone := ParseCellPhoneNum(rawPhone)
	if newPhone != "" && rawPhone == newPhone {
		return true
	}
	return false
}

func IsEmpty(str string) bool {
	if len(strings.Trim(str, " ")) > 0 {
		return false
	}
	return true
}
func NotEmpty(str string) bool {
	return !IsEmpty(str)
}

func RenderHtmlText(text string, vars map[string]interface{}) string {
	t := template.New("")
	t, _ = t.Parse(text)

	buf := new(bytes.Buffer)
	t.Execute(buf, vars)
	content, _ := ioutil.ReadAll(buf)
	return string(content)

}

func IsEmailValid(email string) bool {
	// 使用正则表达式判断邮箱格式
	// 此处使用了简单的邮箱格式正则表达式，实际项目中可能需要更加复杂的正则表达式来匹配邮箱格式
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return emailRegex.MatchString(email)
}

func FormatDateRange(startStr, endStr string) (string, error) {
	// 定义时间格式
	const layout = "2006-01-02"

	// 解析时间
	start, err := time.Parse(layout, startStr)
	if err != nil {
		return "", fmt.Errorf("invalid start time format: %w", err)
	}

	end, err := time.Parse(layout, endStr)
	if err != nil {
		return "", fmt.Errorf("invalid end time format: %w", err)
	}

	// 检查时间顺序
	if end.Before(start) {
		return "", errors.New("end time cannot be before start time")
	}

	// 生成基础格式
	startPart := fmt.Sprintf("%d年%d月%d日", start.Year(), start.Month(), start.Day())

	// 生成结束部分格式
	var endPart string
	switch {
	case start.Year() != end.Year():
		endPart = fmt.Sprintf("%d年%d月%d日", end.Year(), end.Month(), end.Day())
	case start.Month() != end.Month():
		endPart = fmt.Sprintf("%d月%d日", end.Month(), end.Day())
	default:
		endPart = fmt.Sprintf("%d日", end.Day())
	}

	return startPart + "-" + endPart, nil
}
