package utils

import (
	"conferencecenter/internal/constant"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"git.code.tencent.com/smmit/smmbase/logger"
	"github.com/Lofanmi/pinyin-golang/pinyin"
	"github.com/jinzhu/now"
	pinyinv2 "github.com/mozillazg/go-pinyin"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"math"
	"math/rand"
	"os"
	"regexp"
	"strconv"
	"strings"
	"time"
)

const (
	ENV_DEV        = "dev"
	ENV_TEST       = "test"
	ENV_PRODUCTION = "production"
)

func GetServerHost(serverName string, host, ip string) string {
	env := os.Getenv("SMM_SERVER_ENV")
	if env == ENV_TEST {
		return "testplatform.smm.cn/" + serverName
	} else if env == ENV_PRODUCTION {
		return "platform.smm.cn/" + serverName
	}

	return host + ":" + ip
}

func IsTestEnv() bool {
	if os.Getenv("SMM_SERVER_ENV") != "production" {
		return true
	}
	return false
}

func IsNotEnv() bool {
	getenv := os.Getenv("SMM_SERVER_ENV")
	if getenv != ENV_DEV && getenv != "" {
		return true
	}
	return false
}

// ValueIn 判断参数值value是否在args可变参数中
func IntValueIn(value int, args ...int) bool {
	for _, arg := range args {
		if value == arg {
			return true
		}
	}
	return false
}

func StrParamIn(value string, args ...string) bool {
	for _, arg := range args {
		if value == arg {
			return true
		}
	}
	return false
}

// a1-a2
func Float64Sub(a1 float64, a2 float64) float64 {
	r, _ := decimal.NewFromFloat(a1).Sub(decimal.NewFromFloat(a2)).Float64()
	return r
}

// a1+a2
func Float64Add(a1 float64, a2 float64) float64 {
	r, _ := decimal.NewFromFloat(a1).Add(decimal.NewFromFloat(a2)).Float64()
	return r
}

// a1+a2
func Float64AddOfRound(a1 float64, a2 float64, precision int) float64 {
	r, _ := decimal.NewFromFloat(a1).Add(decimal.NewFromFloat(a2)).Round(int32(precision)).Float64()
	return r
}

// a1*a2
func Float64Mul(a1 float64, a2 float64) float64 {
	r, _ := decimal.NewFromFloat(a1).Mul(decimal.NewFromFloat(a2)).Float64()
	return r
}

// a1/a2
func Float64Div(a1 float64, a2 float64, precision int) float64 {
	if a2 == 0 {
		fmt.Printf("%f / %f is invalid", a1, a2)
		return math.NaN()
	}

	r, _ := decimal.NewFromFloat(a1).DivRound(decimal.NewFromFloat(a2), int32(precision)).Float64()
	return r
}

func Float64ToStr(f float64) string {
	return strconv.FormatFloat(f, 'f', -1, 64)
}

func Float64Round(f float64, n int) float64 {
	f1 := decimal.NewFromFloat(f)
	f2, _ := f1.Round(int32(n)).Float64()
	return f2
}

func Float64Abs(f float64) float64 {
	f1, _ := decimal.NewFromFloat(f).Abs().Float64()
	return f1
}

func String2Float64(str string) (v float64, err error) {
	var d decimal.Decimal
	d, err = decimal.NewFromString(str)
	if err != nil {
		return
	}
	v, _ = d.Float64()
	return
}

// 前数1位，倒数2位，其他的用 * 替换
// HideStr(1234789,1,2) --> 12***34
func HideStr(str string, start int, end int) string {
	defer func() {
		if r := recover(); r != nil {
			logger.Debug("recovered from ", r)
			return
		}
	}()
	list := []rune(str)
	length := len(list)

	if start < 0 {
		start = 0
	}

	if end < 0 {
		end = 0
	}

	if length < start {
		logger.Debug(str, "len smaller then", end)
		return str
	}

	if length < end {
		logger.Debug(str, "len smaller then", end)
		return str
	}

	if length < end+start {
		logger.Debug(str, "len smaller then", end+start)
		return str
	}

	if start < 0 {
		return str
	}
	repeat := length - start - end
	if repeat < 0 {
		return str
	}
	//logger.Debug(len(list), start, end, repeat)
	startIndex, endIndex := start, (length - end)

	return string(list[:startIndex]) + strings.Repeat("*", repeat) + string(list[endIndex:])
}

// RealNameWrapper 中文脱敏
func RealNameWrapper(realName string) string {
	ch := []rune(realName)
	l := len(ch)
	if l > 2 {
		return HideStr(realName, 1, 1)
	}
	return HideStr(realName, 1, 0)
}

func BankAcctNoWrapper(acctNo string) string {
	return HideStr(acctNo, 4, 4)
}
func CellphoneWrapper(mobile string) string {
	return HideStr(mobile, 3, 4)
}

func Json2List(a string) ([]string, error) {
	var result []string
	if len(a) == 0 {
		return result, nil
	}
	err := json.Unmarshal([]byte(a), &result)
	return result, err
}

func RandCode() string {
	// 6位数字
	n := rand.Intn(1000000)

	return fmt.Sprintf("%06d", n)
}

func InStoreDays(t1, t2 time.Time) int {
	bt1 := now.New(t1).BeginningOfDay()
	if t2.IsZero() {
		t2 = time.Now()
	}

	if t2.Day() == bt1.Day() {
		//同一天
		return 1
	} else {
		t2 = now.New(t2).BeginningOfDay().AddDate(0, 0, 1)
		days := int(t2.Sub(t1).Hours() / 24)
		return days
	}

}

func DumpJson(v interface{}, pretty bool) string {
	if pretty {
		b, _ := json.MarshalIndent(v, "\n", "    ")
		return string(b)
	} else {
		b, _ := json.Marshal(v)
		return string(b)
	}
}

// 获取文件的扩展名
func Ext(path string) string {
	for i := len(path) - 1; i >= 0 && path[i] != '/'; i-- {
		if path[i] == '.' {
			return path[i:]
		}
	}
	return ""
}

func Md5(s string) string {
	data := []byte(s)
	md5Ctx := md5.New()
	md5Ctx.Write(data)
	return hex.EncodeToString(md5Ctx.Sum(nil))
}

func FormatTime(t time.Time, args ...string) string {
	var layout = constant.DateTime
	if len(args) > 0 {
		layout = args[0]
	}
	if t.IsZero() {
		return ""
	} else {
		return t.Format(layout)
	}
}

// MobileValid 正则校验手机号合法性
func MobileValid(mobile string) error {
	if mobile == "" {
		return errors.New("手机号为空")
	}
	reg := regexp.MustCompile("^1[0-9]{10}$")
	ok := reg.MatchString(mobile)
	if ok {
		return nil
	} else {
		return errors.New("手机号格式不正确")
	}
}

// EmailValid 正则校验邮箱合法性
func EmailValid(email string) (bool, error) {
	if email == "" {
		return false, errors.New("邮箱为空")
	}
	reg := regexp.MustCompile("^[a-zA-Z0-9._%+\\-]+@[a-zA-Z0-9.\\-]+\\.[a-zA-Z]{2,}$")
	ok := reg.MatchString(email)
	if ok {
		return true, nil
	} else {
		return false, errors.New("邮箱格式不正确")
	}
}

func CheckMultiEmail(emails string) bool {
	if emails == "" {
		return false
	}
	reg := regexp.MustCompile("^[a-zA-Z0-9._%+\\-]+@[a-zA-Z0-9.\\-]+\\.[a-zA-Z]{2,}$")
	for _, email := range strings.Split(emails, ",") {
		ok := reg.MatchString(email)
		if !ok {
			return false
		}
	}
	return true
}

func GetCellphoneCountryCode(originCellphone string) (string, string) {
	originCellphones := strings.Split(originCellphone, "-")
	if len(originCellphones) == 1 {
		return "86", originCellphone
	}

	if len(originCellphones) == 2 {
		return originCellphones[0], originCellphones[1]
	}

	return "", originCellphone
}

func GetNonTimeToStr(time time.Time) string {
	if time.IsZero() {
		return ""
	}
	return time.Format("2006-01-02")
}

// 返回全称+首字母简称
func Chinese2Pinyin(chinese string) string {
	if chinese == "" {
		return ""
	}
	pDict := pinyin.NewDict()
	pName := pDict.Convert(chinese, "").None() // 转为拼音，不带声调
	pAbbr := pDict.Abbr(chinese, "")           // 转为首字母简写
	if pName != "" && pAbbr != "" {
		return pName + "," + pAbbr
	} else if pName != "" {
		return pName
	} else if pAbbr != "" {
		return pAbbr
	}
	return ""
}

// 首字母简称
func TransPinyinFirstName(chinese string) string {
	if chinese == "" {
		return ""
	}
	pDict := pinyin.NewDict()
	pAbbr := pDict.Abbr(chinese, "") // 转为首字母简写
	return pAbbr
}

// 检查公司代码合法性
func CheckComCodeWithCityCode(comCode, cityCode string) (tag bool) {
	reg := regexp.MustCompile(fmt.Sprintf("^%s[0-9]{10}$", cityCode))
	return reg.MatchString(comCode)
}

// 检查公司代码(一般检查)
func CheckComCodeGeneral(comCode string) (tag bool) {
	reg := regexp.MustCompile("^[A-Z]{2}[0-9]{10}$")
	return reg.MatchString(comCode)
}

// 检查股票代码合法性
func CheckStockCode(rawCode, exCode string) (tag bool) {
	reg := regexp.MustCompile(fmt.Sprintf("^[0-9]+\\.%s$", exCode))
	return reg.MatchString(rawCode)
}

func TransStringFirstString(str string) (strList []string) {
	if str == "" {
		return []string{}
	}
	r := []rune(str)
	alpha := r[0]
	firstString := string(alpha)
	//英文直接取第一个字符
	isEnLetter := (alpha >= 97 && alpha <= 122) || (alpha >= 65 && alpha <= 90)
	if isEnLetter {
		return []string{strings.ToLower(firstString)}
	}
	args := pinyinv2.NewArgs()
	args.Heteronym = true
	pinYinArray := pinyinv2.Pinyin(firstString, args)
	if len(pinYinArray) == 0 {
		return []string{str}
	}
	firstPinYin := pinYinArray[0]
	if len(firstPinYin) == 0 {
		return []string{}
	}
	for _, pinYin := range firstPinYin {
		runes := []rune(pinYin)
		strList = append(strList, string(runes[0]))
	}
	return
}
