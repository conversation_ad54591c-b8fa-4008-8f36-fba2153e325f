package utils

import (
	"git.code.tencent.com/smmit/smmbase/logger"
	"math/rand"
	"time"
)

type stop struct {
	error
}

func init() {
	rand.Seed(time.Now().UnixNano())
}

func Retry(attempts int64, sleep time.Duration, f func() error) error {
	_ = logger.Debug("some retry", attempts)
	if err := f(); err != nil {
		_ = logger.Warnning("some retry failure", attempts, err)

		if s, ok := err.(stop); ok {
			return s.error
		}

		if attempts--; attempts > 0 {
			jitter := time.Duration(rand.Int63n(int64(sleep)))
			sleep = sleep + jitter/2

			time.Sleep(sleep)
			return Retry(attempts, 2*sleep, f)
		}

		return err
	}

	return nil
}
