package utils

import (
	"strings"
	"time"
)

const (
	DT = "2006-01-02 15:04:05" // date time
)

func ParseDateAuto(s string) (time.Time, error) {
	if strings.Contains(s, ":") {
		return ParseDatetime(s)
	} else {
		return ParseDateYMD(s)
	}
}

func ParseDateYMD(s string) (time.Time, error) {
	return time.ParseInLocation("2006-01-02", s, time.Local)
}
func ParseDatetime(s string) (time.Time, error) {
	return time.ParseInLocation("2006-01-02 15:04:05", s, time.Local)
}
func GetCurDateTime() string {
	return time.Now().Format(DT)
}

func TransDateTimeToEn(s string) string {
	dateTime, err := time.ParseInLocation("2006-01-02", s, time.Local)
	if err != nil {
		return s
	}
	str := dateTime.Format("Jan 02 , 2006")
	return str
}
