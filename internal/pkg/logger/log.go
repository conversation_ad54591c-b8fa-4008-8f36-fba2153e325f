package logger

import (
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"os"
	"strings"
	"time"
)

const (
	DateTimeMilli = "2006-01-02 15:04:05.000"
)

// Options is logger configuration struct
type Options struct {
	Level string `yaml:"level"`
}

// Option is Helper option.
type Option func(*Options)

func WithLevel(level string) Option {
	return func(opts *Options) {
		opts.Level = level
	}
}

func Init(options ...Option) *zap.SugaredLogger {
	dOpt := &Options{
		Level: "INFO",
	}
	for _, o := range options {
		o(dOpt)
	}

	enConfig := zap.NewProductionEncoderConfig() // 生成配置
	// 时间格式
	enConfig.EncodeTime = func(t time.Time, encoder zapcore.PrimitiveArrayEncoder) {
		encoder.AppendString(t.Format(DateTimeMilli))
	}
	// level字母大写
	enConfig.EncodeLevel = zapcore.CapitalLevelEncoder

	var level = zapcore.InfoLevel
	switch strings.ToUpper(dOpt.Level) {
	case "DEBUG":
		level = zapcore.DebugLevel
	}

	core := zapcore.NewCore(
		zapcore.NewConsoleEncoder(enConfig),                     // 编码器配置
		zapcore.NewMultiWriteSyncer(zapcore.AddSync(os.Stdout)), // 打印到控制台和文件
		level, // 日志等级
	)

	logger := zap.New(core, zap.AddCaller())
	l := logger.Sugar()
	l.Debug("初始化日志完成")
	return l
}
