package cache

import (
	"git.code.tencent.com/smmit/smmbase/logger"
	"time"
)

/**
 * 分布式操作锁
 */
const OperationKey = "operate_lock:"

func (c *Cache) LockOperate(keyType, keyVal string, expire int64) bool {
	if keyType == "" || keyVal == "" {
		_ = logger.Warnning("unknown lock type and value ")
		return false
	}
	if expire <= 0 { //默认时间
		expire = 120
	}
	return c.SETNX(getOpKey(keyType, keyVal), "1", expire)
}

func (c *Cache) LockOperateWithTimeOut(keyType, keyVal string, expire int64, tOutSec int) bool {
	timeOut := time.Second * time.Duration(tOutSec)
	ts := time.Now()
	for {
		if ok := c.LockOperate(keyType, keyVal, expire); ok {
			return true
		}

		time.Sleep(time.Second * 1) //休息1s,重试

		dur := time.Since(ts)
		if dur >= timeOut { //超时中断
			return false
		}
	}
}

func (c *Cache) UnLockOperate(keyType, keyVal string) bool {
	if keyType == "" || keyVal == "" {
		_ = logger.Warnning("unknown lock type and value ")
		return false
	}
	err := c.Del(getOpKey(keyType, keyVal))
	if err != nil {
		return false
	}
	return true
}

func getOpKey(keyType, keyVal string) string {
	return OperationKey + keyType + ":" + keyVal
}
