package cache

import "C"
import (
	"conferencecenter/internal/constant"
	"conferencecenter/internal/model"
	"errors"
	"fmt"
	"log"
	"time"

	"git.code.tencent.com/smmit/smmbase/logger"
	"github.com/garyburd/redigo/redis"
)

var (
	ErrInvalidParam           = errors.New("Invalid Parameters")
	ErrZRevRangeByScoreFailed = errors.New("ZREVRANGEBYSCORE failed")
	ErrZRangeByScoreFailed    = errors.New("ZRANGEBYSCORE failed")
)

/*********************** Del ***********************/
func (c *Cache) Del(key string) error {
	conn := c.pool.Get()
	defer conn.Close()

	_ = logger.Info("Redis操作", 0, map[string]interface{}{"键删除": c.prefix + key})
	_, err := conn.Do(constant.CommandDEL, c.prefix+key)
	if err != nil {
		logger.Warnning(err)
		return err
	}

	return nil
}
func (c *Cache) KeySearch(keyPattern string, initIndex, searchCount int) ([]string, error) {
	conn := c.pool.Get()
	defer conn.Close()

	var (
		err  error
		arr  []interface{}
		iter int
		keys []string
	)
	iter = initIndex //起始搜索索引
	keyPatWithPrev := c.prefix + keyPattern
	logger.Debug(keyPattern, keyPatWithPrev, "start search, start at ", iter, ";search step:", searchCount)
	for {
		time.Sleep(2 * time.Microsecond)
		if arr, err = redis.Values(conn.Do(constant.CommandSCAN, iter, "MATCH", keyPatWithPrev, "COUNT", searchCount)); err != nil {
			logger.Warnning("scan err", keyPatWithPrev, err)
			return nil, err
		} else {
			iter, err = redis.Int(arr[0], nil)
			if err != nil {
				return nil, err
			}
			list, err := redis.Strings(arr[1], nil)
			if err != nil {
				return nil, err
			}
			if len(list) > 0 {
				keys = append(keys, list...)
			}
		}
		if iter == 0 {
			break
		}
	}
	logger.Debug(keyPattern, "search result :", len(keys))
	return keys, nil
}
func (c *Cache) KeySearchDefault(keyPattern string) ([]string, error) {
	return c.KeySearch(keyPattern, 0, 10000)
}

/**************string */
func (c *Cache) MSET(h []model.CacheHash) error {
	conn := c.pool.Get()
	defer conn.Close()

	for _, v := range h {
		err := conn.Send(constant.CommandSET, c.prefix+v.GetKey(), v.GetValue())
		if err != nil {
			logger.Warnning(err)
			return err
		} else if dur, need := v.NeedExpire(); need {
			err = conn.Send(constant.CommandEXPIRE, c.prefix+v.GetKey(), dur)
			if err != nil {
				logger.Warnning(err)
				return err
			}
		}
	}

	err := conn.Flush()
	if err != nil {
		logger.Warnning(err)
		return err
	}

	return nil
}
func (c *Cache) IsExist(key string) (bool, error) {
	conn := c.pool.Get()
	defer conn.Close()
	rep, err := redis.Int(conn.Do("EXISTS", c.prefix+key))
	if err != nil {
		return false, err
	}
	return rep == 1, nil
}
func (c *Cache) INCR(key string, expire int64) error {
	conn := c.pool.Get()
	defer conn.Close()
	cacheKey := c.prefix + key
	_, err := conn.Do(constant.CommandINCR, cacheKey)
	if err != nil {
		logger.Warnning(err)
		return err
	}
	if expire > 0 {
		_, err = conn.Do(constant.CommandEXPIRE, cacheKey, expire)
		if err != nil {
			logger.Warnning("set expire failure", cacheKey, err)
		}
	}
	return nil
}
func (c *Cache) INCRWithVal(key string, expire int64) (val int64, err error) {
	conn := c.pool.Get()
	defer conn.Close()
	cacheKey := c.prefix + key
	rp, er := conn.Do(constant.CommandINCR, cacheKey)
	if er != nil {
		err = er
		return
	}
	if expire > 0 {
		_, err = conn.Do(constant.CommandEXPIRE, cacheKey, expire)
		if err != nil {
			logger.Warnning("set expire failure", cacheKey, err)
		}
	}
	if val1, ok := rp.(int64); !ok {
		err = fmt.Errorf("not valid response value type for incr(%v)", rp)
		return
	} else {
		val = val1
	}
	return
}
func (c *Cache) SET(key, value string) error {
	conn := c.pool.Get()
	defer conn.Close()

	_, err := conn.Do(constant.CommandSET, c.prefix+key, value)
	if err != nil {
		logger.Warnning(err)
		return err
	}
	return nil
}
func (c *Cache) SetExpire(key string, value interface{}, expireAt int) error {
	conn := c.pool.Get()
	defer conn.Close()

	_, err := conn.Do(constant.CommandSET, c.prefix+key, value, "EX", expireAt)
	if err != nil {
		logger.Warnning("SET", key, value, err)
		return err
	}
	return nil
}
func (c *Cache) GET(key string) (interface{}, error) {
	conn := c.pool.Get()
	defer conn.Close()

	rep, err := conn.Do(constant.CommandGET, c.prefix+key)
	if err != nil {
		logger.Warnning(err)
		return nil, err
	}
	return rep, nil
}
func (c *Cache) GETSET(key, val string) (interface{}, error) {
	conn := c.pool.Get()
	defer conn.Close()

	rep, err := conn.Do(constant.CommandGETSET, c.prefix+key, val)
	if err != nil {
		logger.Warnning(err)
		return nil, err
	}
	return rep, nil
}
func (c *Cache) SETNX(key, val string, expire int64) bool {
	conn := c.pool.Get()
	defer conn.Close()
	cacheKey := c.prefix + key
	resp, err := redis.Int(conn.Do(constant.CommandSETNX, cacheKey, val))
	if err != nil {
		logger.Warnning(err)
		return false
	}
	//fmt.Println(resp)
	if resp > 0 {
		if expire > 0 {
			err = conn.Send(constant.CommandEXPIRE, cacheKey, expire)
			if err != nil {
				logger.Warnning("set expire failure", cacheKey, err)
			}
		}
		return true
	} else {
		return false
	}
}

/*********************** SET ****************/
func (c *Cache) SSETAddOnly(h []model.CacheHash) error {
	conn := c.pool.Get()
	defer conn.Close()

	for _, v := range h {
		cacheKey := c.prefix + v.GetKey()
		err := conn.Send(constant.CommandSADD, redis.Args{}.Add(cacheKey).AddFlat(v.GetValue())...)
		if err != nil {
			logger.Warnning(cacheKey, err)
			return err
		} else if dur, need := v.NeedExpire(); need {
			err = conn.Send(constant.CommandEXPIRE, cacheKey, dur)
			if err != nil {
				logger.Warnning(cacheKey, err)
				return err
			}
		}
	}

	err := conn.Flush()
	if err != nil {
		logger.Warnning(err)
		return err
	}

	return nil
}
func (c *Cache) SSETAddNew(h []model.CacheHash) error {
	conn := c.pool.Get()
	defer conn.Close()

	for _, v := range h {
		cacheKey := c.prefix + v.GetKey()
		err := conn.Send(constant.CommandDEL, cacheKey)
		if err != nil {
			logger.Warnning("clear key failure", cacheKey, err)
		}
		err = conn.Send(constant.CommandSADD, redis.Args{}.Add(cacheKey).AddFlat(v.GetValue())...)
		if err != nil {
			logger.Warnning(cacheKey, err)
			return err
		} else if dur, need := v.NeedExpire(); need {
			err = conn.Send(constant.CommandEXPIRE, cacheKey, dur)
			if err != nil {
				logger.Warnning(cacheKey, err)
				return err
			}
		}
	}

	err := conn.Flush()
	if err != nil {
		logger.Warnning(err)
		return err
	}

	return nil
}
func (c *Cache) SSETQuery(key string) (interface{}, error) {
	conn := c.pool.Get()
	defer conn.Close()

	rep, err := conn.Do(constant.CommandSMEMBERS, c.prefix+key)
	if err != nil {
		logger.Warnning(err)
		return nil, err
	}
	return rep, nil
}
func (c *Cache) SSCAN(keyPattern string, initIndex, searchCount int) ([][]byte, error) {
	conn := c.pool.Get()
	defer conn.Close()

	var (
		err  error
		arr  []interface{}
		iter int
		keys [][]byte
	)
	iter = initIndex //起始搜索索引
	keyPattWithPrev := c.prefix + keyPattern
	logger.Debug(keyPattern, keyPattWithPrev, "start search, start at ", iter, ";search step:", searchCount)
	for {
		time.Sleep(2 * time.Microsecond)
		if arr, err = redis.Values(conn.Do(constant.CommandSSCAN, keyPattWithPrev, iter, "COUNT", searchCount)); err != nil {
			logger.Warnning("scan err", keyPattWithPrev, err)
			return nil, err
		} else {
			iter, err = redis.Int(arr[0], nil)
			if err != nil {
				return nil, err
			}
			list, err := redis.ByteSlices(arr[1], nil)
			if err != nil {
				return nil, err
			}
			if len(list) > 0 {
				keys = append(keys, list...)
			}
		}
		if iter == 0 {
			break
		}
	}
	logger.Debug(keyPattern, "search result :", len(keys))
	return keys, nil
}

func (c *Cache) SSCANDefault(keyPatt string) ([][]byte, error) {
	return c.SSCAN(keyPatt, 0, 2000)
}

/*********************** Hash ***********************/
func (c *Cache) HMSET(h []model.CacheHash) error {
	conn := c.pool.Get()
	defer conn.Close()

	for _, v := range h {
		err := conn.Send(constant.CommandHMSET, redis.Args{}.Add(c.prefix+v.GetKey()).AddFlat(v.GetValue())...)
		if err != nil {
			logger.Warnning(err)
			return err
		} else if dur, need := v.NeedExpire(); need {
			err = conn.Send(constant.CommandEXPIRE, c.prefix+v.GetKey(), dur)
			if err != nil {
				logger.Warnning(err)
				return err
			}
		}
	}

	err := conn.Flush()
	if err != nil {
		logger.Warnning(err)
		return err
	}

	return nil
}
func (c *Cache) HMSETAddNew(h []model.CacheHash) error {
	conn := c.pool.Get()
	defer conn.Close()

	for _, v := range h {
		cacheKey := c.prefix + v.GetKey()
		err := conn.Send(constant.CommandDEL, cacheKey)
		if err != nil {
			logger.Warnning("clear key failure", cacheKey, err)
		}
		err = conn.Send(constant.CommandHMSET, redis.Args{}.Add(cacheKey).AddFlat(v.GetValue())...)
		if err != nil {
			logger.Warnning(cacheKey, err)
			return err
		} else if dur, need := v.NeedExpire(); need {
			err = conn.Send(constant.CommandEXPIRE, cacheKey, dur)
			if err != nil {
				logger.Warnning(cacheKey, err)
				return err
			}
		}
	}

	err := conn.Flush()
	if err != nil {
		logger.Warnning(err)
		return err
	}

	return nil
}
func (c *Cache) HMSETValue(h []model.CacheHashValue) error {
	conn := c.pool.Get()
	defer conn.Close()

	for _, v := range h {
		err := conn.Send(constant.CommandHMSET, redis.Args{}.Add(c.prefix+v.GetKey()).Add(v.GetField()).AddFlat(v.GetValue())...)
		if err != nil {
			logger.Warnning(err)
			return err
		} else if dur, need := v.NeedExpire(); need {
			err = conn.Send(constant.CommandEXPIRE, c.prefix+v.GetKey(), dur)
			if err != nil {
				logger.Warnning(err)
				return err
			}
		}
	}

	err := conn.Flush()
	if err != nil {
		logger.Warnning(err)
		return err
	}

	return nil
}
func (c *Cache) HGET(key, member string) ([]byte, error) {
	conn := c.pool.Get()
	defer conn.Close()

	rep, err := redis.Bytes(conn.Do(constant.CommandHGET, c.prefix+key, member))
	if err != nil {
		_ = logger.Warnning(err)
		return nil, err
	}

	return rep, nil
}
func (c *Cache) HSET(key, member, value string) error {
	conn := c.pool.Get()
	defer conn.Close()

	_, err := conn.Do(constant.CommandHSET, c.prefix+key, member, value)
	if err != nil {
		logger.Warnning(err)
		return err
	}
	return nil
}
func (c *Cache) HSETMap(key string, kvMap map[string]string) error {
	conn := c.pool.Get()
	defer conn.Close()

	for mb, v := range kvMap {
		err := conn.Send(constant.CommandHSET, c.prefix+key, mb, v)
		if err != nil {
			logger.Warnning(err)
			return err
		}
	}

	err := conn.Flush()
	if err != nil {
		logger.Warnning(err)
		return err
	}

	return nil
}
func (c *Cache) HSETValue(v model.CacheHash) error {
	conn := c.pool.Get()
	defer conn.Close()

	err := conn.Send(constant.CommandHMSET, redis.Args{}.Add(c.prefix+v.GetKey()).AddFlat(v.GetValue())...)
	if err != nil {
		logger.Warnning(err)
		return err
	} else if dur, need := v.NeedExpire(); need {
		err = conn.Send(constant.CommandEXPIRE, c.prefix+v.GetKey(), dur)
		if err != nil {
			logger.Warnning(err)
			return err
		}
	}

	err = conn.Flush()
	if err != nil {
		logger.Warnning(err)
		return err
	}

	return nil
}
func (c *Cache) HMGET(key string) (interface{}, error) {
	conn := c.pool.Get()
	defer conn.Close()

	rep, err := conn.Do(constant.CommandHMGET, c.prefix+key)

	if err != nil {
		logger.Warnning(err)
		return nil, err
	}

	return rep, nil
}
func (c *Cache) HGETALL(key string) (interface{}, error) {
	conn := c.pool.Get()
	defer conn.Close()

	rep, err := conn.Do(constant.CommandHGETALL, c.prefix+key)

	if err != nil {
		logger.Warnning(err)
		return nil, err
	}

	return rep, nil
}

func (c *Cache) HVALS(key string) ([][]byte, error) {
	conn := c.pool.Get()
	defer conn.Close()

	rep, err := redis.ByteSlices(conn.Do(constant.CommandHVALS, c.prefix+key))
	if err != nil {
		_ = logger.Warnning(err)
		return nil, err
	}

	return rep, nil
}

func (c *Cache) HVALS2(key string) ([]interface{}, error) {
	conn := c.pool.Get()
	defer conn.Close()
	return redis.Values(conn.Do(constant.CommandHVALS, c.prefix+key))
}
func (c *Cache) HMDEl(dHash []model.CacheHashValue) error {
	conn := c.pool.Get()
	defer conn.Close()
	for _, v := range dHash {
		_, err := conn.Do(constant.CommandHDEL, c.prefix+v.GetKey(), v.GetField())
		if err != nil {
			logger.Warnning(err)
			return err
		}
	}
	return nil
}
func (c *Cache) HDEl(key, member string) error {
	conn := c.pool.Get()
	defer conn.Close()

	_, err := conn.Do(constant.CommandHDEL, c.prefix+key, member)
	if err != nil {
		logger.Warnning(err)
		return err
	}
	return nil
}

/*********************** Set ***********************/
func (c *Cache) ZADD(set []model.CacheSortedSet) error {
	conn := c.pool.Get()
	defer conn.Close()

	for _, v := range set {
		score := v.GetScores()
		d, err := JsonIter.Marshal(v)
		if err != nil {
			logger.Warnning(err)
			return err
		}

		err = conn.Send(constant.CommandZREMRANGEBYSCORE, c.prefix+v.GetKey(), score, score) //删除名称为key的zset中score >= min且score <= max的所有元素
		if err != nil {
			logger.Warnning(err)
			return err
		}

		err = conn.Send(constant.CommandZADD, c.prefix+v.GetKey(), score, d)
		if err != nil {
			logger.Warnning(err)
			return err
		}
	}

	err := conn.Flush()
	if err != nil {
		logger.Warnning(err)
		return err
	}

	return nil
}
func (c *Cache) ZADDOnly(set []model.CacheSortedSet) error {
	conn := c.pool.Get()
	defer conn.Close()

	for _, v := range set {
		score := v.GetScores()
		d, err := JsonIter.Marshal(v)
		if err != nil {
			logger.Warnning(err)
			return err
		}
		err = conn.Send(constant.CommandZADD, c.prefix+v.GetKey(), score, d)
		if err != nil {
			logger.Warnning(err)
			return err
		}
	}

	err := conn.Flush()
	if err != nil {
		logger.Warnning(err)
		return err
	}

	return nil
}
func (c *Cache) ZRemByScore(key string, minScore, maxScore int64) error {
	conn := c.pool.Get()
	defer conn.Close()
	err := conn.Send(constant.CommandZREMRANGEBYSCORE, c.prefix+key, minScore, maxScore) //删除名称为key的zset中score >= min且score <= max的所有元素
	if err != nil {
		logger.Warnning(err)
		return err
	}
	err = conn.Flush()
	if err != nil {
		_ = logger.Warnning(err)
		return err
	}

	return nil
}

func (c *Cache) ZRemByInterfaceScore(key string, minScore, maxScore interface{}) error {
	conn := c.pool.Get()
	defer conn.Close()
	err := conn.Send(constant.CommandZREMRANGEBYSCORE, c.prefix+key, minScore, maxScore) //删除名称为key的zset中score >= min且score <= max的所有元素
	if err != nil {
		logger.Warnning(err)
		return err
	}
	err = conn.Flush()
	if err != nil {
		_ = logger.Warnning(err)
		return err
	}

	return nil
}

func (c *Cache) ZRevRangeByScore(key string, maxScores, minScores interface{}, page, num int) ([][]byte, error) {
	conn := c.pool.Get()
	defer conn.Close()

	var (
		sortedSetDatas [][]byte
		err            error
	)

	//scores从大到小
	if page == 0 && num == 0 {
		sortedSetDatas, err = redis.ByteSlices(conn.Do(constant.CommandZREVRANGEBYSCORE, c.prefix+key, maxScores, minScores))
	} else if page > 0 && num > 0 {
		sortedSetDatas, err = redis.ByteSlices(conn.Do(constant.CommandZREVRANGEBYSCORE, c.prefix+key, maxScores, minScores, "LIMIT", (page-1)*num, num))
	} else {
		return nil, ErrInvalidParam
	}

	if err != nil {
		return nil, ErrZRevRangeByScoreFailed
	}

	return sortedSetDatas, nil
}
func (c *Cache) ZRangeByScore(key string, minScores, maxScores interface{}, page, num int) ([][]byte, error) {
	conn := c.pool.Get()
	defer conn.Close()

	var (
		sortedSetDatas [][]byte
		err            error
	)

	//scores从大到小
	if page == 0 && num == 0 {
		sortedSetDatas, err = redis.ByteSlices(conn.Do(constant.CommandZRANGEBYSCORE, c.prefix+key, minScores, maxScores))
	} else if page > 0 && num > 0 {
		sortedSetDatas, err = redis.ByteSlices(conn.Do(constant.CommandZRANGEBYSCORE, c.prefix+key, minScores, maxScores, "LIMIT", (page-1)*num, num))
	} else {
		return nil, ErrInvalidParam
	}

	if err != nil {
		return nil, ErrZRangeByScoreFailed
	}

	return sortedSetDatas, nil
}
func (c *Cache) Reload(old, new model.CacheSortedSet) error {
	conn := c.pool.Get()
	defer conn.Close()

	_, err := conn.Do(constant.CommandZREMRANGEBYSCORE, c.prefix+old.GetKey(), old.GetScores(), old.GetScores()) //删除名称为key的zset中score >= min且score <= max的所有元素
	if err != nil {
		logger.Warnning(err)
		return err
	}

	//单纯置空即可，无需reload
	if new == nil {
		return nil
	}

	//删除后新值不为空，需reload
	d, err := JsonIter.Marshal(new)
	if err != nil {
		logger.Warnning(err)
		return err
	}

	_, err = conn.Do(constant.CommandZADD, c.prefix+new.GetKey(), new.GetScores(), d)
	if err != nil {
		logger.Warnning(err)
		return err
	}

	return nil
}

// GetStringValue 获取string值
func (c *Cache) GetStringValue(key string) (string, error) {
	conn := c.pool.Get()
	defer conn.Close()
	value, err := redis.String(conn.Do("GET", c.GetPrefix()+key))
	if err != nil {
		log.Println("error:", err.Error(), key)
		return "", err
	}
	return value, nil
}

// SetStringValue 设置string值
func (c *Cache) SetStringValue(key, value string, expire int64) error {
	conn := c.pool.Get()
	defer conn.Close()
	_, err := conn.Do("SET", c.GetPrefix()+key, value)
	if err != nil {
		log.Println("error:", err.Error())
		return err
	}
	if expire > 0 {
		_, err = conn.Do("EXPIRE", c.GetPrefix()+key, expire)
		if err != nil {
			log.Println("acache EXPIRE ", c.GetPrefix()+key, " error:", err.Error())
			return err
		}
	}
	return nil
}

func (c *Cache) GetPrefix() string {
	return c.prefix
}
func (c *Cache) ExpireKey(key string, expire int64) error {
	conn := c.pool.Get()
	defer conn.Close()
	_, err := conn.Do("EXPIRE", c.GetPrefix()+key, expire)
	if err != nil {
		log.Println("acache EXPIRE ", c.GetPrefix()+key, " error:", err.Error())
		return err
	}

	return nil
}

func (c *Cache) HEXISTS(key string, field string) (interface{}, error) {
	conn := c.pool.Get()
	defer conn.Close()

	rep, err := conn.Do(constant.CommandHEXISTS, c.prefix+key, field)

	if err != nil {
		_ = logger.Warnning(err)
		return nil, err
	}

	return rep, nil
}

func (c *Cache) Lock(key string, timeout int) (bool, error) {
	conn := c.pool.Get()
	defer conn.Close()

	//logger.Debug(c.prefix+key, " lock")
	status, err := redis.String(conn.Do(constant.CommandSET, c.prefix+key, "LOCK", "EX", timeout, "NX"))
	if err == redis.ErrNil {
		// The lock was not successful, it already exists.
		//logger.Debug("---",err)
		return false, nil
	}
	if err != nil {
		_ = logger.Warnning(err)
		return false, err
	}
	return status == "OK", nil
}

func (c *Cache) UnLock(key string) error {
	conn := c.pool.Get()
	defer conn.Close()

	_, err := conn.Do(constant.CommandDEL, c.prefix+key)
	if err != nil {
		_ = logger.Warnning(err)
		return err
	}
	return nil
}

func (c *Cache) TryLock(key string, timeout int) (bool, error) {
	conn := c.pool.Get()
	defer conn.Close()

	loop := 3
	for i := 0; i < loop; i++ {
		ok, err := c.Lock(key, timeout)
		if err != nil {
			_ = logger.Warnning(err)
			return false, err
		}
		if !ok { //未获取到锁
			time.Sleep(time.Second)
			continue
		} else { //拿到锁
			return ok, nil
		}
	}
	//未获取到锁
	_ = logger.Debug(key, "未获取到锁")
	return false, nil
}
