package cache

import (
	"github.com/garyburd/redigo/redis"
	jsoniter "github.com/json-iterator/go"
	"time"
)

var (
	JsonIter = jsoniter.ConfigCompatibleWithStandardLibrary
)

type Config struct {
	Port     string `yaml:"port"`
	MaxConn  int    `yaml:"max_conn" default:"30"`
	Prefix   string `yaml:"prefix"`
	Password string `yaml:"password"`
	Db       int    `yaml:"db"`
	IP       string `yaml:"ip"`
}

type Cache struct {
	pool   *redis.Pool
	prefix string
}

func Init(config *Config) *Cache {
	cache := &Cache{
		prefix: config.Prefix,
	}
	cache.pool = &redis.Pool{
		MaxIdle:     config.MaxConn,
		MaxActive:   config.MaxConn,
		IdleTimeout: 240 * time.Second,
		Dial: func() (redis.Conn, error) {
			c, err := redis.Dial("tcp", config.IP+":"+config.Port,
				redis.DialPassword(config.Password),
				redis.DialDatabase(config.Db))
			if err != nil {
				return nil, err
			}
			return c, nil
		},
		TestOnBorrow: func(c redis.Conn, t time.Time) error {
			_, err := c.Do("PING")
			return err
		},
	}
	return cache
}
