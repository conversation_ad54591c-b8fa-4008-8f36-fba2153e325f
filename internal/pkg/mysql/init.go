package mysql

import (
	"fmt"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
	"log"
	"runtime/debug"
)

type Config struct {
	MaxConn  int    `yaml:"max_conn" default:"30"`
	TraceOn  bool   `yaml:"trace_on"`
	Ip       string `yaml:"ip"`
	Port     string `yaml:"port"`
	Database string `yaml:"database"`
	UserName string `yaml:"user_name"`
	Password string `yaml:"password"`
	LogOn    bool   `yaml:"log_on"`
}

func Init(config *Config) *gorm.DB {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4,utf8&collation=utf8mb4_general_ci&parseTime=true&loc=Asia%%2FShanghai",
		config.UserName, config.Password, config.Ip, config.Port, config.Database)
	mysqlConfig := mysql.Config{
		DSN:                       dsn,   // DSN data source name
		DefaultStringSize:         191,   // string 类型字段的默认长度
		DisableDatetimePrecision:  true,  // 禁用 datetime 精度，MySQL 5.6 之前的数据库不支持
		DontSupportRenameIndex:    true,  // 重命名索引时采用删除并新建的方式，MySQL 5.7 之前的数据库和 MariaDB 不支持重命名索引
		DontSupportRenameColumn:   true,  // 用 `change` 重命名列，MySQL 8 之前的数据库和 MariaDB 不支持重命名列
		SkipInitializeWithVersion: false, // 根据版本自动配置
	}
	db, err := gorm.Open(mysql.New(mysqlConfig), gormConfig(config.LogOn))
	if err != nil {
		log.Panic(fmt.Sprintf("Orm启动失败: %v\n堆栈信息: %v", err, string(debug.Stack())))
	} else {
		sqlDB, _ := db.DB()
		sqlDB.SetMaxIdleConns(config.MaxConn)
		sqlDB.SetMaxOpenConns(config.MaxConn)
	}
	log.Println("Orm init success")
	return db
}

// gormConfig 根据配置决定是否开启日志
func gormConfig(mod bool) *gorm.Config {
	if mod {
		return &gorm.Config{
			Logger:                                   logger.Default.LogMode(logger.Info),
			DisableForeignKeyConstraintWhenMigrating: true,
			NamingStrategy: schema.NamingStrategy{
				SingularTable: true,
			},
		}
	} else {
		return &gorm.Config{
			Logger:                                   logger.Default.LogMode(logger.Silent),
			DisableForeignKeyConstraintWhenMigrating: true,
			NamingStrategy: schema.NamingStrategy{
				SingularTable: true,
			},
		}
	}
}
