package model

import "time"

type BaseInfo struct {
	ID               int64  `json:"com_id" form:"com_id"`                       // 企业ID
	ComCode          string `json:"com_code" form:"com_code"`                   // 企业编码
	IsList           bool   `json:"is_list" form:"is_list"`                     // 是否上市
	ExchCode         string `json:"exch_code" form:"exch_code"`                 // 交易所代号
	StockNum         string `json:"stock_num" form:"stock_num"`                 // 股票编号
	StockCode        string `json:"stock_code" form:"stock_code"`               // 股票代码
	Name             string `json:"name" form:"name"`                           // 公司名称
	NameEn           string `json:"name_en" form:"name_en"`                     // 英文名称
	NameAbbr         string `json:"name_abbr" form:"name_abbr"`                 // A股简称
	Formername       string `json:"formername" form:"formername"`               // 曾用名
	NamePinyin       string `json:"name_pinyin" form:"name_pinyin"`             // 公司名称拼音
	NameAbbrPinyin   string `json:"name_abbr_pinyin" form:"name_abbr_pinyin"`   // A股简称拼音
	FormernamePinyin string `json:"formername_pinyin" form:"formername_pinyin"` // 曾用名拼音
	Industry         string `json:"industry" form:"industry"`                   // 所属东财行业
	SecurityType     string `json:"security_type" form:"security_type"`         // 证券类别
	TradeMarket      string `json:"trade_market" form:"trade_market"`           // 上市交易所
	Industrycsrc     string `json:"industrycsrc" form:"industrycsrc"`           // 所属证监会行业
	President        string `json:"president" form:"president"`                 // 总经理
	LegalPerson      string `json:"legal_person" form:"legal_person"`           // 法人代表
	Secretary        string `json:"secretary" form:"secretary"`                 // 公司秘书
	Chairman         string `json:"chairman" form:"chairman"`                   // 董事长
	SecPresent       string `json:"secpresent" form:"secpresent"`               // 董秘
	Indirectors      string `json:"indedirectors" form:"indedirectors"`         // 独立董事
	OrgTel           string `json:"org_tel" form:"org_tel"`                     // 公司电话
	OrgEmail         string `json:"org_email" form:"org_email"`                 // 公司邮箱
	OrgFax           string `json:"org_fax" form:"org_fax"`                     // 公司传真
	OrgWeb           string `json:"org_web" form:"org_web"`                     // 公司网址
	Address          string `json:"address" form:"address"`                     // 公司地址
	RegAddress       string `json:"reg_address" form:"reg_address"`             // 注册地址
	RegCapital       string `json:"reg_capital" form:"reg_capital"`             // 注册资本
	RegNum           string `json:"reg_num" form:"reg_num"`                     // 注册号
	EmpNum           int    `json:"emp_num" form:"emp_num"`                     // 员工人数
	Tatolnumber      int    `json:"tatolnumber" form:"tatolnumber"`             // 总股本
	LawFirm          string `json:"law_firm" form:"law_firm"`                   // 律师事务所
	AccountfirmName  string `json:"accountfirm_name" form:"accountfirm_name"`   // 会计师事务所
	OrgProfile       string `json:"org_profile" form:"org_profile"`             // 公司简介
	BusinessScope    string `json:"business_scope" form:"business_scope"`       // 经营范围
	ListingDate      string `json:"listing_date" form:"listing_date"`           // 上市日期
	Status           int    `json:"status" form:"status"`                       // 公司状态：10启用，-10禁用
	IsShow           int    `json:"is_show" form:"is_show"`                     // 是否展示：10展示，-10隐藏
	ListingTime      int64  `json:"listing_time" form:"listing_time"`           // 上市时间
}

type ExecuteInfo struct {
	ComID         int64   `json:"com_id" form:"com_id"`                 // 企业ID
	StockNum      string  `json:"stock_num" form:"stock_num"`           // 股票编号
	StockCode     string  `json:"stock_code" form:"stock_code"`         // 股票代码
	Age           string  `json:"age" form:"age"`                       // 年龄
	HighDegree    string  `json:"high_degree" form:"high_degree"`       // 学历
	HoldNum       int64   `json:"hold_num" form:"hold_num"`             // 持股数(股)
	IncumbentTime string  `json:"incumbent_time" form:"incumbent_time"` // 任职时间
	IsCompare     bool    `json:"is_compare" form:"is_compare"`         // 是否是董事
	PersonCode    string  `json:"person_code" form:"person_code"`       // 人物code
	PersonName    string  `json:"person_name" form:"person_name"`       // 人物名称
	Position      string  `json:"position" form:"position"`             // 职务
	PositionCode  string  `json:"position_code" form:"position_code"`   // 职务类型id
	Resume        string  `json:"resume" form:"resume"`                 // 人物简介
	Salary        float64 `json:"salary" form:"salary"`                 // 薪酬(元)
	Sex           string  `json:"sex" form:"sex"`                       // 性别
}

type BusinessInfo struct {
	ComID      int64   `json:"com_id" form:"com_id"`           // 企业ID
	StockNum   string  `json:"stock_num" form:"stock_num"`     // 股票编号
	StockCode  string  `json:"stock_code" form:"stock_code"`   // 股票代码
	Rank       string  `json:"rank" form:"rank"`               // 顺序
	ItemName   string  `json:"item_name" form:"item_name"`     // 主营构成
	MbcRatio   float64 `json:"mbc_ratio" form:"mbc_ratio"`     // 成本比例
	MbiRatio   float64 `json:"mbi_ratio" form:"mbi_ratio"`     // 收入比例
	MbrRatio   float64 `json:"mbr_ratio" form:"mbr_ratio"`     // 利润比例
	Rpofit     float64 `json:"rpofit" form:"rpofit"`           // 主营利润(元)
	Income     float64 `json:"income" form:"income"`           // 主营收入(元)
	Cost       float64 `json:"cost" form:"cost"`               // 主营成本(元)
	Ratio      float64 `json:"ratio" form:"ratio"`             // 毛利率
	MainopType int16   `json:"mainop_type" form:"mainop_type"` // 分类类别，1按行业，2按产品，3按地区
	ReportDate string  `json:"report_date" form:"report_date"` // 数据日期
	ReportTime int64   `json:"report_time" form:"report_time"` // 数据时间
}

// 展商手册订阅信息
type HandBookSubscribeInfo struct {
	ID               int64     `json:"id" gorm:"column:id"`
	ConferenceID     int64     `json:"conference_id" gorm:"column:conference_id"`
	CnConferenceName string    `json:"cn_conference_name" gorm:"column:cn_conference_name"`
	EnConferenceName string    `json:"en_conference_name_en" gorm:"column:en_conference_name"`
	Cellphone        string    `json:"cellphone" gorm:"column:cellphone"`
	Email            string    `json:"email" gorm:"column:email"`
	CreateTime       time.Time `json:"create_time" gorm:"column:create_time"`
	UpdateTime       time.Time `json:"update_time" gorm:"column:update_time"`
}

// 展商线索
type ConferenceClueInfo struct {
	Id               int64  `json:"id" gorm:"column:id"`                                 //主键id
	ConferenceId     int64  `json:"conference_id" gorm:"column:conference_id"`           //展会id
	CnConferenceName string `json:"cn_conference_name" gorm:"column:cn_conference_name"` //展会名称中文
	EnConferenceName string `json:"en_conference_name" gorm:"column:en_conference_name"` //展会名称英文
	CnShortName      string `json:"cn_short_name" gorm:"column:cn_short_name"`           //展会简称中文
	EnShortName      string `json:"en_short_name" gorm:"column:en_short_name"`           //展会简称英文
	UserId           int64  `json:"user_id" gorm:"column:user_id"`                       //用户ID
	LastName         string `json:"last_name" gorm:"column:last_name"`                   //姓
	FirstName        string `json:"first_name" gorm:"column:first_name"`                 //名
	Email            string `json:"email" gorm:"column:email"`                           //邮箱
	CellPhone        string `json:"cellphone" gorm:"column:cellphone"`                   //手机号码
	Company          string `json:"company" gorm:"column:company"`                       //公司
	JobTitle         string `json:"job_title" gorm:"column:job_title"`                   //职位
	Country          string `json:"country" gorm:"column:country"`                       //国家
	Comment          string `json:"comment" gorm:"column:comment"`                       //留言
	InterestLevel    string `json:"interest_level" gorm:"column:interest_level"`         //兴趣程度
	BoothType        string `json:"booth_type" gorm:"column:booth_type"`                 //展位类型
	ExhibitionArea   string `json:"exhibition_area" gorm:"column:exhibition_area"`       //感兴趣的展区

	SourceName string    `json:"source_name" gorm:"source_name"`        //来源
	FromId     string    `json:"from_id" gorm:"from_id"`                //渠道
	FromName   string    `json:"from_name" gorm:"from_name"`            //渠道名称
	Status     int       `json:"status" gorm:"column:status"`           //分配状态
	CreateUser string    `json:"create_user" gorm:"column:create_user"` //创建用户
	UpdateUser string    `json:"update_user" gorm:"column:update_user"` //更新用户
	CreateTime time.Time `json:"create_time" gorm:"column:create_time"` //创建时间
	UpdateTime time.Time `json:"update_time" gorm:"column:update_time"` //更新时间

	MainProducts   string `json:"main_products" form:"main_products" gorm:"column:main_products"`       // 主推产品
	EnterpriseType string `json:"enterprise_type" form:"enterprise_type" gorm:"column:enterprise_type"` // 企业类型

}

// 活动数据
type EventData struct {
	ID           int64  `json:"id" db:"id"`
	Name         string `json:"name" db:"name"`                       //活动名称
	CategoryType int64  `json:"category_type" db:"tycategory_typepe"` // 关联导航菜单（1CLNB展前会，2CLNB主论坛，3CLNB分论坛，4户外赛事，5场馆赛事，6Releasing Stage 新品发布）
}

// 展商数据
type AudienceConferenceCompanyData struct {
	Id                int64     `gorm:"column:id"`                         //主键id
	ConferenceId      int64     `gorm:"column:conference_id"`              //展会id
	ConferenceCompany string    `gorm:"column:conference_company"`         //展商名称
	BoothNumber       string    `gorm:"column:booth_number"`               //展位号
	Url               string    `gorm:"column:url"`                        //展商链接
	Logo              string    `gorm:"column:logo"`                       //logo的url
	Sort              int       `gorm:"column:sort"`                       //排序
	IsDeleted         bool      `gorm:"column:is_deleted"`                 //是否删除
	CreateUser        string    `gorm:"column:create_user"`                //创建用户
	UpdateUser        string    `gorm:"column:update_user"`                //最近更新用户
	CreateTime        time.Time `gorm:"column:create_time;autoCreateTime"` //创建时间
	UpdateTime        time.Time `gorm:"column:update_time;autoUpdateTime"` //最近更新时间
}

// 嘉宾信息
type ConferenceGuestData struct {
	Id           int64     `db:"id" json:"id"`                       //会议嘉宾等信息
	ConferenceId int64     `db:"conference_id" json:"conference_id"` //展会ID
	ColumnId     int64     `db:"column_id" json:"column_id"`         //栏目ID
	Appellation  string    `db:"appellation" json:"appellation"`     //称谓
	Position     string    `db:"position" json:"position"`           //职位
	Company      string    `db:"company" json:"company"`             //公司
	Link         string    `db:"link" json:"link"`                   //链接
	Picture      string    `db:"picture" json:"picture"`             //logo图
	Content      string    `db:"content"  json:"content"`            //嘉宾介绍(或嘉宾身份)--中文
	Type         int       `db:"type" json:"type"`                   //信息类型（1赞助商，2嘉宾,3合作商,4媒体，9日程嘉宾）
	Sorting      float32   `db:"sorting" json:"sorting"`             //排序
	Deleted      int       `db:"deleted" json:"deleted"`             //是否删除
	CreateAdmin  string    `db:"create_admin" json:"create_admin"`   //创建人
	UpdateAdmin  string    `db:"update_admin" json:"update_admin"`   //更新人
	CreateTime   time.Time `db:"create_time" json:"create_time"`     //创建时间
	UpdateTime   time.Time `db:"update_time" json:"update_time"`     //更新时间
}
