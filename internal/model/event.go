package model

import "time"

// ConferenceEventCategory 活动类别
type ConferenceEventCategory struct {
	Id           int64   `json:"id" db:"id" gorm:"id" form:"id"`
	ConferenceId int64   `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"`
	StartTime    string  `json:"start_time" db:"start_time" gorm:"start_time" form:"start_time"`                 // 开始日期
	EndTime      string  `json:"end_time" db:"end_time" gorm:"end_time" form:"end_time"`                         // 结束日期
	EntryType    string  `json:"entry_type" db:"entry_type" gorm:"entry_type" form:"entry_type"`                 // 入场类型
	CnName       string  `json:"cn_name" db:"cn_name" gorm:"cn_name" form:"cn_name"`                             // 活动分类名称-中文
	EnName       string  `json:"en_name" db:"en_name" gorm:"en_name" form:"en_name"`                             // 活动分类名称-英文
	CnPlace      string  `json:"cn_place" db:"cn_place" gorm:"cn_place" form:"cn_place"`                         // 中文地点
	EnPlace      string  `json:"en_place" db:"en_place" gorm:"en_place" form:"en_place"`                         // 英文地点
	CnTitle      string  `json:"cn_title" db:"cn_title" gorm:"cn_title" form:"cn_title"`                         // 中文标题
	EnTitle      string  `json:"en_title" db:"en_title" gorm:"en_title" form:"en_title"`                         // 英文标题
	CnContent    string  `json:"cn_content" db:"cn_content" gorm:"cn_content" form:"cn_content"`                 // 中文内容
	EnContent    string  `json:"en_content" db:"en_content" gorm:"en_content" form:"en_content"`                 // 英文内容
	Type         int     `json:"type" db:"type" gorm:"type" form:"type"`                                         // 关联导航菜单（1CLNB展前会，2CLNB主论坛，3CLNB分论坛，4户外赛事，5场馆赛事，6Releasing Stage 新品发布）
	CnButtonLink string  `json:"cn_button_link" db:"cn_button_link" gorm:"cn_button_link" form:"cn_button_link"` // 中文预约参会按钮跳转链接
	EnButtonLink string  `json:"en_button_link" db:"en_button_link" gorm:"en_button_link" form:"en_button_link"` // 英文预约参会按钮跳转链接
	Picture      string  `json:"picture" db:"picture" gorm:"picture" form:"picture"`                             // 图片
	Sorting      float32 `json:"sorting" db:"sorting" gorm:"sorting" form:"sorting"`                             // 排序
	Deleted      int64   `json:"deleted" db:"deleted" gorm:"deleted" form:"deleted"`
	CreateTime   string  `json:"create_time" db:"create_time" gorm:"create_time" form:"create_time"`
	CreateAdmin  string  `json:"create_admin" db:"create_admin" gorm:"create_admin" form:"create_admin"`
	UpdateAdmin  string  `json:"update_admin" db:"update_admin" gorm:"update_admin" form:"update_admin"`
	UpdateTime   string  `json:"update_time" db:"update_time" gorm:"update_time" form:"update_time"`
}

// TableName 表名称
func (*ConferenceEventCategory) TableName() string {
	return "conference_event_category"
}

// ConferenceEvent 活动一览表
type ConferenceEvent struct {
	Id              int64  `json:"id" db:"id" gorm:"id" form:"id"`
	ConferenceId    int64  `json:"conference_id"  form:"conference_id"`                                                               //展会id
	CnName          string `json:"cn_name" db:"cn_name" gorm:"cn_name" form:"cn_name"`                                                //展会活动名称--中文
	EnName          string `json:"en_name" db:"en_name" gorm:"en_name" form:"en_name"`                                                //展会活动名称--英文
	EventTime       string `json:"event_time" db:"event_time" gorm:"event_time" form:"event_time"`                                    //活动时间-开始日期
	CategoryType    int    `json:"category_type" db:"category_type" gorm:"category_type" form:"category_type"`                        // 所属活动分类（1CLNB展前会，2CLNB主论坛，3CLNB分论坛，4户外赛事，5场馆赛事，6Releasing Stage 新品发布）
	ScheduleIsForum string `gorm:"column:schedule_is_forum" db:"schedule_is_forum" json:"schedule_is_forum" form:"schedule_is_forum"` //日程是否有论坛（0无分论坛，1有分论坛）
	IsDisplayPage   string `gorm:"column:is_display_page" db:"is_display_page" json:"is_display_page" form:"is_display_page"`         //是否展示活动详情页（0是，1否）

	CnSponsors     string `gorm:"column:cn_sponsors" db:"cn_sponsors" json:"cn_sponsors" form:"cn_sponsors"`                     //赞助方-中文
	EnSponsors     string `gorm:"column:en_sponsors" db:"en_sponsors" json:"en_sponsors" form:"en_sponsors"`                     //赞助方-英文
	CnSponsorsLogo string `gorm:"column:cn_sponsors_logo" db:"cn_sponsors_logo" json:"cn_sponsors_logo" form:"cn_sponsors_logo"` //赞助方Logo-中文
	EnSponsorsLogo string `gorm:"column:en_sponsors_logo" db:"en_sponsors_logo" json:"en_sponsors_logo" form:"en_sponsors_logo"` //赞助方Logo-英文

	CnSponsorshipType  string `gorm:"column:cn_sponsorship_type" db:"cn_sponsorship_type" json:"cn_sponsorship_type" form:"cn_sponsorship_type"`     //赞助类型（0赞助方，1合作伙伴）
	EnSponsorshipType  string `gorm:"column:en_sponsorship_type" db:"en_sponsorship_type" json:"en_sponsorship_type" form:"en_sponsorship_type"`     //赞助类型（0赞助方，1合作伙伴）
	CnSponsors2        string `gorm:"column:cn_sponsors2" db:"cn_sponsors2" json:"cn_sponsors2" form:"cn_sponsors2"`                                 //赞助方-中文
	EnSponsors2        string `gorm:"column:en_sponsors2" db:"en_sponsors2" json:"en_sponsors2" form:"en_sponsors2"`                                 //赞助方-英文
	CnSponsorsLogo2    string `gorm:"column:cn_sponsors_logo2" db:"cn_sponsors_logo2" json:"cn_sponsors_logo2" form:"cn_sponsors_logo2"`             //赞助方Logo-中文
	EnSponsorsLogo2    string `gorm:"column:en_sponsors_logo2" db:"en_sponsors_logo2" json:"en_sponsors_logo2" form:"en_sponsors_logo2"`             //赞助方Logo-英文
	CnSponsorshipType2 string `gorm:"column:cn_sponsorship_type2" db:"cn_sponsorship_type2" json:"cn_sponsorship_type2" form:"cn_sponsorship_type2"` //赞助类型（0赞助方，1合作伙伴）
	EnSponsorshipType2 string `gorm:"column:en_sponsorship_type2" db:"en_sponsorship_type2" json:"en_sponsorship_type2" form:"en_sponsorship_type2"` //赞助类型（0赞助方，1合作伙伴）

	Sorting      string `json:"sorting" db:"sorting" gorm:"sorting" form:"sorting"`                                    //排序
	IsSubSection int    `gorm:"column:is_sub_section" db:"is_sub_section" json:"is_sub_section" form:"is_sub_section"` //是否子论坛(0不是子论坛，其他时为父级论坛ID)
	UpdateAdmin  string `json:"update_admin" gorm:"column:update_admin" db:"update_admin"`                             // 更新用户
	CreateAdmin  string `json:"create_admin" gorm:"column:create_admin" db:"create_admin"`                             // 创建用户
	UpdateTime   string `json:"update_time" gorm:"column:update_time" db:"update_time"`                                // 更新时间
	CreateTime   string `json:"create_time" gorm:"column:create_time" db:"create_time"`                                // 创建时间
	Deleted      int64  `json:"deleted" db:"deleted" gorm:"deleted" form:"deleted"`
}

// TableName 表名称
func (*ConferenceEvent) TableName() string {
	return "conference_event"
}

type EventScheduleDate struct {
	ID            int64  `json:"id" db:"id" gorm:"id" form:"id"`
	ConferenceId  int64  `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"`         //展会ID
	EventId       int64  `json:"event_id" db:"event_id" gorm:"event_id" form:"event_id"`                             //活动ID
	CnDayName     string `json:"cn_day_name" db:"cn_day_name" gorm:"cn_day_name" form:"cn_day_name"`                 //日期展示名称
	EnDayName     string `json:"en_day_name" db:"en_day_name" gorm:"en_day_name" form:"en_day_name"`                 //日期展示名称
	Date          string `json:"date" db:"date" gorm:"date" form:"date"`                                             //日期值
	Time          string `json:"time" db:"time" gorm:"time" form:"time"`                                             //时间值
	StartDateTime string `json:"start_date_time" db:"start_date_time" gorm:"start_date_time" form:"start_date_time"` //日期时间值
	EndDateTime   string `json:"end_date_time" db:"end_date_time" gorm:"end_date_time" form:"end_date_time"`         //日期时间值
	Sorting       string `json:"sorting" db:"sorting" gorm:"sorting" form:"sorting"`                                 //排序
	UpdateAdmin   string `json:"update_admin" gorm:"column:update_admin" db:"update_admin"`                          // 更新用户
	CreateAdmin   string `json:"create_admin" gorm:"column:create_admin" db:"create_admin"`                          // 创建用户
	UpdateTime    string `json:"update_time" gorm:"column:update_time" db:"update_time"`                             // 更新时间
	CreateTime    string `json:"create_time" gorm:"column:create_time" db:"create_time"`                             // 创建时间
	Deleted       int64  `json:"deleted" db:"deleted" gorm:"deleted" form:"deleted"`
}

// TableName 表名称
func (*EventScheduleDate) TableName() string {
	return "event_schedule_date"
}

// EventScheduleForum 展会分论坛信息
type EventScheduleForum struct {
	ID           int64     `json:"id" db:"id" gorm:"id" form:"id"`
	ConferenceId int64     `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"` //展会ID
	EventId      int64     `json:"event_id" db:"event_id" gorm:"event_id" form:"event_id"`                     //活动ID
	DayId        int64     `json:"day_id" db:"day_id" gorm:"day_id" form:"day_id"`                             //日期ID
	CnName       string    `json:"cn_name" db:"cn_name" gorm:"cn_name" form:"cn_name"`                         //分论坛名称-中文
	EnName       string    `json:"en_name" db:"en_name" gorm:"en_name" form:"en_name"`                         //分论坛名称-英文
	Sorting      float32   `json:"sorting" db:"sorting" gorm:"sorting" form:"sorting"`                         //排序
	Deleted      int       `json:"deleted" db:"deleted" gorm:"deleted" form:"deleted"`
	CreateTime   time.Time `json:"create_time" db:"create_time" gorm:"create_time" form:"create_time"`
	UpdateTime   time.Time `json:"update_time" db:"update_time" gorm:"update_time" form:"update_time"`
	CreateAdmin  string    `json:"create_admin" db:"create_admin" gorm:"create_admin" form:"create_admin"`
	UpdateAdmin  string    `json:"update_admin" db:"update_admin" gorm:"update_admin" form:"update_admin"`
}

// TableName 表名称
func (*EventScheduleForum) TableName() string {
	return "event_schedule_forum"
}

// EventSchedule 会展日程安排
type EventSchedule struct {
	ID            int64     `json:"id" db:"id" gorm:"id" form:"id"`
	ConferenceId  int64     `json:"conference_id"  form:"conference_id"`                    //展会id
	EventId       int64     `json:"event_id"  form:"event_id"`                              //活动id
	DayId         int64     `json:"day_id" db:"day_id" gorm:"day_id" form:"day_id"`         //属于哪一天
	ForumId       int64     `json:"forum_id" db:"forum_id" gorm:"forum_id" form:"forum_id"` //分论坛ID
	CnTitle       string    `json:"cn_title" db:"cn_title" gorm:"cn_title" form:"cn_title"` //展会日程名称--中文
	EnTitle       string    `json:"en_title" db:"en_title" gorm:"en_title" form:"en_title"` //展会日程名称--英文
	ScheduleStart string    `json:"schedule_start" db:"schedule_start" gorm:"schedule_start" form:"schedule_start"`
	ScheduleEnd   string    `json:"schedule_end" db:"schedule_end" gorm:"schedule_end" form:"schedule_end"`
	CnContent     string    `json:"cn_content" db:"cn_content" gorm:"cn_content" form:"cn_content"` // 会议日程描述--中文
	EnContent     string    `json:"en_content" db:"en_content" gorm:"en_content" form:"en_content"` // 会议日程描述--英文
	IsAgenda      int64     `json:"is_agenda" db:"is_agenda" gorm:"is_agenda" form:"is_agenda"`     //0议程、1餐食、2咖啡、3鸡尾酒
	Sorting       float32   `json:"sorting" db:"sorting" gorm:"sorting" form:"sorting"`
	Deleted       int64     `json:"deleted" db:"deleted" gorm:"deleted" form:"deleted"`
	CreateTime    time.Time `json:"create_time" db:"create_time" gorm:"create_time" form:"create_time"`
	UpdateTime    time.Time `json:"update_time" db:"update_time" gorm:"update_time" form:"update_time"`
	CreateAdmin   string    `json:"create_admin" db:"create_admin" gorm:"create_admin" form:"create_admin"`
	UpdateAdmin   string    `json:"update_admin" db:"update_admin" gorm:"update_admin" form:"update_admin"`
}

// TableName 表名称
func (*EventSchedule) TableName() string {
	return "event_schedule"
}

// EventScheduleGuest 活动一览表—日程嘉宾关联表
type EventScheduleGuest struct {
	ID            int64     `json:"id" gorm:"id"`
	ConferenceId  int64     `json:"conference_id"  form:"conference_id"`  //展会id
	EventId       int64     `json:"event_id" form:"event_id"`             //活动ID
	ScheduleId    int64     `json:"schedule_id" gorm:"schedule_id"`       //日程ID
	Picture       string    `json:"picture" gorm:"picture"`               //图片
	CnCompany     string    `json:"cn_company" form:"cn_company"`         //公司
	EnCompany     string    `json:"en_company" form:"en_company"`         //公司
	CnPosition    string    `json:"cn_position" form:"cn_position"`       //职位
	EnPosition    string    `json:"en_position" form:"en_position"`       //职位
	CnAppellation string    `json:"cn_appellation" form:"cn_appellation"` //称谓
	EnAppellation string    `json:"en_appellation" form:"en_appellation"` //称谓
	GuestIdentity string    `json:"guest_identity" gorm:"guest_identity"` //嘉宾身份
	Sorting       string    `json:"sorting" gorm:"sorting"`               //排序
	Deleted       string    `json:"deleted" gorm:"deleted"`
	CreateTime    time.Time `json:"create_time" gorm:"create_time"`
	CreateAdmin   string    `json:"create_admin" gorm:"create_admin"`
	UpdateTime    time.Time `json:"update_time" gorm:"update_time"`
	UpdateAdmin   string    `json:"update_admin" gorm:"update_admin"`
}

// TableName 表名称
func (*EventScheduleGuest) TableName() string {
	return "event_schedule_guest"
}

// ForumCategoryConfig 活动类别关联导航菜单
type ForumCategoryConfig struct {
	ID   int64  `json:"id" gorm:"id"`
	Name string `json:"name" gorm:"name"` //名称
}

// TableName 表名称
func (*ForumCategoryConfig) TableName() string {
	return "forum_category_config"
}

// EventExpertInfo
type EventExpertInfo struct {
	ID      int64  `json:"id" gorm:"id"`
	Company string `json:"company" gorm:"company"` //公司
}

// AnnualSelection
type AnnualSelection struct {
	ID                int64  `json:"id" gorm:"id"`
	ConferenceId      int64  `json:"conference_id"  gorm:"conference_id"`            //展会id
	ApplicationLink   string `json:"application_link"    gorm:"application_link"`    //参评报名入口链接
	CompaniesLink     string `json:"companies_link"      gorm:"companies_link"`      //参评企业入口链接
	JudgesScoringLink string `json:"judges_scoring_link" gorm:"judges_scoring_link"` //评委打分入口链接
	Process           string `json:"process" gorm:"process"`                         //评选流程
	Introduction      string `json:"introduction" gorm:"introduction"`               //评选介绍
}
