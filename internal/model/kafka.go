package model

type KfkCompanyData struct {
	BaseInfo    BaseInfo       `form:"base_info" json:"base_info" binding:"required,dive"` // 基本信息
	ExecutiveLi []ExecuteInfo  `form:"executive_li" json:"executive_li"`                   // 高管信息
	BusinessLi  []BusinessInfo `form:"business_li" json:"business_li"`                     // 主营构成
	OpUser      string         `form:"op_user" json:"op_user"`
}

type KfkHandBookData struct {
	SubscribeId      int64  `json:"subscribe_id"`       // 订阅ID
	ConferenceId     int64  `json:"conference_id"`      // 展会ID
	CnConferenceName string `json:"cn_conference_name"` // 展会名称中文
	EnConferenceName string `json:"en_conference_name"` // 展会名称英文
	HandBookId       int64  `json:"hand_book_id"`       // 展商手册ID
	CnHandBookName   string `json:"cn_hand_book_name"`  // 展商手册中文
	EnHandBookName   string `json:"en_hand_book_name"`  // 展商手册英文
	CnHandBookUrl    string `json:"cn_hand_book_url"`   // 展商手册中文URl
	EnHandBookUrl    string `json:"en_hand_book_url"`   // 展商手册英文URl
	Cellphone        string `json:"cellphone"`          // 手机号
	Email            string `json:"email"`              // 邮箱
}
