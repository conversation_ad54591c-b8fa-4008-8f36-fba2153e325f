package model

type CacheSortedSet interface {
	GetKey() string
	GetScores() int64
}

type CacheHash interface {
	GetKey() string
	GetValue() interface{}
	NeedExpire() (int64, bool)
}

type CacheHashValue interface {
	GetKey() string
	GetField() string
	GetValue() interface{}
	NeedExpire() (int64, bool)
}

type CacheSortedSetWithValidCheck interface {
	GetKey() string
	GetScores() int64
	IsValid() bool
}

type CacheSortedSetWithValue interface {
	GetKey() string
	GetScores() int64
	GetValue() interface{}
}

/**
 * 需要根据key 进行删除操作才需要
 */
type CacheSortSetUnique interface {
	GetKey() string
	GetMembers() []CacheSortedSet
	NeedExpire() (int64, bool)
}

type CacheSortedSetWithUniqueKey interface {
	GetKey() string
	GetScores() int64
	GetUniqueKey() []string  //数据去重列
	GetUniqueKeyPre() string //去重列前缀，在嵌套数据中提供，以 1.2.3 的顺序，1，2，3为嵌套层次的名称，不支持数组嵌套
}

type CacheKeyValue interface {
	GetKey() string
	GetValue() interface{}
	NeedExpire() (int64, bool)
}

type CacheSetValue interface {
	GetKey() string
	GetMembers() []CacheKeyValue
	NeedExpire() (int64, bool)
}
