package model

import "time"

// ConferenceIntroduction 展会首页-会议介绍和视频
type ConferenceIntroduction struct {
	ID                int64     `json:"id" db:"id" gorm:"id" form:"id"`                                                                 // 大会介绍-页面内容-英文
	IntroductionVideo string    `json:"introduction_video" db:"introduction_video" gorm:"introduction_video" form:"introduction_video"` // 介绍视频
	VideoCover        string    `json:"video_cover" db:"video_cover" gorm:"video_cover" form:"video_cover"`                             // 视频封面
	ConferenceId      int64     `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"`                     // 关联展会ID
	Deleted           int64     `json:"deleted" db:"deleted" gorm:"deleted" form:"deleted"`
	CreateTime        time.Time `json:"create_time" db:"create_time" gorm:"create_time" form:"create_time"`
	CreateUser        string    `json:"create_user" db:"create_user" gorm:"create_user" form:"create_user"`
	UpdateTime        time.Time `json:"update_time" db:"update_time" gorm:"update_time" form:"update_time"`
	UpdateUser        string    `json:"update_user" db:"update_user" gorm:"update_user" form:"update_user"`
}

// TableName 表名称
func (*ConferenceIntroduction) TableName() string {
	return "conference_introduction"
}

// ConferenceData 展会首页-大会数据
type ConferenceData struct {
	ID           int64     `json:"id" db:"id" gorm:"id" form:"id"`
	ConferenceId int64     `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"`
	CnDataName   string    `json:"cn_data_name" db:"cn_data_name" gorm:"cn_data_name" form:"cn_data_name"`     //指标名称-中文
	CnDataValue  string    `json:"cn_data_value" db:"cn_data_value" gorm:"cn_data_value" form:"cn_data_value"` //指标数据-中文
	EnDataName   string    `json:"en_data_name" db:"en_data_name" gorm:"en_data_name" form:"en_data_name"`     //指标名称-英文
	EnDataValue  string    `json:"en_data_value" db:"en_data_value" gorm:"en_data_value" form:"en_data_value"` //指标数据-英文
	DataImage    string    `json:"data_image" db:"data_image" gorm:"data_image" form:"data_image"`             ///图标图片
	Sorting      float32   `json:"sorting" db:"sorting" gorm:"sorting" form:"sorting"`                         //排序
	Deleted      int       `json:"deleted" db:"deleted" gorm:"deleted" form:"deleted"`
	CreateTime   time.Time `json:"create_time" db:"create_time" gorm:"create_time" form:"create_time"`
	UpdateTime   time.Time `json:"update_time" db:"update_time" gorm:"update_time" form:"update_time"`
	CreateAdmin  string    `json:"create_admin" db:"create_admin" gorm:"create_admin" form:"create_admin"`
	UpdateAdmin  string    `json:"update_admin" db:"update_admin" gorm:"update_admin" form:"update_admin"`
}

// TableName 表名称
func (*ConferenceData) TableName() string {
	return "conference_data"
}

// ConferenceOrganization 展会首页—组织架构
type ConferenceOrganization struct {
	ID           int64     `json:"id" db:"id" gorm:"id" form:"id"`
	ConferenceId int64     `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"` //展会ID
	CnName       string    `json:"cn_name" db:"cn_name" gorm:"cn_name" form:"cn_name"`                         // 单位名称/二维码名称-中文
	EnName       string    `json:"en_name" db:"en_name" gorm:"en_name" form:"en_name"`                         // 单位名称/二维码名称-英文
	Logo         string    `json:"logo" db:"logo" gorm:"logo" form:"logo"`                                     // 二维码链接/现场照片
	Sorting      string    `json:"sorting" db:"sorting" gorm:"sorting" form:"sorting"`                         // 排序
	Type         string    `json:"type" db:"type" gorm:"type" form:"type"`                                     // 数据类型（1主办单位，2承办单位，3二维码信息，4现场图片）
	Deleted      int       `json:"deleted" db:"deleted" gorm:"deleted" form:"deleted"`
	CreateTime   time.Time `json:"create_time" db:"create_time" gorm:"create_time" form:"create_time"`
	CreateAdmin  string    `json:"create_admin" db:"create_admin" gorm:"create_admin" form:"create_admin"`
	UpdateTime   time.Time `json:"update_time" db:"update_time" gorm:"update_time" form:"update_time"`
	UpdateAdmin  string    `json:"update_admin" db:"update_admin" gorm:"update_admin" form:"update_admin"`
}

// TableName 表名称
func (*ConferenceOrganization) TableName() string {
	return "conference_organization"
}

// ContactInformation 联系信息
type ContactInformation struct {
	ID            int64     `json:"id" gorm:"id"`
	ConferenceId  int64     `json:"conference_id" gorm:"conference_id"`
	CnTitle       string    `json:"cn_title" gorm:"cn_title"`               //标题-中文
	EnTitle       string    `json:"en_title" gorm:"en_title"`               //标题-英文
	CnContent     string    `json:"cn_content" gorm:"cn_content"`           //中文内容
	EnContent     string    `json:"en_content" gorm:"en_content"`           //英文内容
	CnIsDisplayed int       `json:"cn_is_displayed" gorm:"cn_is_displayed"` //中文其他页底是否显示（0  否 1  是）
	EnIsDisplayed int       `json:"en_is_displayed" gorm:"en_is_displayed"` //英文其他页底是否显示（0  否 1  是）
	Sorting       float32   `json:"sorting" gorm:"sorting"`                 //排序
	Deleted       int64     `json:"deleted" gorm:"deleted"`
	CreateTime    time.Time `json:"create_time" gorm:"create_time"`
	UpdateTime    time.Time `json:"update_time" gorm:"update_time"`
	CreateAdmin   string    `json:"create_admin" gorm:"create_admin"`
	UpdateAdmin   string    `json:"update_admin" gorm:"update_admin"`
}

// TableName 表名称
func (*ContactInformation) TableName() string {
	return "contact_information"
}

// ConferenceBottomPage 展会首页—组织架构-页面底部二维码
type ConferenceBottomPage struct {
	ID           int64     `json:"id" db:"id" gorm:"id" form:"id"`
	ConferenceId int64     `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"` //展会ID
	CnName       string    `json:"cn_name" db:"cn_name" gorm:"cn_name" form:"cn_name"`                         // 单位名称/二维码名称-中文
	EnName       string    `json:"en_name" db:"en_name" gorm:"en_name" form:"en_name"`                         // 单位名称/二维码名称-英文
	CnLogo       string    `json:"cn_logo" db:"cn_logo" gorm:"cn_logo" form:"cn_logo"`                         // 二维码链接-中文
	EnLogo       string    `json:"en_logo" db:"en_logo" gorm:"en_logo" form:"en_logo"`                         // 二维码链接-英文
	CnSorting    string    `json:"cn_sorting" db:"cn_sorting" gorm:"cn_sorting" form:"cn_sorting"`             // 排序-中文
	EnSorting    string    `json:"en_sorting" db:"en_sorting" gorm:"en_sorting" form:"en_sorting"`             // 排序-英文
	Deleted      int       `json:"deleted" db:"deleted" gorm:"deleted" form:"deleted"`
	CreateTime   time.Time `json:"create_time" db:"create_time" gorm:"create_time" form:"create_time"`
	CreateAdmin  string    `json:"create_admin" db:"create_admin" gorm:"create_admin" form:"create_admin"`
	UpdateTime   time.Time `json:"update_time" db:"update_time" gorm:"update_time" form:"update_time"`
	UpdateAdmin  string    `json:"update_admin" db:"update_admin" gorm:"update_admin" form:"update_admin"`
}

// TableName 表名称
func (*ConferenceBottomPage) TableName() string {
	return "conference_bottom_page"
}
