package model

import (
	"time"

	"gorm.io/gorm"
)

type CommonModel struct {
	Id         int64     `gorm:"column:id" json:"id"`                                 //主键id
	CreateUser string    `gorm:"column:create_user" json:"createUser"`                //创建用户
	UpdateUser string    `gorm:"column:update_user" json:"updateUser"`                //最近更新用户
	CreateTime time.Time `gorm:"column:create_time;autoCreateTime" json:"createTime"` //创建时间
	UpdateTime time.Time `gorm:"column:update_time;autoUpdateTime" json:"updateTime"` //最近更新时间
	IsDeleted  bool      `gorm:"column:is_deleted" json:"isDeleted"`                  //是否删除
}

type AdminCommonModel struct {
	Id          int64     `gorm:"column:id;primaryKey" json:"id"`                                 //主键id
	CreateAdmin string    `gorm:"column:create_admin" json:"create_admin"`                        //创建用户
	UpdateAdmin string    `gorm:"column:update_admin" json:"update_admin"`                        //最近更新用户
	CreateTime  time.Time `gorm:"column:create_time;autoCreateTime;<-:false" json:"create_time" ` //创建时间
	UpdateTime  time.Time `gorm:"column:update_time;autoUpdateTime;<-:false" json:"update_time"`  //最近更新时间
	Deleted     int64     `json:"deleted" gorm:"deleted" form:"deleted"`
}

type OperationLog struct {
	ID         int    `gorm:"column:id;primaryKey;autoIncrement;not null" json:"id" db:"id"`   // 操作日志ID
	Operator   string `gorm:"column:operator;not null" json:"operator" db:"operator"`          // 操作者
	AppName    string `gorm:"column:app_name;not null" json:"app_name" db:"app_name"`          // 应用名称
	ModName    string `gorm:"column:mod_name;not null" json:"mod_name" db:"mod_name"`          // 模块名称
	OpType     string `gorm:"column:op_type;not null" json:"op_type" db:"op_type"`             // 操作类型
	Content    string `gorm:"column:content;not null" json:"content" db:"content"`             // 内容
	IP         string `gorm:"column:ip;not null" json:"ip" db:"ip"`                            // IP
	CreateTime int64  `gorm:"column:create_time;not null" json:"create_time" db:"create_time"` // 操作时间
}

// TableName 操作日志表名
func (OperationLog) TableName() string {
	return "operation_log"
}

// 展会信息
type Conference struct {
	ID              int64     `gorm:"column:id;primaryKey;autoIncrement;not null" json:"id" db:"id"`            // 展会ID
	CnName          string    `gorm:"column:cn_name" json:"cn_name" db:"cn_name"`                               // 展会名称中文
	EnName          string    `gorm:"column:en_name" json:"en_name" db:"en_name"`                               // 展会名称英文
	CnShortName     string    `gorm:"column:cn_short_name" json:"cn_short_name" db:"cn_short_name"`             // 展会简称中文
	EnShortName     string    `gorm:"column:en_short_name" json:"en_short_name" db:"en_short_name"`             // 展会简称英文
	CnLocation      string    `gorm:"column:cn_location" json:"cn_location" db:"cn_location"`                   // 地点中文
	EnLocation      string    `gorm:"column:en_location" json:"en_location" db:"en_location"`                   // 地点英文
	StartTime       time.Time `gorm:"column:start_time" json:"start_time" db:"start_time"`                      // 开始时间
	EndTime         time.Time `gorm:"column:end_time" json:"end_time" db:"end_time"`                            // 结束时间
	Status          int       `gorm:"column:status" json:"status" db:"status"`                                  // 展会状态:1正常;2下线;3删除
	TemplateType    int       `gorm:"column:template_type" json:"template_type" db:"template_type"`             // 展会日程模版类型:1时间&主题;0议程&非议程
	CnTopLeftLogo   string    `gorm:"column:cn_top_left_logo" json:"cn_top_left_logo" db:"cn_top_left_logo"`    // 左上角logo
	EnTopLeftLogo   string    `gorm:"column:en_top_left_logo" json:"en_top_left_logo" db:"en_top_left_logo"`    // 左上角logo
	TopLogo         string    `gorm:"column:top_logo" json:"top_logo" db:"top_logo"`                            // 页顶logo
	TinyLogo        string    `gorm:"column:tiny_logo" json:"tiny_logo" db:"tiny_logo"`                         // 小logo
	VideoBack       string    `gorm:"column:video_back" json:"video_back" db:"video_back"`                      // 视频背景
	PcBack          string    `gorm:"column:pc_back" json:"pc_back" db:"pc_back"`                               // PC背景图
	H5Back          string    `gorm:"column:h5_back" json:"h5_back" db:"h5_back"`                               // H5背景图
	CnShareGraph    string    `gorm:"column:cn_share_graph" json:"cn_share_graph" db:"cn_share_graph"`          // 分享图链接中文
	EnShareGraph    string    `gorm:"column:en_share_graph" json:"en_share_graph" db:"en_share_graph"`          // 分享图链接英文
	CnShareTitle    string    `gorm:"column:cn_share_title" json:"cn_share_title" db:"cn_share_title"`          // 分享标题中文
	EnShareTitle    string    `gorm:"column:en_share_title" json:"en_share_title" db:"en_share_title"`          // 分享标题英文
	CnShareContent  string    `gorm:"column:cn_share_content" json:"cn_share_content" db:"cn_share_content"`    // 分享内容中文
	EnShareContent  string    `gorm:"column:en_share_content" json:"en_share_content" db:"en_share_content"`    // 分享内容英文
	MeetingSysId    string    `gorm:"column:meeting_sys_id"  json:"meeting_sys_id" db:"meeting_sys_id"`         // 同步会议系统编号
	QwxUrl          string    `gorm:"column:qwx_url" json:"qwx_url" form:"qwx_url" db:"qwx_url"`                // 企业微信通知地址
	CreateUser      string    `gorm:"column:create_user" json:"create_user" db:"create_user"`                   // 创建人
	UpdateUser      string    `gorm:"column:update_user" json:"update_user" db:"update_user"`                   // 更新人
	CreateTime      time.Time `gorm:"column:create_time" json:"create_time" db:"create_time"`                   // 创建时间
	UpdateTime      time.Time `gorm:"column:update_time" json:"update_time" db:"update_time"`                   // 更新时间
	BdVidToken      string    `gorm:"column:bd_vid_token" json:"bd_vid_token" db:"bd_vid_token"`                // 百度渠道追踪token
	WorkWeChatChain string    `gorm:"column:work_wechat_chain" json:"work_wechat_chain" db:"work_wechat_chain"` // 企业微信链接
	AutomaticMail   string    `gorm:"column:automatic_mail" json:"automatic_mail" db:"automatic_mail"`          // 自动邮件
}

func (Conference) TableName() string {
	return "conference"
}

// ConferenceShortnameMap 简称与ID映射关系
type ConferenceShortNameMap struct {
	ID           int64     `json:"id" db:"id" gorm:"id"`
	ShortName    string    `json:"short_name" db:"short_name" gorm:"short_name"`          // 展会简称(域名)
	ConferenceId int64     `json:"conference_id" db:"conference_id" gorm:"conference_id"` // 展会ID
	CreateUser   string    `json:"create_user" db:"create_user" gorm:"create_user"`       // 创建人
	UpdateUser   string    `json:"update_user" db:"update_user" gorm:"update_user"`       // 更新人
	CreateTime   time.Time `json:"create_time" db:"create_time" gorm:"create_time"`       // 创建时间
	UpdateTime   time.Time `json:"update_time" db:"update_time" gorm:"update_time"`       // 更新时间
}

// TableName 表名称
func (*ConferenceShortNameMap) TableName() string {
	return "conference_shortname_map"
}

// 往届展会
type ConferencePrevious struct {
	ID           int64     `gorm:"column:id;primaryKey;autoIncrement;not null" json:"id" db:"id"`                             // 主键ID
	ConferenceID int64     `gorm:"column:conference_id" json:"conference_id" db:"conference_id"`                              // 关联展会ID
	CnName       string    `gorm:"column:cn_name;size:100;not null;default:''" json:"cn_name" db:"cn_name"`                   // 名称中文
	EnName       string    `gorm:"column:en_name;size:100;not null;default:''" json:"en_name" db:"en_name"`                   // 名称英文
	CnURL        string    `gorm:"column:cn_url" json:"cn_url" db:"cn_url"`                                                   // 链接中文
	EnURL        string    `gorm:"column:en_url" json:"en_url" db:"en_url"`                                                   // 链接英文
	CnPdf        string    `gorm:"column:cn_pdf" json:"cn_pdf" db:"cn_pdf"`                                                   // PDF中文
	EnPdf        string    `gorm:"column:en_pdf" json:"en_pdf" db:"en_pdf"`                                                   // PDF英文
	Sort         float32   `gorm:"column:sort" json:"sort" db:"sort"`                                                         // 排序
	CreateUser   string    `gorm:"column:create_user;size:100;not null;default:''" json:"create_user" db:"create_user"`       // 创建人
	UpdateUser   string    `gorm:"column:update_user;size:100;not null;default:''" json:"update_user" db:"update_user"`       // 更新人
	CreateTime   time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP" json:"create_time" db:"create_time"` // 创建时间
	UpdateTime   time.Time `gorm:"column:update_time;not null;default:CURRENT_TIMESTAMP" json:"update_time" db:"update_time"` // 更新时间
}

func (ConferencePrevious) TableName() string {
	return "conference_previous"
}

// 展商手册订阅
type CompanyHandbookSubscribe struct {
	ID               int64     `gorm:"column:id;primaryKey;autoIncrement;not null" json:"id" db:"id"`
	ConferenceID     int64     `gorm:"column:conference_id" json:"conference_id" db:"conference_id"`                                                          // 展会id // 关联展会ID
	Cellphone        string    `gorm:"column:cellphone;size:100;not null;default:''" json:"cellphone" db:"cellphone"`                                         // 手机号
	Email            string    `gorm:"column:email;size:255;not null;default:''" json:"email" db:"email"`                                                     // 邮箱
	CreateTime       time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP" json:"create_time" db:"create_time"`                             // 创建时间
	UpdateTime       time.Time `gorm:"column:update_time;not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP" json:"update_time" db:"update_time"` // 更新时间
	CnConferenceName string    `gorm:"column:cn_conference_name" json:"cn_conference_name" db:"cn_conference_name"`                                           // 展会名称中文
	EnConferenceName string    `gorm:"column:en_conference_name" json:"en_conference_name" db:"en_conference_name"`                                           // 展会名称英文
}

func (CompanyHandbookSubscribe) TableName() string {
	return "company_handbook_subscribe"
}

// 封面按钮
type ConferenceCoverButton struct {
	ID           int64     `gorm:"column:id;primaryKey;autoIncrement;not null" json:"id" db:"id"`
	ConferenceID int64     `gorm:"column:conference_id" json:"conference_id" db:"conference_id" `                    //展会ID
	CnName       string    `gorm:"column:cn_name" json:"cn_name" db:"cn_name"`                                       //按钮名称中文
	EnName       string    `gorm:"column:en_name" json:"en_name" db:"en_name"`                                       //按钮名称英文
	CnURL        string    `gorm:"column:cn_url" json:"cn_url" db:"cn_url"`                                          //跳转链接中文
	EnURL        string    `gorm:"column:en_url" json:"en_url" db:"en_url"`                                          //跳转链接英文
	CreateUser   string    `gorm:"column:create_user;default:''" json:"create_user" db:"create_user"`                //创建人
	UpdateUser   string    `gorm:"column:update_user;default:''" json:"update_user" db:"update_user"`                //更新人
	CreateTime   time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP" json:"create_time" db:"create_time"` //创建时间
	UpdateTime   time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP" json:"update_time" db:"update_time"` //更新时间
}

func (ConferenceCoverButton) TableName() string {
	return "conference_cover_button"
}

// 展商
type ConferenceCompany struct {
	CommonModel
	ConferenceId      int64  `gorm:"column:conference_id"`       //展会id
	CnUrl             string `gorm:"column:cn_url"`              //中文在线登记表单链接
	EnUrl             string `gorm:"column:en_url"`              //英文在线登记表单链接
	CnContent         string `gorm:"column:cn_content"`          //中文展商在线登记内容
	EnContent         string `gorm:"column:en_content"`          //英文展商在线登记内容
	CnApplyConference string `gorm:"column:cn_apply_conference"` //中文申请参展
	EnApplyConference string `gorm:"column:en_apply_conference"` //英文申请参展
	CnSetUp           string `gorm:"column:cn_set_up"`           //中文现场搭建
	EnSetUp           string `gorm:"column:en_set_up"`           //英文现场搭建
	CnInvitation      string `gorm:"column:cn_invitation"`       //中文观展邀请
	EnInvitation      string `gorm:"column:en_invitation"`       //英文观展邀请
	CnHandbookTitle   string `gorm:"column:cn_handbook_title"`   //展商手册中文标题
	EnHandbookTitle   string `gorm:"column:en_handbook_title"`   //展商手册英文标题
	CnHandbookContent string `gorm:"column:cn_handbook_content"` //展商手册中文页面内容
	EnHandbookContent string `gorm:"column:en_handbook_content"` //展商手册英文页面内容
	ExhibitionQrCode  string `gorm:"column:exhibition_qr_code"`  //申请参展成功二维码
	ExhibitionTips    string `gorm:"column:exhibition_tips"`     //申请参展成功文案提示语

}

// 关于我们
type AboutUs struct {
	Id           int64     `gorm:"column:id"`                         //主键id
	ConferenceId int64     `gorm:"column:conference_id"`              //展商id
	CnAboutUs    string    `gorm:"column:cn_about_us"`                //关于我们中文
	EnAboutUs    string    `gorm:"column:en_about_us"`                //关于我们英文
	CreateUser   string    `gorm:"column:create_user"`                //创建用户
	UpdateUser   string    `gorm:"column:update_user"`                //最近更新用户
	CreateTime   time.Time `gorm:"column:create_time;autoCreateTime"` //创建时间
	UpdateTime   time.Time `gorm:"column:update_time;autoUpdateTime"` //最近更新时间
}

// 展商管理
type AudienceConferenceCompany struct {
	CommonModel
	ConferenceId            int64   `gorm:"column:conference_id"`                //展会id
	CnConferenceCompany     string  `gorm:"column:cn_conference_company"`        //中文展商名称
	EnConferenceCompany     string  `gorm:"column:en_conference_company"`        //英文展商名称
	CnBoothNumber           string  `gorm:"column:cn_booth_number"`              //中文展位号
	EnBoothNumber           string  `gorm:"column:en_booth_number"`              //英文展位号
	CnUrl                   string  `gorm:"column:cn_url"`                       //中文展商链接
	EnUrl                   string  `gorm:"column:en_url"`                       //英文展商链接
	Logo                    string  `gorm:"column:logo"`                         //logo的url
	CnExhibitorVideoLink    string  `gorm:"column:cn_exhibitor_video_link"`      //中文展商视频
	CnExhibitorVideoCover   string  `gorm:"column:cn_exhibitor_video_cover"`     //中文展商视频封面
	CnExhibitorNewsID       int64   `gorm:"column:cn_exhibitor_news_id"`         //中文展商新闻id
	CnExhibitorIntroduction string  `gorm:"column:cn_exhibitor_introduction"`    //中文展商介绍
	CnExhibitorVenueId      int     `gorm:"column:cn_exhibitor_venue_id"`        //展馆id
	Sort                    float32 `gorm:"column:sort"`                         //排序
	Year                    string  `json:"year" form:"year" gorm:"column:year"` // 年度
}

type Industry struct {
	ID           int64  `json:"id"`
	IndustryName string `json:"industry_name"`
}

// 展商管理
type AudienceConferenceCompanyDto struct {
	CommonModel
	ConferenceId            int64  `gorm:"column:conference_id"`                                    //展会id
	CnConferenceCompany     string `gorm:"column:cn_conference_company"`                            //中文展商名称
	EnConferenceCompany     string `gorm:"column:en_conference_company"`                            //英文展商名称
	CnBoothNumber           string `gorm:"column:cn_booth_number"`                                  //中文展位号
	EnBoothNumber           string `gorm:"column:en_booth_number"`                                  //英文展位号
	CnUrl                   string `gorm:"column:cn_url"`                                           //中文展商链接
	EnUrl                   string `gorm:"column:en_url"`                                           //英文展商链接
	Logo                    string `gorm:"column:logo"`                                             //logo的url
	CnExhibitorVideoLink    string `gorm:"column:cn_exhibitor_video_link"`                          //中文展商视频
	CnExhibitorVideoCover   string `gorm:"column:cn_exhibitor_video_cover"`                         //中文展商视频封面
	CnExhibitorNewsID       string `gorm:"column:cn_exhibitor_news_id"`                             //中文展商新闻id
	CnExhibitorIntroduction string `gorm:"column:cn_exhibitor_introduction"`                        //中文展商介绍
	CnExhibitorVenueId      int    `gorm:"column:cn_exhibitor_venue_id"`                            //展馆id
	Sort                    int    `gorm:"column:sort"`                                             //排序
	CnVenueName             string `json:"cn_venue_name" gorm:"cn_venue_name" form:"cn_venue_name"` // 中文场馆名称
	EnVenueName             string `json:"en_venue_name" gorm:"en_venue_name" form:"en_venue_name"` // 英文场馆名称
	Year                    string `json:"year" form:"year" gorm:"column:year"`                     // 年度
}

func (c *AudienceConferenceCompany) AfterUpdate(db *gorm.DB) error {
	user, ok := db.Get("user")
	id, ok2 := db.Get("id")
	var err error
	if ok && ok2 {
		err = db.Model(c).Where("id=?", id).Update("update_user", user).Error
	}
	return err
}

// ProductionManagement 产品管理
type ProductionManagement struct {
	AdminCommonModel
	AudienceCompanyId int64   `json:"audience_company_id" gorm:"audience_company_id" form:"audience_company_id"`
	CnName            string  `json:"cn_name" gorm:"cn_name" form:"cn_name"` // 中文产品名称
	EnName            string  `json:"en_name" gorm:"en_name" form:"en_name"` // 英文产品名称
	Logo              string  `json:"logo" gorm:"logo" form:"logo"`          //图片
	Sorting           float32 `json:"sorting" gorm:"sorting" form:"sorting"` // 排序
}

type ProductionData struct {
	AdminCommonModel
	AudienceCompanyId int64   `json:"audience_company_id" gorm:"audience_company_id" form:"audience_company_id"`
	Name              string  `json:"name" gorm:"name" form:"name"`          // 中文产品名称
	Logo              string  `json:"logo" gorm:"logo" form:"logo"`          //图片
	Sorting           float32 `json:"sorting" gorm:"sorting" form:"sorting"` // 排序
}

// TableName 表名称
func (*ProductionManagement) TableName() string {
	return "production_management"
}

// CnFreeVisitor 中文免费观众信息
type CnFreeVisitor struct {
	ID               int64  `json:"id" gorm:"id"`
	ConferenceID     int64  `json:"conference_id" gorm:"conference_id"`
	CnConferenceName string `json:"cn_conference_name" gorm:"column:cn_conference_name"`               // 中文展商名称
	CnName           string `json:"cn_name" gorm:"cn_name"`                                            // 中文场馆名称
	CnCompany        string `json:"cn_company" gorm:"cn_company"`                                      // 中文公司名称
	CnDepartment     string `json:"cn_department" gorm:"cn_department"`                                // 中文部门名称
	CnJobTitle       string `json:"cn_job_title" gorm:"cn_job_title"`                                  // 中文职位名称
	CnTelephone      string `json:"cn_telephone" gorm:"cn_telephone"`                                  // 中文电话
	CnEmail          string `json:"cn_email" gorm:"cn_email"`                                          // 中文邮箱
	PaperAnswer      string `json:"paper_answer" gorm:"paper_answer"`                                  // 问卷答案
	SubmitTime       string `json:"submitTime" form:"submit_time" gorm:"column:submit_time;<-:false" ` // 报名时间
	Willing          string `json:"willing" gorm:"column:willing" form:"willing" `                     // 是否愿意免费参观
	Source           string `json:"source" gorm:"column:source" form:"source" `                        // 来源
	FromID           string `json:"fromId" gorm:"column:fromId" form:"fromId" `                        // 渠道
	FromName         string `json:"from_name" gorm:"column:from_name" form:"from_name" `               // 渠道名称
	CodePhoto        string `json:"code_photo" gorm:"column:code_photo" form:"code_photo" `            // 二维码代码
	IdNo             string `json:"id_no" gorm:"column:id_no" form:"id_no" `                           // 身份证号

	MainProducts     string `json:"main_products" form:"main_products" gorm:"column:main_products"`             // 主推产品
	EnterpriseType   string `json:"enterprise_type" form:"enterprise_type" gorm:"column:enterprise_type"`       // 企业类型
	ProcurementItems string `json:"procurement_items" form:"procurement_items" gorm:"column:procurement_items"` // 采购产品
	OrderQuantity    string `json:"order_quantity" form:"order_quantity" gorm:"column:order_quantity"`          // 采购数量
}

// TableName 表名称
func (*CnFreeVisitor) TableName() string {
	return "cn_free_visitor"
}

// EnFreeVisitor 英文免费观众信息
type EnFreeVisitor struct {
	ID                 int64  `json:"id" gorm:"id"`
	ConferenceId       int64  `json:"conference_id" gorm:"conference_id"`
	EnEmail            string `json:"en_email" gorm:"en_email"`                                                      // 英文邮箱
	EnFirstName        string `json:"en_first_name" gorm:"en_first_name"`                                            // 姓
	EnLastName         string `json:"en_last_name" gorm:"en_last_name"`                                              // 名
	EnCompany          string `json:"en_company" gorm:"en_company"`                                                  // 英文公司名称
	EnJobTitle         string `json:"en_job_title" gorm:"en_job_title"`                                              // 英文职位名称
	EnCountryResidence string `json:"en_country_residence" gorm:"en_country_residence"`                              // 英文现居国家
	EnCity             string `json:"en_city" gorm:"en_city"`                                                        // 英文城市
	EnPhone            string `json:"en_phone" gorm:"en_phone"`                                                      // 英文电话
	EnTelephone        string `json:"en_telephone" gorm:"en_telephone"`                                              // 英文移动电话
	EnNationality      string `json:"en_nationality" gorm:"en_nationality"`                                          // 英文国家
	EnMainFocus        string `json:"en_main_focus" gorm:"en_main_focus"`                                            // 英文关注行业
	SubmitTime         string `json:"submitTime" form:"submit_time" gorm:"column:submit_time;<-:false" `             // 报名时间
	EnConferenceName   string `json:"en_conference_name" form:"en_conference_name" gorm:"column:en_conference_name"` // 英文展会名称
	Source             string `json:"source" gorm:"column:source" form:"source" `                                    // 来源
	FromID             string `json:"fromId" gorm:"column:fromId" form:"fromId" `                                    // 渠道
	FromName           string `json:"from_name" gorm:"column:from_name" form:"from_name" `                           // 渠道名称
	MainProducts       string `json:"main_products" form:"main_products" gorm:"column:main_products"`                // 主推产品
	EnterpriseType     string `json:"enterprise_type" form:"enterprise_type" gorm:"column:enterprise_type"`          // 企业类型
	ProcurementItems   string `json:"procurement_items" form:"procurement_items" gorm:"column:procurement_items"`    // 采购产品
	OrderQuantity      string `json:"order_quantity" form:"order_quantity" gorm:"column:order_quantity"`             // 采购数量
}

// TableName 表名称
func (*EnFreeVisitor) TableName() string {
	return "en_free_visitor"
}

type QuestionPaper struct {
	Url string `json:"url" gorm:"column:url"`
}

// Venue 场馆管理
type Venue struct {
	AdminCommonModel
	ConferenceId int64   `json:"conference_id" gorm:"conference_id" form:"conference_id"`
	CnVenueName  string  `json:"cn_venue_name" gorm:"cn_venue_name" form:"cn_venue_name"` // 中文场馆名称
	EnVenueName  string  `json:"en_venue_name" gorm:"en_venue_name" form:"en_venue_name"` // 英文场馆名称
	Sorting      float32 `json:"sorting" gorm:"sorting" form:"sorting"`                   // 排序
}

// TableName 表名称
func (*Venue) TableName() string {
	return "venue"
}
func (c *ConferenceFloorGraph) AfterUpdate(db *gorm.DB) error {
	user, ok := db.Get("user")
	id, ok2 := db.Get("id")
	var err error
	if ok && ok2 {
		err = db.Model(c).Where("id=?", id).Update("update_user", user).Error
	}
	return err
}

func (c *AudienceQuestionType) AfterUpdate(db *gorm.DB) error {
	user, ok := db.Get("user")
	id, ok2 := db.Get("id")
	var err error
	if ok && ok2 {
		err = db.Model(c).Where("id=?", id).Update("update_user", user).Error
	}
	return err
}

func (c *AudienceQuestionContent) AfterUpdate(db *gorm.DB) error {
	user, ok := db.Get("user")
	id, ok2 := db.Get("id")
	var err error
	if ok && ok2 {
		err = db.Model(c).Where("id=?", id).Update("update_user", user).Error
	}
	return err
}

type ConferenceCompanyFirstLetter struct {
	CommonModel
	AudienceCompanyId int64  `gorm:"column:audience_company_id"` //展会id
	FirstLetter       string `gorm:"column:first_letter"`        //首字母
	IsCn              bool   `gorm:"column:is_cn"`               //是否中文站
}

type AudienceAfterReport struct {
	CommonModel
	ConferenceId int64   `json:"conference_id" gorm:"conference_id"` // 展会id
	CnName       string  `json:"cn_name" gorm:"cn_name"`             // 展后报告名称
	EnName       string  `json:"en_name" gorm:"en_name"`             // 展后报告英文名称
	CnPdf        string  `json:"cn_pdf" gorm:"cn_pdf"`               // 中文展后报告pdf
	EnPdf        string  `json:"en_pdf" gorm:"en_pdf"`               // 英文展后报告pdf
	Sort         float32 `json:"sort" gorm:"sort"`                   // 排序
}

// TableName 表名称
func (*AudienceAfterReport) TableName() string {
	return "audience_after_report"
}

// PaperCollection 论文征集
type PaperCollection struct {
	ID           int64     `json:"id" gorm:"id"`
	ConferenceId int64     `json:"conference_id" gorm:"conference_id"`   // 展会id
	CnFormUrl    string    `json:"cn_form_url" gorm:"cn_form_url"`       // 中文表单链接
	EnFormUrl    string    `json:"en_form_url" gorm:"en_form_url"`       // 英文表单链接
	CnContent    string    `json:"cn_content" gorm:"cn_content"`         // 中文页面内容
	EnContent    string    `json:"en_content" gorm:"en_content"`         // 中文页面内容
	CreateTime   time.Time `json:"create_time" gorm:"create_time"`       // 创建时间
	UpdateTime   time.Time `json:"update_time" gorm:"update_time"`       // 更新时间
	CreateUser   string    `json:"create_user" gorm:"create_user"`       // 创建用户
	UpdateUser   string    `json:"update_user" gorm:"update_user"`       // 更新用户
	IsDeleted    int8      `json:"is_deleted" gorm:"is_deleted"`         // 是否删除
	CnButtonName string    `json:"cn_button_name" gorm:"cn_button_name"` // 中文按钮名称
	EnButtonName string    `json:"en_button_name" gorm:"en_button_name"` // 英文按钮名称
	CnAboveName  string    `json:"cn_above_name" gorm:"cn_above_name"`   // 中文按钮上方名称
	EnAboveName  string    `json:"en_above_name" gorm:"en_above_name"`   // 英文按钮上方名称
}

// TableName 表名称
func (*PaperCollection) TableName() string {
	return "paper_collection"
}

// 观众预登记
type AudiencePreRegistry struct {
	CommonModel
	Id              int64  `gorm:"column:id"`                //展会id
	ConferenceId    int64  `gorm:"column:conference_id"`     //展会id
	CnTitle         string `gorm:"column:cn_title"`          //中文页面标题
	EnTitle         string `gorm:"column:en_title"`          //英文页面标题
	CnVisitingValue string `gorm:"column:cn_visiting_value"` //中文参观价值
	EnVisitingValue string `gorm:"column:en_visiting_value"` //英文参观价值
	CnHandbookName  string `gorm:"column:cn_handbook_name"`  //中文手册名称
	EnHandbookName  string `gorm:"column:en_handbook_name"`  //英文手册名称
	CnUrl           string `gorm:"column:cn_url"`            //中文观众手册url
	EnUrl           string `gorm:"column:en_url"`            //英文观众手册url
}

// 观众问题内容
type AudienceQuestionContent struct {
	CommonModel
	QuestionTypeId     int64   `gorm:"column:question_type_id"`    //问题类型id
	CnQuestionTypeName string  `gorm:"column:cn_question_type"`    //中文问题类型
	EnQuestionTypeName string  `gorm:"column:en_question_type"`    //英文问题类型
	CnQuestionContent  string  `gorm:"column:cn_question_content"` //问题内容中文
	EnQuestionContent  string  `gorm:"column:en_question_content"` //问题内容英文
	CnQuestionAnswer   string  `gorm:"column:cn_question_answer"`  //答案内容中文
	EnQuestionAnswer   string  `gorm:"column:en_question_answer"`  //答案内容英文
	Sort               float32 `gorm:"column:sort"`                //排序
}

// 观众问题类型
type AudienceQuestionType struct {
	CommonModel
	ConferenceId   int64   `gorm:"column:conference_id"`    //展会id
	EnQuestionType string  `gorm:"column:en_question_type"` //英文问题类型
	CnQuestionType string  `gorm:"column:cn_question_type"` //英文问题类型
	Sort           float32 `gorm:"column:sort"`             //排序
}

// 展商手册
type CompanyHandBook struct {
	CommonModel
	ConferenceId int64   `gorm:"column:conference_id"` //展会id
	CnName       string  `gorm:"column:cn_name"`       //中文展商手册名称
	EnName       string  `gorm:"column:en_name"`       //英文展商手册名称
	CnPdf        string  `gorm:"column:cn_pdf"`        //中文pdf
	EnPdf        string  `gorm:"column:en_pdf"`        //英文pdf
	Sort         float32 `gorm:"column:sort"`          //排序
}

func (c *CompanyHandBook) AfterUpdate(db *gorm.DB) error {
	user, ok := db.Get("user")
	id, ok2 := db.Get("id")
	var err error
	if ok && ok2 {
		err = db.Model(c).Where("id=?", id).Update("update_user", user).Error
	}
	return err
}

func (*CompanyHandBook) TableName() string {
	return "company_handbook"
}

// 展商问题内容
type CompanyQuestionContent struct {
	CommonModel
	QuestionTypeId     int64   `gorm:"column:question_type_id"`    //问题类型id
	CnQuestionTypeName string  `gorm:"column:cn_question_type"`    //中文问题类型
	EnQuestionTypeName string  `gorm:"column:en_question_type"`    //英文问题类型
	CnQuestionContent  string  `gorm:"column:cn_question_content"` //展商问题中文
	EnQuestionContent  string  `gorm:"column:en_question_content"` //展商问题英文
	CnQuestionAnswer   string  `gorm:"column:cn_question_answer"`  //展商答案中文
	EnQuestionAnswer   string  `gorm:"column:en_question_answer"`  //展商问题英文
	Sort               float32 `gorm:"column:sort"`                //排序
}

func (c *CompanyQuestionContent) AfterUpdate(db *gorm.DB) error {
	user, ok := db.Get("user")
	id, ok2 := db.Get("id")
	var err error
	if ok && ok2 {
		err = db.Model(c).Where("id=?", id).Update("update_user", user).Error
	}
	return err
}

type CommonQuestionContent struct {
	CommonModel
	QuestionTypeId    int64   `gorm:"column:question_type_id"`    //问题类型id
	CnQuestionContent string  `gorm:"column:cn_question_content"` //展商问题中文
	EnQuestionContent string  `gorm:"column:en_question_content"` //展商问题英文
	CnQuestionAnswer  string  `gorm:"column:cn_question_answer"`  //展商答案中文
	EnQuestionAnswer  string  `gorm:"column:en_question_answer"`  //展商问题英文
	Sort              float32 `gorm:"column:sort"`                //排序
}

// 展商问题类型
type CompanyQuestionType struct {
	CommonModel
	ConferenceId   int64   `gorm:"column:conference_id"`    //展会id
	CnQuestionType string  `gorm:"column:cn_question_type"` //展商问题类型中文
	EnQuestionType string  `gorm:"column:en_question_type"` //展商问题类型英文
	Sort           float32 `gorm:"column:sort"`             //排序
}

func (c *CompanyQuestionType) AfterUpdate(db *gorm.DB) error {
	user, ok := db.Get("user")
	id, ok2 := db.Get("id")
	var err error
	if ok && ok2 {
		err = db.Model(c).Where("id=?", id).Update("update_user", user).Error
	}
	return err
}

// 展商线索
type ConferenceClue struct {
	Id               int64     `gorm:"column:id"`                                                                 //主键id
	ConferenceId     int64     `gorm:"column:conference_id"`                                                      //展会id
	CnConferenceName string    `gorm:"column:cn_conference_name"`                                                 //展会id
	UserId           int64     `gorm:"column:user_id"`                                                            //用户id
	LastName         string    `gorm:"column:last_name"`                                                          //姓
	FirstName        string    `gorm:"column:first_name"`                                                         //名
	Email            string    `gorm:"column:email"`                                                              //邮箱
	CellPhone        string    `gorm:"column:cellphone"`                                                          //手机号码
	Company          string    `gorm:"column:company"`                                                            //公司
	JobTitle         string    `gorm:"column:job_title"`                                                          //职位
	Country          string    `gorm:"column:country"`                                                            //国家
	Comment          string    `gorm:"column:comment"`                                                            //留言
	InterestLevel    string    `gorm:"column:interest_level"`                                                     //兴趣程度
	BoothType        string    `gorm:"column:booth_type"`                                                         //展位类型
	ExhibitionArea   string    `gorm:"column:exhibition_area"`                                                    //感兴趣的展区
	SourceID         string    `gorm:"column:source_id" db:"source_id" json:"source_id" form:"source_id"`         //来源ID
	SourceName       string    `gorm:"column:source_name" db:"source_name" json:"source_name" form:"source_name"` //来源名称
	FromId           string    `gorm:"column:from_id" db:"from_id" json:"from_id" form:"from_id"`                 //渠道
	FromName         string    `gorm:"column:from_name" db:"from_name" json:"from_name" form:"from_name"`         //渠道名称
	Type             int       `gorm:"column:type"`                                                               //0申请参展，1赞助
	Status           int       `gorm:"column:status"`                                                             //分配状态
	CreateUser       string    `gorm:"column:create_user"`                                                        //创建用户
	UpdateUser       string    `gorm:"column:update_user"`                                                        //更新用户
	CreateTime       time.Time `gorm:"column:create_time;autoCreateTime"`                                         //创建时间
	UpdateTime       time.Time `gorm:"column:update_time;autoUpdateTime"`                                         //更新时间

	MainProducts   string `json:"main_products" form:"main_products" gorm:"column:main_products"`       // 主推产品
	EnterpriseType string `json:"enterprise_type" form:"enterprise_type" gorm:"column:enterprise_type"` // 企业类型

}

type ConferenceFloorGraph struct {
	CommonModel
	ConferenceId int64   `gorm:"column:conference_id"` //展会id
	CnGraphUrl   string  `gorm:"column:cn_graph_url"`  //中文平面图url
	EnGraphUrl   string  `gorm:"column:en_graph_url"`  //英文平面图url
	CnGraphName  string  `gorm:"column:cn_graph_name"` //中文展馆名称
	EnGraphName  string  `gorm:"column:en_graph_name"` //英文展馆名称
	Sort         float32 `gorm:"column:sort"`          //排序
}

// 联系我们
type ContactUs struct {
	ID           int64     `gorm:"column:id"`                         //主键id
	ConferenceId int64     `gorm:"column:conference_id"`              //展会id
	CnContact    string    `gorm:"column:cn_contact"`                 //联系我们内容-中文
	EnContact    string    `gorm:"column:en_contact"`                 //联系我们内容-英文
	IsDeleted    bool      `gorm:"column:is_deleted"`                 //是否删除
	CreateUser   string    `gorm:"column:create_user"`                //创建用户
	UpdateUser   string    `gorm:"column:update_user"`                //最近更新用户
	CreateTime   time.Time `gorm:"column:create_time;autoCreateTime"` //创建时间
	UpdateTime   time.Time `gorm:"column:update_time;autoUpdateTime"` //最近更新时间
}

func (ContactUs) TableName() string {
	return "contact_us"
}

// 参会指南
type ExhibitionGuideline struct {
	CommonModel
	ConferenceId      int64  `gorm:"column:conference_id"`                                                       //展会id
	LocationLongitude string `gorm:"column:location_longitude"`                                                  //展馆位置经度
	LocationLatitude  string `gorm:"column:location_latitude"`                                                   //展馆位置纬度
	CnFloorGraph      string `gorm:"column:cn_floor_graph"`                                                      //展馆平面图url
	EnFloorGraph      string `gorm:"column:en_floor_graph"`                                                      //展馆平面图url
	GoogleUrl         string `gorm:"column:google_url"`                                                          //谷歌地图分享链接
	CnHeading         string `json:"cn_heading" db:"cn_heading" gorm:"cn_heading" form:"cn_heading"`             //上标题-中文
	EnHeading         string `json:"en_heading" db:"en_heading" gorm:"en_heading" form:"en_heading"`             //上标题-英文
	CnHeadlining      string `json:"cn_headlining" db:"cn_headlining" gorm:"cn_headlining" form:"cn_headlining"` //下标题-中文
	EnHeadlining      string `json:"en_headlining" db:"en_headlining" gorm:"en_headlining" form:"en_headlining"` //下标题-英文
	CnHotelExcel      string `gorm:"column:cn_hotel_excel"`                                                      //中文酒店表格
	EnHotelExcel      string `gorm:"column:en_hotel_excel"`                                                      //英文酒店表格
	CnHotelButton     string `gorm:"column:cn_hotel_button" json:"cn_hotel_button" form:"cn_hotel_button"`       //中文酒店按钮
	EnHotelButton     string `gorm:"column:en_hotel_button" json:"en_hotel_button" form:"en_hotel_button" `      //英文酒店按钮
	CnBookingUrl      string `gorm:"column:cn_booking_url"`                                                      //中文预定表单链接
	EnBookingUrl      string `gorm:"column:en_booking_url"`                                                      //英文预定表单链接
	CnHotelContent    string `gorm:"column:cn_hotel_content"`                                                    //中文酒店页面内容
	EnHotelContent    string `gorm:"column:en_hotel_content"`                                                    //英文酒店页面内容
	CnTrafficContent  string `gorm:"column:cn_traffic_content"`                                                  //中文交通内容
	EnTrafficContent  string `gorm:"column:en_traffic_content"`                                                  //英文交通内容
	CnCityOverview    string `gorm:"column:cn_city_overview"`                                                    //中文城市概况
	EnCityOverview    string `gorm:"column:en_city_overview"`                                                    //英文城市概况
}

// 展商在线登记
type OnlineRegister struct {
	CommonModel
	ConferenceId int64  `gorm:"column:conference_id"` //展会id
	CnUrl        string `gorm:"column:cn_url"`        //中文在线登记表表单链接
	EnUrl        string `gorm:"column:en_url"`        //英文在线登记表表单链接
	CnContent    string `gorm:"column:cn_content"`    //中文内容
	EnContent    string `gorm:"column:en_content"`    //英文内容
}

// 展商手册发送记录
type CompanyHandBookSendLog struct {
	ID               int64     `gorm:"primary_key;column:id" json:"id" db:"id"`                                     // 自增ID
	SubscribeID      int64     `gorm:"column:subscribe_id" json:"subscribe_id" db:"subscribe_id"`                   // 订阅ID
	ConferenceID     int64     `gorm:"column:conference_id" json:"conference_id" db:"conference_id"`                // 展会ID
	HandbookID       int64     `gorm:"column:handbook_id" json:"handbook_id" db:"handbook_id"`                      // 手册ID
	CnConferenceName string    `gorm:"column:cn_conference_name" json:"cn_conference_name" db:"cn_conference_name"` // 展会名称中文
	EnConferenceName string    `gorm:"column:en_conference_name" json:"en_conference_name" db:"en_conference_name"` // 展会名称英文
	CnHandBookName   string    `gorm:"column:cn_handbook_name" json:"cn_handbook_name" db:"cn_handbook_name"`       // 展商手册名称
	EnHandBookName   string    `gorm:"column:en_handbook_name" json:"en_handbook_name" db:"en_handbook_name"`       // 展商手册英文名称
	CnHandBookURL    string    `gorm:"column:cn_handbook_url" json:"cn_handbook_url" db:"cn_handbook_url"`          // 中文pdf地址
	EnHandBookURL    string    `gorm:"column:en_handbook_url" json:"en_handbook_url" db:"en_handbook_url"`          // 英文pdf地址
	Cellphone        string    `gorm:"column:cellphone" json:"cellphone" db:"cellphone"`                            // 手机号
	Email            string    `gorm:"column:email" json:"email" db:"email"`                                        // 邮箱
	Content          string    `gorm:"column:content" json:"content" db:"content"`                                  // 内容
	SendType         int       `gorm:"column:send_type" json:"send_type" db:"send_type"`                            // 发送类型,1手机,2邮箱
	Status           int       `gorm:"column:status" json:"status" db:"status"`                                     // 发送状态:1成功;2失败
	UpdateTime       time.Time `gorm:"column:update_time" json:"update_time" db:"update_time"`                      // 更新时间
	CreateTime       time.Time `gorm:"column:create_time" json:"create_time" db:"create_time"`                      // 创建时间
}

func (CompanyHandBookSendLog) TableName() string {
	return "company_handbook_send_log"
}

type BdVidLog struct {
	ID         int64     `json:"id" db:"id" gorm:"id" form:"id"`
	Vid        string    `gorm:"column:v_id" json:"v_id" db:"v_id"`
	PageBdUrl  string    `gorm:"column:page_bd_url" json:"page_bd_url" db:"page_bd_url"` // 手册ID
	NewsType   string    `gorm:"column:news_type" json:"news_type" db:"news_type"`       // 百度表单类型
	FormType   int       `gorm:"column:form_type" json:"form_type" db:"form_type"`       // 表单类型（1观众，2赞助及广告机会，3展览展位，4购票参会）
	CreateTime time.Time `gorm:"column:create_time" json:"create_time" db:"create_time"` // 创建时间
	ReturnMsg  string    `gorm:"column:return_msg" json:"return_msg" db:"return_msg"`    // 返回信息
}

func (BdVidLog) TableName() string {
	return "bd_vid_log"
}

// // ConferenceFromConfig undefined
type ConferenceFromConfig struct {
	Id       string `json:"id" db:"id" gorm:"id" form:"id"`
	FromId   string `json:"from_id" db:"from_id" gorm:"from_id" form:"from_id"`
	FromName string `json:"from_name" db:"from_name" gorm:"from_name" form:"from_name"`
}

//
//// TableName 表名称
//func (*ConferenceFromConfig) TableName() string {
//	return "conference_from_config"
//}

type MeetingChannel struct {
	ChannelId   string `json:"channel_id" db:"channel_id" gorm:"channel_id" form:"channel_id"`
	ChannelName string `json:"channel_name" db:"channel_name" gorm:"channel_name" form:"channel_name"`
}

type MeetingPlatform struct {
	PlatformId   string `json:"platform_id" db:"platform_id" gorm:"platform_id" form:"platform_id"`
	PlatformName string `json:"platform_name" db:"platform_name" gorm:"platform_name" form:"platform_name"`
}

// 随机图库
type NewsGallery struct {
	Id  int    `gorm:"column:id" json:"id" db:"id"`
	Url string `gorm:"column:url" json:"url" db:"url" `
}

func (NewsGallery) TableName() string {
	return "news_gallery"
}
