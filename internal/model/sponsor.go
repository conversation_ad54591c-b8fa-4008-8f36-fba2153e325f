package model

import "time"

// ConferenceSponsor 赞助
type ConferenceSponsor struct {
	CommonModel
	ID           int64  `json:"id" db:"id" gorm:"id" form:"id"`
	ConferenceId int64  `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"` //展会ID
	CnTitle      string `json:"cn_title" db:"cn_title" gorm:"cn_title" form:"cn_title"`                     //赞助标题-中文
	EnTitle      string `json:"en_title" db:"en_title" gorm:"en_title" form:"en_title"`                     //赞助标题-英文
	CnButton     string `json:"cn_button" db:"cn_button" gorm:"cn_button" form:"cn_button"`                 //赞助按钮名称-中文
	EnButton     string `json:"en_button" db:"en_button" gorm:"en_button" form:"en_button"`                 //赞助按钮名称-英文
	CnPdf        string `json:"cn_pdf" db:"cn_pdf" gorm:"cn_pdf" form:"cn_pdf"`
	EnPdf        string `json:"en_pdf" db:"en_pdf" gorm:"en_pdf" form:"en_pdf"`
	CnContent    string `json:"cn_content" db:"cn_content" gorm:"cn_content" form:"cn_content"`
	EnContent    string `json:"en_content" db:"en_content" gorm:"en_content" form:"en_content"`
}

// TableName 表名称
func (*ConferenceSponsor) TableName() string {
	return "conference_sponsor"
}

type SponsorApplication struct {
	Id            int64  `gorm:"column:id" db:"id" json:"id" form:"id"`
	ConferenceId  int64  `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"`            //展会ID
	UserId        int64  `gorm:"column:user_id" db:"user_id" json:"user_id" form:"user_id"`                             //用户ID
	FirstName     string `gorm:"column:first_name" db:"first_name" json:"first_name" form:"first_name"`                 //姓
	LastName      string `gorm:"column:last_name" db:"last_name" json:"last_name" form:"last_name"`                     //名
	Company       string `gorm:"column:company" db:"company" json:"company" form:"company"`                             //公司
	JobTitle      string `gorm:"column:job_title" db:"job_title" json:"job_title" form:"job_title"`                     //职位
	Email         string `gorm:"column:email" db:"email" json:"email" form:"email"`                                     //邮箱
	Phone         string `gorm:"column:phone" db:"phone" json:"phone" form:"phone"`                                     //手机
	Country       string `gorm:"column:country" db:"country" json:"country" form:"country"`                             //国家
	LeaveMessage  string `gorm:"column:leave_message" db:"leave_message" json:"leave_message" form:"leave_message"`     //留言
	LevelInterest string `gorm:"column:level_interest" db:"level_interest" json:"level_interest" form:"level_interest"` //兴趣程度
	BoothType     string `gorm:"column:booth_type" db:"booth_type" json:"booth_type" form:"booth_type"`                 //展位类型
	Type          int    `gorm:"column:type" db:"type" json:"type" form:"type"`                                         //反馈类型
	Status        int    `gorm:"column:status" db:"status" json:"status" form:"status"`                                 //1未分配，2已分配

	CreateTime time.Time `gorm:"column:create_time" db:"create_time" json:"create_time" form:"create_time"` //提交时间
}

// TableName 表名称
func (*SponsorApplication) TableName() string {
	return "sponsor_application"
}
