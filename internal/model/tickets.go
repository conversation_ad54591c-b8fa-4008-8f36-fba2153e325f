package model

import "time"

type ConferenceTicketPrice struct {
	Id                     int64  `gorm:"column:id" db:"id" json:"id" form:"id"`
	ConferenceId           int64  `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"`                                                        //展会ID
	CnName                 string `json:"cn_name" form:"cn_name"  gorm:"column:cn_name" db:"cn_name"`                                                                        //票种名称中文
	EnName                 string `json:"en_name"  form:"en_name" gorm:"column:en_name" db:"en_name"`                                                                        //票种名称英文
	CnServiceId            string `gorm:"column:cn_service_id" db:"cn_service_id" json:"cn_service_id" form:"cn_service_id"`                                                 //中文服务ID
	EnServiceId            string `gorm:"column:en_service_id" db:"en_service_id" json:"en_service_id" form:"en_service_id"`                                                 //英文服务ID
	CnButtonName           string `gorm:"column:cn_button_name" db:"cn_button_name" json:"cn_button_name" form:"cn_button_name"`                                             //自定义按钮名称-中文
	EnButtonName           string `gorm:"column:en_button_name" db:"en_button_name" json:"en_button_name" form:"en_button_name"`                                             //自定义按钮名称-英文
	CnButtonLink           string `gorm:"column:cn_button_link" db:"cn_button_link" json:"cn_button_link" form:"cn_button_link"`                                             //自定义按钮跳转链接-中文
	EnButtonLink           string `gorm:"column:en_button_link" db:"en_button_link" json:"en_button_link" form:"en_button_link"`                                             //自定义按钮跳转链接-英文
	CnRegistrationPageName string `gorm:"column:cn_registration_page_name" db:"cn_registration_page_name" json:"cn_registration_page_name" form:"cn_registration_page_name"` //报名页门票名称-中文
	EnRegistrationPageName string `gorm:"column:en_registration_page_name" db:"en_registration_page_name" json:"en_registration_page_name" form:"en_registration_page_name"` //报名页门票名称-英文

	CnIsDisplayed int     `gorm:"column:cn_is_displayed" db:"cn_is_displayed"  json:"cn_is_displayed" form:"cn_is_displayed"` //是否显示购买按钮（1展示，0不展示）
	EnIsDisplayed int     `gorm:"column:en_is_displayed" db:"en_is_displayed"  json:"en_is_displayed" form:"en_is_displayed"` //是否显示购买按钮（1展示，0不展示）
	CnMaximum     int     `gorm:"column:cn_maximum" db:"cn_maximum" json:"cn_maximum" form:"cn_maximum"`                      //最大人数
	EnMaximum     int     `gorm:"column:en_maximum" db:"en_maximum" json:"en_maximum" form:"en_maximum"`                      //最大人数
	CnSorting     float32 `json:"cn_sorting" db:"cn_sorting" gorm:"cn_sorting" form:"cn_sorting"`                             //排序
	EnSorting     float32 `json:"en_sorting" db:"en_sorting" gorm:"en_sorting" form:"en_sorting"`                             //排序

	CnCurrencyUnit int `gorm:"column:cn_currency_unit" db:"cn_currency_unit" json:"cn_currency_unit" form:"cn_currency_unit"` //货币单位（1中文，2美元，3欧元）
	EnCurrencyUnit int `gorm:"column:en_currency_unit" db:"en_currency_unit" json:"en_currency_unit" form:"en_currency_unit"` //货币单位（1中文，2美元，3欧元）

	Type          int       `json:"type" db:"type" gorm:"type" form:"type"`                                                    ///模版类型 （0权益模版，1人数金额模版）
	CnStandardFee string    `gorm:"column:cn_standard_fee" db:"cn_standard_fee" json:"cn_standard_fee" form:"cn_standard_fee"` //中文服务金额信息
	EnStandardFee string    `gorm:"column:en_standard_fee" db:"en_standard_fee" json:"en_standard_fee" form:"en_standard_fee"` //英文服务金额信息
	CreateTime    time.Time `db:"create_time" json:"create_time" form:"create_time"`
	UpdateTime    time.Time `json:"update_time" db:"update_time" form:"update_time"`
	CreateAdmin   string    `json:"create_admin" db:"create_admin"  form:"create_admin"`
	UpdateAdmin   string    `json:"update_admin" db:"update_admin" form:"update_admin"`
	Deleted       int       `json:"deleted" db:"deleted" form:"deleted"`
}

// TableName 表名称
func (*ConferenceTicketPrice) TableName() string {
	return "conference_ticket_price"
}

type ConferenceRightsInterests struct {
	Id           int64     `json:"id" db:"id" gorm:"id" form:"id"`
	ConferenceId int64     `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"` //展会ID
	CnContent    string    `json:"cn_content" db:"cn_content" gorm:"cn_content" form:"cn_content"`             //权益内容-中文
	EnContent    string    `json:"en_content" db:"en_content" gorm:"en_content" form:"en_content"`             //权益内容-英文
	CnSorting    float32   `json:"cn_sorting" db:"cn_sorting" gorm:"cn_sorting" form:"cn_sorting"`             //排序
	EnSorting    float32   `json:"en_sorting" db:"en_sorting" gorm:"en_sorting" form:"en_sorting"`             //排序
	Type         int       `json:"type" db:"type" gorm:"type" form:"type"`                                     ///模版类型 （0权益模版，1人数金额模版）
	Deleted      int       `json:"deleted" db:"deleted" gorm:"deleted" form:"deleted"`
	CreateTime   time.Time `json:"create_time" db:"create_time" gorm:"create_time" form:"create_time"`
	UpdateTime   time.Time `json:"update_time" db:"update_time" gorm:"update_time" form:"update_time"`
	CreateAdmin  string    `json:"create_admin" db:"create_admin" gorm:"create_admin" form:"create_admin"`
	UpdateAdmin  string    `json:"update_admin" db:"update_admin" gorm:"update_admin" form:"update_admin"`
}

// TableName 表名称
func (*ConferenceRightsInterests) TableName() string {
	return "conference_rights_interests"
}

type ConferenceRightsTicket struct {
	Id           int64 `gorm:"column:id" db:"id" json:"id" form:"id"`
	RightsId     int64 `gorm:"column:rights_id" db:"rights_id" json:"rights_id" form:"rights_id"`          //权益ID
	TicketId     int64 `gorm:"column:ticket_id" db:"ticket_id" json:"ticket_id" form:"ticket_id"`          //票种ID
	ConferenceId int64 `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"` //展会ID
	Deleted      int   `json:"deleted" db:"deleted" form:"deleted"`
}

// TableName 表名称
func (*ConferenceRightsTicket) TableName() string {
	return "conference_rights_ticket"
}

type ConferenceTicketConfig struct {
	Id              int64 `gorm:"column:id" db:"id" json:"id" form:"id"`
	ConferenceId    int64 `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"`                        //展会ID
	CnPriceTemplate int   `gorm:"column:cn_price_template" db:"cn_price_template" json:"cn_price_template" form:"cn_price_template"` //价格模板 (0 人数金额模版,  1 票种权益表格模版)
	EnPriceTemplate int   `gorm:"column:en_price_template" db:"en_price_template" json:"en_price_template" form:"en_price_template"` //价格模板 (0 人数金额模版,  1 票种权益表格模版)
	Deleted         int   `json:"deleted" db:"deleted" form:"deleted"`
}

// TableName 表名称
func (*ConferenceTicketConfig) TableName() string {
	return "conference_ticket_config"
}

type ConferenceRegister struct {
	Id                 int64  `gorm:"column:id" db:"id" json:"id" form:"id"`
	ConferenceId       int64  `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"`                                //展会ID
	UserId             int64  `gorm:"column:user_id" db:"user_id" json:"user_id" form:"user_id"`                                                 //购买用户ID
	FirstName          string `gorm:"column:first_name" db:"first_name" json:"first_name" form:"first_name"`                                     //名 （中文只用first_name）
	LastName           string `gorm:"column:last_name" db:"last_name" json:"last_name" form:"last_name"`                                         //姓
	Company            string `gorm:"column:company" db:"company" json:"company" form:"company"`                                                 //公司
	Email              string `gorm:"column:email" db:"email" json:"email" form:"email"`                                                         //邮箱
	Mobile             string `gorm:"column:mobile" db:"mobile" json:"mobile" form:"mobile"`                                                     //手机号
	InterestedMeetings string `gorm:"column:interested_meetings" db:"interested_meetings" json:"interested_meetings" form:"interested_meetings"` //感兴趣的会议（多个用英文逗号分割）
	ServiceId          string `gorm:"column:service_id" db:"service_id" json:"service_id" form:"service_id"`                                     //服务ID
	OrderId            string `db:"order_id"  json:"order_id" form:"order_id"`                                                                   //订单号ID
	OrderStatus        int    `gorm:"column:order_status" db:"order_status" json:"order_status" form:"order_status"`                             //订单状态
	OrderNum           int    `db:"order_num"  json:"order_num" form:"order_num"`                                                                //订单数量

	MainProducts   string `json:"main_products" form:"main_products" gorm:"column:main_products"`       // 主推产品
	EnterpriseType string `json:"enterprise_type" form:"enterprise_type" gorm:"column:enterprise_type"` // 企业类型

	CreateTime     time.Time `gorm:"column:create_time" db:"create_time" json:"create_time" form:"create_time"` //报名时间
	UpdateTime     time.Time `json:"update_time" db:"update_time"`                                              //修改时间
	ConferenceName string    `db:"conference_name"  json:"conference_name" form:"conference_name"`              //展会名称
	FromId         string    `gorm:"column:fromId" db:"fromId" json:"fromId" form:"fromId"`                     //渠道
	FromName       string    `gorm:"column:from_name" db:"from_name" json:"from_name" form:"from_name"`         //渠道名称
	SourceID       string    `gorm:"column:source_id" db:"source_id" json:"source_id" form:"source_id"`         //来源ID
	SourceName     string    `gorm:"column:source_name" db:"source_name" json:"source_name" form:"source_name"` //来源名称
	CnOrEn         int       `gorm:"column:cn_or_en" db:"cn_or_en" json:"cn_or_en" form:"cn_or_en" `            // 0 中文 1 英文
	Deleted        int       `gorm:"column:deleted" db:"deleted" json:"deleted" form:"deleted"`
}

// TableName 表名称
func (*ConferenceRegister) TableName() string {
	return "conference_register"
}

type ConferenceRegisterUser struct {
	Id         int       `gorm:"column:id" db:"id" json:"id" form:"id"`
	RegisterId int64     `gorm:"column:register_id" db:"register_id" json:"register_id" form:"register_id"` //报名ID
	FirstName  string    `gorm:"column:first_name" db:"first_name" json:"first_name" form:"first_name"`     //名 （中文只用first_name）
	LastName   string    `gorm:"column:last_name" db:"last_name" json:"last_name" form:"last_name"`         //姓
	Email      string    `gorm:"column:email" db:"email" json:"email" form:"email"`                         //邮箱
	Mobile     string    `gorm:"column:mobile" db:"mobile" json:"mobile" form:"mobile"`                     //手机号
	Company    string    `gorm:"column:company" db:"company" json:"company" form:"company"`                 //公司
	JobTitle   string    `gorm:"column:job_title" db:"job_title" json:"job_title" form:"job_title"`         //职位
	CreateTime time.Time `gorm:"column:create_time" db:"create_time" json:"create_time" form:"create_time"` //报名时间
	Deleted    int       `gorm:"column:deleted" db:"deleted" json:"deleted" form:"deleted"`                 //删除状态
}

// TableName 表名称
func (*ConferenceRegisterUser) TableName() string {
	return "conference_register_user"
}

type RegisterLog struct {
	Id         int       `gorm:"column:id" db:"id" json:"id" form:"id"`
	RegisterId int64     `gorm:"column:register_id" db:"register_id" json:"register_id" form:"register_id"`
	ReqLog     string    `gorm:"column:req_log" db:"req_log" json:"req_log" form:"req_log"`
	RespLog    string    `gorm:"column:resp_log" db:"resp_log" json:"resp_log" form:"resp_log"`
	ErrLog     string    `gorm:"column:err_log" db:"err_log" json:"err_log" form:"err_log"`
	LogType    int       `gorm:"column:log_type" db:"log_type" json:"log_type" form:"log_type"`
	CreateTime time.Time `gorm:"column:create_time" db:"create_time" json:"create_time" form:"create_time"`
}

// TableName 表名称
func (*RegisterLog) TableName() string {
	return "register_log"
}

type ChannelSource struct {
	Id   string `gorm:"column:id" db:"id" json:"id" form:"id"`
	Name string `json:"name" gorm:"column:name" db:"name"` //渠道来源名称
}

// TableName 表名称
func (*ChannelSource) TableName() string {
	return "channel_source"
}

type ForumConfigList struct {
	Id              int64  `gorm:"column:id" db:"id" json:"id" form:"id"`
	ConferenceId    int64  `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"` //展会ID
	CnPriceTemplate string `gorm:"column:cn_name" db:"cn_name" json:"cn_name" form:"cn_name"`                  //中文会议名
	EnPriceTemplate string `gorm:"column:en_name" db:"en_name" json:"en_name" form:"en_name"`                  //英文会议名
	Type            int    `gorm:"column:type" json:"type" db:"type" form:"type"`
}

// TableName 表名称
func (*ForumConfigList) TableName() string {
	return "forum_config_list"
}

type ConferenceRegisterFrom struct {
	ID           int64  `json:"id"`
	ConferenceId int64  `json:"conference_id" form:"conference_id" db:"conference_id" gorm:"conference_id"` //展会ID
	RegisterId   int64  `json:"register_id" db:"register_id" gorm:"register_id" form:"register_id"`
	FromName     string `json:"from_name" form:"from_name" db:"from_name" gorm:"from_name"` //渠道
	FromType     int    `json:"from_type" db:"from_type" gorm:"from_type" form:"from_type"` //
	CreateTime   string `json:"create_time" db:"create_time" gorm:"create_time" form:"create_time"`
	Deleted      int    `json:"deleted" db:"deleted" gorm:"deleted" form:"deleted"`
}

// TableName 表名称
func (*ConferenceRegisterFrom) TableName() string {
	return "conference_register_from"
}
