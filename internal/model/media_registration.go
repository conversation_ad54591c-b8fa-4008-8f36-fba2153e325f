package model

import "time"

type MediaRegistration struct {
	Id                 int64     `gorm:"column:id" db:"id" json:"id" form:"id"`
	ConferenceName     string    `gorm:"column:conference_name" db:"conference_name" json:"conference_name" form:"conference_name"`                         //会议
	ConferenceId       int64     `gorm:"column:conference_id"  db:"conference_id" json:"conference_id" form:"conference_id" `                               //会展ID
	UserId             int64     `gorm:"column:user_id" db:"user_id" json:"user_id" form:"user_id"`                                                         //用户ID
	Name               string    `gorm:"column:name" db:"name" json:"name" form:"name" binding:"required"`                                                  //姓名
	FirstName          string    `gorm:"column:first_name" db:"first_name" json:"first_name" form:"first_name"`                                             //姓
	LastName           string    `gorm:"column:last_name" db:"last_name" json:"last_name" form:"last_name"`                                                 //名
	Organisation       string    `gorm:"column:organisation" db:"organisation" json:"organisation" form:"organisation"`                                     //组织
	Phone              string    `gorm:"column:phone" db:"phone" json:"phone" form:"phone"`                                                                 //手机
	JobTitle           string    `gorm:"column:job_title" db:"job_title" json:"job_title" form:"job_title"`                                                 //职位
	Email              string    `gorm:"column:email" db:"email" json:"email" form:"email"`                                                                 //邮箱
	Country            string    `gorm:"column:country" db:"country" json:"country" form:"country"`                                                         //国家
	FromId             string    `gorm:"column:from_id" db:"from_id" json:"from_id" form:"from_id"`                                                         //渠道
	FromName           string    `gorm:"column:from_name" db:"from_name" json:"from_name" form:"from_name"`                                                 //渠道名称
	SourceID           string    `gorm:"column:source_id" db:"source_id" json:"source_id" form:"source_id"`                                                 //来源ID
	IsAgreeSendMessage int       `gorm:"column:is_agree_send_message" db:"is_agree_send_message" json:"is_agree_send_message" form:"is_agree_send_message"` //是否同意发送邮件(2同意，1不同意)
	CreateTime         time.Time `gorm:"column:create_time" db:"create_time" json:"create_time" form:"create_time"`                                         //提交时间
	UpdateTime         time.Time `json:"update_time" gorm:"update_time"`                                                                                    // 更新时间
	CreateAdmin        string    `json:"create_admin" gorm:"create_admin"`                                                                                  // 创建用户
	UpdateAdmin        string    `json:"update_admin" gorm:"update_admin"`                                                                                  // 更新用户
	Language           string    `gorm:"column:language" db:"language" json:"language" form:"language"`                                                     //语言
}

// TableName 表名称
func (*MediaRegistration) TableName() string {
	return "media_registration"
}

type MediaRegistrationConfig struct {
	Id                  int64     `gorm:"column:id" db:"id" json:"id" form:"id"`
	ConferenceId        int64     `json:"conference_id" form:"conference_id" gorm:"conference_id"`                         //会展ID
	CnNavigationDisplay int       `json:"cn_navigation_display" form:"cn_navigation_display" gorm:"cn_navigation_display"` //媒体注册中文导航是否显示（0  否 1  是）
	EnNavigationDisplay int       `json:"en_navigation_display" form:"en_navigation_display" gorm:"en_navigation_display"` //英文其他页底是否显示（0  否 1  是）
	CnContent           string    `json:"cn_content" form:"cn_content" gorm:"cn_content"`                                  //中文内容
	EnContent           string    `json:"en_content" form:"en_content" gorm:"en_content"`                                  //英文内容
	CreateTime          time.Time `gorm:"column:create_time" db:"create_time" json:"create_time" form:"create_time"`       //提交时间
	UpdateTime          time.Time `json:"update_time" gorm:"update_time"`                                                  // 更新时间
	CreateAdmin         string    `json:"create_admin" gorm:"create_admin"`                                                // 创建用户
	UpdateAdmin         string    `json:"update_admin" gorm:"update_admin"`                                                // 更新用户
}

// TableName 表名称
func (*MediaRegistrationConfig) TableName() string {
	return "media_registration_config"
}
