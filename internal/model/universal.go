package model

import "time"

type ConferenceColumn struct {
	Id           int64     `gorm:"column:id" db:"id" json:"id" form:"id"`
	ConferenceId int64     `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"` //展会ID
	CnName       string    `gorm:"column:cn_name" db:"cn_name" json:"cn_name" form:"cn_name"`                  //栏目名称-中文
	EnName       string    `gorm:"column:en_name" db:"en_name" json:"en_name" form:"en_name"`                  //栏目名称-英文
	SubSectionId int64     `gorm:"column:sub_section_id" db:"sub_section_id" json:"sub_section_id" form:"sub_section_id"`
	IsSubSection int       `gorm:"column:is_sub_section" db:"is_sub_section" json:"is_sub_section" form:"is_sub_section"`
	Sorting      float32   `gorm:"column:sorting" db:"sorting" json:"sorting" form:"sorting"` //排序
	Type         int       `gorm:"column:type" db:"type" json:"type" form:"type"`             //信息类型（1赞助商&合作商&媒体，2嘉宾）
	Deleted      int       `gorm:"column:deleted" json:"deleted"`
	CreateTime   time.Time `json:"create_time" gorm:"column:create_time" `
	UpdateTime   time.Time `json:"update_time" gorm:"update_time"`
	CreateAdmin  string    `json:"create_admin" gorm:"create_admin"`
	UpdateAdmin  string    `json:"update_admin" gorm:"update_admin"`
}

type ConferenceInformation struct {
	Id            int64     `gorm:"column:id" db:"id" json:"id" form:"id"`                                                 //会议嘉宾等信息
	ConferenceId  int64     `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"`            //展会ID
	ColumnId      int64     `gorm:"column:column_id" db:"column_id" json:"column_id" form:"column_id"`                     //栏目ID
	CnAppellation string    `gorm:"column:cn_appellation" db:"cn_appellation" json:"cn_appellation" form:"cn_appellation"` //称谓
	EnAppellation string    `gorm:"column:en_appellation" db:"en_appellation" json:"en_appellation" form:"en_appellation"` //称谓
	CnPosition    string    `gorm:"column:cn_position" db:"cn_position" json:"cn_position" form:"cn_position"`             //职位
	EnPosition    string    `gorm:"column:en_position" db:"en_position" json:"en_position" form:"en_position"`             //职位
	CnCompany     string    `gorm:"column:cn_company" db:"cn_company" json:"cn_company" form:"cn_company"`                 //公司
	EnCompany     string    `gorm:"column:en_company" db:"en_company" json:"en_company" form:"en_company"`                 //公司
	CnLink        string    `gorm:"column:cn_link" db:"cn_link" json:"cn_link" form:"cn_link"`                             //链接
	EnLink        string    `gorm:"column:en_link" db:"cn_link" json:"en_link" form:"en_link"`                             //链接
	CnPicture     string    `gorm:"column:cn_picture" db:"cn_picture" json:"cn_picture" form:"cn_picture"`                 //logo图-中文
	EnPicture     string    `gorm:"column:en_picture" db:"en_picture" json:"en_picture" form:"en_picture"`                 //logo图-英文
	Sorting       float32   `gorm:"column:sorting" db:"sorting" json:"sorting" form:"sorting"`                             //排序
	CnSorting     float32   `gorm:"column:cn_sorting" db:"cn_sorting" json:"cn_sorting" form:"cn_sorting"`                 //排序-中文
	EnSorting     float32   `gorm:"column:en_sorting" db:"en_sorting" json:"en_sorting" form:"en_sorting"`                 //排序-英文
	CnContent     string    `gorm:"column:cn_content" db:"cn_content"  json:"cn_content" form:"cn_content"`                //嘉宾介绍(或嘉宾身份)--中文
	EnContent     string    `gorm:"column:en_content" db:"en_content"  json:"en_content" form:"en_content"`                //嘉宾介绍(或嘉宾身份)--英文
	Type          int       `gorm:"column:type" db:"type" json:"type" form:"type"`                                         //信息类型（1赞助商，2嘉宾,3合作商,4媒体，9日程嘉宾）
	Deleted       int       `gorm:"column:deleted" db:"deleted" json:"deleted" form:"deleted"`
	CreateTime    time.Time `gorm:"column:create_time" db:"create_time" json:"create_time" form:"create_time"`
	UpdateTime    time.Time `json:"update_time" db:"update_time"`
	CreateAdmin   string    `json:"create_admin" db:"create_admin"`
	UpdateAdmin   string    `json:"update_admin" db:"update_admin"`
}

type ConferenceInformationEvent struct {
	Id            int64 `gorm:"column:id" db:"id" json:"id" form:"id"`                                                 //会议嘉宾等信息
	ConferenceId  int64 `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"`            //展会ID
	InformationId int64 `gorm:"column:information_id" db:"information_id" json:"information_id" form:"information_id"` //公司/嘉宾信息ID
	EventId       int64 `gorm:"column:event_id" db:"event_id" json:"event_id" form:"event_id"`                         //大会或活动ID（0是大会ID）
	Deleted       int   `gorm:"column:deleted" db:"deleted" json:"deleted" form:"deleted"`
}

type ConferenceEnterpriseType struct {
	Id           int64  `gorm:"column:id" db:"id" json:"id" form:"id"`                                      //会议嘉宾等信息
	ConferenceId int64  `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"` //展会ID
	Value        string `gorm:"column:value" db:"value" json:"value" form:"value"`
}
