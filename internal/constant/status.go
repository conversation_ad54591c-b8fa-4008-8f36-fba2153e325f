package constant

const (
	ComStatusEnable  = 10
	ComStatusDisable = -10
)

var StatusMap = map[int]string{
	ComStatusEnable:  "已启用",
	ComStatusDisable: "已禁用",
}

const (
	ShowStatusEnable  = 10  // 展示
	ShowStatusDisable = -10 // 隐藏
)

const (
	UserStatusLogin    = "UserLogin" // 用户已登录
	UserStatusNotLogin = "NotLogin"  // 用户未登录
)

const (
	UserLoginStatus = "LoginStatus" // 用户登录状态
	UserToken       = "UserToken"   // 用户登录token
	UserId          = "UserId"      // 用户Id
)

// 展会状态
const (
	ConferenceStatusOnline  = 1 // 启用
	ConferenceStatusOffline = 2 // 下线
	ConferenceStatusDeleted = 3 // 删除
)

// 往届状态
const (
	PreviousStatusDeleted = -1 // 删除
	PreviousStatusEnable  = 1  // 正常
)

// 发送状态
const (
	SendStatusSuccess  = 1 // 发送成功
	SendStatusFailure  = 2 // 发送失败
	SendStatusAbnormal = 3 // 数据异常
)

const (
	ClueStatusDislocate = iota // 未分配
	ClueStatusLocated          // 已分配
)

var ClueStatusMap = map[int]string{
	ClueStatusDislocate: "未分配",
	ClueStatusLocated:   "已分配",
}

// 状态
const (
	ConferenceStatus = 0
)
