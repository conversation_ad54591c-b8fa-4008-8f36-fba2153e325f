package constant

// 常量定义
const (
	AuthModuleComInfo = "company_info"
	AuthModuleComList = "company_list"
)

const (
	MaxPageLimit = 99999 // 每页最大限制
)

// 时间格式
const (
	DateTime = "2006-01-02 15:04:05"
	DateOnly = "2006-01-02"
	TimeOnly = "15:04:05"

	DateOnly2 = "1月.1.2"
)

const (
	UserClientIp = "user_client_ip" // 用户操作时客户端ip
)

const (
	AppConference   = "conference"    // SMM展会
	AppConferenceCn = "cn_conference" // 中文展会(用于订单)
	AppConferenceEn = "en_conference" // 英文展会(用于订单)
)

const (
	TopicHandBookData = "HandBookData" // 展商手册
)

const (
	AuthModuleInfo      = "manage_info" // 展会信息
	AuthModuleClue      = "manage_clue" // 展会线索
	AuthModuleSignUp    = "sign_up"     // 报名信息
	AuthModuleSubscribe = "subscribe"   // 订阅管理
)

// 展商手册发送方式
const (
	HandBookSendTypeCellphone = 1 // 手机发送
	HandBookSendTypeEmail     = 2 // 短信发送
)

var Alphabet = []string{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z"}

const (
	SearchDataTypeActivity = 1 // 活动
	SearchDataTypeCompany  = 2 // 展商
	SearchDataTypeGuest    = 3 // 嘉宾
)

const (
	IndexPoster = iota + 1
	AudiencePoster
	Company
	ConferenceDate
	AboutUs
	Guideline
	Previous
	Sponsor
	Ticket
	Search
	Speaker
)

const AdminInfoKey = "admin_user_info"

const (
	Cn = 0
	En = 1
)
