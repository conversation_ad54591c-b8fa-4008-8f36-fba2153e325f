package conf

import (
	"conferencecenter/internal/pkg/cache"
	"conferencecenter/internal/pkg/logger"
	"conferencecenter/internal/pkg/mysql"
	"fmt"
	"github.com/jinzhu/configor"
)

func Init(filePath string) (cfg *Configuration, err error) {
	cfg = new(Configuration)
	if err = configor.Load(cfg, filePath); err != nil {
		fmt.Printf("Unmarshal file:%s Failed:%v \n", filePath, err)
		return nil, err
	}

	return
}

type Configuration struct {
	ClientId   int            `yaml:"client_id"`
	App        AppConfig      `yaml:"app"`
	Server     Server         `yaml:"server"`
	RPC        Rpc            `yaml:"rpc"`
	Log        logger.Options `yaml:"log"`
	Data       Data           `yaml:"data"`
	Kafka      KafkaConf      `yaml:"kafka"`
	NotifyConf NotifyConf     `yaml:"notify_conf"`
	YtxConf    YtxConf        `yaml:"ytx_conf"`
	UCloudConf UCloudConf     `yaml:"ucloud_conf"`
	MailGun    MailGun        `yaml:"mail_gun"`
}

type MailGun struct {
	Key    string `yaml:"key"`
	Secret string `yaml:"secret"`
	Domain string `yaml:"domain"`
}
type AppConfig struct {
	Name      string `yaml:"name"`
	Env       string `yaml:"env"`
	Domain    string `yaml:"domain"`
	UrlPrefix string `yaml:"url_prefix"`
}

type Rpc struct {
	UserCenter       string `yaml:"user_center"`
	AdminCenter      string `yaml:"admin_center"`
	OrderCenter      string `yaml:"order_center"`
	MeetingCenter    string `yaml:"meeting_center"`
	VcodeCenter      string `yaml:"vcode_center"`
	VcodeCenterInner string `yaml:"vcode_center_inner"`
	NewsCenter       string `yaml:"news_center"`
}

type Public struct {
	Ip   string `yaml:"ip"`
	Port int    `yaml:"port"`
}

type Server struct {
	Http Http `yaml:"http"`
}

type Http struct {
	Public ServerConfig `yaml:"public"`
	Inner  ServerConfig `yaml:"inner"`
}

type ServerConfig struct {
	Host string `yaml:"host"`
	Port int    `yaml:"port"`
}

type Data struct {
	Mysql      mysql.Config `yaml:"mysql"`
	MysqlEvent mysql.Config `yaml:"mysql_event"`
	Redis      cache.Config `yaml:"redis"`
}

type Api struct {
	QiXin ApiConfig `yaml:"qixin"`
	Geo   ApiConfig `yaml:"geo"`
}

type ApiConfig struct {
	Address string `yaml:"address"`
	AppKey  string `yaml:"app_key"`
	Secret  string `yaml:"secret"`
}

type KafkaConf struct {
	Brokers    []string     `yaml:"brokers"`
	Conference KafkaConfSub `yaml:"conference"`
}

type KafkaConfSub struct {
	Brokers       []string `yaml:"brokers"`
	Topics        []string `yaml:"topics"`
	GroupId       string   `yaml:"group_id"`
	ProcessSwitch string   `yaml:"process_switch"` //处理开关
}

type YtxConf struct {
	HandBookTemplateId string `yaml:"handbook_template_id"`
}

type UCloudConf struct {
	ProjectId          string `yaml:"project_id"`
	HandBookTemplateId string `yaml:"handbook_template_id"`
	Sign               string `yaml:"sign"`
}

type NotifyConf struct {
	NotifyLevel int    `yaml:"notify_level"` //0 只通知异常，1 通知全部包含消息
	WechatAddr  string `yaml:"wechat_addr"`
	ProcessAddr string `yaml:"process_addr"` //kafka处理
	KafkaAddr   string `yaml:"kafka_addr"`   //kafka通知
}
