package protocol

import (
	"conferencecenter/internal/model"
	"encoding/json"
	"time"
)

// ConferenceNameItem 会议名称项，包含会议ID和会议名称
type ConferenceNameItem struct {
	ConferenceID   int64  `json:"conference_id"`   // 会议ID
	ConferenceName string `json:"conference_name"` // 会议名称
}

// ResAdminConferenceNameList 会议名称列表响应
type ResAdminConferenceNameList struct {
	List []ConferenceNameItem `json:"list"` // 会议名称列表
}

type ResOriginCom struct {
	Info ResPagination `json:"info"`
	List []QiXinItem   `json:"list"`
}

type ResQiXin struct {
	Status  string      `json:"status"`
	Message string      `json:"message"`
	Sign    string      `json:"sign"`
	Data    interface{} `json:"data"`
}

type ResQiXinSearch struct {
	Status  string       `json:"status"`
	Message string       `json:"message"`
	Sign    string       `json:"sign"`
	Data    ResQiXinData `json:"data"`
}

type ResQiXinData struct {
	Total int         `json:"total"`
	Num   int         `json:"num"`
	Items []QiXinItem `json:"items"`
}

type QiXinItem struct {
	Id         string `json:"-"`          // 企业编号
	Name       string `json:"name"`       // 公司名称
	RegNo      string `json:"reg_no"`     // 注册号
	StartDate  string `json:"start_date"` // 成立日期
	OperName   string `json:"oper_name"`  // 企业法人
	CreditNo   string `json:"credit_no"`  // 社会统一信用代码
	Type       int    `json:"-"`          // 企业类型，枚举值：0企业，4社团，5律所，6香港公司
	MatchType  string `json:"-"`          // 匹配类型
	MatchItems string `json:"-"`          // 匹配关键字
}

type ResQiXinBaseInfo struct {
	Status  string           `json:"status"`
	Message string           `json:"message"`
	Sign    string           `json:"sign"`
	Data    QiXinCompanyInfo `json:"data"`
}

type QiXinCompanyInfo struct {
	ID           string   `json:"id"`
	Name         string   `json:"name"`
	FormatName   string   `json:"format_name"`
	EconKind     string   `json:"econKind"`
	EconKindCode string   `json:"econKindCode"`
	RegistCapi   string   `json:"registCapi"` // 注册资本
	CurrencyUnit string   `json:"currency_unit"`
	TypeNew      string   `json:"type_new"` // 企业大类
	HistoryNames []string `json:"historyNames"`
	Address      string   `json:"address"`
	RegNo        string   `json:"regNo"`
	Scope        string   `json:"scope"`
	TermStart    string   `json:"termStart"`
	TermEnd      string   `json:"termEnd"`
	BelongOrg    string   `json:"belongOrg"`
	OperName     string   `json:"operName"` // 法人代表
	Title        string   `json:"title"`
	StartDate    string   `json:"startDate"`
	EndDate      string   `json:"endDate"`
	CheckDate    string   `json:"checkDate"`
	Status       string   `json:"status"`
	NewStatus    string   `json:"new_status"` // 经营状态
	OrgNo        string   `json:"orgNo"`
	CreditNo     string   `json:"creditNo"`
	DistrictCode string   `json:"districtCode"`
	ActualCapi   string   `json:"actualCapi"`
	CategoryNew  string   `json:"categoryNew"`
	Domain       string   `json:"domain"`
	Tags         []string `json:"tags"`
	RevokeReason string   `json:"revoke_reason"`
	LogoutReason string   `json:"logout_reason"` // 注销原因
	RevokeDate   string   `json:"revoke_date"`
	Fenname      string   `json:"fenname"` // 企业英文名
}

type ResQiXinEmployee struct {
	Status  string       `json:"status"`
	Message string       `json:"message"`
	Sign    string       `json:"sign"`
	Data    EmployeeData `json:"data"`
}

type EmployeeData struct {
	Total int64          `json:"total"`
	Items []EmployeeInfo `json:"items"`
}

type EmployeeInfo struct {
	Name  string `json:"name"`
	Title string `json:"title"`
}

type GeoCode struct {
	FormattedAddress string `json:"formatted_address"`
	Country          string `json:"country"`
	Province         string `json:"province"`
	City             string `json:"city"`
	District         string `json:"district"`
	Location         string `json:"location"`
	Level            string `json:"level"`
}

type ResGeo struct {
	Status   string    `json:"status"`
	Info     string    `json:"info"`
	Infocode string    `json:"infocode"`
	Count    string    `json:"count"`
	GeoCodes []GeoCode `json:"geocodes"`
}

// 展会列表
type ResAdminConferenceList struct {
	Info ResPagination       `json:"info"`
	List []ResConferenceInfo `json:"list"`
}

// 展会基本信息
type ResConferenceInfo struct {
	ID             int64  `json:"id"`               // 展会ID
	CnName         string `json:"cn_name"`          // 展会名称中文
	EnName         string `json:"en_name"`          // 展会名称英文
	CnShortName    string `json:"cn_short_name"`    // 展会简称中文
	EnShortName    string `json:"en_short_name"`    // 展会简称英文
	CnLocation     string `json:"cn_location"`      // 地点中文
	EnLocation     string `json:"en_location"`      // 地点英文
	StartTime      string `json:"start_time"`       // 开始时间
	EndTime        string `json:"end_time"`         // 结束时间
	Status         int    `json:"status"`           // 展会状态:1正常;2下线;3删除
	CnTopLeftLogo  string `json:"cn_top_left_logo"` // 左上角logo
	EnTopLeftLogo  string `json:"en_top_left_logo"` // 左上角logo
	TopLogo        string `json:"top_logo"`         // 页顶logo
	TinyLogo       string `json:"tiny_logo"`        // 小logo
	VideoBack      string `json:"video_back"`       // 视频背景
	PcBack         string `json:"pc_back"`          // PC背景图
	H5Back         string `json:"h5_back"`          // H5背景图
	CnShareGraph   string `json:"cn_share_graph"`   // 分享图链接中文
	EnShareGraph   string `json:"en_share_graph"`   // 分享图链接英文
	CnShareTitle   string `json:"cn_share_title"`   // 分享标题中文
	EnShareTitle   string `json:"en_share_title"`   // 分享标题英文
	CnShareContent string `json:"cn_share_content"` // 分享内容中文
	EnShareContent string `json:"en_share_content"` // 分享内容英文
	CnFloorGraph   string `json:"cn_floor_graph"`   //展馆平面图url
	EnFloorGraph   string `json:"en_floor_graph"`   //展馆平面图url
	//FloorGraph     string          `json:"floor_graph"`      // 展馆平面图url
	MeetingSysId  string          `json:"meeting_sys_id"`  // 同步会议系统编号
	QwxUrl        string          `json:"qwx_url"`         // 企业微信通知地址
	TemplateType  int             `json:"template_type"`   // 展会日程模版类型:1时间&主题;0议程&非议程
	CnAfterReport string          `json:"cn_after_report"` // 中文上届报告
	EnAfterReport string          `json:"en_after_report"` // 英文上届报告
	ButtonLi      []ResButtonInfo `json:"button_li"`       // 跳转按钮
	CreateUser    string          `json:"create_user"`     // 创建人
	UpdateUser    string          `json:"update_user"`     // 更新人
	CreateTime    string          `json:"create_time"`     // 创建时间
	UpdateTime    string          `json:"update_time"`     // 更新时间
}

// 封面按钮信息
type ResButtonInfo struct {
	ID           int64  `json:"id"`
	ConferenceID int64  `json:"-"`       // 展会ID
	CnName       string `json:"cn_name"` // 按钮名称中文
	EnName       string `json:"en_name"` // 按钮名称英文
	CnURL        string `json:"cn_url"`  // 跳转链接
	EnURL        string `json:"en_url"`  // 跳转链接英文
	CreateUser   string `json:"-"`       // 创建人
	UpdateUser   string `json:"-"`       // 更新人
	CreateTime   string `json:"-"`       // 创建时间
	UpdateTime   string `json:"-"`       // 更新时间
}

type ResMapInfo struct {
	ShortName    string `json:"short_name"`    // 展会简称
	ConferenceID int64  `json:"conference_id"` // 展会ID
}

// 往届列表
type ResAdminPreviousList struct {
	Info ResPagination             `json:"info"`
	List []AdminConferencePrevious `json:"list"`
}

// 往届展会
type AdminConferencePrevious struct {
	ID           int64  `json:"id"`
	ConferenceID int64  `json:"conference_id"` // 展会ID
	CnName       string `json:"cn_name"`       // 名称中文
	EnName       string `json:"en_name"`       // 名称英文
	CnURL        string `json:"cn_url"`        // 链接中文
	EnURL        string `json:"en_url"`        // 链接英文
	CnPdf        string `json:"cn_pdf"`        // 链接中文
	EnPdf        string `json:"en_pdf"`        // 链接英文
	Sort         string `json:"sort"`          // 排序
	CreateUser   string `json:"create_user"`   // 创建人
	UpdateUser   string `json:"update_user"`   // 更新人
	CreateTime   string `json:"create_time"`   // 创建时间
	UpdateTime   string `json:"update_time"`   // 更新时间
}

// 订阅列表
type ResAdminSubscribeList struct {
	Info ResPagination              `json:"info"`
	List []ResHandBookSubscribeInfo `json:"list"`
}

type ResHandBookSubscribeInfo struct {
	ID               int64  `json:"id"`
	ConferenceID     int64  `json:"conference_id" excel_smm:"展会id,B"`      // 展会ID
	CnConferenceName string `json:"cn_conference_name" excel_smm:"展会名称,A"` // 展会名称中文
	EnConferenceName string `json:"en_conference_name" `                   // 展会名称英文
	Cellphone        string `json:"cellphone" excel_smm:"手机,C"`            // 手机
	Email            string `json:"email" excel_smm:"邮箱,D"`                // 邮箱
	CreateTime       string `json:"create_time" excel_smm:"提交时间,E"`        // 提交时间
	UpdateTime       string `json:"-"`
}

// 往届列表
type ResUserPreviousList struct {
	Info ResPagination      `json:"info"`
	List []UserPreviousInfo `json:"list"`
}

// 往届展会
type UserPreviousInfo struct {
	ID     int64   `json:"id"`
	CnName string  `json:"cn_name"` // 名称中文
	EnName string  `json:"en_name"` // 名称英文
	CnURL  string  `json:"cn_url"`  // 链接中文
	URLEn  string  `json:"en_url"`  // 链接英文
	CnPdf  string  `json:"cn_pdf"`  // PDF中文
	EnPdf  string  `json:"en_pdf"`  // PDF英文
	Sort   float32 `json:"sort"`    // 排序
}

// 观众预登记
type ResAudiencePreRegister struct {
	ConferenceId    int64  `json:"conference_id"`     //展会id
	CnTitle         string `json:"cn_title"`          //中文页面标题
	EnTitle         string `json:"en_title"`          //英文页面标题
	CnHandbookName  string `json:"cn_handbook_name"`  //中文手册名称
	EnHandbookName  string `json:"en_handbook_name"`  //英文手册名称
	CnVisitingValue string `json:"cn_visiting_value"` //中文参观价值
	EnVisitingValue string `json:"en_visiting_value"` //英文参观价值
	CnUrl           string `json:"cn_url"`            //中文观众手册url
	EnUrl           string `json:"en_url"`            //英文观众手册url
}

// 观众-参观价值
type ResAudienceVisitingInfo struct {
	ConferenceId    int64  `json:"conference_id"`     //展会id
	CnVisitingValue string `json:"cn_visiting_value"` //中文参观价值
	EnVisitingValue string `json:"en_visiting_value"` //英文参观价值
}

// CompanyManagement 展商管理后台
type CompanyManagement struct {
	Id                      int64  `json:"id"`                                                                                                 //主键id
	ConferenceId            int64  `json:"conference_id"`                                                                                      //展会id
	CnConferenceCompany     string `json:"cn_conference_company"`                                                                              //中文展商名称
	EnConferenceCompany     string `json:"en_conference_company"`                                                                              //英文展商名称
	CnBoothNumber           string `json:"cn_booth_number"`                                                                                    //中文展位号
	EnBoothNumber           string `json:"en_booth_number"`                                                                                    //英文展位号
	CnUrl                   string `json:"cn_url"`                                                                                             //中文展商链接
	EnUrl                   string `json:"en_url"`                                                                                             //英文展商链接
	Logo                    string `json:"logo"`                                                                                               //logo的url
	CnExhibitorVideoLink    string `json:"cn_exhibitor_video_link" form:"cn_exhibitor_video_link" gorm:"column:cn_exhibitor_video_link"`       //中文展商视频
	CnExhibitorVideoCover   string `json:"cn_exhibitor_video_cover" form:"cn_exhibitor_video_cover" gorm:"column:cn_exhibitor_video_cover"`    //中文展商视频封面
	CnExhibitorNewsID       int64  `json:"cn_exhibitor_news_id" form:"cn_exhibitor_news_id" gorm:"column:cn_exhibitor_news_id"`                //中文展商新闻id
	CnExhibitorIntroduction string `json:"cn_exhibitor_introduction" form:"cn_exhibitor_introduction" gorm:"column:cn_exhibitor_introduction"` //中文展商介绍
	CnExhibitorVenueId      int    `json:"cn_exhibitor_venue_id" form:"cn_exhibitor_venue_id" gorm:"column:cn_exhibitor_venue_id"`             //展馆id
	Sort                    string `json:"sort"`                                                                                               //排序
	Year                    string `json:"year" form:"year" gorm:"column:year"`                                                                // 年度

}

type ProductionManagement struct {
	ID                int64   `json:"id" gorm:"id" form:"id"`
	AudienceCompanyId int64   `json:"audience_company_id" gorm:"audience_company_id" form:"audience_company_id"`
	CnName            string  `json:"cn_name" gorm:"cn_name" form:"cn_name"` // 中文产品名称
	EnName            string  `json:"en_name" gorm:"en_name" form:"en_name"` // 英文产品名称
	Logo              string  `json:"logo" gorm:"logo" form:"logo"`
	Sorting           float32 `json:"sorting" gorm:"sorting" form:"sorting"` // 排序
}

type ResPageProductionManagement struct {
	Info ResPagination          `json:"info"`
	List []ProductionManagement `json:"list"`
}

type Option struct {
	Choice   string  `json:"choice"`    // 选项文案
	NeedNote bool    `json:"need_note"` // 是否需要补充内容（展示问卷用）
	Sorting  float32 `json:"sorting" `  // 排序（展示问卷用）
	NeedShow bool    `json:"need_show"` // 是否需要列表页展示
}

type QuestionAndChoice struct {
	Number       int      `json:"number"`        //序号
	Question     string   `json:"question"`      //问题内容
	Sorting      float32  `json:"sorting" `      //排序
	NeedAnswer   bool     `json:"need_answer"`   //是否必答
	QuestionType int      `json:"question_type"` //问题类型 1 单选 2 多选 3 填空
	Choice       []Option `json:"choice"`        //选项
}

type QuestionPaper struct {
	//PaperName             string              `json:"paper_name"` //问卷名称
	QuestionAndChoiceList []QuestionAndChoice //问题和答案
}

type ResFreeVisitorInfo struct {
	Info ResPagination `json:"info"` //
	List []FreeVisitor `json:"list"`
}
type FreeVisitor struct {
	CnVisitor CnVisitor `json:"cn_visitor"`
	EnVisitor EnVisitor `json:"en_visitor"`
}

type CnVisitor struct {
	CnConferenceName string `json:"cn_conference_name" excel_smm:"展会名称,A"`                               // 中文展会名称
	Source           string `json:"source" excel_smm:"来源,B"`                                             // 来源
	Name             string `json:"name" form:"name" valid:"required~名字必填" excel_smm:"姓名,C"`             // 姓名
	Company          string `json:"cn_company" form:"cn_company" valid:"required~公司必填" excel_smm:"公司,D"` // 公司
	Department       string `json:"department" form:"department" valid:"required~部门必填" excel_smm:"部门,E"` // 部门
	JobTitle         string `json:"job_title" form:"job_title" excel_smm:"职位,F"`                         // 职位
	Telephone        string `json:"telephone" form:"telephone" valid:"required~电话必填" excel_smm:"手机号,G"`  // 移动电话
	VerificationCode string `json:"verification_code" form:"verification_code" valid:"required~验证码必填"`   // 验证码
	Email            string `json:"email" form:"email" excel_smm:"邮箱,H" `                                // 邮箱
	Willing          string `json:"willing" form:"willing" excel_smm:"是否愿意参与免费组团参观计划,I"`                 // 是否愿意参与免费组团参观计划
	SubmitTime       string `json:"submitTime" form:"submit_time" excel_smm:"报名时间,J" `                   //报名时间
	PaperAnswer      string `json:"papaer_answer" form:"papaer_answer" `                                 // 问卷答案
	FromID           string `json:"fromId" gorm:"column:fromId" form:"fromId"`                           // 渠道
	FromName         string `json:"from_name" form:"from_name" excel_smm:"渠道,L"`
	IdNo             string `json:"id_no" gorm:"column:id_no" form:"id_no"`                        // 身份证号
	MainProducts     string `json:"main_products" form:"main_products" excel_smm:"主推产品,M"`         // 主推产品
	EnterpriseType   string `json:"enterprise_type" form:"enterprise_type" excel_smm:"企业类型,N"`     // 企业类型
	ProcurementItems string `json:"procurement_items" form:"procurement_items" excel_smm:"采购产品,O"` // 采购产品
	OrderQuantity    string `json:"order_quantity" form:"order_quantity" excel_smm:"采购数量,P"`       // 采购数量

}

type EnVisitor struct {
	EnConferenceName string `json:"en_conference_name" excel_smm:"展会名称,A"`                                                 // 中文展会名称
	Source           string `json:"source" excel_smm:"来源,B"`                                                               // 来源
	Email            string `json:"email" form:"email" valid:"required~Email required"`                                    // 邮箱
	FirstName        string `json:"first_name" form:"first_name" valid:"required~First Name required"`                     // 名
	LastName         string `json:"last_name" form:"last_name" valid:"required~Last Name required"`                        // 姓
	FullName         string `json:"full_name" form:"full_name" excel_smm:"姓名,C"`                                           //姓名
	Company          string `json:"company" form:"company" valid:"required~Company required" excel_smm:"公司,D"`             // 公司
	JobTitle         string `json:"job_title" form:"job_title" excel_smm:"职位,F"`                                           // 职位
	CountryResidence string `json:"country_residence" form:"country_residence" excel_smm:"居住国,H"`                          // 现居国家
	City             string `json:"city" form:"city" excel_smm:"城市,I" `                                                    // 城市
	Phone            string `json:"phone" form:"phone" `                                                                   // 电话
	Telephone        string `json:"telephone" form:"telephone" valid:"required~Mobile Number required" excel_smm:"手机号,G"`  // 移动电话
	Nationality      string `json:"nationality" form:"nationality" valid:"required~Nationality required" excel_smm:"国籍,J"` // 国家
	MainFocus        string `json:"main_focus" form:"main_focus" excel_smm:"行业,E"`                                         // 关注行业
	SubmitTime       string `json:"submitTime" form:"submit_time" excel_smm:"报名时间,K" `                                     // 报名时间
	FromID           string `json:"fromId" gorm:"column:fromId" form:"fromId"`                                             // 渠道
	FromName         string `json:"from_name" form:"from_name" excel_smm:"渠道,L"`
	MainProducts     string `json:"main_products" form:"main_products" gorm:"column:main_products"`             // 主推产品
	EnterpriseType   string `json:"enterprise_type" form:"enterprise_type" gorm:"column:enterprise_type"`       // 企业类型
	ProcurementItems string `json:"procurement_items" form:"procurement_items" gorm:"column:procurement_items"` // 采购产品
	OrderQuantity    string `json:"order_quantity" form:"order_quantity" gorm:"column:order_quantity"`          // 采购数量
}

// Venue 场馆管理
type Venue struct {
	ID           int64  `json:"id" gorm:"id" form:"id"`
	ConferenceID int64  `json:"conference_id" gorm:"conference_id" form:"conference_id"`
	CnVenueName  string `json:"cn_venue_name" gorm:"cn_venue_name" form:"cn_venue_name"` // 中文场馆名称
	EnVenueName  string `json:"en_venue_name" gorm:"en_venue_name" form:"en_venue_name"` // 英文场馆名称
	Sorting      string `json:"sorting" gorm:"sorting" form:"sorting"`                   // 排序
}

type ResSubmitFreeVisitor struct {
	ID          int64  `json:"id"`           // 主键
	Company     string `json:"company"`      // 公司
	Name        string `json:"name"`         // 名称
	StartTime   string `json:"start_time"`   // 开始时间
	EndTime     string `json:"end_time"`     // 结束时间
	Location    string `json:"location"`     // 会议地点
	CodePhoto   string `json:"code_photo"`   // 二维码
	ErrorString string `json:"error_string"` // 错误信息
}

type ResSubmitQuestionPaper struct {
	Company     string `json:"company"`      // 公司
	Name        string `json:"name"`         // 名称
	StartTime   string `json:"start_time"`   // 开始时间
	EndTime     string `json:"end_time"`     // 结束时间
	Location    string `json:"location"`     // 会议地点
	CodePhoto   string `json:"code_photo"`   // 二维码
	ErrorString string `json:"error_string"` // 错误信息
}

type ResCompanyManagement struct {
	Info ResPagination       `json:"info"`
	List []CompanyManagement `json:"list"`
}

type CompanyBearing struct {
	ConferenceId          int64  `json:"conference_id"`                                                                                   // 展会id
	CnExhibitorVideoLink  string `json:"cn_exhibitor_video_link" form:"cn_exhibitor_video_link" gorm:"column:cn_exhibitor_video_link"`    // 中文展商视频
	CnExhibitorVideoCover string `json:"cn_exhibitor_video_cover" form:"cn_exhibitor_video_cover" gorm:"column:cn_exhibitor_video_cover"` // 中文展商视频封面
	CnExhibitorNewsID     int64  `json:"cn_exhibitor_news_id" form:"cn_exhibitor_news_id" gorm:"column:cn_exhibitor_news_id"`             // 中文展商新闻id
	CnNews                string `json:"cn_news"`                                                                                         // 中文新闻
	CnNewsTitle           string `json:"cn_news_title"`                                                                                   // 中文新闻标题
	CnBoothNumber         string `json:"cn_booth_number"`                                                                                 // 中文展位号
	EnBoothNumber         string `json:"en_booth_number"`                                                                                 // 英文展位号
	CnExhibitorVenueID    int    `json:"cn_exhibitor_venue_id" form:"cn_exhibitor_venue_id" gorm:"column:cn_exhibitor_venue_id"`          // 展馆id
	CnExhibitorVenueName  string `json:"cn_exhibitor_venue_name" `                                                                        // 场馆名称
	PubTime               string `json:"pub_time"`                                                                                        // 发布时间
	CnNewsURL             string `json:"cn_news_url"`                                                                                     // 中文新闻链接
	Source                string `json:"source"`                                                                                          // 来源
	CnContent             string `json:"cn_content"`                                                                                      // 中文正文内容
	Logo                  string `json:"logo" form:"logo" gorm:"column:logo"`                                                             // logo的url
}

type ConferenceNews struct {
	CnNews            string `json:"cn_news"`                                                                             // 中文新闻
	CnNewsTitle       string `json:"cn_news_title"`                                                                       // 中文新闻标题
	PubTime           string `json:"pub_time"`                                                                            // 发布时间
	Thumb             string `json:"thumb"`                                                                               // 新闻封面
	CnNewsURL         string `json:"cn_news_url"`                                                                         // 中文新闻链接
	Source            string `json:"source"`                                                                              // 来源
	CnExhibitorNewsID int64  `json:"cn_exhibitor_news_id" form:"cn_exhibitor_news_id" gorm:"column:cn_exhibitor_news_id"` // 中文展商新闻id
}

type IndustryNews struct {
	CnNews      string `json:"cn_news"`       // 中文新闻
	CnNewsTitle string `json:"cn_news_title"` // 中文新闻标题
	PubTime     string `json:"pub_time"`      // 发布时间
	Thumb       string `json:"thumb"`         // 新闻封面
	CnNewsURL   string `json:"cn_news_url"`   // 中文新闻链接
	Source      string `json:"source"`        // 来源
	NewsID      int    `json:"news_id"`       // 新闻id
}

type ResIndustryInfo struct {
	CompanyBearingList []CompanyBearing `json:"company_bearing_list"`
	ConferenceNewsList []ConferenceNews `json:"conference_news"`
	IndustryNewsList   []IndustryNews   `json:"industry_news_list"`
}

type IndustryFocus struct {
	ID           int64  `json:"id"`
	IndustryName string `json:"industry_name"`
}

type ResCompanyBearingList struct {
	Info ResPagination    `json:"info"`
	List []CompanyBearing `json:"list"`
}

type CompanyDirectories struct {
	Id                      int64                  `json:"id"`                                                                                                  // 展商主键id
	CnConferenceCompany     string                 `json:"cn_conference_company"`                                                                               // 中文展商名称
	EnConferenceCompany     string                 `json:"en_conference_company"`                                                                               // 英文展商名称
	CnBoothNumber           string                 `json:"cn_booth_number"`                                                                                     // 中文展位号
	EnBoothNumber           string                 `json:"en_booth_number"`                                                                                     // 英文展位号
	CnUrl                   string                 `json:"cn_url"`                                                                                              // 中文展商链接
	EnUrl                   string                 `json:"en_url"`                                                                                              // 英文展商链接
	VenueId                 int64                  `json:"venue_id" form:"venue_id"`                                                                            //场馆id
	CnVenueName             string                 `json:"cn_venue_name" gorm:"cn_venue_name" form:"cn_venue_name"`                                             // 中文场馆名称
	EnVenueName             string                 `json:"en_venue_name" gorm:"en_venue_name" form:"en_venue_name"`                                             // 英文场馆名称
	CnExhibitorIntroduction string                 `json:"cn_exhibitor_introduction" gorm:"column:cn_exhibitor_introduction" form:"cn_exhibitor_introduction" ` // 中文展商介绍
	Logo                    string                 `json:"logo" form:"logo" gorm:"column:logo"`                                                                 // logo的url
	Year                    string                 `json:"year" form:"year" gorm:"column:year"`                                                                 // 年度
	ProductionList          []ProductionManagement `json:"production_list"`                                                                                     //新品推荐
}

type ResCompanyDirectories struct {
	Info ResPagination        `json:"info"`
	List []CompanyDirectories `json:"list"`
}

type ResCompanyList struct {
	VenueId     int64  `json:"venue_id"`
	CnVenueName string `json:"cn_venue_name"`
	EnVenueName string `json:"en_venue_name"`
	ResCompanyDirectories
}

type FloorGraph struct {
	Id          int64  `json:"id"`            //id
	CnGraphUrl  string `json:"cn_graph_url"`  //中文平面图url
	EnGraphUrl  string `json:"en_graph_url"`  //英文平面图url
	CnGraphName string `json:"cn_graph_name"` //中文展馆名称
	EnGraphName string `json:"en_graph_name"` //英文展馆名称
	Sort        string `json:"sort"`          //排序
}

type ResPageFloorGraph struct {
	Info ResPagination `json:"info"`
	List []FloorGraph  `json:"list"` //展览平面图
}

type ResCommonQuestionType struct {
	Info ResPagination        `json:"info"`
	List []CommonQuestionType `json:"list"`
}

type CommonQuestionAnswer struct {
	Id                int64   `json:"id"`                                              //id
	CnQuestionContent string  `json:"cn_question_content"`                             //问题内容中文
	CnQuestionAnswer  string  `json:"cn_question_answer"`                              //答案内容中文
	CnQuestionType    string  `json:"cn_question_type" form:"cn_question_type"`        //展商问题类型中文
	EnQuestionType    string  `json:"en_question_type" form:"en_question_type" `       //展商问题类型英文
	EnQuestionContent string  `json:"en_question_content" form:"en_question_content" ` //问题英文
	EnQuestionAnswer  string  `json:"en_question_answer" form:"en_question_answer" `   //问题英文
	Sort              float32 `json:"sort"`                                            //排序
}

type ResCommonQuestionAnswer struct {
	Info ResPagination          `json:"info"`
	List []CommonQuestionAnswer `json:"list"`
}

type AfterReport struct {
	Id           int64  `json:"id"`                                 //id
	ConferenceId int64  `json:"conference_id" gorm:"conference_id"` // 展会id
	CnName       string `json:"cn_name" gorm:"cn_name"`             // 展后报告名称
	EnName       string `json:"en_name" gorm:"en_name"`             // 展后报告英文名称
	CnPdf        string `json:"cn_pdf" gorm:"cn_pdf"`               // 中文展后报告pdf
	EnPdf        string `json:"en_pdf" gorm:"en_pdf"`               // 英文展后报告pdf
	Sort         string `json:"sort" gorm:"sort"`                   // 排序
}

// 前端常见问题响应
type ResCommonQA struct {
	QuestionTypes []QuestionType `json:"cn_question_types"` //问题类型结构体
}

type QuestionType struct {
	TypeId int64  `json:"type_id"` //问题类型id
	CnType string `json:"cn_type"` //中文问题类型
	EnType string `json:"en_type"` //英文问题类型
	QAs    []QA   `json:"q_as"`    //问答内容
}
type UserAudienceQuestionList []QuestionType

type QA struct {
	Id         int64  `json:"id"`          //id
	CnQuestion string `json:"cn_question"` //中文问题
	EnQuestion string `json:"en_question"` //英文问题
	CnAnswer   string `json:"cn_answer"`   //中文答案
	EnAnswer   string `json:"en_answer"`   //英文答案
}

type ResCompanyHandbook struct {
	Id           int64  `json:"id"`            //id
	ConferenceId int64  `json:"conference_id"` //展会id
	CnName       string `json:"cn_name"`       //中文展商手册名称
	EnName       string `json:"en_name"`       //英文展商手册名称
	CnPdf        string `json:"cn_pdf"`        //中文pdf
	EnPdf        string `json:"en_pdf"`        //英文pdf
	Sort         string `json:"sort"`          //排序
}

type ResCompanyHandbookList struct {
	Info ResPagination        `json:"info"`
	List []ResCompanyHandbook `json:"list"`
}

// ResCompanyQuestionTypeList 展商常见问题类型列表
type ResCompanyQuestionTypeList struct {
	Info ResPagination        `json:"info"`
	List []CommonQuestionType `json:"list"` //list
}

// ResCompanyQuestionContentList 展商常见问题内容列表
type ResCompanyQuestionContentList struct {
	Info ResPagination     `json:"info"`
	List []QuestionContent `json:"list"`
}

type PaperCollection struct {
	ID           int64  `json:"id" gorm:"id" form:"id" `
	ConferenceId int64  `json:"conference_id" gorm:"conference_id" form:"conference_id" `    // 展会id
	CnFormUrl    string `json:"cn_form_url" gorm:"cn_form_url" form:"cn_form_url" `          // 中文表单链接
	EnFormUrl    string `json:"en_form_url" gorm:"en_form_url" form:"en_form_url" `          // 英文表单链接
	CnContent    string `json:"cn_content" gorm:"cn_content" form:"cn_content" `             // 中文页面内容
	EnContent    string `json:"en_content" gorm:"en_content" form:"en_content" `             // 中文页面内容
	CnButtonName string `json:"cn_button_name" gorm:"cn_button_name" form:"cn_button_name" ` // 中文按钮名称
	EnButtonName string `json:"en_button_name" gorm:"en_button_name" form:"en_button_name" ` // 英文按钮名称
	CnAboveName  string `json:"cn_above_name" gorm:"cn_above_name" form:"cn_above_name" `    // 中文按钮上方名称
	EnAboveName  string `json:"en_above_name" gorm:"en_above_name" form:"en_above_name" `    // 英文按钮上方名称
}

type AuthorNotice struct {
	ID           int64  `json:"id" gorm:"id" form:"id" `
	ConferenceId int64  `json:"conference_id" gorm:"conference_id" form:"conference_id" `    // 展会id
	CnFormUrl    string `json:"cn_form_url" gorm:"cn_form_url" form:"cn_form_url" `          // 中文表单链接
	EnFormUrl    string `json:"en_form_url" gorm:"en_form_url" form:"en_form_url" `          // 英文表单链接
	CnContent    string `json:"cn_content" gorm:"cn_content" form:"cn_content" `             // 中文页面内容
	EnContent    string `json:"en_content" gorm:"en_content" form:"en_content" `             // 中文页面内容
	CnButtonName string `json:"cn_button_name" gorm:"cn_button_name" form:"cn_button_name" ` // 中文按钮名称
	EnButtonName string `json:"en_button_name" gorm:"en_button_name" form:"en_button_name" ` // 英文按钮名称
	CnAboveName  string `json:"cn_above_name" gorm:"cn_above_name" form:"cn_above_name" `    // 中文按钮上方名称
	EnAboveName  string `json:"en_above_name" gorm:"en_above_name" form:"en_above_name" `    // 英文按钮上方名称
}
type ResContactUs struct {
	Id           int64  `json:"id"`            //主键id
	ConferenceId int64  `json:"conference_id"` //展会id
	CnContact    string `json:"cn_contact"`    //联系我们内容-中文
	EnContact    string `json:"en_contact"`    //联系我们内容-英文
	Sort         int    `json:"sort"`          //排序
	CreateUser   string `json:"create_user,omitempty"`
	UpdateUser   string `json:"update_user,omitempty"`
	CreateTime   string `json:"create_time,omitempty"`
	UpdateTime   string `json:"update_time,omitempty"`
}

// 联系我们列表返回体
type ResContactUsList struct {
	Info ResPagination  `json:"info"`
	List []ResContactUs `json:"list"`
}

// 展会线索
type ConferenceClue struct {
	Id               int64  `json:"id"`                                    //主键ID
	ConferenceId     int64  `json:"conference_id" excel_smm:"展会ID,C"`      //展会id
	UserId           int64  `json:"user_id" excel_smm:"用户ID,D"`            //用户id
	CnConferenceName string `json:"cn_conference_name" excel_smm:"展会名称,A"` //展会名称中文
	EnConferenceName string `json:"en_conference_name"`                    //展会名称英文
	CnShortName      string `json:"cn_short_name"`                         //展会简称中文
	EnShortName      string `json:"en_short_name"`                         //展会简称英文
	Name             string `json:"name" excel_smm:"姓名,B"`                 //姓名
	CellPhone        string `json:"cellphone" excel_smm:"手机,H"`            //手机号
	Email            string `json:"email" excel_smm:"邮箱,G"`                //邮箱
	Company          string `json:"company" excel_smm:"公司,E"`              //公司
	JobTitle         string `json:"job_title" excel_smm:"职位,F"`            //职位
	Country          string `json:"country" excel_smm:"国家,L"`              //国家
	ExhibitionArea   string `json:"exhibition_area" excel_smm:"感兴趣的展区,I"`  //感兴趣的展区
	BoothType        string `json:"booth_type" excel_smm:"展位类型,M"`         //展位类型
	Status           int    `json:"status" excel_smm:"分配状态,N"`             //分配状态,1已分配,0未分配
	CreateTime       string `json:"create_time" excel_smm:"提交时间,O"`        //提交时间
	SourceName       string `json:"source_name" excel_smm:"来源,P"`          //来源
	FromId           string `json:"fromId"`                                //渠道
}

// 展会线索
type ConferenceExhibition struct {
	Id               int64  `json:"id"`                                                                   //主键ID
	ConferenceId     int64  `json:"conference_id" excel_smm:"展会ID,C"`                                     //展会id
	UserId           int64  `json:"user_id" excel_smm:"用户ID,D"`                                           //用户id
	CnConferenceName string `json:"cn_conference_name" excel_smm:"展会名称,A"`                                //展会名称中文
	EnConferenceName string `json:"en_conference_name"`                                                   //展会名称英文
	CnShortName      string `json:"cn_short_name"`                                                        //展会简称中文
	EnShortName      string `json:"en_short_name"`                                                        //展会简称英文
	Name             string `json:"name" excel_smm:"姓名,B"`                                                //姓名
	CellPhone        string `json:"cellphone" excel_smm:"手机,G"`                                           //手机号
	Email            string `json:"email" excel_smm:"邮箱,F"`                                               //邮箱
	Company          string `json:"company" excel_smm:"公司,E"`                                             //公司
	ExhibitionArea   string `json:"exhibition_area" excel_smm:"感兴趣的展区,H"`                                 //感兴趣的展区
	Status           int    `json:"status" excel_smm:"分配状态,M"`                                            //分配状态,1已分配,0未分配
	CreateTime       string `json:"create_time" excel_smm:"提交时间,K"`                                       //提交时间
	SourceName       string `json:"source_name" excel_smm:"来源,I"`                                         //来源
	FromId           string `json:"fromId" `                                                              //渠道
	FromName         string `json:"from_name" excel_smm:"渠道名称,J"`                                         // 渠道名称
	Country          string `json:"country" excel_smm:"国家,L"`                                             //国家
	MainProducts     string `json:"main_products" form:"main_products" gorm:"column:main_products"`       // 主推产品
	EnterpriseType   string `json:"enterprise_type" form:"enterprise_type" gorm:"column:enterprise_type"` // 企业类型

}

// 展会线索
type ConferenceExhibitionExport struct {
	Id               int64  `json:"id"`                                    //主键ID
	ConferenceId     int64  `json:"conference_id" excel_smm:"展会ID,C"`      //展会id
	UserId           int64  `json:"user_id" excel_smm:"用户ID,D"`            //用户id
	CnConferenceName string `json:"cn_conference_name" excel_smm:"展会名称,A"` //展会名称中文
	EnConferenceName string `json:"en_conference_name"`                    //展会名称英文
	CnShortName      string `json:"cn_short_name"`                         //展会简称中文
	EnShortName      string `json:"en_short_name"`                         //展会简称英文
	Name             string `json:"name" excel_smm:"姓名,B"`                 //姓名
	CellPhone        string `json:"cellphone" excel_smm:"手机,G"`            //手机号
	Email            string `json:"email" excel_smm:"邮箱,F"`                //邮箱
	Company          string `json:"company" excel_smm:"公司,E"`              //公司
	ExhibitionArea   string `json:"exhibition_area" excel_smm:"感兴趣的展区,H"`  //感兴趣的展区
	MainProducts     string `json:"main_products" excel_smm:"主推产品,I"`      // 主推产品
	EnterpriseType   string `json:"enterprise_type" excel_smm:"企业类型,J"`    // 企业类型
	Status           string `json:"status" excel_smm:"分配状态,M"`             //分配状态,1已分配,0未分配
	CreateTime       string `json:"create_time" excel_smm:"提交时间,N"`        //提交时间
	SourceName       string `json:"source_name" excel_smm:"来源,K"`          //来源
	FromId           string `json:"fromId" `                               //渠道
	FromName         string `json:"from_name" excel_smm:"渠道名称,L"`          //渠道名称
}

// 展会线索列表
type ResConferenceClueList struct {
	Info ResPagination    `json:"info"`
	List []ConferenceClue `json:"list"`
}
type ResAdminColumnList struct {
	Info ResPagination      `json:"info"`
	List []ConferenceColumn `json:"list"` //赞助商，合作方，媒体信息
}

type ConferenceColumn struct {
	Id           int64     `gorm:"column:id" db:"id" json:"id" form:"id"`
	ConferenceId int64     `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"` //展会ID
	CnName       string    `gorm:"column:cn_name" db:"cn_name" json:"cn_name" form:"cn_name"`                  //栏目名称-中文
	EnName       string    `gorm:"column:en_name" db:"en_name" json:"en_name" form:"en_name"`                  //栏目名称-英文
	SubSectionId int64     `gorm:"column:sub_section_id" db:"sub_section_id" json:"sub_section_id" form:"sub_section_id"`
	IsSubSection int       `gorm:"column:is_sub_section" db:"is_sub_section" json:"is_sub_section" form:"is_sub_section"`
	Sorting      string    `gorm:"column:sorting" db:"sorting" json:"sorting" form:"sorting"` //排序
	Type         int       `gorm:"column:type" db:"type" json:"type" form:"type"`             //信息类型（1赞助商&合作商&媒体，2嘉宾）
	Deleted      int       `gorm:"column:deleted" json:"deleted"`
	CreateTime   time.Time `json:"create_time" gorm:"column:create_time" `
	UpdateTime   time.Time `json:"update_time" gorm:"update_time"`
	CreateAdmin  string    `json:"create_admin" gorm:"create_admin"`
	UpdateAdmin  string    `json:"update_admin" gorm:"update_admin"`
}

// 展会线索列表
type ResConferenceExhibitionList struct {
	Info ResPagination          `json:"info"`
	List []ConferenceExhibition `json:"list"`
}

type ConferenceColumnInfo struct {
	Id                        int64                         `gorm:"column:id" db:"id" json:"id" form:"id"`
	ConferenceId              int64                         `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"` //展会ID
	CnName                    string                        `gorm:"column:cn_name" db:"cn_name" json:"cn_name" form:"cn_name"`                  //栏目名称-中文
	EnName                    string                        `gorm:"column:en_name" db:"en_name" json:"en_name" form:"en_name"`                  //栏目名称-英文
	SubSectionId              int64                         `gorm:"column:sub_section_id" db:"sub_section_id" json:"sub_section_id" form:"sub_section_id"`
	IsSubSection              int                           `gorm:"column:is_sub_section" db:"is_sub_section" json:"is_sub_section" form:"is_sub_section"`
	Sorting                   float32                       `gorm:"column:sorting" db:"sorting" json:"sorting" form:"sorting"` //排序
	Type                      int                           `gorm:"column:type" db:"type" json:"type" form:"type"`             //信息类型（1赞助商&合作商&媒体，2嘉宾）
	ConferenceColumnList      []ConferenceColumnInfo        `json:"sun_column_list"`                                           //子栏目列表
	ConferenceInformationList []model.ConferenceInformation `json:"conference_information_list"`                               //栏目数据信息列表
}

type RespWebColumnInformationList struct {
	List []ConferenceColumnInfo `json:"list"` //赞助商，合作方，媒体信息
}

type ResWebGuestInformationList struct {
	Info      ResPagination           `json:"info"`
	GuestList []ConferenceInformation `json:"guest_list"` //嘉宾信息
}

type RespConferenceColumnInfo struct {
	Id           int64   `db:"id" json:"id" form:"id"`                                                       //服务ID
	ConferenceId int64   `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"` //展会ID
	CnName       string  `gorm:"column:cn_name" db:"cn_name" json:"cn_name" form:"cn_name"`                  //论坛名称
	EnName       string  `gorm:"column:en_name" db:"en_name" json:"en_name" form:"en_name"`                  //论坛名称
	IsSubSection int     `gorm:"column:is_sub_section" db:"is_sub_section" json:"is_sub_section" form:"is_sub_section"`
	Sorting      float32 `gorm:"column:sorting" db:"sorting" json:"sorting" form:"sorting"` //排序
	CreateTime   string  `db:"create_time" json:"create_time" form:"create_time"`
	UpdateTime   string  `json:"update_time" db:"update_time" form:"update_time"`
	CreateAdmin  string  `json:"create_admin" db:"create_admin"  form:"create_admin"`
	UpdateAdmin  string  `json:"update_admin" db:"update_admin" form:"update_admin"`
	Deleted      int     `json:"deleted" db:"deleted" form:"deleted"`
}

type ResAdminInformationList struct {
	Info ResPagination           `json:"info"`
	List []ConferenceInformation `json:"list"`
}

type ConferenceInformation struct {
	Id            int64     `gorm:"column:id" db:"id" json:"id" form:"id"`                                                 //会议嘉宾等信息
	ConferenceId  int64     `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"`            //展会ID
	ColumnId      int64     `gorm:"column:column_id" db:"column_id" json:"column_id" form:"column_id"`                     //栏目ID
	CnAppellation string    `gorm:"column:cn_appellation" db:"cn_appellation" json:"cn_appellation" form:"cn_appellation"` //称谓
	EnAppellation string    `gorm:"column:en_appellation" db:"en_appellation" json:"en_appellation" form:"en_appellation"` //称谓
	CnPosition    string    `gorm:"column:cn_position" db:"cn_position" json:"cn_position" form:"cn_position"`             //职位
	EnPosition    string    `gorm:"column:en_position" db:"en_position" json:"en_position" form:"en_position"`             //职位
	CnCompany     string    `gorm:"column:cn_company" db:"cn_company" json:"cn_company" form:"cn_company"`                 //公司
	EnCompany     string    `gorm:"column:en_company" db:"en_company" json:"en_company" form:"en_company"`                 //公司
	CnLink        string    `gorm:"column:cn_link" db:"cn_link" json:"cn_link" form:"cn_link"`                             //链接
	EnLink        string    `gorm:"column:en_link" db:"cn_link" json:"en_link" form:"en_link"`                             //链接
	CnPicture     string    `gorm:"column:cn_picture" db:"cn_picture" json:"cn_picture" form:"cn_picture"`                 //logo图-中文
	EnPicture     string    `gorm:"column:en_picture" db:"en_picture" json:"en_picture" form:"en_picture"`                 //logo图-英文
	Sorting       string    `gorm:"column:sorting" db:"sorting" json:"sorting" form:"sorting"`                             //排序
	CnSorting     string    `gorm:"column:cn_sorting" db:"cn_sorting" json:"cn_sorting" form:"cn_sorting"`                 //排序-中文
	EnSorting     string    `gorm:"column:en_sorting" db:"en_sorting" json:"en_sorting" form:"en_sorting"`                 //排序-英文
	CnContent     string    `gorm:"column:cn_content" db:"cn_content"  json:"cn_content" form:"cn_content"`                //嘉宾介绍(或嘉宾身份)--中文
	EnContent     string    `gorm:"column:en_content" db:"en_content"  json:"en_content" form:"en_content"`                //嘉宾介绍(或嘉宾身份)--英文
	Type          int       `gorm:"column:type" db:"type" json:"type" form:"type"`                                         //信息类型（1赞助商，2嘉宾,3合作商,4媒体，9日程嘉宾）
	Deleted       int       `gorm:"column:deleted" db:"deleted" json:"deleted" form:"deleted"`
	CreateTime    time.Time `gorm:"column:create_time" db:"create_time" json:"create_time" form:"create_time"`
	UpdateTime    time.Time `json:"update_time" db:"update_time"`
	CreateAdmin   string    `json:"create_admin" db:"create_admin"`
	UpdateAdmin   string    `json:"update_admin" db:"update_admin"`
}

type ResAdminInformationEventList struct {
	Info ResPagination                      `json:"info"`
	List []model.ConferenceInformationEvent `json:"list"`
}

type RespConferenceInformationInfo struct {
	Id            int64   `db:"id" json:"id" form:"id"`                                                                  //服务ID
	ConferenceId  int64   `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"`            //展会ID
	ColumnId      int64   `gorm:"column:column_id" db:"column_id" json:"column_id" form:"column_id"`                     //栏目ID
	CnCompany     string  `gorm:"column:cn_company" db:"cn_company" json:"cn_company" form:"cn_company"`                 //公司
	EnCompany     string  `gorm:"column:en_company" db:"en_company" json:"en_company" form:"en_company"`                 //公司
	CnLink        string  `gorm:"column:cn_link" db:"cn_link" json:"cn_link" form:"cn_link"`                             //链接
	EnLink        string  `gorm:"column:en_link" db:"en_link" json:"en_link" form:"en_link"`                             //链接
	CnPicture     string  `gorm:"column:cn_picture" db:"cn_picture" json:"cn_picture" form:"cn_picture"`                 //logo图-中文
	EnPicture     string  `gorm:"column:en_picture" db:"en_picture" json:"en_picture" form:"en_picture"`                 //logo图-英文
	Sorting       float32 `gorm:"column:sorting" db:"sorting" json:"sorting" form:"sorting"`                             //排序
	CnSorting     float32 `gorm:"column:cn_sorting" db:"cn_sorting" json:"cn_sorting" form:"cn_sorting"`                 //排序-中文
	EnSorting     float32 `gorm:"column:en_sorting" db:"en_sorting" json:"en_sorting" form:"en_sorting"`                 //排序-英文
	CnAppellation string  `gorm:"column:cn_appellation" db:"cn_appellation" json:"cn_appellation" form:"cn_appellation"` //称谓
	EnAppellation string  `gorm:"column:en_appellation" db:"en_appellation" json:"en_appellation" form:"en_appellation"` //称谓
	CnPosition    string  `gorm:"column:cn_position" db:"cn_position" json:"cn_position" form:"cn_position"`             //职位
	EnPosition    string  `gorm:"column:en_position" db:"en_position" json:"en_position" form:"en_position"`             //职位
	CnContent     string  `gorm:"column:cn_content" db:"cn_content"  json:"cn_content" form:"cn_content"`                //嘉宾介绍--中文
	EnContent     string  `gorm:"column:en_content" db:"en_content"  json:"en_content" form:"en_content"`                //嘉宾介绍--英文
	Type          int     `gorm:"column:type" db:"type" json:"type" form:"type"`                                         //信息类型（1赞助商&合作商&媒体，2嘉宾）
	CreateTime    string  `db:"create_time" json:"create_time" form:"create_time"`
	UpdateTime    string  `json:"update_time" db:"update_time" form:"update_time"`
	CreateAdmin   string  `json:"create_admin" db:"create_admin"  form:"create_admin"`
	UpdateAdmin   string  `json:"update_admin" db:"update_admin" form:"update_admin"`
	Deleted       int     `json:"deleted" db:"deleted" form:"deleted"`
}

type ResAdminTicketPriceList struct {
	List []ConferenceTicketPrice `json:"list"`
	Info ResPagination           `json:"info"`
}

type ConferenceTicketPrice struct {
	Id                     int64  `gorm:"column:id" db:"id" json:"id" form:"id"`
	ConferenceId           int64  `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"`                                                        //展会ID
	CnName                 string `json:"cn_name" form:"cn_name"  gorm:"column:cn_name" db:"cn_name"`                                                                        //票种名称中文
	EnName                 string `json:"en_name"  form:"en_name" gorm:"column:en_name" db:"en_name"`                                                                        //票种名称英文
	CnServiceId            string `gorm:"column:cn_service_id" db:"cn_service_id" json:"cn_service_id" form:"cn_service_id"`                                                 //中文服务ID
	EnServiceId            string `gorm:"column:en_service_id" db:"en_service_id" json:"en_service_id" form:"en_service_id"`                                                 //英文服务ID
	CnButtonName           string `gorm:"column:cn_button_name" db:"cn_button_name" json:"cn_button_name" form:"cn_button_name"`                                             //自定义按钮名称-中文
	EnButtonName           string `gorm:"column:en_button_name" db:"en_button_name" json:"en_button_name" form:"en_button_name"`                                             //自定义按钮名称-英文
	CnButtonLink           string `gorm:"column:cn_button_link" db:"cn_button_link" json:"cn_button_link" form:"cn_button_link"`                                             //自定义按钮跳转链接-中文
	EnButtonLink           string `gorm:"column:en_button_link" db:"en_button_link" json:"en_button_link" form:"en_button_link"`                                             //自定义按钮跳转链接-英文
	CnRegistrationPageName string `gorm:"column:cn_registration_page_name" db:"cn_registration_page_name" json:"cn_registration_page_name" form:"cn_registration_page_name"` //报名页门票名称-中文
	EnRegistrationPageName string `gorm:"column:en_registration_page_name" db:"en_registration_page_name" json:"en_registration_page_name" form:"en_registration_page_name"` //报名页门票名称-英文

	CnIsDisplayed int    `gorm:"column:cn_is_displayed" db:"cn_is_displayed"  json:"cn_is_displayed" form:"cn_is_displayed"` //是否显示购买按钮（1展示，0不展示）
	EnIsDisplayed int    `gorm:"column:en_is_displayed" db:"en_is_displayed"  json:"en_is_displayed" form:"en_is_displayed"` //是否显示购买按钮（1展示，0不展示）
	CnMaximum     int    `gorm:"column:cn_maximum" db:"cn_maximum" json:"cn_maximum" form:"cn_maximum"`                      //最大人数
	EnMaximum     int    `gorm:"column:en_maximum" db:"en_maximum" json:"en_maximum" form:"en_maximum"`                      //最大人数
	CnSorting     string `json:"cn_sorting" db:"cn_sorting" gorm:"cn_sorting" form:"cn_sorting"`                             //排序
	EnSorting     string `json:"en_sorting" db:"en_sorting" gorm:"en_sorting" form:"en_sorting"`                             //排序

	CnCurrencyUnit int `gorm:"column:cn_currency_unit" db:"cn_currency_unit" json:"cn_currency_unit" form:"cn_currency_unit"` //货币单位（1中文，2美元，3欧元）
	EnCurrencyUnit int `gorm:"column:en_currency_unit" db:"en_currency_unit" json:"en_currency_unit" form:"en_currency_unit"` //货币单位（1中文，2美元，3欧元）

	Type          int       `json:"type" db:"type" gorm:"type" form:"type"`                                                    ///模版类型 （0权益模版，1人数金额模版）
	CnStandardFee string    `gorm:"column:cn_standard_fee" db:"cn_standard_fee" json:"cn_standard_fee" form:"cn_standard_fee"` //中文服务金额信息
	EnStandardFee string    `gorm:"column:en_standard_fee" db:"en_standard_fee" json:"en_standard_fee" form:"en_standard_fee"` //英文服务金额信息
	CreateTime    time.Time `db:"create_time" json:"create_time" form:"create_time"`
	UpdateTime    time.Time `json:"update_time" db:"update_time" form:"update_time"`
	CreateAdmin   string    `json:"create_admin" db:"create_admin"  form:"create_admin"`
	UpdateAdmin   string    `json:"update_admin" db:"update_admin" form:"update_admin"`
	Deleted       int       `json:"deleted" db:"deleted" form:"deleted"`
}

type ResUserTicketPriceList struct {
	Info            ResPagination                     `json:"info"`
	List            []ConferenceTicketPriceInfo       `json:"list"`              //票种列表
	RightsList      []model.ConferenceRightsInterests `json:"cn_rights_list"`    //权益列表
	EnRightsList    []model.ConferenceRightsInterests `json:"en_rights_list"`    //权益列表
	CnPriceTemplate int                               `json:"cn_price_template"` //价格模板 (0 人数金额模版,  1 票种权益表格模版)
	EnPriceTemplate int                               `json:"en_price_template"` //价格模板 (0 人数金额模版,  1 票种权益表格模版)
}

type ResUserForumConfigList struct {
	List []model.ForumConfigList `json:"list"` //票种列表
}

type ResConferenceFromConfigList struct {
	List []model.ConferenceFromConfig `json:"list"` //票种列表
}
type ConferenceTicketPriceInfo struct {
	Id                     int64   `gorm:"column:id" db:"id" json:"id" form:"id"`
	ConferenceId           int64   `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"`                                                        //展会ID
	CnName                 string  `json:"cn_name" gorm:"column:cn_name" db:"cn_name"`                                                                                        //票种名称中文
	EnName                 string  `json:"en_name" gorm:"column:en_name" db:"en_name"`                                                                                        //票种名称英文
	CnServiceId            string  `gorm:"column:cn_service_id" db:"cn_service_id" json:"cn_service_id" form:"cn_service_id"`                                                 //中文服务ID
	EnServiceId            string  `gorm:"column:en_service_id" db:"en_service_id" json:"en_service_id" form:"en_service_id"`                                                 //英文服务ID
	CnButtonName           string  `gorm:"column:cn_button_name" db:"cn_button_name" json:"cn_button_name" form:"cn_button_name"`                                             //自定义按钮名称-中文
	EnButtonName           string  `gorm:"column:en_button_name" db:"en_button_name" json:"en_button_name" form:"en_button_name"`                                             //自定义按钮名称-英文
	CnButtonLink           string  `gorm:"column:cn_button_link" db:"cn_button_link" json:"cn_button_link" form:"cn_button_link"`                                             //自定义按钮跳转链接-中文
	EnButtonLink           string  `gorm:"column:en_button_link" db:"en_button_link" json:"en_button_link" form:"en_button_link"`                                             //自定义按钮跳转链接-英文
	CnRegistrationPageName string  `gorm:"column:cn_registration_page_name" db:"cn_registration_page_name" json:"cn_registration_page_name" form:"cn_registration_page_name"` //报名页门票名称-中文
	EnRegistrationPageName string  `gorm:"column:en_registration_page_name" db:"en_registration_page_name" json:"en_registration_page_name" form:"en_registration_page_name"` //报名页门票名称-英文
	CnCurrencyUnit         int     `gorm:"column:cn_currency_unit" db:"cn_currency_unit" json:"cn_currency_unit" form:"cn_currency_unit"`                                     //货币单位（1中文，2美元，3欧元）
	EnCurrencyUnit         int     `gorm:"column:en_currency_unit" db:"en_currency_unit" json:"en_currency_unit" form:"en_currency_unit"`                                     //货币单位（1中文，2美元，3欧元）
	CnIsDisplayed          int     `gorm:"column:cn_is_displayed" db:"cn_is_displayed"  json:"cn_is_displayed" form:"cn_is_displayed"`                                        //是否显示购买按钮（1展示，0不展示）
	EnIsDisplayed          int     `gorm:"column:en_is_displayed" db:"en_is_displayed"  json:"en_is_displayed" form:"en_is_displayed"`                                        //是否显示购买按钮（1展示，0不展示）
	CnMaximum              int     `gorm:"column:cn_maximum" db:"cn_maximum" json:"cn_maximum" form:"cn_maximum"`                                                             //最大人数
	EnMaximum              int     `gorm:"column:en_maximum" db:"en_maximum" json:"en_maximum" form:"en_maximum"`                                                             //最大人数
	CnSorting              float32 `json:"cn_sorting" db:"cn_sorting" gorm:"cn_sorting" form:"cn_sorting"`                                                                    //排序
	EnSorting              float32 `json:"en_sorting" db:"en_sorting" gorm:"en_sorting" form:"en_sorting"`                                                                    //排序

	CnStandardFee string    `gorm:"column:cn_standard_fee" db:"cn_standard_fee" json:"cn_standard_fee" form:"cn_standard_fee"` //中文服务金额信息
	EnStandardFee string    `gorm:"column:en_standard_fee" db:"en_standard_fee" json:"en_standard_fee" form:"en_standard_fee"` //英文服务金额信息
	CreateTime    time.Time `db:"create_time" json:"create_time" form:"create_time"`
	CnRightsIds   string    `json:"cn_rights_id" form:"cn_rights_id"`
	EnRightsIds   string    `json:"en_rights_id" form:"en_rights_id"`
}

type RespTicketPriceInfo struct {
	Id                     int64   `json:"id" form:"id" form:"id"`
	ConferenceId           int64   `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"`                                                        //展会ID
	CnName                 string  `json:"cn_name"`                                                                                                                           // 票种名称中文
	EnName                 string  `json:"en_name"`                                                                                                                           // 票种名称英文
	CnServiceId            string  `gorm:"column:cn_service_id" db:"cn_service_id" json:"cn_service_id" form:"cn_service_id"`                                                 //中文服务ID
	EnServiceId            string  `gorm:"column:en_service_id" db:"en_service_id" json:"en_service_id" form:"en_service_id"`                                                 //英文服务ID
	CnButtonName           string  `gorm:"column:cn_button_name" db:"cn_button_name" json:"cn_button_name" form:"cn_button_name"`                                             //自定义按钮名称-中文
	EnButtonName           string  `gorm:"column:en_button_name" db:"en_button_name" json:"en_button_name" form:"en_button_name"`                                             //自定义按钮名称-英文
	CnButtonLink           string  `gorm:"column:cn_button_link" db:"cn_button_link" json:"cn_button_link" form:"cn_button_link"`                                             //自定义按钮跳转链接-中文
	EnButtonLink           string  `gorm:"column:en_button_link" db:"en_button_link" json:"en_button_link" form:"en_button_link"`                                             //自定义按钮跳转链接-英文
	CnRegistrationPageName string  `gorm:"column:cn_registration_page_name" db:"cn_registration_page_name" json:"cn_registration_page_name" form:"cn_registration_page_name"` //报名页门票名称-中文
	EnRegistrationPageName string  `gorm:"column:en_registration_page_name" db:"en_registration_page_name" json:"en_registration_page_name" form:"en_registration_page_name"` //报名页门票名称-英文
	CnCurrencyUnit         int     `gorm:"column:cn_currency_unit" db:"cn_currency_unit" json:"cn_currency_unit" form:"cn_currency_unit"`                                     //货币单位（1中文，2美元，3欧元）
	EnCurrencyUnit         int     `gorm:"column:en_currency_unit" db:"en_currency_unit" json:"en_currency_unit" form:"en_currency_unit"`                                     //货币单位（1中文，2美元，3欧元）
	CnIsDisplayed          int     `gorm:"column:cn_is_displayed" db:"cn_is_displayed"  json:"cn_is_displayed" form:"cn_is_displayed"`                                        //是否显示购买按钮（1展示，0不展示）
	EnIsDisplayed          int     `gorm:"column:en_is_displayed" db:"en_is_displayed"  json:"en_is_displayed" form:"en_is_displayed"`                                        //是否显示购买按钮（1展示，0不展示）
	CnMaximum              int     `gorm:"column:cn_maximum" db:"cn_maximum" json:"cn_maximum" form:"cn_maximum"`                                                             //最大人数
	EnMaximum              int     `gorm:"column:en_maximum" db:"en_maximum" json:"en_maximum" form:"en_maximum"`                                                             //最大人数
	CnSorting              float32 `json:"cn_sorting" db:"cn_sorting" gorm:"cn_sorting" form:"cn_sorting"`                                                                    //排序
	EnSorting              float32 `json:"en_sorting" db:"en_sorting" gorm:"en_sorting" form:"en_sorting"`                                                                    //排序
	Type                   int     `json:"type" db:"type" gorm:"type" form:"type"`                                                                                            //是否活动的门票信息（1主会议，0其他活动）
	CnStandardFee          string  `gorm:"column:cn_standard_fee" db:"cn_standard_fee" json:"cn_standard_fee" form:"cn_standard_fee"`                                         //中文服务金额信息
	EnStandardFee          string  `gorm:"column:en_standard_fee" db:"en_standard_fee" json:"en_standard_fee" form:"en_standard_fee"`                                         //英文服务金额信息
}

type ResAdminRightsInterestsList struct {
	Info ResPagination                     `json:"info"`
	List []model.ConferenceRightsInterests `json:"list"`
}

type ResConferenceRightsInterestsInfo struct {
	Id           int64   `json:"id" db:"id" gorm:"id" form:"id"`
	ConferenceId int64   `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"` //展会ID
	CnContent    string  `json:"cn_content" db:"cn_content" gorm:"cn_content" form:"cn_content"`             //权益内容-中文
	EnContent    string  `json:"en_content" db:"en_content" gorm:"en_content" form:"en_content"`             //权益内容-英文
	CnSorting    float32 `json:"cn_sorting" db:"cn_sorting" gorm:"cn_sorting" form:"cn_sorting"`             //排序
	EnSorting    float32 `json:"en_sorting" db:"en_sorting" gorm:"en_sorting" form:"en_sorting"`             //排序
	Type         int     `json:"type" db:"type" gorm:"type" form:"type"`                                     ///模版类型 （0权益模版，1人数金额模版）
}

type ResAdminRightsTicketList struct {
	Info ResPagination                 `json:"info"`
	List []ConferenceTicketsRightsInfo `json:"list"`
}
type ConferenceTicketsRightsInfo struct {
	Id           int64  `json:"id" db:"id" gorm:"id" form:"id"`
	ConferenceId int64  `json:"conference_id"  form:"conference_id"`                               //展会id
	TicketId     int64  `gorm:"column:ticket_id" db:"ticket_id" json:"ticket_id" form:"ticket_id"` //票种ID
	CnContent    string `json:"cn_content" db:"cn_content" gorm:"cn_content" form:"cn_content"`    //权益内容-中文
	EnContent    string `json:"en_content" db:"en_content" gorm:"en_content" form:"en_content"`    //权益内容-英文
	IsSelected   bool   `json:"is_selected" form:"is_selected"`                                    //是否被选中 （true已被选中）
}
type ResConferenceTicketConfigInfo struct {
	Id              int64 `gorm:"column:id" db:"id" json:"id" form:"id"`
	ConferenceId    int64 `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"`                        //展会ID
	CnPriceTemplate int   `gorm:"column:cn_price_template" db:"cn_price_template" json:"cn_price_template" form:"cn_price_template"` //价格模板 (0 人数金额模版,  1 票种权益表格模版)
	EnPriceTemplate int   `gorm:"column:en_price_template" db:"en_price_template" json:"en_price_template" form:"en_price_template"` //价格模板 (0 人数金额模版,  1 票种权益表格模版)
}

type ResAdminConferenceRegisterList struct {
	Info ResPagination               `json:"info"`
	List []ResConferenceRegisterList `json:"list"`
}

type ResAdminConferenceRegisterFromList struct {
	List []string `json:"list"`
}

type ResAdminConferenceChannelSourceList struct {
	List []model.ChannelSource `json:"list"`
}

type ResConferenceRegister struct {
	ID             int64  `json:"id"`
	ConferenceId   int64  `json:"conference_id" form:"conference_id"`     //展会ID
	UserId         int64  `json:"user_id" form:"user_id"`                 //购买用户ID
	FirstName      string `json:"first_name" form:"first_name"`           //名 （中文只用first_name）
	LastName       string `json:"last_name" form:"last_name"`             //姓
	Company        string `json:"company" form:"company"`                 //公司
	Email          string `json:"email" form:"email"`                     //邮箱
	Mobile         string `json:"mobile" form:"mobile"`                   //手机号
	ServiceId      string `json:"service_id" form:"service_id"`           //服务ID
	OrderId        string `json:"order_id" form:"order_id"`               //订单号ID
	OrderStatus    int    `json:"order_status" form:"order_status"`       //订单状态
	OrderNum       int    `json:"order_num" form:"order_num"`             //订单数量
	CreateTime     string `json:"create_time" form:"create_time"`         //报名时间
	ConferenceName string `json:"conference_name" form:"conference_name"` //展会名称
	SourceID       string `json:"source_id" form:"source_id"`             //来源ID
	SourceName     string `json:"source_name" form:"source_name"`         //来源名称
}

type RespConferenceRegisterInfo struct {
	Id        int64  `json:"id"`
	FirstName string `json:"first_name" form:"first_name"` //名 （中文只用first_name）
	LastName  string `json:"last_name" form:"last_name"`   //姓
	Email     string `json:"email" form:"email"`           //邮箱
}

type ResConferenceRegisterList struct {
	ID                 int64  `json:"id"`
	ConferenceId       int64  `json:"conference_id" form:"conference_id"`             //展会ID
	UserId             int64  `json:"user_id" form:"user_id"`                         //购买用户ID
	FirstName          string `json:"first_name" form:"first_name"`                   //名 （中文只用first_name）
	LastName           string `json:"last_name" form:"last_name"`                     //姓
	Company            string `json:"company" form:"company"`                         //公司
	Email              string `json:"email" form:"email"`                             //邮箱
	Mobile             string `json:"mobile" form:"mobile"`                           //手机号
	ServiceId          string `json:"service_id" form:"service_id"`                   //服务ID
	OrderId            string `json:"order_id" form:"order_id"`                       //订单号ID
	OrderStatus        int    `json:"order_status" form:"order_status"`               //订单状态
	OrderNum           int    `json:"order_num" form:"order_num"`                     //订单数量
	CreateTime         string `json:"create_time" form:"create_time"`                 //报名时间
	ConferenceName     string `json:"conference_name" form:"conference_name"`         //展会名称
	RegisterNum        int    `json:"register_num" form:"register_num"`               //参会人信息
	SourceID           string `json:"source_id" form:"source_id"`                     //来源ID
	SourceName         string `json:"source_name" form:"source_name"`                 //来源名称
	FromId             string `json:"fromId" form:"fromId"`                           //渠道
	InterestedMeetings string `json:"interested_meetings" form:"interested_meetings"` //感兴趣的会议（多个用英文逗号分割）
	UserFirstName      string `json:"user_first_name" form:"user_first_name"`         //名 （中文只用first_name）
	UserLastName       string `json:"user_last_name" form:"user_last_name"`           //姓
	UserEmail          string `json:"user_email" form:"user_email"`                   //邮箱
	UserMobile         string `json:"user_mobile" form:"user_mobile"`                 //手机号
	UserCompany        string `json:"user_company" form:"user_company"`               //公司
	UserJobTitle       string `json:"user_job_title" form:"user_job_title"`           //职位

	MainProducts   string `json:"main_products" form:"main_products" gorm:"column:main_products"`       // 主推产品
	EnterpriseType string `json:"enterprise_type" form:"enterprise_type" gorm:"column:enterprise_type"` // 企业类型

}

type RespMeetingRegisterUser struct {
	RegisterId int64 `gorm:"column:register_id" db:"register_id" json:"register_id" form:"register_id"`
	Count      int   `json:"count" form:"count"`
}

type RespMeetingRegisterUserList struct {
	RegisterId int64  `gorm:"column:register_id" db:"register_id" json:"register_id" form:"register_id"` //报名ID
	FirstName  string `gorm:"column:first_name" db:"first_name" json:"first_name" form:"first_name"`     //名 （中文只用first_name）
	LastName   string `gorm:"column:last_name" db:"last_name" json:"last_name" form:"last_name"`         //姓
	Email      string `gorm:"column:email" db:"email" json:"email" form:"email"`                         //邮箱
	Mobile     string `gorm:"column:mobile" db:"mobile" json:"mobile" form:"mobile"`                     //手机号
	Company    string `gorm:"column:company" db:"company" json:"company" form:"company"`                 //公司
	JobTitle   string `gorm:"column:job_title" db:"job_title" json:"job_title" form:"job_title"`         //职位
}

type ResAdminConferenceRegisterUserList struct {
	Info ResPagination                  `json:"info"`
	List []model.ConferenceRegisterUser `json:"list"`
}

type ResConferenceRegisterInfo struct {
	ID             int64  `json:"id"`
	ConferenceName string `json:"conference_name" excel_smm:"展会名称,A"`                        // 展会名称中文
	SourceName     string `json:"source_name" excel_smm:"来源,B""`                             //来源名称
	FromId         string `json:"fromId" `                                                   //渠道
	FromName       string `json:"from_name" excel_smm:"渠道名称,C""`                             //渠道名称
	Name           string `json:"name" excel_smm:"购票人姓名,D"`                                  // 购票人姓名
	ConferenceID   int64  `json:"conference_id" excel_smm:"展会id,E"`                          // 展会ID
	UserId         int64  `json:"user_id" excel_smm:"报名人用户ID,F"`                             //报名人用户ID
	Email          string `json:"email" excel_smm:"邮箱,G"`                                    // 邮箱
	Mobile         string `gorm:"column:mobile" db:"mobile" json:"mobile" excel_smm:"手机号,H"` //手机号
	OrderId        string `json:"order_id" excel_smm:"订单号,I"`                                //订单号
	OrderStatus    string `json:"order_status" excel_smm:"订单状态,J"`                           //订单状态
	OrderNum       int    `json:"order_num" excel_smm:"报名数量,K"`                              //报名数量
	CreateTime     string `json:"create_time" excel_smm:"提交时间,L"`                            // 提交时间
	MainProducts   string `json:"main_products" excel_smm:"主推产品,M"`                          // 主推产品
	EnterpriseType string `json:"enterprise_type" excel_smm:"企业类型,N"`                        // 企业类型
	UpdateTime     string `json:"-"`
}

type RegisterUser struct {
	RegisterId int64  `gorm:"column:register_id" db:"register_id" json:"register_id" form:"register_id"` //报名ID
	FirstName  string `gorm:"column:first_name" db:"first_name" json:"first_name" form:"first_name"`     //名 （中文只用first_name）
	LastName   string `gorm:"column:last_name" db:"last_name" json:"last_name" form:"last_name"`         //姓
	Email      string `gorm:"column:email" db:"email" json:"email" form:"email"`                         //邮箱
	Mobile     string `gorm:"column:mobile" db:"mobile" json:"mobile" form:"mobile"`                     //手机号
	Company    string `gorm:"column:company" db:"company" json:"company" form:"company"`                 //公司
	JobTitle   string `gorm:"column:job_title" db:"job_title" json:"job_title" form:"job_title"`         //职位
}

type RespConferencePosterInfo struct {
	ID           int64  `json:"id" db:"id" gorm:"id" form:"id"`
	ConferenceId int64  `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"` //展会ID
	PcLogo       string `json:"pc_logo" db:"pc_logo" gorm:"pc_logo" form:"pc_logo"`                         // pc背景/海报图
	H5Logo       string `json:"h5_logo" db:"h5_logo" gorm:"h5_logo" form:"h5_logo"`                         // H5海报图
	Link         string `json:"link" db:"link" gorm:"link" form:"link"`                                     // 跳转链接
	Sorting      string `json:"sorting" db:"sorting" gorm:"sorting" form:"sorting"`                         // 排序
	CreateTime   string `db:"create_time" json:"create_time" form:"create_time"`
	CreateAdmin  string `json:"create_admin" db:"create_admin"  form:"create_admin"`
}

type RespIndexConferencePosterInfo struct {
	ID           int64  `json:"id" db:"id" gorm:"id" form:"id"`
	ConferenceId int64  `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"` //展会ID
	PcLogo       string `json:"pc_logo" db:"pc_logo" gorm:"pc_logo" form:"pc_logo"`                         // pc背景/海报图
}

type ResAdminConferenceEventCategoryList struct {
	Info ResPagination                 `json:"info"`
	List []ConferenceEventCategoryInfo `json:"list"`
}

// ConferenceEventCategory 活动类别
type ConferenceEventCategoryInfo struct {
	Id           int64  `json:"id" db:"id" gorm:"id" form:"id"`
	ConferenceId int64  `json:"conference_id"  form:"conference_id"`                                            //展会id
	StartTime    string `json:"start_time" db:"start_time" gorm:"start_time" form:"start_time"`                 // 开始日期
	EndTime      string `json:"end_time" db:"end_time" gorm:"end_time" form:"end_time"`                         // 结束日期
	EntryType    string `json:"entry_type" db:"entry_type" gorm:"entry_type" form:"entry_type"`                 // 入场类型
	CnName       string `json:"cn_name" db:"cn_name" gorm:"cn_name" form:"cn_name"`                             // 活动分类名称-中文
	EnName       string `json:"en_name" db:"en_name" gorm:"en_name" form:"en_name"`                             // 活动分类名称-英文
	CnPlace      string `json:"cn_place" db:"cn_place" gorm:"cn_place" form:"cn_place"`                         // 中文地点
	EnPlace      string `json:"en_place" db:"en_place" gorm:"en_place" form:"en_place"`                         // 英文地点
	CnTitle      string `json:"cn_title" db:"cn_title" gorm:"cn_title" form:"cn_title"`                         // 中文标题
	EnTitle      string `json:"en_title" db:"en_title" gorm:"en_title" form:"en_title"`                         // 英文标题
	CnContent    string `json:"cn_content" db:"cn_content" gorm:"cn_content" form:"cn_content"`                 // 中文内容
	EnContent    string `json:"en_content" db:"en_content" gorm:"en_content" form:"en_content"`                 // 英文内容
	CnButtonLink string `json:"cn_button_link" db:"cn_button_link" gorm:"cn_button_link" form:"cn_button_link"` // 中文预约参会按钮跳转链接
	EnButtonLink string `json:"en_button_link" db:"en_button_link" gorm:"en_button_link" form:"en_button_link"` // 英文预约参会按钮跳转链接
	Type         int    `json:"type" db:"type" gorm:"type" form:"type"`                                         // 关联导航菜单
	Picture      string `json:"picture" db:"picture" gorm:"picture" form:"picture"`                             // 图片
	Sorting      string `json:"sorting" db:"sorting" gorm:"sorting" form:"sorting"`                             // 排序                                                                      //活动时间
}

type RespAdminConferenceEventCategoryInfo struct {
	ID           int64  `json:"id" db:"id" gorm:"id" form:"id"`
	ConferenceId int64  `json:"conference_id"  form:"conference_id"`                                            //展会id
	StartTime    string `json:"start_time" db:"start_time" gorm:"start_time" form:"start_time"`                 // 开始日期
	EndTime      string `json:"end_time" db:"end_time" gorm:"end_time" form:"end_time"`                         // 结束日期
	EntryType    string `json:"entry_type" db:"entry_type" gorm:"entry_type" form:"entry_type"`                 // 入场类型
	CnName       string `json:"cn_name" db:"cn_name" gorm:"cn_name" form:"cn_name"`                             // 活动分类名称-中文
	EnName       string `json:"en_name" db:"en_name" gorm:"en_name" form:"en_name"`                             // 活动分类名称-英文
	CnPlace      string `json:"cn_place" db:"cn_place" gorm:"cn_place" form:"cn_place"`                         // 中文地点
	EnPlace      string `json:"en_place" db:"en_place" gorm:"en_place" form:"en_place"`                         // 英文地点
	CnTitle      string `json:"cn_title" db:"cn_title" gorm:"cn_title" form:"cn_title"`                         // 中文标题
	EnTitle      string `json:"en_title" db:"en_title" gorm:"en_title" form:"en_title"`                         // 英文标题
	CnContent    string `json:"cn_content" db:"cn_content" gorm:"cn_content" form:"cn_content"`                 // 中文内容
	EnContent    string `json:"en_content" db:"en_content" gorm:"en_content" form:"en_content"`                 // 英文内容
	CnButtonLink string `json:"cn_button_link" db:"cn_button_link" gorm:"cn_button_link" form:"cn_button_link"` // 中文预约参会按钮跳转链接
	EnButtonLink string `json:"en_button_link" db:"en_button_link" gorm:"en_button_link" form:"en_button_link"` // 英文预约参会按钮跳转链接
	Type         int    `json:"type" db:"type" gorm:"type" form:"type"`                                         // 关联导航菜单（1CLNB展前会，2CLNB主论坛，3CLNB分论坛，4户外赛事，5场馆赛事，6Releasing Stage 新品发布）
	Picture      string `json:"picture" db:"picture" gorm:"picture" form:"picture"`                             // 图片
	Sorting      string `json:"sorting" db:"sorting" gorm:"sorting" form:"sorting"`                             // 排序
}

type ResAdminConferenceEventList struct {
	Info ResPagination         `json:"info"`
	List []ConferenceEventInfo `json:"list"`
}

// ConferenceEvent 活动一览表
type ConferenceEventInfo struct {
	Id           int64 `json:"id" db:"id" gorm:"id" form:"id"`
	ConferenceId int64 `json:"conference_id"  form:"conference_id"` //展会id

	CnName          string `json:"cn_name" db:"cn_name" gorm:"cn_name" form:"cn_name"`                                                //展会活动名称--中文
	CnPlace         string `json:"cn_place" db:"cn_place" gorm:"cn_place" form:"cn_place"`                                            //展会活动地点--中文
	CnIntroduce     string `json:"cn_introduce" db:"cn_introduce" gorm:"cn_introduce" form:"cn_introduce"`                            //展会活动介绍--中文
	CnLink          string `json:"cn_link" db:"cn_link" gorm:"cn_link" form:"cn_link"`                                                //展会活动标题--中文
	EnName          string `json:"en_name" db:"en_name" gorm:"en_name" form:"en_name"`                                                //展会活动名称--英文
	EnPlace         string `json:"en_place" db:"en_place" gorm:"en_place" form:"en_place"`                                            //展会活动地点--英文
	EnIntroduce     string `json:"en_introduce" db:"en_introduce" gorm:"en_introduce" form:"en_introduce"`                            //展会活动介绍--英文
	EnLink          string `json:"en_link" db:"en_link" gorm:"en_link" form:"en_link"`                                                //展会活动标题--英文
	TemplateType    int    `gorm:"column:template_type" db:"template_type" json:"template_type" form:"template_type"`                 //日程模板 (0 议程&非议程模板,  1是时间&主题 )
	CategoryType    int    `json:"category_type" db:"category_type" gorm:"category_type" form:"category_type"`                        // 所属活动分类（1CLNB展前会，2CLNB主论坛，3CLNB分论坛，4户外赛事，5场馆赛事，6Releasing Stage 新品发布）
	ScheduleIsForum string `gorm:"column:schedule_is_forum" db:"schedule_is_forum" json:"schedule_is_forum" form:"schedule_is_forum"` //日程是否有论坛（0无分论坛，1有分论坛）
	IsDisplayPage   string `gorm:"column:is_display_page" db:"is_display_page" json:"is_display_page" form:"is_display_page"`         //是否展示活动详情页（0是，1否）
	Sorting         string `json:"sorting" db:"sorting" gorm:"sorting" form:"sorting"`                                                //排序
	IsSubSection    int    `gorm:"column:is_sub_section" db:"is_sub_section" json:"is_sub_section" form:"is_sub_section"`             //是否子论坛(0不是子论坛，其他时为父级论坛ID)
	CreateTime      string `json:"create_time" gorm:"column:create_time" db:"create_time"`                                            // 创建时间
	EventTime       string `json:"event_time" db:"event_time" gorm:"event_time" form:"event_time"`                                    //活动时间-开始日期
	Times           string `json:"times" form:"times"`                                                                                //活动时间
	CnCategoryName  string `json:"cn_category_name" form:"cn_category_name"`
	EnCategoryName  string `json:"en_category_name" form:"en_category_name"`

	CnSponsors     string `gorm:"column:cn_sponsors" db:"cn_sponsors" json:"cn_sponsors" form:"cn_sponsors"`                     //赞助方-中文
	EnSponsors     string `gorm:"column:en_sponsors" db:"en_sponsors" json:"en_sponsors" form:"en_sponsors"`                     //赞助方-英文
	CnSponsorsLogo string `gorm:"column:cn_sponsors_logo" db:"cn_sponsors_logo" json:"cn_sponsors_logo" form:"cn_sponsors_logo"` //赞助方Logo-中文
	EnSponsorsLogo string `gorm:"column:en_sponsors_logo" db:"en_sponsors_logo" json:"en_sponsors_logo" form:"en_sponsors_logo"` //赞助方Logo-英文

	CnSponsorshipType  string `gorm:"column:cn_sponsorship_type" db:"cn_sponsorship_type" json:"cn_sponsorship_type" form:"cn_sponsorship_type"`     //赞助类型（0赞助方，1合作伙伴）
	EnSponsorshipType  string `gorm:"column:en_sponsorship_type" db:"en_sponsorship_type" json:"en_sponsorship_type" form:"en_sponsorship_type"`     //赞助类型（0赞助方，1合作伙伴）
	CnSponsors2        string `gorm:"column:cn_sponsors2" db:"cn_sponsors2" json:"cn_sponsors2" form:"cn_sponsors2"`                                 //赞助方-中文
	EnSponsors2        string `gorm:"column:en_sponsors2" db:"en_sponsors2" json:"en_sponsors2" form:"en_sponsors2"`                                 //赞助方-英文
	CnSponsorsLogo2    string `gorm:"column:cn_sponsors_logo2" db:"cn_sponsors_logo2" json:"cn_sponsors_logo2" form:"cn_sponsors_logo2"`             //赞助方Logo-中文
	EnSponsorsLogo2    string `gorm:"column:en_sponsors_logo2" db:"en_sponsors_logo2" json:"en_sponsors_logo2" form:"en_sponsors_logo2"`             //赞助方Logo-英文
	CnSponsorshipType2 string `gorm:"column:cn_sponsorship_type2" db:"cn_sponsorship_type2" json:"cn_sponsorship_type2" form:"cn_sponsorship_type2"` //赞助类型（0赞助方，1合作伙伴）
	EnSponsorshipType2 string `gorm:"column:en_sponsorship_type2" db:"en_sponsorship_type2" json:"en_sponsorship_type2" form:"en_sponsorship_type2"` //赞助类型（0赞助方，1合作伙伴）

	CnButtonLink string `json:"cn_button_link" db:"cn_button_link" gorm:"cn_button_link" form:"cn_button_link"` // 中文预约参会按钮跳转链接
	EnButtonLink string `json:"en_button_link" db:"en_button_link" gorm:"en_button_link" form:"en_button_link"` // 英文预约参会按钮跳转链接
}

type ResAdminConferenceEventDownList struct {
	Info ResPagination             `json:"info"`
	List []ConferenceEventDownInfo `json:"list"`
}

type ConferenceEventDownInfo struct {
	Id           int64  `json:"id" db:"id" gorm:"id" form:"id"`
	ConferenceId int64  `json:"conference_id"  form:"conference_id"`                //展会id
	EventId      int64  `json:"event_id"  form:"event_id"`                          //活动id
	CnName       string `json:"cn_name" db:"cn_name" gorm:"cn_name" form:"cn_name"` //展会活动名称--中文
	EnName       string `json:"en_name" db:"en_name" gorm:"en_name" form:"en_name"` //展会活动名称--英文
	IsSelected   bool   `json:"is_selected" form:"is_selected"`                     //是否被选中 （true已被选中）
}

type ResUserConferenceEventList struct {
	Info ResPagination `json:"info"`
	//Category ConferenceEventCategory `json:"category_info"`
	List []ResConferenceEvent `json:"list"`
}

// ConferenceEventCategory 活动类别
type ConferenceEventCategory struct {
	Id           int64  `json:"id" db:"id" gorm:"id" form:"id"`
	ConferenceId int64  `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"`
	CnDate       string `json:"cn_date" db:"cn_date" gorm:"cn_date" form:"cn_date"`                             // 中文日期
	EnDate       string `json:"en_date" db:"en_date" gorm:"en_date" form:"en_date"`                             // 英文日期
	EntryType    string `json:"entry_type" db:"entry_type" gorm:"entry_type" form:"entry_type"`                 // 入场类型
	CnEntryType  string `json:"cn_entry_type" db:"cn_entry_type" gorm:"cn_entry_type" form:"cn_entry_type"`     // 中文入场类型
	EnEntryType  string `json:"en_entry_type" db:"en_entry_type" gorm:"en_entry_type" form:"en_entry_type"`     // 英文入场类型
	CnName       string `json:"cn_name" db:"cn_name" gorm:"cn_name" form:"cn_name"`                             // 活动分类名称-中文
	EnName       string `json:"en_name" db:"en_name" gorm:"en_name" form:"en_name"`                             // 活动分类名称-英文
	CnTitle      string `json:"cn_title" db:"cn_title" gorm:"cn_title" form:"cn_title"`                         // 中文标题
	EnTitle      string `json:"en_title" db:"en_title" gorm:"en_title" form:"en_title"`                         // 英文标题
	CnPlace      string `json:"cn_place" db:"cn_place" gorm:"cn_place" form:"cn_place"`                         // 中文地点
	EnPlace      string `json:"en_place" db:"en_place" gorm:"en_place" form:"en_place"`                         // 英文地点
	CnContent    string `json:"cn_content" db:"cn_content" gorm:"cn_content" form:"cn_content"`                 // 中文内容
	EnContent    string `json:"en_content" db:"en_content" gorm:"en_content" form:"en_content"`                 // 英文内容
	Type         int    `json:"type" db:"type" gorm:"type" form:"type"`                                         // 关联导航菜单（1CLNB展前会，2CLNB主论坛，3CLNB分论坛，4户外赛事，5场馆赛事，6Releasing Stage 新品发布）
	CnButtonLink string `json:"cn_button_link" db:"cn_button_link" gorm:"cn_button_link" form:"cn_button_link"` // 中文预约参会按钮跳转链接
	EnButtonLink string `json:"en_button_link" db:"en_button_link" gorm:"en_button_link" form:"en_button_link"` // 英文预约参会按钮跳转链接
	Picture      string `json:"picture" db:"picture" gorm:"picture" form:"picture"`                             // 图片
	CnLanguage   string `json:"cn_language" form:"cn_language"`                                                 // 中文语言
	EnLanguage   string `json:"en_language" form:"en_language"`                                                 // 英文标题
}

type ResConferenceEvent struct {
	Id     int64                 `json:"id"`
	Date   string                `json:"date"`
	CnDate string                `json:"cn_date"`
	EnDate string                `json:"en_date"`
	List   []ConferenceEventInfo `json:"event_list"`
}

type RespAdminConferenceEventInfo struct {
	ID              int64  `json:"id" db:"id" gorm:"id" form:"id"`
	ConferenceId    int64  `json:"conference_id"  form:"conference_id"`                                                               //展会id
	CnName          string `json:"cn_name" db:"cn_name" gorm:"cn_name" form:"cn_name"`                                                //展会活动名称--中文
	EnName          string `json:"en_name" db:"en_name" gorm:"en_name" form:"en_name"`                                                //展会活动名称--英文
	CategoryType    int    `json:"category_type" db:"category_type" gorm:"category_type" form:"category_type"`                        // 所属活动分类（1CLNB展前会，2CLNB主论坛，3CLNB分论坛，4户外赛事，5场馆赛事，6Releasing Stage 新品发布）
	ScheduleIsForum string `gorm:"column:schedule_is_forum" db:"schedule_is_forum" json:"schedule_is_forum" form:"schedule_is_forum"` //日程是否有论坛（0无分论坛，1有分论坛）
	ScheduleTotal   int    `gorm:"column:schedule_total" db:"schedule_total" json:"schedule_total" form:"schedule_total"`             //活动日程总天数
	IsDisplayPage   string `gorm:"column:is_display_page" db:"is_display_page" json:"is_display_page" form:"is_display_page"`         //是否展示活动详情页（0是，1否）
	Sorting         string `json:"sorting" db:"sorting" gorm:"sorting" form:"sorting"`                                                //排序
	Deleted         int64  `json:"deleted" db:"deleted" gorm:"deleted" form:"deleted"`
	EventTime       string `json:"event_time" db:"event_time" gorm:"event_time" form:"event_time"`                                //活动时间-开始日期
	CnSponsors      string `gorm:"column:cn_sponsors" db:"cn_sponsors" json:"cn_sponsors" form:"cn_sponsors"`                     //赞助方-中文
	EnSponsors      string `gorm:"column:en_sponsors" db:"en_sponsors" json:"en_sponsors" form:"en_sponsors"`                     //赞助方-英文
	CnSponsorsLogo  string `gorm:"column:cn_sponsors_logo" db:"cn_sponsors_logo" json:"cn_sponsors_logo" form:"cn_sponsors_logo"` //赞助方Logo-中文
	EnSponsorsLogo  string `gorm:"column:en_sponsors_logo" db:"en_sponsors_logo" json:"en_sponsors_logo" form:"en_sponsors_logo"` //赞助方Logo-英文

	CnSponsorshipType  string `gorm:"column:cn_sponsorship_type" db:"cn_sponsorship_type" json:"cn_sponsorship_type" form:"cn_sponsorship_type"`     //赞助类型（0赞助方，1合作伙伴）
	EnSponsorshipType  string `gorm:"column:en_sponsorship_type" db:"en_sponsorship_type" json:"en_sponsorship_type" form:"en_sponsorship_type"`     //赞助类型（0赞助方，1合作伙伴）
	CnSponsors2        string `gorm:"column:cn_sponsors2" db:"cn_sponsors2" json:"cn_sponsors2" form:"cn_sponsors2"`                                 //赞助方-中文
	EnSponsors2        string `gorm:"column:en_sponsors2" db:"en_sponsors2" json:"en_sponsors2" form:"en_sponsors2"`                                 //赞助方-英文
	CnSponsorsLogo2    string `gorm:"column:cn_sponsors_logo2" db:"cn_sponsors_logo2" json:"cn_sponsors_logo2" form:"cn_sponsors_logo2"`             //赞助方Logo-中文
	EnSponsorsLogo2    string `gorm:"column:en_sponsors_logo2" db:"en_sponsors_logo2" json:"en_sponsors_logo2" form:"en_sponsors_logo2"`             //赞助方Logo-英文
	CnSponsorshipType2 string `gorm:"column:cn_sponsorship_type2" db:"cn_sponsorship_type2" json:"cn_sponsorship_type2" form:"cn_sponsorship_type2"` //赞助类型（0赞助方，1合作伙伴）
	EnSponsorshipType2 string `gorm:"column:en_sponsorship_type2" db:"en_sponsorship_type2" json:"en_sponsorship_type2" form:"en_sponsorship_type2"` //赞助类型（0赞助方，1合作伙伴）

	ScheduleDateList []model.EventScheduleDate `json:"schedule_date_list"` //日程日期信息列表
}

type RespUserConferenceEventInfo struct {
	ID              int64  `json:"id" db:"id" gorm:"id" form:"id"`
	ConferenceId    int64  `json:"conference_id"  form:"conference_id"`                                                               //展会id
	CnName          string `json:"cn_name" db:"cn_name" gorm:"cn_name" form:"cn_name"`                                                //展会活动名称--中文
	EnName          string `json:"en_name" db:"en_name" gorm:"en_name" form:"en_name"`                                                //展会活动名称--英文
	CategoryType    int    `json:"category_type" db:"category_type" gorm:"category_type" form:"category_type"`                        // 所属活动分类（1CLNB展前会，2CLNB主论坛，3CLNB分论坛，4户外赛事，5场馆赛事，6Releasing Stage 新品发布）
	ScheduleIsForum string `gorm:"column:schedule_is_forum" db:"schedule_is_forum" json:"schedule_is_forum" form:"schedule_is_forum"` //日程是否有论坛（0无分论坛，1有分论坛）
	IsDisplayPage   string `gorm:"column:is_display_page" db:"is_display_page" json:"is_display_page" form:"is_display_page"`         //是否展示活动详情页（0是，1否）

	CnSponsors         string `gorm:"column:cn_sponsors" db:"cn_sponsors" json:"cn_sponsors" form:"cn_sponsors"`                                     //赞助方-中文
	EnSponsors         string `gorm:"column:en_sponsors" db:"en_sponsors" json:"en_sponsors" form:"en_sponsors"`                                     //赞助方-英文
	CnSponsorsLogo     string `gorm:"column:cn_sponsors_logo" db:"cn_sponsors_logo" json:"cn_sponsors_logo" form:"cn_sponsors_logo"`                 //赞助方Logo-中文
	EnSponsorsLogo     string `gorm:"column:en_sponsors_logo" db:"en_sponsors_logo" json:"en_sponsors_logo" form:"en_sponsors_logo"`                 //赞助方Logo-英文
	CnSponsorshipType  string `gorm:"column:cn_sponsorship_type" db:"cn_sponsorship_type" json:"cn_sponsorship_type" form:"cn_sponsorship_type"`     //赞助类型（0赞助方，1合作伙伴）
	EnSponsorshipType  string `gorm:"column:en_sponsorship_type" db:"en_sponsorship_type" json:"en_sponsorship_type" form:"en_sponsorship_type"`     //赞助类型（0赞助方，1合作伙伴）
	CnSponsors2        string `gorm:"column:cn_sponsors2" db:"cn_sponsors2" json:"cn_sponsors2" form:"cn_sponsors2"`                                 //赞助方-中文
	EnSponsors2        string `gorm:"column:en_sponsors2" db:"en_sponsors2" json:"en_sponsors2" form:"en_sponsors2"`                                 //赞助方-英文
	CnSponsorsLogo2    string `gorm:"column:cn_sponsors_logo2" db:"cn_sponsors_logo2" json:"cn_sponsors_logo2" form:"cn_sponsors_logo2"`             //赞助方Logo-中文
	EnSponsorsLogo2    string `gorm:"column:en_sponsors_logo2" db:"en_sponsors_logo2" json:"en_sponsors_logo2" form:"en_sponsors_logo2"`             //赞助方Logo-英文
	CnSponsorshipType2 string `gorm:"column:cn_sponsorship_type2" db:"cn_sponsorship_type2" json:"cn_sponsorship_type2" form:"cn_sponsorship_type2"` //赞助类型（0赞助方，1合作伙伴）
	EnSponsorshipType2 string `gorm:"column:en_sponsorship_type2" db:"en_sponsorship_type2" json:"en_sponsorship_type2" form:"en_sponsorship_type2"` //赞助类型（0赞助方，1合作伙伴）

	ScheduleTotal    int                           `gorm:"column:schedule_total" db:"schedule_total" json:"schedule_total" form:"schedule_total"` //活动日程总天数
	Sorting          string                        `json:"sorting" db:"sorting" gorm:"sorting" form:"sorting"`                                    //排序
	Deleted          int64                         `json:"deleted" db:"deleted" gorm:"deleted" form:"deleted"`
	EventDateList    []RespEventDateInfo           `json:"event_date_list"`    //活动详情日期时间
	GuestList        []model.ConferenceInformation `json:"guest_list"`         //嘉宾信息
	InformationList  []ConferenceColumnInfo        `json:"information_list"`   //赞助商，合作伙伴，媒体等信息
	ScheduleDateList []model.EventScheduleDate     `json:"schedule_date_list"` //日程日期信息列表

}
type RespEventDateInfo struct {
	Date string `json:"date" db:"date" gorm:"date" form:"date"` //日期值
	Time string `json:"time" db:"time" gorm:"time" form:"time"` //时间值
}

type ResAdminEventScheduleForumList struct {
	Info ResPagination              `json:"info"`
	List []model.EventScheduleForum `json:"list"`
}

type RespEventScheduleForumInfo struct {
	ID           int64   `json:"id" db:"id" gorm:"id" form:"id"`
	ConferenceId int64   `json:"conference_id"  form:"conference_id"`                //展会id
	CnName       string  `json:"cn_name" db:"cn_name" gorm:"cn_name" form:"cn_name"` //分论坛名称-中文
	EnName       string  `json:"en_name" db:"en_name" gorm:"en_name" form:"en_name"` //分论坛名称-英文
	DayId        int64   `json:"day_id" db:"day_id" gorm:"day_id" form:"day_id"`     //日期ID
	Sorting      float32 `json:"sorting" db:"sorting" gorm:"sorting" form:"sorting"` //排序
	Deleted      int64   `json:"deleted" db:"deleted" gorm:"deleted" form:"deleted"`
}

type ResAdminEventScheduleList struct {
	Info ResPagination   `json:"info"`
	List []EventSchedule `json:"list"`
}

type EventSchedule struct {
	ID            int64     `json:"id" db:"id" gorm:"id" form:"id"`
	ConferenceId  int64     `json:"conference_id"  form:"conference_id"`                    //展会id
	EventId       int64     `json:"event_id"  form:"event_id"`                              //活动id
	DayId         int64     `json:"day_id" db:"day_id" gorm:"day_id" form:"day_id"`         //属于哪一天
	ForumId       int64     `json:"forum_id" db:"forum_id" gorm:"forum_id" form:"forum_id"` //分论坛ID
	CnTitle       string    `json:"cn_title" db:"cn_title" gorm:"cn_title" form:"cn_title"` //展会日程名称--中文
	EnTitle       string    `json:"en_title" db:"en_title" gorm:"en_title" form:"en_title"` //展会日程名称--英文
	ScheduleStart string    `json:"schedule_start" db:"schedule_start" gorm:"schedule_start" form:"schedule_start"`
	ScheduleEnd   string    `json:"schedule_end" db:"schedule_end" gorm:"schedule_end" form:"schedule_end"`
	CnContent     string    `json:"cn_content" db:"cn_content" gorm:"cn_content" form:"cn_content"` // 会议日程描述--中文
	EnContent     string    `json:"en_content" db:"en_content" gorm:"en_content" form:"en_content"` // 会议日程描述--英文
	IsAgenda      int64     `json:"is_agenda" db:"is_agenda" gorm:"is_agenda" form:"is_agenda"`     //0议程、1餐食、2咖啡、3鸡尾酒
	Sorting       string    `json:"sorting" db:"sorting" gorm:"sorting" form:"sorting"`
	Deleted       int64     `json:"deleted" db:"deleted" gorm:"deleted" form:"deleted"`
	CreateTime    time.Time `json:"create_time" db:"create_time" gorm:"create_time" form:"create_time"`
	UpdateTime    time.Time `json:"update_time" db:"update_time" gorm:"update_time" form:"update_time"`
	CreateAdmin   string    `json:"create_admin" db:"create_admin" gorm:"create_admin" form:"create_admin"`
	UpdateAdmin   string    `json:"update_admin" db:"update_admin" gorm:"update_admin" form:"update_admin"`
}

type ResAdminEventScheduleDateList struct {
	Info ResPagination             `json:"info"`
	List []model.EventScheduleDate `json:"list"`
}

type RespEventScheduleInfo struct {
	ID            int64   `json:"id" db:"id" gorm:"id" form:"id"`
	ConferenceId  int64   `json:"conference_id"  form:"conference_id"`                                            //展会id
	EventId       int64   `json:"event_id"  form:"event_id"`                                                      //活动id
	DayId         int64   `json:"day_id" db:"day_id" gorm:"day_id" form:"day_id"`                                 //属于哪一天
	ForumId       int64   `json:"forum_id" db:"forum_id" gorm:"forum_id" form:"forum_id"`                         //分论坛ID
	CnTitle       string  `json:"cn_title" db:"cn_title" gorm:"cn_title" form:"cn_title"`                         //展会日程名称--中文
	EnTitle       string  `json:"en_title" db:"en_title" gorm:"en_title" form:"en_title"`                         //展会日程名称--英文
	ScheduleStart string  `json:"schedule_start" db:"schedule_start" gorm:"schedule_start" form:"schedule_start"` //日程开始时间 （一般展示时分"15:18"）
	ScheduleEnd   string  `json:"schedule_end" db:"schedule_end" gorm:"schedule_end" form:"schedule_end"`         //日程结束时间 （一般展示时分"15:28"）
	CnContent     string  `json:"cn_content" db:"cn_content" gorm:"cn_content" form:"cn_content"`                 // 会议日程描述--中文
	EnContent     string  `json:"en_content" db:"en_content" gorm:"en_content" form:"en_content"`                 // 会议日程描述--英文
	IsAgenda      int64   `json:"is_agenda" db:"is_agenda" gorm:"is_agenda" form:"is_agenda"`                     //0议程、1餐食、2咖啡、3鸡尾酒
	Sorting       float32 `json:"sorting" db:"sorting" gorm:"sorting" form:"sorting"`                             //排序
	Deleted       int64   `json:"deleted" db:"deleted" gorm:"deleted" form:"deleted"`
}

type ResAdminEventScheduleGuestList struct {
	Info ResPagination              `json:"info"`
	List []model.EventScheduleGuest `json:"list"`
}

type RespEventScheduleGuestInfo struct {
	ID            int64  `json:"id" db:"id" gorm:"id" form:"id"`
	ConferenceId  int64  `json:"conference_id"  form:"conference_id"`  //展会id
	ScheduleId    int64  `json:"schedule_id" gorm:"schedule_id"`       //日程ID
	Picture       string `json:"picture" gorm:"picture"`               //图片
	CnCompany     string `json:"cn_company" form:"cn_company"`         //公司
	EnCompany     string `json:"en_company" form:"en_company"`         //公司
	CnPosition    string `json:"cn_position" form:"cn_position"`       //职位
	EnPosition    string `json:"en_position" form:"en_position"`       //职位
	CnAppellation string `json:"cn_appellation" form:"cn_appellation"` //称谓
	EnAppellation string `json:"en_appellation" form:"en_appellation"` //称谓
	GuestIdentity string `json:"guest_identity" gorm:"guest_identity"` //嘉宾身份
	Sorting       string `json:"sorting" gorm:"sorting"`               //排序
	Deleted       int64  `json:"deleted" db:"deleted" gorm:"deleted" form:"deleted"`
}

type RespIntroductionInfo struct {
	ID                  int64  `json:"id" db:"id" gorm:"id" form:"id"`
	IntroductionDiagram string `json:"introduction_diagram" db:"introduction_diagram" gorm:"introduction_diagram" form:"introduction_diagram"` // 会议介绍配图
	CnContent           string `json:"cn_content" db:"cn_content" gorm:"cn_content" form:"cn_content"`                                         // 大会介绍-页面内容-中文
	EnContent           string `json:"en_content" db:"en_content" gorm:"en_content" form:"en_content"`                                         // 大会介绍-页面内容-英文
	IntroductionVideo   string `json:"introduction_video" db:"introduction_video" gorm:"introduction_video" form:"introduction_video"`         // 介绍视频
	VideoCover          string `json:"video_cover" db:"video_cover" gorm:"video_cover" form:"video_cover"`                                     // 视频封面
	ConferenceId        int64  `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"`                             //展会ID
}

type RespConferenceSponsorInfo struct {
	ID           int64  `json:"id" db:"id" gorm:"id" form:"id"`
	CnTitle      string `json:"cn_title" db:"cn_title" gorm:"cn_title" form:"cn_title"`     //赞助标题-中文
	EnTitle      string `json:"en_title" db:"en_title" gorm:"en_title" form:"en_title"`     //赞助标题-英文
	CnButton     string `json:"cn_button" db:"cn_button" gorm:"cn_button" form:"cn_button"` //赞助按钮名称-中文
	EnButton     string `json:"en_button" db:"en_button" gorm:"en_button" form:"en_button"` //赞助按钮名称-英文
	CnPdf        string `json:"cn_pdf" db:"cn_pdf" gorm:"cn_pdf" form:"cn_pdf"`
	EnPdf        string `json:"en_pdf" db:"en_pdf" gorm:"en_pdf" form:"en_pdf"`
	CnContent    string `json:"cn_content" db:"cn_content" gorm:"cn_content" form:"cn_content"`
	EnContent    string `json:"en_content" db:"en_content" gorm:"en_content" form:"en_content"` //
	ConferenceId int64  `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"`
}

type ResAdminConferenceDataList struct {
	Info ResPagination    `json:"info"`
	List []ConferenceData `json:"list"`
}

type ConferenceData struct {
	ID           int64     `json:"id" db:"id" gorm:"id" form:"id"`
	ConferenceId int64     `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"`
	CnDataName   string    `json:"cn_data_name" db:"cn_data_name" gorm:"cn_data_name" form:"cn_data_name"`     //指标名称-中文
	CnDataValue  string    `json:"cn_data_value" db:"cn_data_value" gorm:"cn_data_value" form:"cn_data_value"` //指标数据-中文
	EnDataName   string    `json:"en_data_name" db:"en_data_name" gorm:"en_data_name" form:"en_data_name"`     //指标名称-英文
	EnDataValue  string    `json:"en_data_value" db:"en_data_value" gorm:"en_data_value" form:"en_data_value"` //指标数据-英文
	DataImage    string    `json:"data_image" db:"data_image" gorm:"data_image" form:"data_image"`             ///图标图片
	Sorting      string    `json:"sorting" db:"sorting" gorm:"sorting" form:"sorting"`                         //排序
	Deleted      int       `json:"deleted" db:"deleted" gorm:"deleted" form:"deleted"`
	CreateTime   time.Time `json:"create_time" db:"create_time" gorm:"create_time" form:"create_time"`
	UpdateTime   time.Time `json:"update_time" db:"update_time" gorm:"update_time" form:"update_time"`
	CreateAdmin  string    `json:"create_admin" db:"create_admin" gorm:"create_admin" form:"create_admin"`
	UpdateAdmin  string    `json:"update_admin" db:"update_admin" gorm:"update_admin" form:"update_admin"`
}

type ResWebConferenceIntroductionList struct {
	Info                   ResPagination                  `json:"info"`
	DataList               []model.ConferenceData         `json:"conference_data"`                                                                                //大会数据列表
	OrganizationList       []model.ConferenceOrganization `json:"conference_organization"`                                                                        //大会组织结构列表
	CnBottomPageList       []model.ConferenceBottomPage   `json:"cn_conference_bottom_page"`                                                                      //大会组织结构底部页面列表-中文
	EnBottomPageList       []model.ConferenceBottomPage   `json:"en_conference_bottom_page"`                                                                      //大会组织结构底部页面列表-英文
	ContactInformationList []model.ContactInformation     `json:"contact_information"`                                                                            //大会组织-联系信息
	IntroductionVideo      string                         `json:"introduction_video" db:"introduction_video" gorm:"introduction_video" form:"introduction_video"` // 介绍视频
	VideoCover             string                         `json:"video_cover" db:"video_cover" gorm:"video_cover" form:"video_cover"`                             // 视频封面
	ConferenceId           int64                          `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"`
}

type RespIntroductionDataInfo struct {
	ID           int64   `json:"id" db:"id" gorm:"id" form:"id"`
	CnDataName   string  `json:"cn_data_name" db:"cn_data_name" gorm:"cn_data_name" form:"cn_data_name"`     //指标名称-中文
	CnDataValue  string  `json:"cn_data_value" db:"cn_data_value" gorm:"cn_data_value" form:"cn_data_value"` //指标数据-中文
	EnDataName   string  `json:"en_data_name" db:"en_data_name" gorm:"en_data_name" form:"en_data_name"`     //指标名称-英文
	EnDataValue  string  `json:"en_data_value" db:"en_data_value" gorm:"en_data_value" form:"en_data_value"` //指标数据-英文
	DataImage    string  `json:"data_image" db:"data_image" gorm:"data_image" form:"data_image"`             ///图标图片
	Sorting      float32 `json:"sorting" db:"sorting" gorm:"sorting" form:"sorting"`                         //排序
	Deleted      string  `json:"deleted" db:"deleted" gorm:"deleted" form:"deleted"`
	CreateTime   string  `json:"create_time" db:"create_time" gorm:"create_time" form:"create_time"`
	UpdateTime   string  `json:"update_time" db:"update_time" gorm:"update_time" form:"update_time"`
	CreateAdmin  string  `json:"create_admin" db:"create_admin" gorm:"create_admin" form:"create_admin"`
	UpdateAdmin  string  `json:"update_admin" db:"update_admin" gorm:"update_admin" form:"update_admin"`
	ConferenceId int64   `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"`
}

type ResAdminConferenceOrganizationList struct {
	Info ResPagination                  `json:"info"`
	List []model.ConferenceOrganization `json:"list"`
}

type ResAdminContactInformationList struct {
	Info ResPagination              `json:"info"`
	List []model.ContactInformation `json:"list"`
}

type ResAdminConferenceBottomPageList struct {
	Info ResPagination                `json:"info"`
	List []model.ConferenceBottomPage `json:"list"`
}

type RespOrganizationInfo struct {
	ID           int64  `json:"id" db:"id" gorm:"id" form:"id"`
	CnName       string `json:"cn_name" db:"cn_name" gorm:"cn_name" form:"cn_name"` // 单位名称/二维码名称-中文
	EnName       string `json:"en_name" db:"en_name" gorm:"en_name" form:"en_name"` // 单位名称/二维码名称-英文
	Logo         string `json:"logo" db:"logo" gorm:"logo" form:"logo"`             // 二维码链接
	Sorting      string `json:"sorting" db:"sorting" gorm:"sorting" form:"sorting"` // 排序
	Type         string `json:"type" db:"type" gorm:"type" form:"type"`             // 数据类型（1主办单位，2承办单位，3二维码信息，4现场图片）
	Deleted      string `json:"deleted" db:"deleted" gorm:"deleted" form:"deleted"`
	CreateTime   string `json:"create_time" db:"create_time" gorm:"create_time" form:"create_time"`
	UpdateTime   string `json:"update_time" db:"update_time" gorm:"update_time" form:"update_time"`
	CreateAdmin  string `json:"create_admin" db:"create_admin" gorm:"create_admin" form:"create_admin"`
	UpdateAdmin  string `json:"update_admin" db:"update_admin" gorm:"update_admin" form:"update_admin"`
	ConferenceId int64  `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"`
}

type RespContactInformationInfo struct {
	ID            int64   `json:"id" db:"id" gorm:"id" form:"id"`
	ConferenceId  int64   `json:"conference_id" gorm:"conference_id"`
	CnTitle       string  `json:"cn_title" gorm:"cn_title"`
	EnTitle       string  `json:"en_title" gorm:"en_title"`
	CnContent     string  `json:"cn_content" gorm:"cn_content"`
	EnContent     string  `json:"en_content" gorm:"en_content"`
	CnIsDisplayed int     `json:"cn_is_displayed" gorm:"cn_is_displayed"` //其他页底是否显示（0  否 1  是）
	EnIsDisplayed int     `json:"en_is_displayed" gorm:"en_is_displayed"` //其他页底是否显示（0  否 1  是）
	Sorting       float32 `json:"sorting" gorm:"sorting"`
}

type RespBottomPageInfo struct {
	ID           int64  `json:"id" db:"id" gorm:"id" form:"id"`
	CnName       string `json:"cn_name" db:"cn_name" gorm:"cn_name" form:"cn_name"`             // 单位名称/二维码名称-中文
	EnName       string `json:"en_name" db:"en_name" gorm:"en_name" form:"en_name"`             // 单位名称/二维码名称-英文
	CnLogo       string `json:"cn_logo" db:"cn_logo" gorm:"cn_logo" form:"cn_logo"`             // 二维码链接-中文
	EnLogo       string `json:"en_logo" db:"en_logo" gorm:"en_logo" form:"en_logo"`             // 二维码链接-英文
	CnSorting    string `json:"cn_sorting" db:"cn_sorting" gorm:"cn_sorting" form:"cn_sorting"` // 排序-中文
	EnSorting    string `json:"en_sorting" db:"en_sorting" gorm:"en_sorting" form:"en_sorting"` // 排序-英文
	Deleted      string `json:"deleted" db:"deleted" gorm:"deleted" form:"deleted"`
	CreateTime   string `json:"create_time" db:"create_time" gorm:"create_time" form:"create_time"`
	UpdateTime   string `json:"update_time" db:"update_time" gorm:"update_time" form:"update_time"`
	CreateAdmin  string `json:"create_admin" db:"create_admin" gorm:"create_admin" form:"create_admin"`
	UpdateAdmin  string `json:"update_admin" db:"update_admin" gorm:"update_admin" form:"update_admin"`
	ConferenceId int64  `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"`
}

type ResUserEventScheduleList struct {
	CnName       string `json:"cn_name"`        // 活动名称-中文
	EnName       string `json:"en_name"`        // 活动名称-英文
	Category     int    `json:"category_type"`  // 活动分类
	TemplateType int    `json:"template_type"`  // 日程模版类型
	CnButtonLink string `json:"cn_button_link"` // 中文预约参会按钮跳转链接
	EnButtonLink string `json:"en_button_link"` // 英文预约参会按钮跳转链接

	CnSponsors         string `gorm:"column:cn_sponsors" db:"cn_sponsors" json:"cn_sponsors" form:"cn_sponsors"`                                     //赞助方-中文
	EnSponsors         string `gorm:"column:en_sponsors" db:"en_sponsors" json:"en_sponsors" form:"en_sponsors"`                                     //赞助方-英文
	CnSponsorsLogo     string `gorm:"column:cn_sponsors_logo" db:"cn_sponsors_logo" json:"cn_sponsors_logo" form:"cn_sponsors_logo"`                 //赞助方Logo-中文
	EnSponsorsLogo     string `gorm:"column:en_sponsors_logo" db:"en_sponsors_logo" json:"en_sponsors_logo" form:"en_sponsors_logo"`                 //赞助方Logo-英文
	CnSponsorshipType  string `gorm:"column:cn_sponsorship_type" db:"cn_sponsorship_type" json:"cn_sponsorship_type" form:"cn_sponsorship_type"`     //赞助类型（0赞助方，1合作伙伴）
	EnSponsorshipType  string `gorm:"column:en_sponsorship_type" db:"en_sponsorship_type" json:"en_sponsorship_type" form:"en_sponsorship_type"`     //赞助类型（0赞助方，1合作伙伴）
	CnSponsors2        string `gorm:"column:cn_sponsors2" db:"cn_sponsors2" json:"cn_sponsors2" form:"cn_sponsors2"`                                 //赞助方-中文
	EnSponsors2        string `gorm:"column:en_sponsors2" db:"en_sponsors2" json:"en_sponsors2" form:"en_sponsors2"`                                 //赞助方-英文
	CnSponsorsLogo2    string `gorm:"column:cn_sponsors_logo2" db:"cn_sponsors_logo2" json:"cn_sponsors_logo2" form:"cn_sponsors_logo2"`             //赞助方Logo-中文
	EnSponsorsLogo2    string `gorm:"column:en_sponsors_logo2" db:"en_sponsors_logo2" json:"en_sponsors_logo2" form:"en_sponsors_logo2"`             //赞助方Logo-英文
	CnSponsorshipType2 string `gorm:"column:cn_sponsorship_type2" db:"cn_sponsorship_type2" json:"cn_sponsorship_type2" form:"cn_sponsorship_type2"` //赞助类型（0赞助方，1合作伙伴）
	EnSponsorshipType2 string `gorm:"column:en_sponsorship_type2" db:"en_sponsorship_type2" json:"en_sponsorship_type2" form:"en_sponsorship_type2"` //赞助类型（0赞助方，1合作伙伴）

	List []EventScheduleInfo `json:"list"` //日程列表
	Info ResPagination       `json:"info"`
}

type EventScheduleInfo struct {
	ID            int64                      `json:"id" db:"id" gorm:"id" form:"id"`
	ConferenceId  int64                      `json:"conference_id"  form:"conference_id"`                    //展会id
	EventId       int64                      `json:"event_id"  form:"event_id"`                              //活动id
	DayId         int64                      `json:"day_id" db:"day_id" gorm:"day_id" form:"day_id"`         //属于哪一天
	ForumId       int64                      `json:"forum_id" db:"forum_id" gorm:"forum_id" form:"forum_id"` //分论坛ID
	CnTitle       string                     `json:"cn_title" db:"cn_title" gorm:"cn_title" form:"cn_title"` //展会日程名称--中文
	EnTitle       string                     `json:"en_title" db:"en_title" gorm:"en_title" form:"en_title"` //展会日程名称--英文
	ScheduleStart string                     `json:"schedule_start" db:"schedule_start" gorm:"schedule_start" form:"schedule_start"`
	ScheduleEnd   string                     `json:"schedule_end" db:"schedule_end" gorm:"schedule_end" form:"schedule_end"`
	CnContent     string                     `json:"cn_content" db:"cn_content" gorm:"cn_content" form:"cn_content"` // 会议日程描述--中文
	EnContent     string                     `json:"en_content" db:"en_content" gorm:"en_content" form:"en_content"` // 会议日程描述--英文
	IsAgenda      int64                      `json:"is_agenda" db:"is_agenda" gorm:"is_agenda" form:"is_agenda"`     //0议程、1餐食、2咖啡、3鸡尾酒
	Sorting       float32                    `json:"sorting" db:"sorting" gorm:"sorting" form:"sorting"`
	CnButtonLink  string                     `json:"cn_button_link" form:"cn_button_link"` // 中文预约参会按钮跳转链接
	EnButtonLink  string                     `json:"en_button_link" form:"en_button_link"` // 英文预约参会按钮跳转链接
	GuestList     []model.EventScheduleGuest `json:"guest_list" form:"guest_list"`         //日程嘉宾列表
}

type ResUserEventScheduleForumList struct {
	List []model.EventScheduleForum `json:"list"` //日程列表
	Info ResPagination              `json:"info"`
}

type ResAboutUs struct {
	ConferenceID int64  `json:"conference_id"`
	CnAboutUs    string `json:"cn_about_us"`
	EnAboutUs    string `json:"en_about_us"`
	CreateUser   string `json:"create_user,omitempty"`
	UpdateUser   string `json:"update_user,omitempty"`
	CreateTime   string `json:"create_time,omitempty"`
	UpdateTime   string `json:"update_time,omitempty"`
}

// 搜索内容
type ResSearchDataList struct {
	Info ResPagination   `json:"info"`
	List []ResSearchData `json:"list"`
}

type ResSearchData struct {
	DataId       int64  `json:"data_id"`       // 数据ID(用于跳转传参)
	DataType     int    `json:"data_type"`     // 数据类型(1:活动,2:展商,3:嘉宾)
	Title        string `json:"title"`         // 标题
	Content      string `json:"content"`       // 内容
	CategoryType int    `json:"category_type"` // 关联导航菜单（1CLNB展前会，2CLNB主论坛，3CLNB分论坛，4户外赛事，5场馆赛事，6Releasing Stage 新品发布）
}

type RespCustomInfo struct {
	Name  string `json:"name" form:"name"`
	Email string `json:"email" form:"email"`
}
type Message struct {
	Code int              `json:"code" form:"code"`
	Msg  string           `json:"msg" form:"msg"`
	Data *json.RawMessage `json:"data" form:"data"` // must be *json.RawMessage
}

type RespService struct {
	ServiceList []Service `json:"service_list"`
}

type RespMeetingChannelList struct {
	ChannelList []model.MeetingChannel `json:"service_list"`
}

type Service struct {
	CurrencyUnit      int             `json:"currency_unit"`
	PriceTypeList     []PriceType     `json:"price_type_list"`
	SalesStrategyList []SalesStrategy `json:"sales_strategy_list"`

	ServiceID   int64  `json:"service_id"`
	ServiceName string `json:"service_name"`
	AppName     string `json:"app_name"`
}
type SalesStrategy struct {
	RequireNum int     `json:"require_num"`
	UnitPrice  float64 `json:"unit_price"`
}
type PriceType struct {
	Price float64 `json:"price"`
}

type Standards struct {
	Delegates    string  `json:"delegates"`
	EarlyBird    float64 `json:"early_bird"`
	CurrencyUnit int     `json:"currency_unit"`
}

type ResConferenceRegisterFromList struct {
	ID           int64  `json:"id"`
	ConferenceId int64  `json:"conference_id" form:"conference_id" db:"conference_id" gorm:"conference_id"` //展会ID
	RegisterId   int64  `json:"register_id" db:"register_id" gorm:"register_id" form:"register_id"`
	FromName     string `json:"from_name" form:"from_name" db:"from_name" gorm:"from_name"` //渠道
	FromType     int    `json:"from_type" db:"from_type" gorm:"from_type" form:"from_type"` //
	CreateTime   string `json:"create_time" db:"create_time" gorm:"create_time" form:"create_time"`
	Deleted      int    `json:"deleted" db:"deleted" gorm:"deleted" form:"deleted"`
}

type RespBatchImportAudience struct {
	Result string `json:"result"`
}

type ResForumCategoryConfigList struct {
	List []model.ForumCategoryConfig `json:"list"` //日程列表
	Info ResPagination               `json:"info"`
}

type ConferenceNewsSet struct {
	ID           int64  `json:"id"`
	ConferenceId int64  `json:"conference_id" form:"conference_id" db:"conference_id" gorm:"conference_id"` //展会ID
	SetId        int    `json:"set_id"  form:"set_id" db:"set_id" gorm:"set_id"`
	ExpoNews     string `json:"expo_news"  form:"expo_news" db:"expo_news" gorm:"expo_news"`
}

type ResEventExpertInfoList struct {
	List []model.EventExpertInfo `json:"list"` //
	Info ResPagination           `json:"info"`
}

type ResAnnualSelectionList struct {
	List []model.AnnualSelection `json:"list"` //
	Info ResPagination           `json:"info"`
}

type ResAdminEnterpriseTypeList struct {
	Info ResPagination                    `json:"info"`
	List []model.ConferenceEnterpriseType `json:"list"`
}

type ResAdminMediaRegistrationList struct {
	Info ResPagination             `json:"info"`
	List []model.MediaRegistration `json:"list"`
}

type RespMediaRegistrationConfigInfo struct {
	ID                  int64  `json:"id" db:"id" gorm:"id" form:"id"`
	CnNavigationDisplay int    `json:"cn_navigation_display" form:"cn_navigation_display" gorm:"cn_navigation_display"` //媒体注册中文导航是否显示（0  否 1  是）
	EnNavigationDisplay int    `json:"en_navigation_display" form:"en_navigation_display" gorm:"en_navigation_display"` //英文其他页底是否显示（0  否 1  是）
	CnContent           string `json:"cn_content" form:"cn_content" gorm:"cn_content"`                                  //中文内容
	EnContent           string `json:"en_content" form:"en_content" gorm:"en_content"`                                  //英文内容
	ConferenceId        int64  `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"`      //展会ID
}

type CnResMediaRegistrationInfo struct {
	ID             int64  `json:"id"`
	ConferenceName string `json:"conference_name" excel_smm:"展会名称,A"` // 展会名称中文
	SourceName     string `json:"source_name" excel_smm:"来源,B""`      //来源名称
	//FromId         string `json:"fromId" excel_smm:"渠道,C""`           //渠道
	FromName     string `json:"from_name" excel_smm:"渠道名称,C"`
	Name         string `json:"name" excel_smm:"媒体人姓名,D"`                                  // 购票人姓名
	ConferenceID int64  `json:"conference_id" excel_smm:"展会id,E"`                          // 展会ID
	UserId       int64  `json:"user_id" excel_smm:"报名人用户ID,F"`                             //报名人用户ID
	Email        string `json:"email" excel_smm:"邮箱,G"`                                    // 邮箱
	Mobile       string `gorm:"column:mobile" db:"mobile" json:"mobile" excel_smm:"手机号,H"` //手机号
	Organisation string `json:"organisation" excel_smm:"媒体名,I"`                            //媒体名
	JobTitle     string `json:"job_title" form:"job_title" excel_smm:"职位,J"`               //职位
	CreateTime   string `json:"create_time" excel_smm:"提交时间,K"`                            // 提交时间
	UpdateTime   string `json:"-"`
}

type EnResMediaRegistrationInfo struct {
	ID             int64  `json:"id"`
	ConferenceName string `json:"conference_name" excel_smm:"展会名称,A"` // 展会名称中文
	SourceName     string `json:"source_name" excel_smm:"来源,B""`      //来源名称
	//FromId         string `json:"fromId" excel_smm:"渠道,C""`           //渠道
	FromName     string `json:"from_name" excel_smm:"渠道名称,C"`
	Name         string `json:"name" excel_smm:"媒体人姓名,D"`                                  // 购票人姓名
	ConferenceID int64  `json:"conference_id" excel_smm:"展会id,E"`                          // 展会ID
	UserId       int64  `json:"user_id" excel_smm:"报名人用户ID,F"`                             //报名人用户ID
	Email        string `json:"email" excel_smm:"邮箱,G"`                                    // 邮箱
	Mobile       string `gorm:"column:mobile" db:"mobile" json:"mobile" excel_smm:"手机号,H"` //手机号
	Organisation string `json:"organisation" excel_smm:"媒体名,I"`                            //媒体名
	JobTitle     string `json:"job_title" form:"job_title" excel_smm:"职位,J"`               //职位
	Country      string `json:"country" excel_smm:"国家,K"`                                  //国家
	CreateTime   string `json:"create_time" excel_smm:"提交时间,L"`                            // 提交时间
	UpdateTime   string `json:"-"`
}
