package protocol

// ApplyConference 申请参展
type ApplyConference struct {
	ConferenceId      int64  `json:"conference_id" form:"conference_id" ` //展会id
	CnApplyConference string `json:"cn_apply_conference" form:"cn_apply_conference" `
	EnApplyConference string `json:"en_apply_conference" form:"en_apply_conference" `
	OpUser            string `swaggerignore:"true"`
}

// ExhibitionSuccess 申请参展成功提示
type ExhibitionSuccess struct {
	ConferenceId     int64  `json:"conference_id" form:"conference_id"`           //展会id
	ExhibitionQrCode string `json:"exhibition_qr_code" form:"exhibition_qr_code"` //申请参展成功二维码
	ExhibitionTips   string `json:"exhibition_tips" form:"exhibition_tips"`       //申请参展成功文案提示语
	OpUser           string `swaggerignore:"true"`
}

// OnlineRegister 在线登记
type OnlineRegister struct {
	ConferenceId int64  `json:"conference_id" form:"conference_id" ` //展会id
	CnUrl        string `json:"cn_url" form:"cn_url" `               //中文在线登记表表单链接
	EnUrl        string `json:"en_url" form:"en_url" `               //英文在线登记表表单链接
	CnContent    string `json:"cn_content" form:"cn_content" `       //中文内容
	EnContent    string `json:"en_content" form:"en_content" `       //英文内容
	OpUser       string `swaggerignore:"true"`
}

// CompanyHandbook 展商手册
type CompanyHandbook struct {
	Id           int64   `json:"id" form:"id" valid:"required~展商手册主键id必传"`                   //展商手册id 0为新增
	ConferenceId int64   `json:"conference_id" form:"conference_id" valid:"required~展会id必传"` //展会id
	CnName       string  `json:"cn_name" form:"cn_name" valid:"required~文件中文名必传"`            //中文展商手册名称
	EnName       string  `json:"en_name" form:"en_name" `                                    //英文展商手册名称
	CnPdf        string  `json:"cn_pdf" form:"cn_pdf" valid:"required~中文展商手册必传"`             //中文pdf
	EnPdf        string  `json:"en_pdf" form:"en_pdf" `                                      //英文pdf
	Sort         float32 `json:"sort" form:"sort" valid:"range(0|9999999)~排序要大于等于0"`         //排序
	OpUser       string  `swaggerignore:"true"`
}

// SetUp 保存现场搭建
type SetUp struct {
	ConferenceId int64  `json:"conference_id" form:"conference_id" ` //展会id
	CnSetUp      string `json:"cn_set_up" form:"cn_set_up" `         //中文现场搭建
	EnSetUp      string `json:"en_set_up" form:"en_set_up" `         //英文现场搭建
	OpUser       string `swaggerignore:"true"`
}

type Invitation struct {
	ConferenceId int64  `json:"conference_id" form:"conference_id" valid:"required~展会id必传"`      //展会id
	CnInvitation string `json:"cn_invitation" gorm:"column:cn_invitation" form:"cn_invitation" ` //中文观展邀请
	EnInvitation string `json:"en_invitation" gorm:"column:en_invitation" form:"en_invitation" ` //英文观展邀请
	OpUser       string `swaggerignore:"true"`
}

type CommonQuestionType struct {
	Id             int64   `json:"id" form:"id"`                                                       //主键id
	ConferenceId   int64   `json:"conference_id" form:"conference_id" valid:"required~展会id必传"`         //展会id
	CnQuestionType string  `json:"cn_question_type" form:"cn_question_type" valid:"required~问题类型中文必传"` //展商问题类型中文
	EnQuestionType string  `json:"en_question_type" form:"en_question_type" `                          //展商问题类型英文
	Sort           float32 `json:"sort" form:"sort" valid:"range(0|9999999)~排序要大于等于0"`                 //排序
	OpUser         string  `swaggerignore:"true"`
}

type QuestionContent struct {
	Id                int64   `json:"id" form:"id"`                                                           //主键id
	QuestionTypeId    int64   `json:"question_type_id" form:"question_type_id" valid:"required~问题类型id必传"`     //问题类型id
	CnQuestionContent string  `json:"cn_question_content" form:"cn_question_content" valid:"required~问题中文必传"` //问题中文
	EnQuestionContent string  `json:"en_question_content" form:"en_question_content" `                        //问题英文
	CnQuestionAnswer  string  `json:"cn_question_answer" form:"cn_question_answer" valid:"required~中文答案必传"`   //答案中文
	EnQuestionAnswer  string  `json:"en_question_answer" form:"en_question_answer" `                          //问题英文
	Sort              float32 `json:"sort" form:"sort" valid:"range(0|9999999)~排序要大于等于0"`                     //排序
	OpUser            string  `swaggerignore:"true"`
}
