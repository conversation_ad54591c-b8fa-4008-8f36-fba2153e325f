package protocol

type HallInfo struct {
	ConferenceId      int64  `json:"conference_id" form:"conference_id"  valid:"required~展会id必传"`                //展会id
	CnHeading         string `json:"cn_heading" db:"cn_heading" gorm:"cn_heading" form:"cn_heading"`             //上标题-中文
	EnHeading         string `json:"en_heading" db:"en_heading" gorm:"en_heading" form:"en_heading"`             //上标题-英文
	CnHeadlining      string `json:"cn_headlining" db:"cn_headlining" gorm:"cn_headlining" form:"cn_headlining"` //下标题-中文
	EnHeadlining      string `json:"en_headlining" db:"en_headlining" gorm:"en_headlining" form:"en_headlining"` //下标题-英文
	LocationLongitude string `json:"location_longitude" form:"location_longitude"  valid:"required~经度必传"`        //展馆位置经度
	LocationLatitude  string `json:"location_latitude" form:"location_latitude"  valid:"required~纬度必传"`          //展馆位置纬度
	GoogleUrl         string `json:"google_url" form:"google_url"  valid:"required~谷歌地图链接必传"`                    //谷歌地图分享链接
	CnFloorGraph      string `json:"cn_floor_graph" form:"cn_floor_graph"  valid:"required~展馆平面图必传"`             //展馆平面图url
	EnFloorGraph      string `json:"en_floor_graph" form:"en_floor_graph"`                                       //英文展馆平面图url
	OpUser            string `swaggerignore:"true"`
}

type HotelPage struct {
	ConferenceId   int64  `json:"conference_id" form:"conference_id"  valid:"required~展会id必传"`          //展会id
	CnHotelExcel   string `json:"cn_hotel_excel" form:"cn_hotel_excel"`                                 //中文酒店表格
	EnHotelExcel   string `json:"en_hotel_excel" form:"en_hotel_excel"`                                 //中文酒店表格
	CnHotelButton  string `json:"cn_hotel_button" form:"cn_hotel_button"`                               //中文酒店按钮
	EnHotelButton  string `json:"en_hotel_button" form:"en_hotel_button" `                              //英文酒店按钮
	CnBookingUrl   string `json:"cn_booking_url" form:"cn_booking_url" `                                //中文预定表单链接
	EnBookingUrl   string `json:"en_booking_url" form:"en_booking_url" `                                //英文预定表单链接
	CnHotelContent string `json:"cn_hotel_content" form:"cn_hotel_content" valid:"required~中文住宿页面内容必传"` //中文酒店页面内容
	EnHotelContent string `json:"en_hotel_content" form:"en_hotel_content" `                            //英文酒店页面内容
	OpUser         string `swaggerignore:"true"`
}

type TrafficService struct {
	ConferenceId     int64  `json:"conference_id" form:"conference_id"  valid:"required~展会id必传"` //展会id
	CnTrafficContent string `json:"cn_traffic_content" form:"cn_traffic_content" `               //中文交通内容
	EnTrafficContent string `json:"en_traffic_content" form:"en_traffic_content" `               //英文交通内容
	OpUser           string `swaggerignore:"true"`
}

type CityInfo struct {
	ConferenceId   int64  `json:"conference_id" form:"conference_id"  valid:"required~展会id必传"`        //展会id
	CnCityOverview string `json:"cn_city_overview" form:"cn_city_overview" valid:"required~中文城市概况必传"` //中文城市概况
	EnCityOverview string `json:"en_city_overview" form:"en_city_overview" `                          //英文城市概况
	OpUser         string `swaggerignore:"true"`
}
