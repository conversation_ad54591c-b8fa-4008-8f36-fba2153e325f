package protocol

import (
	"conferencecenter/internal/model"
	"mime/multipart"
	"time"
)

type ReqComList struct {
	ReqPage
	Content  string `form:"content" json:"content"`     // 查询内容
	Status   int    `form:"status" json:"status"`       // 企业状态,10启用,-10禁用
	IsShow   int    `form:"is_show" json:"is_show"`     // 是否展示,10展示,-10隐藏
	IsExport bool   `form:"is_export" json:"is_export"` // 是否导出
}

type ReqSetStatus struct {
	ComId  int64 `form:"com_id" json:"com_id" binding:"required,min=1"`        // 公司ID
	Status int   `form:"status" json:"status" binding:"required,oneof=-10 10"` // 企业状态,10启用,-10禁用
	OpUser string
}

type ReqComDetail struct {
	ComId  int64  `form:"com_id" json:"com_id"`   // 公司ID
	RegNum string `form:"reg_num" json:"reg_num"` // 工商登记
}

type ReqSetCode struct {
	ComId   int64  `form:"com_id" json:"com_id" binding:"required,min=1"` // 公司ID
	ComCode string `form:"com_code" json:"com_code" binding:"required"`   // 公司编码
	OpUser  string
}

type ReqOriginCom struct {
	ReqPage
	Search string `form:"search" json:"search" binding:"required"` // 搜索内容:企业名、注册号或统一社会信用代码
}

type ReqComData struct {
	BaseInfo    model.BaseInfo       `form:"base_info" json:"base_info" binding:"required,dive"` // 基本信息
	ExecutiveLi []model.ExecuteInfo  `form:"executive_li" json:"executive_li"`                   // 高管信息
	BusinessLi  []model.BusinessInfo `form:"business_li" json:"business_li"`                     // 主营构成
	OpUser      string               `form:"op_user" json:"op_user"`
}

type ReqComInfo struct {
	ID               int64  `json:"com_id" form:"com_id"`         // 企业ID
	ComCode          string `json:"com_code" form:"com_code"`     // 企业编码
	IsList           bool   `json:"is_list" form:"is_list"`       // 是否上市
	ExchCode         string `json:"exch_code" form:"exch_code"`   // 交易所代号
	StockNum         string `json:"stock_num" form:"stock_num"`   // 股票编号
	StockCode        string `json:"stock_code" form:"stock_code"` // 股票代码
	Name             string `json:"name" form:"name"`             // 公司名称
	NameEn           string `json:"name_en" form:"name_en"`       // 英文名称
	NameAbbr         string `json:"name_abbr" form:"name_abbr"`   // A股简称
	Formername       string `json:"formername" form:"formername"` // 曾用名
	NamePinyin       string // 公司名称拼音
	NameAbbrPinyin   string // A股简称拼音
	FormernamePinyin string // 曾用名拼音
	Industry         string `json:"industry" form:"industry"`                 // 所属东财行业
	SecurityType     string `json:"security_type" form:"security_type"`       // 证券类别
	TradeMarket      string `json:"trade_market" form:"trade_market"`         // 上市交易所
	Industrycsrc     string `json:"industrycsrc" form:"industrycsrc"`         // 所属证监会行业
	President        string `json:"president" form:"president"`               // 总经理
	LegalPerson      string `json:"legal_person" form:"legal_person"`         // 法人代表
	Secretary        string `json:"secretary" form:"secretary"`               // 公司秘书
	Chairman         string `json:"chairman" form:"chairman"`                 // 董事长
	SecPresent       string `json:"secpresent" form:"secpresent"`             // 董秘
	Indirectors      string `json:"indedirectors" form:"indedirectors"`       // 独立董事
	OrgTel           string `json:"org_tel" form:"org_tel"`                   // 公司电话
	OrgEmail         string `json:"org_email" form:"org_email"`               // 公司邮箱
	OrgFax           string `json:"org_fax" form:"org_fax"`                   // 公司传真
	OrgWeb           string `json:"org_web" form:"org_web"`                   // 公司网址
	Address          string `json:"address" form:"address"`                   // 公司地址
	RegAddress       string `json:"reg_address" form:"reg_address"`           // 注册地址
	RegCapital       string `json:"reg_capital" form:"reg_capital"`           // 注册资本
	RegNum           string `json:"reg_num" form:"reg_num"`                   // 注册号
	EmpNum           int    `json:"emp_num" form:"emp_num"`                   // 员工人数
	Tatolnumber      int    `json:"tatolnumber" form:"tatolnumber"`           // 总股本
	LawFirm          string `json:"law_firm" form:"law_firm"`                 // 律师事务所
	AccountfirmName  string `json:"accountfirm_name" form:"accountfirm_name"` // 会计师事务所
	OrgProfile       string `json:"org_profile" form:"org_profile"`           // 公司简介
	BusinessScope    string `json:"business_scope" form:"business_scope"`     // 经营范围
	ListingDate      string `json:"listing_date" form:"listing_date"`         // 上市日期
	Status           int    `json:"status" form:"status"`                     // 公司状态：10启用，-10禁用
	ListingTime      int64
}

type ReqExecute struct {
	ComID         int64  `json:"com_id" form:"com_id"`                 // 企业ID
	StockNum      string `json:"stock_num" form:"stock_num"`           // 股票编号
	StockCode     string `json:"stock_code" form:"stock_code"`         // 股票代码
	Age           string `json:"age" form:"age"`                       // 年龄
	HighDegree    string `json:"high_degree" form:"high_degree"`       // 学历
	HoldNum       int64  `json:"hold_num" form:"hold_num"`             // 持股数(股)
	IncumbentTime string `json:"incumbent_time" form:"incumbent_time"` // 任职时间
	IsCompare     bool   `json:"is_compare" form:"is_compare"`         // 是否是董事
	PersonCode    string `json:"person_code" form:"person_code"`       // 人物code
	PersonName    string `json:"person_name" form:"person_name"`       // 人物名称
	Position      string `json:"position" form:"position"`             // 职务
	PositionCode  string `json:"position_code" form:"position_code"`   // 职务类型id
	Resume        string `json:"resume" form:"resume"`                 // 人物简介
	Salary        int32  `json:"salary" form:"salary"`                 // 薪酬(元)
	Sex           string `json:"sex" form:"sex"`                       // 性别
}

type ReqBusiness struct {
	ComID      int64   `json:"com_id" form:"com_id"`           // 企业ID
	StockNum   string  `json:"stock_num" form:"stock_num"`     // 股票编号
	StockCode  string  `json:"stock_code" form:"stock_code"`   // 股票代码
	Rank       string  `json:"rank" form:"rank"`               // 顺序
	ItemName   string  `json:"item_name" form:"item_name"`     // 主营构成
	MbcRatio   float64 `json:"mbc_ratio" form:"mbc_ratio"`     // 成本比例
	MbiRatio   float64 `json:"mbi_ratio" form:"mbi_ratio"`     // 收入比例
	MbrRatio   float64 `json:"mbr_ratio" form:"mbr_ratio"`     // 利润比例
	Rpofit     float64 `json:"rpofit" form:"rpofit"`           // 主营利润(元)
	Income     float64 `json:"income" form:"income"`           // 主营收入(元)
	Cost       float64 `json:"cost" form:"cost"`               // 主营成本(元)
	Ratio      float64 `json:"ratio" form:"ratio"`             // 毛利率
	MainopType int16   `json:"mainop_type" form:"mainop_type"` // 分类类别，1按行业，2按产品，3按地区
	ReportDate string  `json:"report_date" form:"report_date"` // 数据时间
	ReportTime int64
}

// 展会列表
type ReqAdminConferenceList struct {
	Keyword string `json:"keyword" form:"keyword"` // 搜索词
	ReqPage
}

type ReqAdminConferenceNameList struct {
	Type int `json:"type" form:"type"` // 1 参展信息 2 赞助信息 3 付费信息 4 国内免费观众 5 国外免费观众 6 国内媒体报名信息 7 国外媒体报名信息
}

type ReqAdminSetConferenceStatus struct {
	ConferenceId int64  `json:"conference_id" form:"conference_id" binding:"required"` // 展会ID
	Status       int    `form:"status" json:"status" binding:"required,oneof=1 2 3"`   // 展会状态,1启用,2下线,3删除
	OpUser       string `swaggerignore:"true"`
}

type ReqAdminSetConferenceTemplate struct {
	ConferenceId int64  `json:"conference_id" form:"conference_id" binding:"required"` // 展会ID
	TemplateType int    `form:"template_type" json:"template_type"`                    // 展会日程模版类型:1时间&主题;0议程&非议程
	OpUser       string `swaggerignore:"true"`
}

// 往届列表
type ReqAdminPreviousList struct {
	ConferenceId int64 `json:"conference_id" form:"conference_id" binding:"required"` // 展会ID
	ReqPage
}

type ReqSaveProductionManagement struct {
	ID                int64   `json:"id" gorm:"id" form:"id"`                                                    // 主键id
	AudienceCompanyId int64   `json:"audience_company_id" gorm:"audience_company_id" form:"audience_company_id"` // 展商id
	CnName            string  `json:"cn_name" gorm:"cn_name" form:"cn_name"`                                     // 中文产品名称
	EnName            string  `json:"en_name" gorm:"en_name" form:"en_name"`                                     // 英文产品名称
	Logo              string  `json:"logo" gorm:"logo" form:"logo" valid:"required~图片必传"`
	Sorting           float32 `json:"sorting" gorm:"sorting" form:"sorting"` // 排序
	UserName          string  `swaggerignore:"true"`
}

// 订阅列表
type ReqAdminSubscribeList struct {
	Keyword   string `json:"keyword" form:"keyword"`       // 搜索词
	StartTime string `json:"start_time" form:"start_time"` // 开始时间
	EndTime   string `json:"end_time" form:"end_time"`     // 结束时间
	ReqPage
}

// 基本信息
type ReqAdminConferenceInfo struct {
	ID             int64  `json:"id" form:"id" binding:"gte=0"`                                   // 展会ID,大于0表示编辑;0表示新增
	CnName         string `json:"cn_name" form:"cn_name" binding:"required"`                      // 展会名称中文
	EnName         string `json:"en_name" form:"en_name"`                                         // 展会名称英文
	CnShortName    string `json:"cn_short_name" form:"cn_short_name" swaggerignore:"true"`        // 展会简称中文
	EnShortName    string `json:"en_short_name" form:"en_short_name" swaggerignore:"true"`        // 展会简称英文
	CnLocation     string `json:"cn_location" form:"cn_location" binding:"required"`              // 地点中文
	EnLocation     string `json:"en_location" form:"en_location"`                                 // 地点英文
	StartTime      int64  `json:"start_time" form:"start_time" binding:"required"`                // 开始时间戳
	EndTime        int64  `json:"end_time" form:"end_time" binding:"required,gtefield=StartTime"` // 结束时间戳
	CnTopLeftLogo  string `json:"cn_top_left_logo" form:"cn_top_left_logo"`                       // 左上角logo
	EnTopLeftLogo  string `json:"en_top_left_logo" form:"en_top_left_logo"`                       // 左上角logo
	TopLogo        string `json:"top_logo" form:"top_logo" binding:"required"`                    // 页顶logo
	TinyLogo       string `json:"tiny_logo" form:"tiny_logo" binding:"required"`                  // 小logo
	VideoBack      string `json:"video_back" form:"video_back"`                                   // 视频背景
	PcBack         string `json:"pc_back" form:"pc_back"`                                         // PC背景图
	H5Back         string `json:"h5_back" form:"h5_back"`                                         // H5背景图
	CnShareGraph   string `json:"cn_share_graph" form:"cn_share_graph"`                           // 分享图链接中文
	EnShareGraph   string `json:"en_share_graph" form:"en_share_graph"`                           // 分享图链接英文
	CnShareTitle   string `json:"cn_share_title" form:"cn_share_title"`                           // 分享标题中文
	EnShareTitle   string `json:"en_share_title" form:"en_share_title"`                           // 分享标题英文
	CnShareContent string `json:"cn_share_content" form:"cn_share_content"`                       // 分享内容中文
	EnShareContent string `json:"en_share_content" form:"en_share_content"`                       // 分享内容英文
	MeetingSysId   string `json:"meeting_sys_id" form:"meeting_sys_id"`                           // 同步会议系统编号
	QwxUrl         string `json:"qwx_url" form:"qwx_url"`                                         // 企业微信通知地址

	//TemplateType int64 `json:"template_type" form:"template_type"` // 日程模版类型

	ButtonLi []ButtonParam `json:"button_li" form:"button_li" binding:"dive"` // 跳转按钮
	Start    time.Time     `swaggerignore:"true"`
	End      time.Time     `swaggerignore:"true"`
	OpUser   string        `swaggerignore:"true"`
}

type ButtonParam struct {
	ConferenceID int64  `json:"conference_id" form:"conference_id" swaggerignore:"true"` // 展会ID
	CnName       string `json:"cn_name" form:"cn_name"`                                  // 按钮名称中文(必填)
	EnName       string `json:"en_name" form:"en_name"`                                  // 按钮名称英文(英文链接非空时，英文名称不能为空)
	CnURL        string `json:"cn_url" form:"cn_url"`                                    // 跳转链接中文(必填)
	EnURL        string `json:"en_url" form:"en_url"`                                    // 跳转链接英文(英文名称非空时，英文链接不能为空)
}

type ReqAdminPreviousInfo struct {
	ID           int64   `json:"id" form:"id" binding:"gte=0"`                          // ID,大于0表示编辑;0表示新增
	ConferenceID int64   `json:"conference_id" form:"conference_id" binding:"required"` // 关联展会ID
	CnName       string  `json:"cn_name" form:"cn_name" binding:"required"`             // 名称中文
	EnName       string  `json:"en_name" form:"en_name"`                                // 名称英文
	CnURL        string  `json:"cn_url" form:"cn_url" binding:"required"`               // 链接中文
	EnURL        string  `json:"en_url" form:"en_url"`                                  // 链接英文
	CnPdf        string  `json:"cn_pdf" form:"cn_pdf"`                                  // 往届PDF链接中文
	EnPdf        string  `json:"en_pdf" form:"en_pdf"`                                  // 往届PDF链接英文
	Sort         float32 `json:"sort" form:"sort" binding:"gte=0"`                      // 排序
	OpUser       string  `swaggerignore:"true"`
}

type ReqAdminShortMap struct {
	ShortName    string `json:"short_name" form:"short_name" binding:"required"`       // 展会简称
	ConferenceID int64  `json:"conference_id" form:"conference_id" binding:"required"` // 关联展会ID
	OpUser       string `swaggerignore:"true"`
}

// 查询往届列表
type ReqUserPreviousList struct {
	ConferenceID int64 `json:"conference_id" form:"conference_id" binding:"required"` // 关联展会ID
	ReqPage
}

type ReqUserSubscribe struct {
	ConferenceID int64  `json:"conference_id" form:"conference_id" binding:"required"` // 关联展会ID
	Cellphone    string `json:"cellphone" form:"cellphone"`                            // 手机
	Email        string `json:"email" form:"email"`                                    // 邮箱
}

type ReqUserMeetingNo struct {
	MeetingNo string `json:"meeting_no" form:"meeting_no"` // 会议编号
}

// ReqAudiencePreRegister 观众预登记记录
type ReqAudiencePreRegister struct {
	Id             int64  `form:"id" json:"id" `                                                      //展会id
	ConferenceId   int64  `form:"conference_id" json:"conference_id" valid:"required~未传展会id"`         //展会id
	CnTitle        string `form:"cn_title" json:"cn_title" valid:"required~中文页面标题必传"`                 //中文页面标题
	EnTitle        string `form:"en_title" json:"en_title" `                                          //英文页面标题
	CnHandbookName string `form:"cn_handbook_name" json:"cn_handbook_name" valid:"required~中文手册名称必传"` //中文手册名称
	EnHandbookName string `form:"en_handbook_name" json:"en_handbook_name"`                           //英文手册名称
	CnUrl          string `form:"cn_url" json:"cn_url" valid:"required~中文手册必传"`                       //中文观众手册url
	EnUrl          string `form:"en_url" json:"en_url"`                                               //英文观众手册url
	OpUser         string `swaggerignore:"true"`
}

// ReqAudienceVisitingValue 参观价值
type ReqAudienceVisitingValue struct {
	ConferenceId    int64  `form:"conference_id" json:"conference_id" valid:"required~未传展会id"`                 //展会id
	CnVisitingValue string `form:"cn_visiting_value" json:"cn_visiting_value" gorm:"column:cn_visiting_value"` //中文参观价值
	EnVisitingValue string `form:"en_visiting_value" json:"en_visiting_value" gorm:"column:en_visiting_value"` //英文参观价值
	OpUser          string `swaggerignore:"true"`
}

// 展商管理后台
type ReqCompanyManagement struct {
	Id                      int64   `json:"id" form:"id" gorm:"column:id"`                                                                                              // 展商id
	ConferenceId            int64   `json:"conference_id" form:"conference_id"  valid:"required~展会id必传" gorm:"column:conference_id"`                                    // 展会id
	CnConferenceCompany     string  `json:"cn_conference_company" form:"cn_conference_company"   gorm:"column:cn_conference_company"`                                   // 中文展商名称
	EnConferenceCompany     string  `json:"en_conference_company" form:"en_conference_company" gorm:"column:en_conference_company"`                                     // 英文展商名称
	CnBoothNumber           string  `json:"cn_booth_number" form:"cn_booth_number" gorm:"column:cn_booth_number"`                                                       // 中文展位号
	EnBoothNumber           string  `json:"en_booth_number" form:"en_booth_number" gorm:"column:en_booth_number"`                                                       // 英文展位号
	CnUrl                   string  `json:"cn_url" form:"cn_url" gorm:"column:cn_url"`                                                                                  // 中文展商链接
	EnUrl                   string  `json:"en_url" form:"en_url" gorm:"column:en_url"`                                                                                  // 英文展商链接
	Logo                    string  `json:"logo" form:"logo" gorm:"column:logo"`                                                                                        // logo的url
	CnExhibitorVideoLink    string  `json:"cn_exhibitor_video_link" form:"cn_exhibitor_video_link" gorm:"column:cn_exhibitor_video_link"`                               // 中文展商视频
	CnExhibitorVideoCover   string  `json:"cn_exhibitor_video_cover" form:"cn_exhibitor_video_cover" gorm:"column:cn_exhibitor_video_cover"`                            // 中文展商视频封面
	CnExhibitorNewsID       int64   `json:"cn_exhibitor_news_id" form:"cn_exhibitor_news_id" gorm:"column:cn_exhibitor_news_id"`                                        // 中文展商新闻id
	CnExhibitorIntroduction string  `json:"cn_exhibitor_introduction" form:"cn_exhibitor_introduction" gorm:"column:cn_exhibitor_introduction" valid:"required~展商介绍必传"` // 中文展商介绍
	CnExhibitorVenueID      int     `json:"cn_exhibitor_venue_id" form:"cn_exhibitor_venue_id" gorm:"column:cn_exhibitor_venue_id"`                                     // 展馆id
	Year                    string  `json:"year" form:"year" gorm:"column:year"`                                                                                        // 年度
	Sort                    float32 `json:"sort" form:"sort" gorm:"column:sort"`                                                                                        // 排序
	OpUser                  string  `swaggerignore:"true"`
}

type ReqSaveVenue struct {
	ID           int64   `json:"id" gorm:"id" form:"id"`
	ConferenceId int64   `json:"conference_id" gorm:"conference_id" form:"conference_id"`
	CnVenueName  string  `json:"cn_venue_name" gorm:"column:cn_venue_name" form:"cn_venue_name"  valid:"required~中文展馆名称必传"` // 中文场馆名称
	EnVenueName  string  `json:"en_venue_name" gorm:"column:en_venue_name" form:"en_venue_name" `                           // 英文场馆名称
	Sorting      float32 `json:"sorting" gorm:"sorting" form:"sorting" valid:"required~排序必传"`                               // 排序
	UserName     string  `swaggerignore:true`
}

type ReqCompanyId struct {
	Id int64 `json:"id" form:"id" gorm:"column:id" valid:"required~展会id必传"` //展商id
	ReqPage
}

type ReqProductionId struct {
	Id int64 `json:"id" form:"id" gorm:"column:id" valid:"required~产品id必传"` //产品信息id
}

type ReqVenueId struct {
	Id int64 `json:"id" form:"id" gorm:"column:id" valid:"required~场馆id必传"` //场馆id
}

type ReqCompanyHandbookId struct {
	Id int64 `json:"id" form:"id" gorm:"column:id"` //展商手册id

}

type ReqCompanyDirectories struct {
	IsCn         bool   `json:"is_cn" form:"is_cn" `                                         //中文站请求
	KeyWord      string `json:"key_word" form:"key_word"`                                    //关键字
	VenueId      int64  `json:"venue_id" form:"venue_id"`                                    //场馆id
	ConferenceId int64  `json:"conference_id" form:"conference_id"  valid:"required~展会id必传"` //展会id
	Year         string `json:"year" form:"year" gorm:"column:year"`                         // 年度
	ReqPage
}

type ReqFloorGraph struct {
	ConferenceId int64   `json:"conference_id" form:"conference_id"  valid:"required~展会id必传" gorm:"column:conference_id"` //展会id
	CnGraphUrl   string  `json:"cn_graph_url" form:"cn_graph_url"  valid:"required~展览平面图中文名称必传"`                          //中文平面图url
	EnGraphUrl   string  `json:"en_graph_url" form:"en_graph_url" `                                                       //英文平面图url
	CnGraphName  string  `json:"cn_graph_name" form:"cn_graph_name"  valid:"required~展览平面图中文站图片必传"`                       //中文展馆名称
	EnGraphName  string  `json:"en_graph_name" form:"en_graph_name" `                                                     //英文展馆名称
	Sort         float32 `json:"sort" valid:"range(0|9999999)~排序要大于等于0" form:"sort" `                                     //排序
	OpUser       string  `swaggerignore:"true"`
}
type ReqUpdateFloorGraph struct {
	Id int64 `json:"id" form:"id" valid:"required~主键id必传"` //主键id
	ReqFloorGraph
}

type ReqCommonQuestionType struct {
	ConferenceId   int64   `json:"conference_id" form:"conference_id"  valid:"required~展会id必传" gorm:"column:conference_id"` //展会id
	CnQuestionType string  `json:"cn_question_type" form:"cn_question_type"  valid:"required~问题类型中文必传"`                     //展商问题类型中文
	EnQuestionType string  `json:"en_question_type" form:"en_question_type"  gorm:"column:en_question_type"`                //展商问题类型英文
	Sort           float32 `json:"sort" form:"sort"  gorm:"column:sort" valid:"range(0|9999999)~排序要大于等于0"`
	OpUser         string  `swaggerignore:"true"`
	//排序
}

type CommonQuestionAnswerAdd struct {
	QuestionTypeId    int64   `json:"question_type_id" form:"question_type_id" valid:"required~请传问题类型id"`       //问题类型id
	CnQuestionContent string  `json:"cn_question_content" form:"cn_question_content" valid:"required~请传中文问题内容"` //问题内容中文
	EnQuestionContent string  `json:"en_question_content" form:"en_question_content" `                          //问题内容英文
	CnQuestionAnswer  string  `json:"cn_question_answer" form:"cn_question_answer" valid:"required~请传中文答案内容"`   //答案内容中文
	EnQuestionAnswer  string  `json:"en_question_answer" form:"en_question_answer" `                            //答案内容英文
	Sort              float32 `json:"sort" form:"sort" valid:"range(0|9999999)~排序要大于等于0"`                       //排序
	OpUser            string  `swaggerignore:"true"`
}

type CommonQuestionAnswerUpdate struct {
	Id int64 `json:"id" form:"id" ` //问答内容主键id
	CommonQuestionAnswerAdd
}

type EditCompanyHandbook struct {
	Id     int     `json:"id"`      //展商手册id
	CnName string  `json:"cn_name"` //中文展商手册名称
	EnName string  `json:"en_name"` //英文展商手册名称
	CnPdf  string  `json:"cn_pdf"`  //中文pdf
	EnPdf  string  `json:"en_pdf"`  //英文pdf
	Sort   float32 `json:"sort"`    //排序
}

type HandbookContent struct {
	ConferenceId      int64  `json:"conference_id" form:"conference_id" `             // 展会id
	CnHandbookTitle   string `json:"cn_handbook_title" form:"cn_handbook_title" `     // 展商手册中文标题
	EnHandbookTitle   string `json:"en_handbook_title" form:"en_handbook_title" `     // 展商手册英文标题
	CnHandbookContent string `json:"cn_handbook_content" form:"cn_handbook_content" ` // 展商手册中文页面内容
	EnHandbookContent string `json:"en_handbook_content" form:"en_handbook_content" ` // 展商手册英文页面内容
	OpUser            string `swaggerignore:"true"`
}

type ReqConferenceId struct {
	Id     int64 `json:"id" form:"id" valid:"required~展会id必传"` //展会id
	CnOrEn int   `form:"cn_or_en"  json:"cn_or_en"`            // 中文 0 英文 1
}

type ReqSaveAfterReport struct {
	Id           int64   `json:"id" form:"id"`                                                   // 主键id 修改时传
	ConferenceId int64   `json:"conference_id" gorm:"conference_id" form:"conference_id" `       // 展会id
	CnName       string  `json:"cn_name" gorm:"cn_name" valid:"required~文件名称必传" form:"cn_name" ` // 展后报告名称
	EnName       string  `json:"en_name" gorm:"en_name" form:"en_name" `                         // 展后报告英文名称
	CnPdf        string  `json:"cn_pdf" gorm:"cn_pdf" valid:"required~中文PDF必传" form:"cn_pdf" `   // 中文展后报告pdf
	EnPdf        string  `json:"en_pdf" gorm:"en_pdf" form:"en_pdf" `                            // 英文展后报告pdf
	Sort         float32 `json:"sort" gorm:"sort" valid:"required~排序必传" form:"sort" `            // 排序
}

type PageReqConferenceId struct {
	Year string `json:"year" form:"year"` // 年度
	ReqConferenceId
	ReqPage
}

type QuestionPaperAnswer struct {
	Question string `json:"question"` // 问题内容
	Choice   Option `json:"choice"`   // 选项
}

type ReqSaveQuestionPaper struct {
	Id      int64                 `json:"id" valid:"required~id必传"` // 上一步提交基本信息返回的id
	Content []QuestionPaperAnswer `json:"content"`                  // 问卷内容
}

type ReqSaveFreeVisitorInfo struct {
	CnVisitor    CnVisitor `json:"cn_visitor" form:"cn_visitor" `
	EnVisitor    EnVisitor `json:"en_visitor" form:"en_visitor" `
	CnOrEn       int       `json:"cn_or_en" form:"cn_or_en" ` // 0 中文 1 英文
	ConferenceID int64     `json:"conference_id" gorm:"conference_id" form:"conference_id" binding:"required"`
	SourceId     string    `json:"source_id" form:"source_id" `                           // 来源
	FromID       string    `json:"fromId" gorm:"column:fromId" form:"fromId" `            // 渠道
	BdVid        string    `gorm:"column:bd_vid" db:"bd_vid" json:"bd_vid" form:"bd_vid"` //百度跟踪ID

}

type ReqVisitorID struct {
	CnOrEn         int    `json:"cn_or_en" form:"cn_or_en" `                                            // 0 中文 1 英文
	ConferenceId   int64  `json:"conference_id" form:"conference_id"`                                   // 会议id
	ConferenceName string `json:"conference_name" form:"conference_name"`                               // 会议名称
	FullName       string `json:"full_name" form:"full_name" `                                          // 姓名
	Email          string `json:"email" form:"email"`                                                   // 邮箱
	FromID         string `json:"fromId"  form:"fromId" `                                               // 渠道
	FromName       string `json:"from_name" form:"from_name" `                                          // 渠道名称
	StartTime      string `json:"start_time" form:"start_time"`                                         // 开始时间
	EndTime        string `json:"end_time" form:"end_time"`                                             // 结束时间
	SourceID       int    `json:"source_id" form:"source_id" `                                          // 来源ID
	EnterpriseType string `json:"enterprise_type" form:"enterprise_type" gorm:"column:enterprise_type"` // 企业类型
	ReqPage
}

type ReqCompanyBearingID struct {
	ID int64 `json:"id" form:"id"` // 新闻id
}
type ReqAfterReportID struct {
	ID     int64  `json:"id" form:"id"`
	OpUser string `swaggerignore:"true"`
}
type ReqSavePaperCollection struct {
	ID           int64  `json:"id" gorm:"id" form:"id" `
	ConferenceID int64  `json:"conference_id" gorm:"conference_id" form:"conference_id" `                             // 展会id
	CnFormURL    string `json:"cn_form_url" gorm:"cn_form_url" form:"cn_form_url" valid:"required~中文表单链接必传"`          // 中文表单链接
	EnFormURL    string `json:"en_form_url" gorm:"en_form_url" form:"en_form_url" `                                   // 英文表单链接
	CnContent    string `json:"cn_content" gorm:"cn_content" form:"cn_content" valid:"required~中文页面内容必传"`             // 中文页面内容
	EnContent    string `json:"en_content" gorm:"en_content" form:"en_content" `                                      // 英文页面内容
	CnButtonName string `json:"cn_button_name" gorm:"cn_button_name" form:"cn_button_name" valid:"required~中文按钮标签必传"` // 中文按钮名称
	EnButtonName string `json:"en_button_name" gorm:"en_button_name" form:"en_button_name"`                           // 英文按钮名称
	CnAboveName  string `json:"cn_above_name" gorm:"cn_above_name" form:"cn_above_name" valid:"required~中文按钮上方标题必传"`  // 中文按钮上方名称
	EnAboveName  string `json:"en_above_name" gorm:"en_above_name" form:"en_above_name" `                             // 英文按钮上方名称
}

type ReqSaveAuthorNotice struct {
	ID           int64  `json:"id" gorm:"id" form:"id" `
	ConferenceID int64  `json:"conference_id" gorm:"conference_id" form:"conference_id" `    // 展会id
	CnFormURL    string `json:"cn_form_url" gorm:"cn_form_url" form:"cn_form_url" `          // 中文表单链接
	EnFormURL    string `json:"en_form_url" gorm:"en_form_url" form:"en_form_url" `          // 英文表单链接
	CnContent    string `json:"cn_content" gorm:"cn_content" form:"cn_content" `             // 中文页面内容
	EnContent    string `json:"en_content" gorm:"en_content" form:"en_content" `             // 英文页面内容
	CnButtonName string `json:"cn_button_name" gorm:"cn_button_name" form:"cn_button_name" ` // 中文按钮名称
	EnButtonName string `json:"en_button_name" gorm:"en_button_name" form:"en_button_name" ` // 英文按钮名称
	CnAboveName  string `json:"cn_above_name" gorm:"cn_above_name" form:"cn_above_name" `    // 中文按钮上方名称
	EnAboveName  string `json:"en_above_name" gorm:"en_above_name" form:"en_above_name" `    // 英文按钮上方名称
}

type ReqFloorGraphID struct {
	ID     int64  `json:"id" form:"id" valid:"required~平面图id必传"`
	OpUser string `swaggerignore:"true"`
}

type ReqUpdateQuestionType struct {
	Id             int64   `json:"id" form:"id" valid:"required~问题类型主键id必传"`
	OpUser         string  `swaggerignore:"true" gorm:"column:update_user"`
	ConferenceId   int64   `json:"conference_id" form:"conference_id" valid:"required~展会id必传"`         //展会id
	CnQuestionType string  `json:"cn_question_type" form:"cn_question_type" valid:"required~问题类型中文必传"` //展商问题类型中文
	EnQuestionType string  `json:"en_question_type" form:"en_question_type" `                          //展商问题类型英文
	Sort           float32 `json:"sort" form:"sort" valid:"range(0|9999999)~排序要大于等于0"`                 //排序
}
type ReqQuestionTypeId struct {
	Id     int64  `json:"id" form:"id" valid:"required~问题类型主键id必传"` //问题类型id
	OpUser string `swaggerignore:"true" gorm:"column:update_user"`
}

// ReqColumnList 通用模块列表
type ReqColumnList struct {
	ConferenceId int64  `json:"conference_id"  form:"conference_id"`  //展会id
	SubSectionId int64  `json:"sub_section_id" form:"sub_section_id"` //子栏目ID
	Name         string `json:"name" form:"name"`                     //搜索
	ReqPage
}

type ReqColumnInformationList struct {
	ConferenceId int64 `json:"conference_id"  form:"conference_id"`                               //展会id
	ColumnId     int64 `gorm:"column:column_id" db:"column_id" json:"column_id" form:"column_id"` //栏目ID(嘉宾列表时不传)
	Type         int   `gorm:"column:type" db:"type" json:"type" form:"type"`                     //模块类型（1赞助商&合作商&媒体，2嘉宾）
	IsEn         bool  `json:"is_en" form:"is_en"`                                                //是否是中文 false 中文，true 英文
	ReqPage
	IsAdmin bool `swaggerignore:"true"`
}

type ReqUserColumnInformationList struct {
	ConferenceId int64 `json:"conference_id" form:"conference_id"` //展会id
	Type         int   `json:"type" form:"type"`                   //模块类型（1赞助商，3合作商，4媒体）不传时返回除嘉宾外其他所有信息
	IsEn         bool  `json:"is_en" form:"is_en"`                 //是否是中文 false 中文，true 英文
}
type ReqUserGuestInformationList struct {
	ConferenceId int64 `json:"conference_id" form:"conference_id"` //展会id
	Type         int   `swaggerignore:"true"`
}

type ReqUserGuestInformationInfo struct {
	InformationId int64 `json:"information_id" form:"information_id"` //公司或嘉宾信息id
}

type ReqAdminSaveColumn struct {
	Id           int64   `json:"id" form:"id" form:"id"`                                                                //修改时必传，添加时不传
	ConferenceId int64   `json:"conference_id" form:"conference_id"`                                                    //展会id
	CnName       string  `gorm:"column:cn_name" db:"cn_name" json:"cn_name" form:"cn_name"`                             //栏目名称-中文
	EnName       string  `gorm:"column:en_name" db:"en_name" json:"en_name" form:"en_name"`                             //栏目名称-英文
	Type         int     `gorm:"column:type" db:"type" json:"type" form:"type"`                                         //信息类型（1赞助商，3合作商，4媒体）
	Sorting      float32 `gorm:"column:sorting" db:"sorting" json:"sorting" form:"sorting"`                             //排序
	IsSubSection int     `gorm:"column:is_sub_section" db:"is_sub_section" json:"is_sub_section" form:"is_sub_section"` //是否有子栏目
	SubSectionId int64   `gorm:"column:sub_section_id" db:"sub_section_id" json:"sub_section_id" form:"sub_section_id"` // 父栏目ID（如果不是从父级栏目来可以不传）
	AdminEmail   string  `swaggerignore:"true"`
}

type ReqAdminColumnInfo struct {
	Id         int64  `json:"id" form:"id"` //栏目ID
	AdminEmail string `swaggerignore:"true"`
}

// ReqInformationList 通用模块列表
type ReqInformationList struct {
	ConferenceId int64 `json:"conference_id" form:"conference_id"` //展会id
	ColumnId     int64 `json:"column_id" form:"column_id"`         //栏目ID（嘉宾列表时不需要传）
	Type         int   `swaggerignore:"true"`
	ReqPage
}

type ReqAdminInformationInfo struct {
	Id         int64  `json:"id" form:"id"` //栏目信息ID
	AdminEmail string `swaggerignore:"true"`
}

type ReqUserInformationList struct {
	ConferenceId int64 `json:"conference_id"  form:"conference_id"`                               //展会id
	ColumnId     int64 `gorm:"column:column_id" db:"column_id" json:"column_id" form:"column_id"` //栏目ID
	Type         int   `json:"type" form:"type"`                                                  //信息类型（1赞助商，2嘉宾，3合作商，4媒体）
}

type ReqAdminSaveInformation struct {
	Id            int64   `json:"id" form:"id" form:"id"`
	ConferenceId  int64   `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"`            //展会ID
	ColumnId      int64   `gorm:"column:column_id" db:"column_id" json:"column_id" form:"column_id"`                     //栏目ID
	CnCompany     string  `gorm:"column:cn_company" db:"cn_company" json:"cn_company" form:"cn_company"`                 //公司
	EnCompany     string  `gorm:"column:en_company" db:"en_company" json:"en_company" form:"en_company"`                 //公司
	CnLink        string  `gorm:"column:cn_link" db:"cn_link" json:"cn_link" form:"cn_link"`                             //链接
	EnLink        string  `gorm:"column:en_link" db:"en_link" json:"en_link" form:"en_link"`                             //链接
	CnPicture     string  `gorm:"column:cn_picture" db:"cn_picture" json:"cn_picture" form:"cn_picture"`                 //logo图-中文
	EnPicture     string  `gorm:"column:en_picture" db:"en_picture" json:"en_picture" form:"en_picture"`                 //logo图-英文
	CnAppellation string  `gorm:"column:cn_appellation" db:"cn_appellation" json:"cn_appellation" form:"cn_appellation"` //称谓
	EnAppellation string  `gorm:"column:en_appellation" db:"en_appellation" json:"en_appellation" form:"en_appellation"` //称谓
	CnPosition    string  `gorm:"column:cn_position" db:"cn_position" json:"cn_position" form:"cn_position"`             //职位
	EnPosition    string  `gorm:"column:en_position" db:"en_position" json:"en_position" form:"en_position"`             //职位
	CnContent     string  `gorm:"column:cn_content" db:"cn_content"  json:"cn_content" form:"cn_content"`                //嘉宾介绍--中文
	EnContent     string  `gorm:"column:en_content" db:"en_content"  json:"en_content" form:"en_content"`                //嘉宾介绍--英文
	Type          int     `gorm:"column:type" db:"type" json:"type" form:"type"`                                         //信息类型（1赞助商，2嘉宾，3合作商，4媒体）
	Sorting       float32 `gorm:"column:sorting" db:"sorting" json:"sorting" form:"sorting"`                             //排序
	CnSorting     float32 `gorm:"column:cn_sorting" db:"cn_sorting" json:"cn_sorting" form:"cn_sorting"`                 //排序-中文
	EnSorting     float32 `gorm:"column:en_sorting" db:"en_sorting" json:"en_sorting" form:"en_sorting"`                 //排序-英文
	AdminEmail    string  `swaggerignore:"true"`
}

type ReqAdminSaveInformationEvent struct {
	Id            int64  `json:"id" form:"id" form:"id"`
	ConferenceId  int64  `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"`     //展会ID
	InformationId int64  `json:"information_id" db:"information_id" gorm:"information_id" form:"information_id"` //信息ID
	EventId       string `json:"event_id" db:"event_id" gorm:"event_id" form:"event_id"`                         //活动ID，多个以英文逗号分割,(主会议传0)
	AdminEmail    string `swaggerignore:"true"`
}

type ReqGetTicketPriceList struct {
	ReqPage
	ConferenceId int64 `json:"conference_id" form:"conference_id"` //展会id
	IsCn         int   ` json:"is_cn" form:"is_cn"`                //展会ID 0是中文，1是英文 默认中文
}

type ReqGetList struct {
	ReqPage
	ConferenceId int64 `json:"conference_id" form:"conference_id"` //展会id
}

type ReqAdminSaveTicketPrice struct {
	Id                     int64   `json:"id" form:"id" form:"id"`                                                                                                            //添加时不传修改时必传
	ConferenceId           int64   `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"`                                                        //展会ID
	CnName                 string  `json:"cn_name" form:"cn_name"`                                                                                                            //票种名称中文
	EnName                 string  `json:"en_name" form:"en_name"`                                                                                                            //票种名称英文
	CnServiceId            string  `gorm:"column:cn_service_id" db:"cn_service_id" json:"cn_service_id" form:"cn_service_id"`                                                 //中文服务ID
	EnServiceId            string  `gorm:"column:en_service_id" db:"en_service_id" json:"en_service_id" form:"en_service_id"`                                                 //英文服务ID
	CnButtonName           string  `gorm:"column:cn_button_name" db:"cn_button_name" json:"cn_button_name" form:"cn_button_name"`                                             //自定义按钮名称-中文
	EnButtonName           string  `gorm:"column:en_button_name" db:"en_button_name" json:"en_button_name" form:"en_button_name"`                                             //自定义按钮名称-英文
	CnRegistrationPageName string  `gorm:"column:cn_registration_page_name" db:"cn_registration_page_name" json:"cn_registration_page_name" form:"cn_registration_page_name"` //报名页门票名称-中文
	EnRegistrationPageName string  `gorm:"column:en_registration_page_name" db:"en_registration_page_name" json:"en_registration_page_name" form:"en_registration_page_name"` //报名页门票名称-英文
	CnButtonLink           string  `gorm:"column:cn_button_link" db:"cn_button_link" json:"cn_button_link" form:"cn_button_link"`                                             //自定义按钮跳转链接-中文
	EnButtonLink           string  `gorm:"column:en_button_link" db:"en_button_link" json:"en_button_link" form:"en_button_link"`                                             //自定义按钮跳转链接-英文
	CnIsDisplayed          int     `gorm:"column:cn_is_displayed" db:"cn_is_displayed"  json:"cn_is_displayed" form:"cn_is_displayed"`                                        //是否显示购买按钮（1展示，0不展示）
	EnIsDisplayed          int     `gorm:"column:en_is_displayed" db:"en_is_displayed"  json:"en_is_displayed" form:"en_is_displayed"`                                        //是否显示购买按钮（1展示，0不展示）
	CnMaximum              int     `gorm:"column:cn_maximum" db:"cn_maximum" json:"cn_maximum" form:"cn_maximum" valid:"range(0|100)~排序要大于等于0"`                               //最大人数
	EnMaximum              int     `gorm:"column:en_maximum" db:"en_maximum" json:"en_maximum" form:"en_maximum" valid:"range(0|100)~排序要大于等于0"`                               //最大人数
	CnSorting              float32 `json:"cn_sorting" db:"cn_sorting" gorm:"cn_sorting" form:"cn_sorting" valid:"range(0|999999)~排序要大于等于0"`                                   //排序
	EnSorting              float32 `json:"en_sorting" db:"en_sorting" gorm:"en_sorting" form:"en_sorting" valid:"range(0|999999)~排序要大于等于0"`                                   //排序
	Type                   int     `json:"type" db:"type" gorm:"type" form:"type"`                                                                                            //费用类型（0人数金额模版，1票种权益模版）
	AdminEmail             string  `swaggerignore:"true"`
}

type ReqGetRightsTicketList struct {
	ReqPage
	ConferenceId int64 `json:"conference_id"  form:"conference_id"`                               //展会id
	TicketId     int64 `gorm:"column:ticket_id" db:"ticket_id" json:"ticket_id" form:"ticket_id"` //票种ID
	IsCn         int   `json:"is_cn" form:"is_cn"`                                                //展会ID 0是中文，1是英文 默认中文
}

type ReqGetRightsInterestsList struct {
	ReqPage
	ConferenceId int64 `json:"conference_id"  form:"conference_id"` //展会id
	IsCn         int   `json:"is_cn" form:"is_cn"`                  //展会ID 0是中文，1是英文 默认中文
}
type ReqTicketPriceId struct {
	Id int64 `json:"id" form:"id" gorm:"column:id"` //票种id
}
type ReqRightInterestsId struct {
	Id         int64  `json:"id" form:"id" gorm:"column:id"` //权益id
	AdminEmail string `swaggerignore:"true"`
}

type ReqAdminSaveRightInterests struct {
	Id           int64   `gorm:"column:id" db:"id" json:"id" form:"id"`                                                            //添加时不传修改时必传
	ConferenceId int64   `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"`                       //展会ID
	CnContent    string  `json:"cn_content" db:"cn_content" gorm:"cn_content" form:"cn_content"`                                   //权益内容-中文
	EnContent    string  `json:"en_content" db:"en_content" gorm:"en_content" form:"en_content"`                                   //权益内容-英文
	CnSorting    float32 `json:"cn_sorting" db:"cn_sorting" gorm:"cn_sorting" form:"cn_sorting" valid:"range(0|9999999)~排序要大于等于0"` //排序
	EnSorting    float32 `json:"en_sorting" db:"en_sorting" gorm:"en_sorting" form:"en_sorting" valid:"range(0|9999999)~排序要大于等于0"` //排序
	Type         int     `json:"type" db:"type" gorm:"type" form:"type"`                                                           //模版类型 （0权益模版，1人数金额模版）
	AdminEmail   string  `swaggerignore:"true"`
}

type ReqAdminSaveRightTicket struct {
	ConferenceId int64    `json:"conference_id"  form:"conference_id"`                               //展会id
	TicketId     int64    `gorm:"column:ticket_id" db:"ticket_id" json:"ticket_id" form:"ticket_id"` //票种ID
	IsCn         int64    `gorm:"column:is_cn" db:"is_cn" json:"is_cn" form:"is_cn"`                 //展会ID 0是中文，1是英文 默认中文
	RightsId     string   `gorm:"column:rights_id" db:"rights_id" json:"rights_id" form:"rights_id"` //权益ID多个英文逗号分割
	RightsIds    []string `swaggerignore:"true"`
	AdminEmail   string   `swaggerignore:"true"`
}

type ReqAdminSaveTicketConfig struct {
	ConferenceId    int64  `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"`                        //展会ID
	CnPriceTemplate int    `gorm:"column:cn_price_template" db:"cn_price_template" json:"cn_price_template" form:"cn_price_template"` //价格模板 (0 人数金额模版,  1 票种权益表格模版)
	EnPriceTemplate int    `gorm:"column:en_price_template" db:"en_price_template" json:"en_price_template" form:"en_price_template"` //价格模板 (0 人数金额模版,  1 票种权益表格模版)
	AdminEmail      string `swaggerignore:"true"`
}

type ReqGetConferenceRegisterList struct {
	ReqPage
	ConferenceId   int64  `json:"conference_id"  form:"conference_id"`    //展会id
	ConferenceName string `json:"conference_name" form:"conference_name"` //会议名称
	//CnConferenceName string  `json:"conference_name" form:"conference_name"` //会议名称
	FirstName      string `json:"first_name" form:"first_name"`                                         //名
	LastName       string `json:"last_name" form:"last_name"`                                           //姓
	Email          string `json:"email" form:"email"`                                                   //邮箱
	StartTime      string `json:"start_time" form:"start_time"`                                         //提交时间-开始
	EndTime        string `json:"end_time" form:"end_time"`                                             //提交时间-结束
	SourceId       string `json:"source_id" form:"source_id"`                                           //来源id
	FromId         string `json:"fromId" form:"fromId"`                                                 // 渠道id
	FromName       string `json:"from_name" form:"from_name"`                                           // 渠道名称
	EnterpriseType string `json:"enterprise_type" form:"enterprise_type" gorm:"column:enterprise_type"` // 企业类型

	Ids []int64 `swaggerignore:"true"`
}

type ReqGetConferenceFromIdList struct {
	ConferenceId int64 `json:"conference_id"  form:"conference_id"`                        //展会id
	FromType     int   `json:"from_type" db:"from_type" gorm:"from_type" form:"from_type"` //渠道类型1参会购买，2展位购买，3赞助
}

type ReqGetConferenceSourceList struct {
	ConferenceId int64 `json:"conference_id"  form:"conference_id"` //展会id
}

type ReqGetConferenceRegisterUserList struct {
	RegisterId int64 `json:"register_id"  form:"register_id"` //报名id
}

type ReqAddConferenceRegister struct {
	Id               int64                       `gorm:"column:id" db:"id" json:"id" form:"id"`                                                             //添加时不传修改时必传
	ConferenceId     int64                       `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"`                        //展会ID
	FirstName        string                      `gorm:"column:first_name" db:"first_name" json:"first_name" form:"first_name"`                             //购买人姓名 (中文购买是必填)
	LastName         string                      `gorm:"column:last_name" db:"last_name" json:"last_name" form:"last_name"`                                 //购买人姓名 (中文购买时不用填写)
	Company          string                      `gorm:"column:company" db:"company" json:"company" form:"company"`                                         //公司
	JobTitle         string                      `gorm:"column:job_title" db:"job_title" json:"job_title" form:"job_title"`                                 //职位
	Cellphone        string                      `gorm:"column:cellphone" db:"cellphone" json:"cellphone" form:"cellphone"`                                 //手机号
	Email            string                      `gorm:"column:email" db:"email" json:"email" form:"email"`                                                 //邮箱
	InterestMeetings string                      `gorm:"column:interest_meetings" db:"interest_meetings" json:"interest_meetings" form:"interest_meetings"` //感兴趣的会议（多个用英文逗号分割）
	FromId           string                      `gorm:"column:fromId" db:"fromId" json:"fromId" form:"fromId"`                                             //渠道
	BdVid            string                      `gorm:"column:bd_vid" db:"bd_vid" json:"bd_vid" form:"bd_vid"`                                             //百度跟踪ID
	SourceID         string                      `gorm:"column:source_id" db:"source_id" json:"source_id" form:"source_id"`                                 //来源ID
	SourceName       string                      `gorm:"column:source_name" db:"source_name" json:"source_name" form:"source_name"`                         //来源名称
	CnOrEn           int                         `gorm:"column:cn_or_en" db:"cn_or_en" json:"cn_or_en" form:"cn_or_en" `                                    // 0 中文 1 英文
	RegisterUser     []ReqConferenceRegisterUser `json:"register_user" form:"register_user"`                                                                //第二步时再传

	MainProducts   string `json:"main_products" form:"main_products" gorm:"column:main_products"`       // 主推产品
	EnterpriseType string `json:"enterprise_type" form:"enterprise_type" gorm:"column:enterprise_type"` // 企业类型

	ConferenceName string `swaggerignore:"true"`
	AuthEmail      string `swaggerignore:"true"`
}

type ReqConferenceRegisterUser struct {
	UserId    int64  `json:"user_id"  form:"user_id"`
	FirstName string `gorm:"column:first_name" db:"first_name" json:"first_name" form:"first_name"` //名 （中文只用first_name）
	LastName  string `gorm:"column:last_name" db:"last_name" json:"last_name" form:"last_name"`     //姓 购买人姓名 (中文购买时不用填写)
	Email     string `gorm:"column:email" db:"email" json:"email" form:"email"`                     //邮箱
	Mobile    string `gorm:"column:mobile" db:"mobile" json:"mobile" form:"mobile"`                 //手机号
	Company   string `gorm:"column:company" db:"company" json:"company" form:"company"`             //公司
	JobTitle  string `gorm:"column:job_title" db:"job_title" json:"job_title" form:"job_title"`     //职位
}

type ReqGetConferencePosterList struct {
	ReqPage
	ConferenceId int64 `json:"conference_id"  form:"conference_id"` //展会id
	Type         int   `json:"type" form:"type"`                    //海报类型（1首页，2观众，3展商，4大会日程，5关于我们，6参会指南，7往届，8赞助，9门票）
}

type ReqWebConferencePosterList struct {
	ReqPage
	ConferenceId int64 `json:"conference_id"  form:"conference_id"` //展会id
	Type         int   `json:"type" form:"type"`                    //海报类型（1首页，2观众，3展商，4大会日程，5关于我们，6参会指南，7往届，8赞助，9门票）
}

type ReqAdminSaveConferencePoster struct {
	Id           int64  `json:"id" form:"id" form:"id"`                                                                      //添加时不传修改时必传
	PcLogo       string `gorm:"column:pc_logo" db:"pc_logo" json:"pc_logo" form:"pc_logo" valid:"required~pc海报图必传"`          //pc背景/海报图
	H5Logo       string `gorm:"column:h5_logo" db:"h5_logo" json:"h5_logo" form:"h5_logo" valid:"required~h5海报图必传"`          //H5海报图
	Link         string `gorm:"column:link" db:"link" json:"link" form:"link"`                                               //链接
	Sorting      string `gorm:"column:sorting" db:"sorting" json:"sorting" form:"sorting" valid:"range(0|9999999)~排序要大于等于0"` //排序
	ConferenceId int64  `json:"conference_id"  form:"conference_id"`                                                         //展会id
	Type         int    `json:"type" form:"type"`                                                                            //海报类型（1首页，2观众，3展商，4大会日程，5关于我们，6参会指南，7往届，8赞助，9门票 10 搜索 11 演讲加冰）
	AdminEmail   string `swaggerignore:"true"`
	OpUser       string `swaggerignore:"true"`
}

type ReqAdminSaveIndexConferencePoster struct {
	Id           int64  `json:"id" form:"id" form:"id"`                                                             //添加时不传修改时必传
	ConferenceId int64  `json:"conference_id"  form:"conference_id"`                                                //展会id
	PcLogo       string `gorm:"column:pc_logo" db:"pc_logo" json:"pc_logo" form:"pc_logo" valid:"required~pc海报图必传"` //pc背景/海报图
	OpUser       string `swaggerignore:"true"`
}

type ReqGetConferenceEventList struct {
	ReqPage
	ConferenceId int64  `json:"conference_id" form:"conference_id"` //展会ID
	NamePlace    string `json:"name_place" form:"name_place"`       //会议名称/地点
}

type ReqGetConferenceEventCategoryList struct {
	ReqPage
	ConferenceId int64 `json:"conference_id" form:"conference_id"` //展会ID
}

type ReqGetConferenceForumList struct {
	DayId int64 `json:"day_id" form:"day_id"` //日期ID
}

type ReqGetConferenceInformationEventList struct {
	ConferenceId  int64 `json:"conference_id" form:"conference_id"`    //展会ID
	InformationId int64 `json:"information_id"  form:"information_id"` //公司或嘉宾信息id
}

type ReqAdminSaveConferenceEventCategory struct {
	ID           int64   `json:"id" db:"id" gorm:"id" form:"id"`                                                 //添加时不传修改时必传
	ConferenceId int64   `json:"conference_id"  form:"conference_id"`                                            //展会id
	StartTime    string  `json:"start_time" db:"start_time" gorm:"start_time" form:"start_time"`                 // 开始日期
	EndTime      string  `json:"end_time" db:"end_time" gorm:"end_time" form:"end_time"`                         // 结束日期
	EntryType    string  `json:"entry_type" db:"entry_type" gorm:"entry_type" form:"entry_type"`                 // 入场类型
	CnName       string  `json:"cn_name" db:"cn_name" gorm:"cn_name" form:"cn_name"`                             // 活动分类名称-中文
	EnName       string  `json:"en_name" db:"en_name" gorm:"en_name" form:"en_name"`                             // 活动分类名称-英文
	CnPlace      string  `json:"cn_place" db:"cn_place" gorm:"cn_place" form:"cn_place"`                         // 中文地点
	EnPlace      string  `json:"en_place" db:"en_place" gorm:"en_place" form:"en_place"`                         // 英文地点
	CnTitle      string  `json:"cn_title" db:"cn_title" gorm:"cn_title" form:"cn_title"`                         // 中文标题
	EnTitle      string  `json:"en_title" db:"en_title" gorm:"en_title" form:"en_title"`                         // 英文标题
	CnContent    string  `json:"cn_content" db:"cn_content" gorm:"cn_content" form:"cn_content"`                 // 中文内容
	EnContent    string  `json:"en_content" db:"en_content" gorm:"en_content" form:"en_content"`                 // 英文内容
	CnButtonLink string  `json:"cn_button_link" db:"cn_button_link" gorm:"cn_button_link" form:"cn_button_link"` // 中文预约参会按钮跳转链接
	EnButtonLink string  `json:"en_button_link" db:"en_button_link" gorm:"en_button_link" form:"en_button_link"` // 英文预约参会按钮跳转链接
	Type         int     `json:"type" db:"type" gorm:"type" form:"type"`                                         // 关联导航菜单（1CLNB展前会，2CLNB主论坛，3CLNB分论坛，4户外赛事，5场馆赛事，6Releasing Stage 新品发布）
	Picture      string  `json:"picture" db:"picture" gorm:"picture" form:"picture"`                             // 图片
	Sorting      float32 `json:"sorting" db:"sorting" gorm:"sorting" form:"sorting"`                             // 排序
}

type ReqWebConferenceList struct {
	ConferenceId int64 `json:"conference_id" form:"conference_id"` //展会ID
	//CategoryType int   `json:"category_type" form:"category_type"` // 所属活动分类（1CLNB展前会，2CLNB主论坛，3CLNB分论坛，4户外赛事，5场馆赛事，6Releasing Stage 新品发布）
}
type ReqWebConferenceTicketList struct {
	ConferenceId int64 `json:"conference_id" form:"conference_id"` //展会ID
	IsCn         int   `json:"is_cn" form:"is_cn"`                 //展会ID 0是中文，1是英文 默认中文
}

type ReqConferenceEventInfo struct {
	Id int64 `json:"id" form:"id"` //活动ID
}

type ReqWebConferenceEventInfo struct {
	EventId int64 `json:"event_id" form:"event_id"` //活动ID
}

type ReqGetEventScheduleList struct {
	ReqPage
	EventId int64  `json:"event_id" form:"event_id"`                       //活动ID
	DayId   int64  `json:"day_id" db:"day_id" gorm:"day_id" form:"day_id"` //日期ID
	ForumId int64  `json:"forum_id" form:"forum_id"`                       //分论坛ID
	Name    string `json:"name" form:"name"`                               //查询
}

type ReqGetEventScheduleGuestList struct {
	ReqPage
	ScheduleId int64  `json:"schedule_id" form:"schedule_id"` //日程ID
	Name       string `json:"name" form:"name"`               //称谓，职位，公司查询
}

type ReqWebEventScheduleList struct {
	EventId int64 `json:"event_id" form:"event_id"` //活动ID
	DayId   int64 `json:"day_id" form:"day_id"`     //日期ID
	ForumId int64 `json:"forum_id" form:"forum_id"` //分论坛ID
}

type ReqWebEventScheduleForumList struct {
	EventId int64 `json:"event_id" form:"event_id"` //活动ID
	DayId   int64 `json:"day_id" form:"day_id"`     //日期ID
}

type ReqAdminSaveConferenceEvent struct {
	ID              int64  `json:"id" form:"id"`                                                   //添加时不传修改时必传
	ConferenceId    int64  `json:"conference_id"  form:"conference_id"`                            //展会id
	CnName          string `json:"cn_name" form:"cn_name"`                                         //展会活动名称--中文
	EnName          string `json:"en_name" form:"en_name"`                                         //展会活动名称--英文
	CategoryType    int    `json:"category_type" form:"category_type"`                             // 所属活动分类（1CLNB展前会，2CLNB主论坛，3CLNB分论坛，4户外赛事，5场馆赛事，6Releasing Stage 新品发布）
	Sorting         string `json:"sorting" form:"sorting"`                                         //排序
	TemplateType    int    `json:"template_type" form:"template_type"`                             //日程模板 (0 议程&非议程模板,  1是时间&主题 )
	ScheduleIsForum string `json:"schedule_is_forum" form:"schedule_is_forum"`                     //日程是否有论坛（0无分论坛，1有分论坛）
	IsDisplayPage   string `json:"is_display_page" form:"is_display_page"`                         //是否展示活动详情页（0是，1否）
	StartTime       string `json:"start_time" db:"start_time" gorm:"start_time" form:"start_time"` //活动时间-开始日期

	CnSponsors        string `json:"cn_sponsors" form:"cn_sponsors"`                 //赞助方-中文
	EnSponsors        string `json:"en_sponsors" form:"en_sponsors"`                 //赞助方-英文
	CnSponsorsLogo    string `json:"cn_sponsors_logo" form:"cn_sponsors_logo"`       //赞助方Logo-中文
	EnSponsorsLogo    string `json:"en_sponsors_logo" form:"en_sponsors_logo"`       //赞助方Logo-英文
	CnSponsorshipType string `json:"cn_sponsorship_type" form:"cn_sponsorship_type"` //赞助类型（0赞助方，1合作伙伴）
	EnSponsorshipType string `json:"en_sponsorship_type" form:"en_sponsorship_type"` //赞助类型（0赞助方，1合作伙伴）

	CnSponsors2        string `json:"cn_sponsors2" form:"cn_sponsors2"`                 //赞助方-中文
	EnSponsors2        string `json:"en_sponsors2" form:"en_sponsors2"`                 //赞助方-英文
	CnSponsorsLogo2    string `json:"cn_sponsors_logo2" form:"cn_sponsors_logo2"`       //赞助方Logo-中文
	EnSponsorsLogo2    string `json:"en_sponsors_logo2" form:"en_sponsors_logo2"`       //赞助方Logo-英文
	CnSponsorshipType2 string `json:"cn_sponsorship_type2" form:"cn_sponsorship_type2"` //赞助类型（0赞助方，1合作伙伴）
	EnSponsorshipType2 string `json:"en_sponsorship_type2" form:"en_sponsorship_type2"` //赞助类型（0赞助方，1合作伙伴）
	AdminEmail         string `swaggerignore:"true"`
}

type ReqAdminSaveEventScheduleDate struct {
	ID           int64  `json:"id" db:"id" gorm:"id" form:"id"`                                     //添加时不传修改时必传
	ConferenceId int64  `json:"conference_id"  form:"conference_id"`                                //展会id
	EventId      int64  `json:"event_id" form:"event_id"`                                           //活动ID
	CnDayName    string `json:"cn_day_name" db:"cn_day_name" gorm:"cn_day_name" form:"cn_day_name"` //日期名称
	EnDayName    string `json:"en_day_name" db:"en_day_name" gorm:"en_day_name" form:"en_day_name"` //日期名称
	Sorting      string `json:"sorting" db:"sorting" gorm:"sorting" form:"sorting"`                 //排序
}
type ReqAdminSaveEventScheduleForum struct {
	ID           int64   `json:"id" db:"id" gorm:"id" form:"id"`                     //添加时不传修改时必传
	ConferenceId int64   `json:"conference_id"  form:"conference_id"`                //展会id
	EventId      int64   `json:"event_id" form:"event_id"`                           //活动ID
	DayId        int64   `json:"day_id" db:"day_id" gorm:"day_id" form:"day_id"`     //属于哪一天
	CnName       string  `json:"cn_name" db:"cn_name" gorm:"cn_name" form:"cn_name"` //分论坛名称-中文
	EnName       string  `json:"en_name" db:"en_name" gorm:"en_name" form:"en_name"` //分论坛名称-英文
	Sorting      float32 `json:"sorting" db:"sorting" gorm:"sorting" form:"sorting"`
}

type ReqGetEventScheduleForumInfo struct {
	ForumId int64 `json:"id" form:"id"` //分论坛ID
}

type ReqAdminSaveEventSchedule struct {
	ID            int64   `json:"id" db:"id" gorm:"id" form:"id"`                                                 //添加时不传修改时必传
	ConferenceId  int64   `json:"conference_id"  form:"conference_id"`                                            //展会id
	EventId       int64   `json:"event_id" form:"event_id"`                                                       //活动ID
	DayId         int64   `json:"day_id" db:"day_id" gorm:"day_id" form:"day_id"`                                 //属于哪一天
	ForumId       int64   `json:"forum_id" db:"forum_id" gorm:"forum_id" form:"forum_id"`                         //分论坛ID
	CnTitle       string  `json:"cn_title" db:"cn_title" gorm:"cn_title" form:"cn_title"`                         //展会日程名称--中文
	EnTitle       string  `json:"en_title" db:"en_title" gorm:"en_title" form:"en_title"`                         //展会日程名称--英文
	ScheduleStart string  `json:"schedule_start" db:"schedule_start" gorm:"schedule_start" form:"schedule_start"` //时间-开始日期
	ScheduleEnd   string  `json:"schedule_end" db:"schedule_end" gorm:"schedule_end" form:"schedule_end"`         //时间-结束日期
	CnContent     string  `json:"cn_content" db:"cn_content" gorm:"cn_content" form:"cn_content"`                 //日程描述--中文
	EnContent     string  `json:"en_content" db:"en_content" gorm:"en_content" form:"en_content"`                 //日程描述--英文
	Sorting       float32 `json:"sorting" db:"sorting" gorm:"sorting" form:"sorting"`                             //排序
	IsAgenda      int64   `json:"is_agenda" db:"is_agenda" gorm:"is_agenda" form:"is_agenda"`                     //日程类型（0议程、1非议程/餐食、2咖啡、3鸡尾酒）
	AdminEmail    string  `swaggerignore:"true"`
}
type ReqGetEventScheduleInfo struct {
	Id int64 `json:"id" form:"id"` //日程ID
}

type ReqAdminSaveEventScheduleGuest struct {
	ID            int64  `json:"id" db:"id" gorm:"id" form:"id"`       //添加时不传修改时必传
	ConferenceId  int64  `json:"conference_id"  form:"conference_id"`  //展会id
	EventId       int64  `json:"event_id" form:"event_id"`             //活动ID
	ScheduleId    int64  `json:"schedule_id" form:"schedule_id"`       //日程ID
	CnCompany     string `json:"cn_company" form:"cn_company"`         //公司
	EnCompany     string `json:"en_company" form:"en_company"`         //公司
	CnPosition    string `json:"cn_position" form:"cn_position"`       //职位
	EnPosition    string `json:"en_position" form:"en_position"`       //职位
	CnAppellation string `json:"cn_appellation" form:"cn_appellation"` //称谓
	EnAppellation string `json:"en_appellation" form:"en_appellation"` //称谓
	Picture       string `json:"picture" form:"picture"`               //图片
	GuestIdentity string `json:"guest_identity" form:"guest_identity"` //嘉宾身份
	Sorting       string `json:"sorting" form:"sorting"`               //排序
	AdminEmail    string `swaggerignore:"true"`
}

type ReqEventScheduleGuestInfo struct {
	Id int64 `json:"id" form:"id"` //日程嘉宾ID
}

type ReqGetIntroductionList struct {
	ReqPage
	ConferenceId int64 `json:"conference_id" form:"conference_id"` //展会ID
}

type ReqGetIntroductionOrganizationList struct {
	ReqPage
	Type         int   `json:"type" db:"type" gorm:"type" form:"type"` // 数据类型（1主办单位，2承办单位，3二维码信息，4现场图片）
	ConferenceId int64 `json:"conference_id" form:"conference_id"`     //展会ID
}

type ReqGetIntroductionInfo struct {
	ConferenceId   int64  `json:"conference_id" form:"conference_id"`     //展会ID
	ConferenceName string `json:"conference_name" form:"conference_name"` //展会名称
	Type           int    `json:"type" form:"type"`                       // 1 参展信息 2 赞助信息 3 付费信息 4 国内免费观众 5 国外免费观众 6 国内媒体报名信息 7 国外媒体报名信息
}

type UserGetIntroductionList struct {
	ConferenceId int64 `json:"conference_id" form:"conference_id"` //展会ID
}

type ReqAdminSaveIntroduction struct {
	ID                  int64  `json:"id" db:"id" gorm:"id" form:"id"`                   //添加时不传修改时必传
	ConferenceId        int64  `json:"conference_id" form:"conference_id"`               //展会ID
	IntroductionDiagram string `json:"introduction_diagram" form:"introduction_diagram"` // 会议介绍配图
	CnContent           string `json:"cn_content" form:"cn_content"`                     // 大会介绍-页面内容-中文
	EnContent           string `json:"en_content" form:"en_content"`                     // 大会介绍-页面内容-英文
	IntroductionVideo   string `json:"introduction_video" form:"introduction_video"`     // 介绍视频
	VideoCover          string `json:"video_cover" form:"video_cover"`                   // 视频封面
	AdminEmail          string `swaggerignore:"true"`
}

type ReqAdminSaveSponsor struct {
	ID           int64  `json:"id" db:"id" gorm:"id" form:"id"`                                                           //添加时不传修改时必传
	CnTitle      string `json:"cn_title" db:"cn_title" gorm:"cn_title" form:"cn_title"`                                   //赞助标题-中文
	EnTitle      string `json:"en_title" db:"en_title" gorm:"en_title" form:"en_title"`                                   //赞助标题-英文
	CnButton     string `json:"cn_button" db:"cn_button" gorm:"cn_button" form:"cn_button"`                               //赞助按钮名称-中文
	EnButton     string `json:"en_button" db:"en_button" gorm:"en_button" form:"en_button"`                               //赞助按钮名称-英文
	CnPdf        string `json:"cn_pdf" db:"cn_pdf" gorm:"cn_pdf" form:"cn_pdf"`                                           //海报pdf-中文
	EnPdf        string `json:"en_pdf" db:"en_pdf" gorm:"en_pdf" form:"en_pdf"`                                           //海报pdf-英文
	CnContent    string `json:"cn_content" db:"cn_content" gorm:"cn_content" form:"cn_content" valid:"required~海报内容中文必传"` //海报内容-中文
	EnContent    string `json:"en_content" db:"en_content" gorm:"en_content" form:"en_content"`                           //海报内容-英文
	ConferenceId int64  `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"`               //关联展会ID
	AdminEmail   string `swaggerignore:"true"`
}

type ReqAdminSaveSponsorApplication struct {
	ConferenceId  int64  `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"`            //展会ID
	UserId        int64  `gorm:"column:user_id" db:"user_id" json:"user_id" form:"user_id"`                             //用户ID
	FirstName     string `gorm:"column:first_name" db:"first_name" json:"first_name" form:"first_name"`                 //姓
	LastName      string `gorm:"column:last_name" db:"last_name" json:"last_name" form:"last_name"`                     //名
	Company       string `gorm:"column:company" db:"company" json:"company" form:"company"`                             //公司
	JobTitle      string `gorm:"column:job_title" db:"job_title" json:"job_title" form:"job_title"`                     //职位
	Email         string `gorm:"column:email" db:"email" json:"email" form:"email"`                                     //邮箱
	Phone         string `gorm:"column:phone" db:"phone" json:"phone" form:"phone"`                                     //手机
	Country       string `gorm:"column:country" db:"country" json:"country" form:"country"`                             //国家
	LeaveMessage  string `gorm:"column:leave_message" db:"leave_message" json:"leave_message" form:"leave_message"`     //留言
	LevelInterest string `gorm:"column:level_interest" db:"level_interest" json:"level_interest" form:"level_interest"` //兴趣程度
	BoothType     string `gorm:"column:booth_type" db:"booth_type" json:"booth_type" form:"booth_type"`                 //展位类型
	AuthEmail     string
}

type ReqAdminSaveIntroductionData struct {
	ID           int64   `json:"id" db:"id" gorm:"id" form:"id"`                                             //添加时不传修改时必传
	CnDataName   string  `json:"cn_data_name" db:"cn_data_name" gorm:"cn_data_name" form:"cn_data_name"`     //指标名称-中文
	CnDataValue  string  `json:"cn_data_value" db:"cn_data_value" gorm:"cn_data_value" form:"cn_data_value"` //指标数据-中文
	EnDataName   string  `json:"en_data_name" db:"en_data_name" gorm:"en_data_name" form:"en_data_name"`     //指标名称-英文
	EnDataValue  string  `json:"en_data_value" db:"en_data_value" gorm:"en_data_value" form:"en_data_value"` //指标数据-英文
	DataImage    string  `json:"data_image" db:"data_image" gorm:"data_image" form:"data_image"`             ///图标图片
	Sorting      float32 `json:"sorting" db:"sorting" gorm:"sorting" form:"sorting"`                         //排序
	ConferenceId int64   `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"`
	AdminEmail   string  `swaggerignore:"true"`
}
type ReqGetIntroductionDataInfo struct {
	Id int64 `json:"id" form:"id"` //展会数据ID
}

type ReqAdminSaveGuidance struct {
	ID           int64   `json:"id" db:"id" gorm:"id" form:"id"`                                             //添加时不传修改时必传
	CnTitle      string  `json:"cn_title" db:"cn_title" gorm:"cn_title" form:"cn_title"`                     // 标题-中文
	EnTitle      string  `json:"en_title" db:"en_title" gorm:"en_title" form:"en_title"`                     // 标题-英文
	CnLink       string  `json:"cn_link" db:"cn_link" gorm:"cn_link" form:"cn_link"`                         // 链接-中文
	EnLink       string  `json:"en_link" db:"en_link" gorm:"en_link" form:"en_link"`                         // 链接-英文
	CnDescribe   string  `json:"cn_describe" db:"cn_describe" gorm:"cn_describe" form:"cn_describe"`         // 描述-中文
	EnDescribe   string  `json:"en_describe" db:"en_describe" gorm:"en_describe" form:"en_describe"`         // 描述-英文
	PcImage      string  `json:"pc_image" db:"pc_image" gorm:"pc_image" form:"pc_image"`                     // pc图片
	PcIcon       string  `json:"pc_icon" db:"pc_icon" gorm:"pc_icon" form:"pc_icon"`                         // pc图标
	H5Icon       string  `json:"h5_icon" db:"h5_icon" gorm:"h5_icon" form:"h5_icon"`                         // h5图标
	Sorting      float32 `json:"sorting" db:"sorting" gorm:"sorting" form:"sorting"`                         //排序
	ConferenceId int64   `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"` //展会ID
	AdminEmail   string  `swaggerignore:"true"`
}
type ReqGetIntroductionGuidanceInfo struct {
	Id int64 `json:"id" form:"id"` //展会大会引导ID
}

type ReqAdminSaveOrganization struct {
	ID           int64  `json:"id" db:"id" gorm:"id" form:"id"`                                             //添加时不传修改时必传
	CnName       string `json:"cn_name" db:"cn_name" gorm:"cn_name" form:"cn_name"`                         // 单位名称/二维码名称-中文
	EnName       string `json:"en_name" db:"en_name" gorm:"en_name" form:"en_name"`                         // 单位名称/二维码名称-英文
	Logo         string `json:"logo" db:"logo" gorm:"logo" form:"logo"`                                     // 二维码链接
	Sorting      string `json:"sorting" db:"sorting" gorm:"sorting" form:"sorting"`                         // 排序
	Type         string `json:"type" db:"type" gorm:"type" form:"type"`                                     // 数据类型（1主办单位，2承办单位，3二维码信息，4现场图片）
	ConferenceId int64  `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"` // 展会ID
	AdminEmail   string `swaggerignore:"true"`
}

type ReqAdminSaveContactInformation struct {
	ID            int64   `json:"id" db:"id" gorm:"id" form:"id"`                                //添加时不传修改时必传
	ConferenceId  int64   `json:"conference_id" form:"conference_id" gorm:"conference_id"`       //会展ID
	CnTitle       string  `json:"cn_title" form:"cn_title" gorm:"cn_title"`                      //标题-中文
	EnTitle       string  `json:"en_title" form:"en_title"  gorm:"en_title"`                     //标题-英文
	CnContent     string  `json:"cn_content" form:"cn_content" gorm:"cn_content"`                //中文内容
	EnContent     string  `json:"en_content" form:"en_content" gorm:"en_content"`                //英文内容
	CnIsDisplayed int     `json:"cn_is_displayed" form:"cn_is_displayed" gorm:"cn_is_displayed"` //中文其他页底是否显示（0  否 1  是）
	EnIsDisplayed int     `json:"en_is_displayed" form:"en_is_displayed" gorm:"en_is_displayed"` //英文其他页底是否显示（0  否 1  是）
	Sorting       float32 `json:"sorting" form:"sorting" gorm:"sorting"`                         //排序
	AdminEmail    string  `swaggerignore:"true"`
}

type ReqAdminSaveBottomPage struct {
	ID           int64  `json:"id" db:"id" gorm:"id" form:"id"`                                             //添加时不传修改时必传
	CnName       string `json:"cn_name" db:"cn_name" gorm:"cn_name" form:"cn_name"`                         // 单位名称/二维码名称-中文
	EnName       string `json:"en_name" db:"en_name" gorm:"en_name" form:"en_name"`                         // 单位名称/二维码名称-英文
	CnLogo       string `json:"cn_logo" db:"cn_logo" gorm:"cn_logo" form:"cn_logo"`                         // 二维码链接-中文
	EnLogo       string `json:"en_logo" db:"en_logo" gorm:"en_logo" form:"en_logo"`                         // 二维码链接-英文
	CnSorting    string `json:"cn_sorting" db:"cn_sorting" gorm:"cn_sorting" form:"cn_sorting"`             // 排序-中文
	EnSorting    string `json:"en_sorting" db:"en_sorting" gorm:"en_sorting" form:"en_sorting"`             // 排序-英文
	ConferenceId int64  `json:"conference_id" db:"conference_id" gorm:"conference_id" form:"conference_id"` // 展会ID
	AdminEmail   string `swaggerignore:"true"`
}

type ReqGetIntroductionOrganizationInfo struct {
	Id int64 `json:"id" form:"id"` //组织架构ID
}

type ReqAudienceQuestionContentId struct {
	Id     int64  `json:"id" form:"id" valid:"required~问答内容主键id必传"` //问答内容主键id
	OpUser string `swaggerignore:"true"`
}

type ReqHandbookId struct {
	Id int64 `json:"id" form:"id" valid:"required~问答手册id必传"` //问答手册主键id
}

type ReqEditCompanyQuestionType struct {
	Id int64 `json:"id" form:"id" valid:"required~主键id必传"`
	CommonQuestionType
}

type ReqQuestionContentId struct {
	Id     int64  `json:"id" form:"id" valid:"required~问答内容主键id必传"` //问答内容主键id
	OpUser string `swaggerignore:"true"`
}

type ReqAboutUs struct {
	ConferenceId int64  `form:"conference_id" json:"conference_id" binding:"required"` //展会ID
	CnAboutUs    string `form:"cn_about_us" json:"cn_about_us" binding:"required"`     //关于我们中文
	EnAboutUs    string `form:"en_about_us" json:"en_about_us"`                        //关于我们英文
	OpUser       string `swaggerignore:"true"`
}

type ReqContactUs struct {
	ID           int64  `form:"id" json:"id"`                       //添加时不传修改时必传                              //主键id
	ConferenceId int64  `form:"conference_id" json:"conference_id"` //展会id
	CnContact    string `form:"cn_contact" json:"cn_contact"`       //联系我们内容-中文
	EnContact    string `form:"en_contact" json:"en_contact"`       //联系我们内容-英文
	OpUser       string `swaggerignore:"true"`
}

type ReqContactUsList struct {
	ConferenceId int64 `form:"conference_id" json:"conference_id" binding:"required"` //展会id
}

// 用户提交申请(参展或赞助)
type ReqUserApplySubmit struct {
	ConferenceID  int64  `json:"conference_id" form:"conference_id" binding:"required"` // 关联展会ID
	FirstName     string `json:"first_name" form:"first_name" binding:"required"`       // 名
	LastName      string `json:"last_name" form:"last_name"`                            // 姓 （中文可以不传，英文必填）
	Email         string `json:"email" form:"email" binding:"required"`                 // 邮箱
	CellPhone     string `json:"cellphone" form:"cellphone" binding:"required"`         // 电话
	Company       string `json:"company" form:"company" binding:"required"`             // 公司
	JobTitle      string `json:"job_title" form:"job_title"`                            // 职位
	Country       string `json:"country" form:"country" binding:"required"`             // 国家
	Comment       string `json:"comment" form:"comment" swaggerignore:"true"`           // 留言
	InterestLevel string `json:"interest_level" form:"interest_level"`                  // 兴趣等级
	BoothType     string `json:"booth_type" form:"booth_type"`                          // 展位类型
	SourceID      string `json:"source_id" form:"source_id"`                            //来源ID （跟报名/user/register/add接口传的一样）
	FromId        string `json:"fromId" form:"fromId"`                                  // 渠道
	SourceName    string ` swaggerignore:"true"`                                        //来源名称
	UserId        int64  ` swaggerignore:"true"`
}

// 展会线索列表
type ReqAdminClueList struct {
	Keyword        string   `json:"keyword" form:"keyword"`                                               // 搜索词
	ConferenceID   int64    `json:"conference_id" form:"conference_id"`                                   // 关联展会ID
	StartTime      string   `json:"start_time" form:"start_time"`                                         // 开始时间
	EndTime        string   `json:"end_time" form:"end_time"`                                             // 结束时间
	SourceId       int64    `json:"source_id" form:"source_id"`                                           //来源id
	FromId         string   `json:"fromId" form:"fromId"`                                                 // 渠道
	FromName       string   `json:"from_name" form:"from_name"`                                           // 渠道名称
	EnterpriseType string   `json:"enterprise_type" form:"enterprise_type" gorm:"column:enterprise_type"` // 企业类型
	ConferenceName string   `json:"conference_name" form:"conference_name"`                               // 展会名称
	Type           int      ` swaggerignore:"true"`
	Ids            []string `swaggerignore:"true"`
	ReqPage
}

type ReqSearchData struct {
	ConferenceID int64  `json:"conference_id" form:"conference_id" binding:"required"`   // 展会ID
	Keyword      string `json:"keyword" form:"keyword" binding:"required"`               // 搜索内容
	Language     string `json:"language" form:"language" binding:"required,oneof=cn en"` // 对应语言
	ReqPage
}

// 订单购买状态同步
type ReqAdminUpdConferenceRegister struct {
	Id           int64   `json:"id" form:"id" form:"id"`
	OrderNumber  string  `json:"order_number" form:"order_number"`
	OrderStatus  int     `json:"order_status" form:"order_status"`
	Quantity     int     `json:"quantity" form:"quantity"`
	Amount       float64 `json:"amount" form:"amount"`
	UserID       int64   `json:"user_id"  form:"user_id"`
	ServiceID    string  `json:"service_id" form:"service_id"`
	ServiceName  string  `json:"service_name" form:"service_name"`
	Source       string  `json:"source" form:"source"`
	PayMethod    string  `json:"pay_method" form:"pay_method"`
	CurrencyUnit int     `json:"currency_unit" form:"currency_unit"` // 1人民币   2美金  3欧元
	CustomInfo   string  `json:"custom_info" form:"custom_info"`
	CouponCode   string  `json:"coupon_code" form:"coupon_code"`
	OffVaule     string  `json:"off_vaule" form:"off_vaule"`
	AdminEmail   string
}
type ReqAddAPiSignAddEn struct {
	MeetingNo   string `json:"meeting_no" form:"meeting_no"`
	UserId      int64  `json:"user_id"  form:"user_id"`
	FirstName   string `gorm:"column:first_name" db:"first_name" json:"first_name" form:"first_name"`
	LastName    string `gorm:"column:last_name" db:"last_name" json:"last_name" form:"last_name"`
	Email       string `gorm:"column:email" db:"email" json:"email" form:"email"`
	Mobile      string `gorm:"column:mobile" db:"mobile" json:"mobile" form:"mobile"`
	Company     string `gorm:"column:company" db:"company" json:"company" form:"company"`
	JobTitle    string `gorm:"column:job_title" db:"job_title" json:"job_title" form:"job_title"`
	OrderStatus string `json:"order_status"`
	FromId      string `gorm:"column:fromId" db:"fromId" json:"fromId" form:"fromId"` //渠道
	PlatformId  string `json:"platformId" form:"platformId"`                          //平台
	Country     string `json:"country"`
	Identity    string `json:"identity"`                  //1参会人，2展商赞助商，3观众
	CnOrEn      int    `json:"cn_or_en" form:"cn_or_en" ` // 0 中文 1 英文
	//Language       string `json:"language" form:"language"`  //语言类型 （中文：cn，英文：en）
	MeetingRemarks string `db:"meeting_remarks" json:"meeting_remarks" form:"meeting_remarks"`

	MainProduct string `json:"main_product"`
	CompanyType string `json:"company_type"`
}

// 同步会议系统参数
type ReqAddAPiSignUpAddEn struct {
	MeetingNo    string `json:"meeting_no" form:"meeting_no"`
	UserId       int64  `json:"user_id"  form:"user_id"`
	FirstName    string `gorm:"column:first_name" db:"first_name" json:"first_name" form:"first_name"`
	LastName     string `gorm:"column:last_name" db:"last_name" json:"last_name" form:"last_name"`
	Email        string `gorm:"column:email" db:"email" json:"email" form:"email"`
	Mobile       string `gorm:"column:mobile" db:"mobile" json:"mobile" form:"mobile"`
	Company      string `gorm:"column:company" db:"company" json:"company" form:"company"`
	JobTitle     string `gorm:"column:job_title" db:"job_title" json:"job_title" form:"job_title"`
	FromId       string `gorm:"column:fromId" db:"fromId" json:"fromId" form:"fromId"` //渠道
	PlatformId   string `json:"platformId" form:"platformId"`                          //平台
	CnOrEn       int    `json:"cn_or_en" form:"cn_or_en" `                             // 0 中文 1 英文
	CurrencyUnit int    `json:"currency_unit" form:"currency_unit"`                    // 1人民币   2美金  3欧元
	Country      string `json:"country"`
	Identity     string `json:"identity"`
	PayAmount    string `json:"pay_amount"`
	OrderId      string `json:"order_id"`
	ServiceId    string `json:"service_id"`
	PayMethod    string `json:"pay_method"`
	OrderCount   string `json:"order_count"`
	OrderStatus  string `json:"order_status"`
	Source       string `json:"source"`
}

type ReqAdminSaveMeetingTicketPrice struct {
	Id                int64   `json:"id" form:"id" form:"id"`
	Name              string  `gorm:"column:name" db:"name" json:"name" form:"name"` //名称
	Logo              string  `gorm:"column:logo" db:"logo" json:"logo" form:"logo"`
	ButtonLink        string  `gorm:"column:button_link" db:"button_link" json:"button_link" form:"button_link"`
	ButtonName        string  `gorm:"column:button_name" db:"button_name" json:"button_name" form:"button_name"`
	Sorting           float32 `gorm:"column:sorting" db:"sorting" json:"sorting" form:"sorting"`             //排序
	Type              int     `gorm:"column:type" db:"type" json:"type" form:"type"`                         //1展商，2赞助商
	MeetingId         int64   `gorm:"column:meeting_id" db:"meeting_id" json:"meeting_id" form:"meeting_id"` //会议ID
	ServiceId         string  `gorm:"column:service_id" db:"service_id" json:"service_id" form:"service_id"`
	Maximum           int     `gorm:"column:maximum" db:"maximum" json:"maximum" form:"maximum"`                                                 //最大人数\
	StandardFee       string  `gorm:"column:standard_fee" db:"standard_fee" json:"standard_fee" form:"standard_fee"`                             //最大人数\
	ButtonIsDisplayed int     `gorm:"column:button_is_displayed" db:"button_is_displayed" json:"button_is_displayed" form:"button_is_displayed"` //1展示，0不展示
	AdminEmail        string
}

// vcodecenter发短信
type ReqAddAPiSendSms struct {
	Source    string `json:"source" form:"source"`
	Cellphone string `json:"cellphone" form:"cellphone"` //需要发送的手机号
	CodeType  string `json:"code_type" form:"code_type"`
}

// vcodecenter判断验证码
type ReqCheckCode struct {
	Code      string `json:"code" form:"code"`
	Cellphone string `json:"cellphone"  form:"cellphone"` //发送验证码的手机号
	CodeType  string `json:"code_type" form:"code_type"`
}

// 发短信验证码
type ReqUserAddAPiSendSms struct {
	Cellphone string `json:"cellphone"  form:"cellphone"` //需要发送的手机号
}

// 用户提交申请(表单参观)
type ReqUserApplyVisitSubmit struct {
	Name      string `json:"name" form:"name" binding:"required"`             // 姓名
	Company   string `json:"company" form:"company" binding:"required"`       // 公司
	JobTitle  string `json:"job_title" form:"job_title" binding:"required"`   // 职位
	CellPhone string `json:"cellphone" form:"cellphone" binding:"required"`   // 电话
	PhoneCode string `json:"phone_code" form:"phone_code" binding:"required"` // 手机号验证码
}

// 用户提交申请(表单购买展览展位)
type ReqUserApplyPurchaseBoothSubmit struct {
	ConferenceId   int64  `json:"conference_id" form:"conference_id" binding:"required"` // 关联展会ID
	FirstName      string `json:"first_name" form:"first_name"`
	LastName       string `json:"last_name" form:"last_name"`
	Company        string `json:"company" form:"company" binding:"required"`     // 公司
	CellPhone      string `json:"cellphone" form:"cellphone" binding:"required"` // 电话
	Email          string `json:"email" form:"email"`                            // 邮箱
	JobTitle       string `json:"job_title" form:"job_title" binding:"required"` // 职位
	Country        string `json:"country" form:"country"`                        //国家
	ExhibitionArea string `json:"exhibition_area" form:"exhibition_area"`        // 感兴趣的展区
	FromId         string `json:"fromId" form:"fromId"`                          //渠道
	BdVid          string `json:"bd_vid" form:"bd_vid"`                          //百度跟踪ID
	CnOrEn         int    `json:"cn_or_en" form:"cn_or_en"`                      // 0 中文 1 英文
	SourceID       string `json:"source_id" form:"source_id"`                    //来源ID （跟报名/user/register/add接口传的一样）
	SourceName     string `swaggerignore:"true"`                                 //来源名称
	UserId         int64  `swaggerignore:"true"`

	MainProducts   string `json:"main_products" form:"main_products" gorm:"column:main_products"`       // 主推产品
	EnterpriseType string `json:"enterprise_type" form:"enterprise_type" gorm:"column:enterprise_type"` // 企业类型

}

// 用户提交申请(表单申请赞助)
type ReqUserApplySponsorSubmit struct {
	ConferenceId   int64  `json:"conference_id" form:"conference_id" binding:"required"` // 关联展会ID
	FirstName      string `gorm:"column:first_name" db:"first_name" json:"first_name" form:"first_name"`
	LastName       string `gorm:"column:last_name" db:"last_name" json:"last_name" form:"last_name"`
	Company        string `json:"company" form:"company" binding:"required"` // 公司
	CellPhone      string `json:"telephone" form:"telephone"`                // 电话
	PhoneCode      string `json:"phone_code" form:"phone_code"`              // 手机号验证码
	Email          string `json:"email" form:"email"`                        // 邮箱
	JobTitle       string `json:"job_title" form:"job_title"`                // 职位
	ExhibitionArea string `json:"exhibition_area" form:"exhibition_area"`    // 感兴趣的展区
	FromId         string `json:"fromId" form:"fromId"`                      //渠道
	BdVid          string `json:"bd_vid" form:"bd_vid"`                      //百度跟踪ID
	CnOrEn         int    `json:"cn_or_en" form:"cn_or_en" `                 // 0 中文 1 英文
	SourceID       string `json:"source_id" form:"source_id"`                //来源ID （跟报名/user/register/add接口传的一样）
	SourceName     string ` swaggerignore:"true"`                            //来源名称
	UserId         int64  ` swaggerignore:"true"`
	Country        string `json:"country"`
	MainProducts   string `json:"main_products" form:"main_products" gorm:"column:main_products"`       // 主推产品
	EnterpriseType string `json:"enterprise_type" form:"enterprise_type" gorm:"column:enterprise_type"` // 企业类型

}

// 同步会议系统参数
type ReqAddAPiSignUpInterestedParticipant struct {
	MeetingNo   string `json:"meeting_no" form:"meeting_no"`
	Name        string `json:"name" form:"name"`
	Cellphone   string `json:"cellphone" form:"cellphone"`
	CompanyName string `json:"company_name" form:"company_name"`
	Position    string `json:"position" form:"position"`
}

type ReqUserForumConfigList struct {
	ConferenceId int64  `json:"conference_id" form:"conference_id"` //展会id
	Type         string `json:"type" form:"type"`                   //0感兴趣的会议，1感兴趣的展区
}

// 百度
type ConversionTypes struct {
	MeetingNo string `json:"meeting_no" form:"meeting_no"`
	UserId    int64  `json:"user_id"  form:"user_id"`
	FirstName string `gorm:"column:first_name" db:"first_name" json:"first_name" form:"first_name"`
	LastName  string `gorm:"column:last_name" db:"last_name" json:"last_name" form:"last_name"`
	Email     string `gorm:"column:email" db:"email" json:"email" form:"email"`
}

type ReqBatchImportAudience struct {
	File       multipart.File `json:"file" form:"file"` //
	AdminEmail string
}

type ReqonferenceMap struct {
	ShortName string `json:"short_name" form:"short_name"` // 会议编号
}

type ReqAdminSaveMediaRegistrationConfig struct {
	ID                  int64  `json:"id" db:"id" gorm:"id" form:"id"`                                                  //添加时不传修改时必传
	ConferenceId        int64  `json:"conference_id" form:"conference_id" gorm:"conference_id"`                         //会展ID
	CnNavigationDisplay int    `json:"cn_navigation_display" form:"cn_navigation_display" gorm:"cn_navigation_display"` //媒体注册中文导航是否显示（0  否 1  是）
	EnNavigationDisplay int    `json:"en_navigation_display" form:"en_navigation_display" gorm:"en_navigation_display"` //英文其他页底是否显示（0  否 1  是）
	CnContent           string `json:"cn_content" form:"cn_content" gorm:"cn_content"`                                  //中文内容
	EnContent           string `json:"en_content" form:"en_content" gorm:"en_content"`                                  //英文内容
	AdminEmail          string `swaggerignore:"true"`
}

type ReqAdminSaveMediaRegistration struct {
	Token              string    `json:"token" form:"token"`
	AuthToken          string    `json:"auth_token" form:"auth_token"`
	Id                 int64     `json:"id" form:"id" form:"id"`
	ConferenceId       int64     `json:"conference_id" form:"conference_id" gorm:"conference_id"`                       //会展ID
	UserId             int64     `gorm:"column:user_id" db:"user_id" json:"user_id" form:"user_id"`                     //用户ID
	Name               string    `gorm:"column:name" db:"name" json:"name" form:"name"`                                 //姓名
	FirstName          string    `gorm:"column:first_name" db:"first_name" json:"first_name" form:"first_name"`         //姓
	LastName           string    `gorm:"column:last_name" db:"last_name" json:"last_name" form:"last_name"`             //名
	Organisation       string    `gorm:"column:organisation" db:"organisation" json:"organisation" form:"organisation"` //组织
	Phone              string    `gorm:"column:phone" db:"phone" json:"phone" form:"phone"`                             //手机
	PhoneCode          string    `json:"phone_code" form:"phone_code"`
	JobTitle           string    `gorm:"column:job_title" db:"job_title" json:"job_title" form:"job_title"`                                                 //职位
	Email              string    `gorm:"column:email" db:"email" json:"email" form:"email"`                                                                 //邮箱
	Country            string    `gorm:"column:country" db:"country" json:"country" form:"country"`                                                         //国家
	IsAgreeSendMessage int       `gorm:"column:is_agree_send_message" db:"is_agree_send_message" json:"is_agree_send_message" form:"is_agree_send_message"` //是否同意发送邮件(2同意，1不同意)
	FromId             string    `json:"from_id" form:"from_id"`                                                                                            //渠道
	SourceID           string    `json:"source_id" form:"source_id"`                                                                                        //来源ID(平台ID)
	CreateTime         time.Time `gorm:"column:create_time" db:"create_time" json:"create_time" form:"create_time"`                                         //提交时间
	AdminEmail         string
}

type ReqGetMediaRegistrationList struct {
	ReqPage
	ConferenceId   int64  `json:"conference_id" form:"conference_id"`                            //展会ID
	ConferenceName string `json:"conference_name" form:"conference_name"`                        //展会名称
	Language       string `gorm:"column:language" db:"language" json:"language" form:"language"` //语言

	MediaName string `json:"media_name" form:"media_name"`
	Name      string `json:"name" form:"name"`
	Email     string `json:"email" form:"email"`
	Phone     string `json:"phone" form:"phone"`
	Country   string `json:"country" form:"country"`
	FromId    string `json:"from_id" form:"from_id"`     //渠道
	FromName  string `json:"from_name" form:"from_name"` //渠道名称
	SourceID  string `json:"source_id" form:"source_id"` //来源ID
	StartTime string `json:"start_time" form:"start_time"`
	EndTime   string `json:"end_time" form:"end_time"`
}

type ReqAddApiMediaSignUp struct {
	MeetingNo    string `json:"meeting_no" form:"meeting_no"`
	UserId       int64  `json:"user_id" form:"user_id"`
	Platform     string `json:"platform" form:"platform"`           //平台
	Channel      string `json:"channel" form:"channel"`             //渠道
	MediaName    string `json:"media_name" form:"media_name"`       //媒体名称
	CustomerName string `json:"customer_name" form:"customer_name"` //客户名称
	Cellphone    string `json:"cellphone"`
	Position     string `json:"position" form:"position"` //职位
	Email        string `json:"email" form:"email"`
	Country      string `json:"country"`
	CnOrEn       int    `json:"cn_or_en" form:"cn_or_en"` //
}

// EventScheduleImportRow 活动日程批量导入Excel行结构体
// 字段顺序与Excel表头一致
// excel_smm tag用于表头映射
// 必填项用注释标明

type EventScheduleImportRow struct {
	EventCode       string `excel_smm:"活动编号,A"` // 必填 - 新增字段
	CategoryName    string `excel_smm:"活动分类,B"` // 必填
	EventName       string `excel_smm:"活动名称,C"` // 必填
	EventNameEn     string `excel_smm:"活动名称英文,D"`
	EventDate       string `excel_smm:"活动日期,E"` // 必填
	ShowEventDetail string `excel_smm:"是否展示活动详情,F"`
	ScheduleTitle   string `excel_smm:"日程标题,G"` // 必填
	ScheduleTitleEn string `excel_smm:"标题英文,H"`
	ScheduleType    string `excel_smm:"日程类型,I"`
	ScheduleTime    string `excel_smm:"日程时间,J"` // 必填
	ScheduleDesc    string `excel_smm:"日程描述,K"`
	ScheduleDescEn  string `excel_smm:"描述英文,L"`
	GuestName       string `excel_smm:"嘉宾称谓,M"`
	GuestNameEn     string `excel_smm:"称谓英文,N"`
	GuestPosition   string `excel_smm:"嘉宾职位,O"`
	GuestPositionEn string `excel_smm:"职位英文,P"`
	GuestCompany    string `excel_smm:"嘉宾公司,Q"`
	GuestCompanyEn  string `excel_smm:"公司英文,R"`
	GuestIdentity   string `excel_smm:"嘉宾身份,S"`
	ConferenceId    int64
}
