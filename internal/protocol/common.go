package protocol

import (
	"conferencecenter/internal/constant"
	"conferencecenter/internal/errcode"
	"database/sql/driver"
	"fmt"
	"math"
	"time"
)

const (
	DefaultCount       = 1
	DefaultCurrentPage = 1
	DefaultPage        = 1
	DefaultPageSize    = 10
	MaxPageSize        = 1000
)

type ReqPage struct {
	Page     int `form:"page" json:"page"`           // 页码
	PageSize int `form:"page_size" json:"page_size"` // 分页大小
}

func (r *ReqPage) DefaultPage() {
	if r.Page <= 0 {
		r.Page = DefaultPage
	}
	if r.PageSize <= 0 {
		r.PageSize = DefaultPageSize
	}
}

type Response struct {
	Code      int         `json:"code"`
	Msg       string      `json:"msg"`
	ErrorDesc string      `json:"error_desc,omitempty"` //异常情况显示真实的异常信息
	Data      interface{} `json:"data"`
}

type ResPagination struct {
	Total       int64 `json:"total"`        // 总数
	CurrentPage int   `json:"current_page"` // 当前页码
	PageSize    int   `json:"page_size"`    // 每页大小
	PageCount   int   `json:"page_count"`   // 总页数
}

func (info *ResPagination) InitPageInfo(total int64, page, limit int) {
	info.Total = total
	info.CurrentPage = page
	if page > 0 && limit > 0 {
		info.PageSize = limit
		info.CurrentPage = page
		info.PageCount = int(math.Ceil(float64(total) / float64(limit)))
	} else {
		info.PageSize = int(total)
		info.PageCount = DefaultCount
		info.CurrentPage = DefaultCurrentPage
	}
}

func NewPagination(total int64, page, pageSize int) (info ResPagination) {
	if page <= 0 {
		page = DefaultPage
	}
	if pageSize <= 0 {
		pageSize = DefaultPageSize
	}
	if total < 0 {
		total = 0
	}
	info.Total = total
	info.CurrentPage = page
	info.PageSize = pageSize
	info.PageCount = int(math.Ceil(float64(total) / float64(pageSize)))
	return
}

type ResError struct {
	E    error      // 存放错误堆栈
	Info *ErrorInfo // 存放返回的业务错误描述
}

func (er ResError) HasError() bool {
	return er.E != nil || er.Info != nil
}

type ErrorInfo struct {
	Code   int    `json:"code"`
	Msg    string `json:"msg"`
	ErrMsg string `json:"error_desc,omitempty"`
}

func (e *ErrorInfo) Error() string {
	return e.Msg
}
func (e *ErrorInfo) New(code int, respMsg string, args ...interface{}) *ErrorInfo {
	erMsg := ""
	if len(args) > 0 {
		erMsg = fmt.Sprintf("%v", args...)
	}
	return &ErrorInfo{
		Code:   code,
		Msg:    respMsg,
		ErrMsg: erMsg,
	}
}

func NewResErrWithMsg(code int, msg string, err error) ResError {
	var (
		errMsg string
	)
	_, ok := errcode.CodeMsg[code]
	if msg == "" && ok {
		msg = errcode.CodeMsg[code]
	}
	if err != nil {
		errMsg = err.Error()
	}
	return ResError{
		E: err,
		Info: &ErrorInfo{
			Code:   code,
			Msg:    msg,
			ErrMsg: errMsg,
		},
	}
}

func NewResErr(code int, err error) ResError {
	var (
		msg    = ""
		errMsg string
	)
	if _, ok := errcode.CodeMsg[code]; ok {
		msg = errcode.CodeMsg[code]
	}
	if err != nil {
		errMsg = err.Error()
	}

	return ResError{
		E: err,
		Info: &ErrorInfo{
			Code:   code,
			Msg:    msg,
			ErrMsg: errMsg,
		},
	}
}

type WorkWxRobotResp struct {
	ErrCode int    `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
}

type HookMsg struct {
	MsgType  string       `json:"msgtype"` //WechatHook固定形式msgtype中间没有下划线_
	Text     hookText     `json:"text"`
	Markdown hookMarkdown `json:"markdown"`
}

type hookText struct {
	Content             string   `json:"content"`
	MentionedList       []string `json:"mentioned_list"`
	MentionedMobileList []string `json:"mentioned_mobile_list"`
}

type hookMarkdown struct {
	Content string `json:"content"`
}

type LocalTime time.Time

func (t *LocalTime) UnmarshalJSON(data []byte) (err error) {
	if len(data) == 2 {
		*t = LocalTime(time.Time{})
		return
	}

	now, err := time.Parse(`"`+constant.DateTime+`"`, string(data))
	*t = LocalTime(now)
	return
}

func (t LocalTime) MarshalJSON() ([]byte, error) {
	b := make([]byte, 0, len(constant.DateTime)+2)
	b = append(b, '"')
	b = time.Time(t).AppendFormat(b, constant.DateTime)
	b = append(b, '"')
	return b, nil
}

func (t LocalTime) Value() (driver.Value, error) {
	if t.String() == "0001-01-01 00:00:00" {
		return nil, nil
	}
	return []byte(time.Time(t).Format(constant.DateTime)), nil
}

func (t *LocalTime) Scan(v interface{}) error {
	tTime, _ := time.Parse("2006-01-02 15:04:05 +0800 CST", v.(time.Time).String())
	*t = LocalTime(tTime)
	return nil
}

func (t LocalTime) String() string {
	return time.Time(t).Format(constant.DateTime)
}
