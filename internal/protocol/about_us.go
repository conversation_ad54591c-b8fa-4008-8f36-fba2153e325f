package protocol

type AboutUs struct {
	ConferenceId int64  `form:"conference_id" json:"conference_id" binding:"required"` //展会ID
	CnAboutUs    string `form:"cn_about_us" json:"cn_about_us" binding:"required"`     //关于我们中文
	EnAboutUs    string `form:"en_about_us" json:"en_about_us"`                        //关于我们英文
}

type ContactUs struct {
	Id              int64  `json:"int"`               //主键id
	ConferenceId    int64  `json:"conference_id"`     //展会id
	CnModule        string `json:"cn_module"`         //中文负责模块
	EnModule        string `json:"en_module"`         //英文负责模块
	CnContactPerson string `json:"cn_contact_person"` //中文联系人
	EnContactPerson string `json:"en_contact_person"` //英文联系人
	Telephone       string `json:"telephone"`         //电话
	Email           string `json:"email"`             //邮箱地址
}
