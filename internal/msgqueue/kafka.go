package msgqueue

import (
	"conferencecenter/internal/corecontext"
	"conferencecenter/internal/pkg/utils"
	"git.code.tencent.com/smmit/smmbase/logger"
	"github.com/Shopify/sarama"
	"github.com/pkg/errors"
	"time"
)

var KafkaClient sarama.Client

func NewKafkaClient() {
	kafkaConf := sarama.NewConfig()
	kafkaConf.Producer.RequiredAcks = sarama.WaitForAll
	kafkaConf.Producer.Partitioner = sarama.NewRandomPartitioner
	kafkaConf.Producer.Return.Successes = true
	kafkaConf.Producer.Return.Errors = true
	kafkaConf.Version = sarama.V0_10_0_1
	kafkaConf.Producer.Retry.Max = 2

	var err error
	_ = logger.Info("kafka client initial start")
	KafkaClient, err = sarama.NewClient(corecontext.Config().Kafka.Brokers, kafkaConf)
	if err != nil {
		_ = logger.Error("init kafka client", err)
		panic(err)
	}
	_ = logger.Info("kafka client initial end")

	return
}

func KafkaNotify(topic string, msg []byte) (err error) {
	if KafkaClient == nil {
		_ = logger.Warnning("kafka client is empty,can't push kafka messages")
		return errors.New("kafka client is empty")
	}
	defer func() {
		_ = logger.Info("实时推送至kafka队列, topic:", topic, "resp err:", err)
	}()

	producer, err := sarama.NewAsyncProducerFromClient(KafkaClient)
	if err != nil {
		_ = logger.Error("创建Kafka Producer失败", err)
		return errors.New("创建Kafka Producer失败")
	}

	defer func() {
		if closeErr := producer.Close(); closeErr != nil {
			_ = logger.Error(closeErr)
		}
	}()

	err = utils.Retry(3, time.Second*10, func() error {
		producer.Input() <- &sarama.ProducerMessage{Topic: topic, Key: nil, Value: sarama.StringEncoder(utils.ToString(msg))}

		select {
		case fail := <-producer.Errors():
			_ = logger.Error("response error, topic:"+topic+"data:", utils.ToString(msg)+"error:", fail.Err)
			err = fail.Err
			return err
		case suc := <-producer.Successes():
			_ = logger.Debug("response success! topic:", suc.Topic, "partition:", suc.Partition, "time:", suc.Timestamp)
			break
		}

		return nil
	})

	return err
}
