package task

import (
	"conferencecenter/internal/corecontext"
	"conferencecenter/internal/memory"
	"fmt"
	"git.code.tencent.com/smmit/smmbase/logger"
	"github.com/pkg/errors"
	"github.com/robfig/cron"
)

func InitBaseInfoTask() {
	{
		//加载简称映射关系
		memory.LoadConferenceShortNameMap()

		//加载渠道映射信息
		memory.LoadMeetingFromNameMap()

		//加载平台映射信息
		memory.LoadChannelSourceNameMap()

		memory.LoadConferenceNameMap()
	}
	return
}

/**
 * cron schedule
 * cron express: * * * * * * ==>秒 分 时 天 月 周
 * demo: * * * * * * ==>每分钟执行60次
 */
func StartCronTask() {
	cTask := cron.New()
	_ = logger.Debug("Cron task")
	{
		_ = loadCronTask(cTask, InitBaseInfoTask, "0 0/5 * * * *", true, false)
	}
	cTask.Start()
}

/**
 * 定时任务
 * chFunc = 执行逻辑
 * timePattern = 定时样式
 * firstExec = 是否启动执行
 * mainExec = 是否主服务器执行
 */
func loadCronTask(cTask *cron.Cron, chFunc func(), timePattern string, firstExec bool, mainExec bool) error {
	if mainExec && !corecontext.IsMainNode() { //主服务器执行
		return nil
	}
	if firstExec {
		chFunc() //启动执行一次
	}
	if timePattern == "" {
		return errors.New(fmt.Sprintf("not a valid cron schedule (%s)", timePattern))
	}
	err := cTask.AddFunc(timePattern, chFunc) //添加到定时任务
	if err != nil {
		return err
	}
	return nil
}
