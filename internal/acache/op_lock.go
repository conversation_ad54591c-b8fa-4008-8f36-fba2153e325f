package acache

import (
	"conferencecenter/internal/pkg/cache"
	"strconv"
)

/**
 * 生成序列号上锁
 */
func GetSeqNoOpLock(c *cache.Cache, prefix string) bool {
	return c.LockOperateWithTimeOut("generate_seq_no", prefix, 0, 5) //超时时间:5秒
}

/**
 * 生成序列号解锁
 */
func RemoveSeqNoOpLock(c *cache.Cache, prefix string) bool {
	return c.UnLockOperate("generate_seq_no", prefix)
}

/**
 * 展会操作上锁
 */
func GetConferenceOpLock(c *cache.Cache, confId int64) bool {
	return c.LockOperateWithTimeOut("conference_info", strconv.FormatInt(confId, 10), 0, 5) //超时时间:5秒
}

/**
 * 展会操作解锁
 */
func RemoveConferenceOpLock(c *cache.Cache, confId int64) bool {
	return c.UnLockOperate("conference_info", strconv.FormatInt(confId, 10))
}

/**
 * 往届操作上锁
 */
func GetPreviousOpLock(c *cache.Cache, infoId int64) bool {
	return c.LockOperateWithTimeOut("previous_info", strconv.FormatInt(infoId, 10), 0, 5) //超时时间:5秒
}

/**
 * 往届操作解锁
 */
func RemovePreviousOpLock(c *cache.Cache, infoId int64) bool {
	return c.UnLockOperate("previous_info", strconv.FormatInt(infoId, 10))
}
