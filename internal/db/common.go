package db

import (
	"conferencecenter/internal/constant"
	"conferencecenter/internal/model"
	"conferencecenter/internal/pkg/utils"
	"database/sql"
	"fmt"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// 更新或存储关于我们
func UpsertAboutUs(tx *gorm.DB, aboutUs *model.AboutUs) (err error) {
	err = OrmDB(tx).Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "conference_id"}}, // 联合唯一键 或者 主键
		DoUpdates: clause.AssignmentColumns([]string{"cn_about_us", "en_about_us", "update_user", "update_time"}),
	}).Create(&aboutUs).Error
	return err
}

// 查询关于我们(一条记录)
func GetAboutUs(tx *gorm.DB, conferenceID int64) (aboutUs model.AboutUs, err error) {
	err = OrmDB(tx).Model(&model.AboutUs{}).Where(`conference_id = ?`, conferenceID).First(&aboutUs).Error
	if err != nil && err == gorm.ErrRecordNotFound {
		return aboutUs, nil
	}
	return aboutUs, err
}

func QueryContactUsInfo(tx *gorm.DB, id int64) (contactUs model.ContactUs, err error) {
	err = OrmDB(tx).Model(&model.ContactUs{}).Where(`id = ?`, id).First(&contactUs).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return contactUs, err
	}
	return contactUs, nil
}

func SaveContactUsInfo(tx *gorm.DB, contactUs *model.ContactUs) (err error) {
	return OrmDB(tx).Save(contactUs).Error
}

func QueryContactUsList(tx *gorm.DB, conferenceId int64, filter bool) (int64, []model.ContactUs, error) {
	var (
		total  int64
		dataLi = make([]model.ContactUs, 0)
		err    error
	)
	query := OrmDB(tx).Model(&model.ContactUs{})
	if conferenceId > 0 {
		query = query.Where(` conference_id = ? `, conferenceId)
	}
	if filter {
		query = query.Where(`is_deleted = ?`, false)
	}
	if err = query.Count(&total).Error; err != nil {
		return 0, dataLi, err
	}
	if total <= 0 {
		return 0, dataLi, nil
	}
	query = query.Order("id desc")
	query = query.Limit(1)
	err = query.Find(&dataLi).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return 0, dataLi, err
	}
	return total, dataLi, nil
}

// 查询线索列表
func QueryConferenceClueList(tx *gorm.DB, conferenceID, sourceId int64, fromId, keyword, startTime, endTime, enterpriseType string, typeId, page, pageSize int, withCount bool, ids []string, fromName string, conferenceName string) (int64, []model.ConferenceClueInfo, error) {
	var (
		total         int64
		dataLi        = make([]model.ConferenceClueInfo, 0)
		limit, offset = utils.PageOffset(page, pageSize)
		qParams       = make([]interface{}, 0)
		cParams       = make([]interface{}, 0)
		err           error
	)
	tx = OrmDB(tx)
	querySql := fmt.Sprintf(" SELECT c.id,c.conference_id,c.user_id,c.cn_conference_name,c.en_conference_name,c.cn_short_name,c.en_short_name,c.last_name,c.first_name,c.email,c.cellphone,c.company,c.job_title,c.country,c.comment,c.exhibition_area,c.interest_level,c.booth_type,c.from_id,c.from_name,c.source_name,c.status,c.create_user,c.update_user,c.create_time,c.update_time,c.main_products,c.enterprise_type FROM `conference_clue` c  WHERE 1=1 ")
	countSql := fmt.Sprintf(" SELECT count(c.id) FROM `conference_clue` c WHERE 1=1 ")
	if typeId > 0 {
		querySql += " AND c.type = 1 "
		countSql += " AND c.type = 1 "
	} else {
		querySql += " AND c.type = 0 "
		countSql += " AND c.type = 0 "
	}
	if conferenceID > 0 {
		querySql += " AND c.conference_id = ? "
		countSql += " AND c.conference_id = ? "
		qParams = append(qParams, conferenceID)
		cParams = append(cParams, conferenceID)
	}
	if sourceId > 0 {
		querySql += " AND c.source_id = ? "
		countSql += " AND c.source_id = ? "
		qParams = append(qParams, sourceId)
		cParams = append(cParams, sourceId)
	}
	if utils.NotEmpty(fromId) {
		querySql += " AND c.from_id = ? "
		countSql += " AND c.from_id = ? "
		qParams = append(qParams, fromId)
		cParams = append(cParams, fromId)
	}
	if utils.NotEmpty(fromName) {
		querySql += " AND c.from_name = ? "
		countSql += " AND c.from_name = ? "
		qParams = append(qParams, fromName)
		cParams = append(cParams, fromName)
	}
	if utils.NotEmpty(conferenceName) {
		querySql += " AND c.cn_conference_name =? "
		countSql += " AND c.cn_conference_name =? "
		qParams = append(qParams, conferenceName)
		cParams = append(cParams, conferenceName)
	}
	if utils.NotEmpty(enterpriseType) {
		querySql += " AND c.enterprise_type = ? "
		countSql += " AND c.enterprise_type = ? "
		qParams = append(qParams, enterpriseType)
		cParams = append(cParams, enterpriseType)
	}
	if startTime != "" {
		querySql += " AND c.create_time >= ?"
		countSql += " AND c.create_time >= ?"
		qParams = append(qParams, startTime)
		cParams = append(cParams, startTime)
	}
	if endTime != "" {
		querySql += " AND c.create_time <= ?"
		countSql += " AND c.create_time <= ?"
		qParams = append(qParams, endTime)
		cParams = append(cParams, endTime)
	}

	if len(ids) > 0 {
		if len(ids) == 1 {
			querySql += " AND c.id = ? "
			countSql += " AND c.id = ? "
			qParams = append(qParams, ids[0])
			cParams = append(cParams, ids[0])
		} else {
			querySql += " AND c.id in (?) "
			countSql += " AND c.id in (?) "
			qParams = append(qParams, ids)
			cParams = append(cParams, ids)
		}
	}

	querySql += " ORDER BY id DESC "
	if page > 0 && pageSize > 0 {
		querySql += " LIMIT ?,? "
		qParams = append(qParams, offset, limit)
	}
	{
		err = tx.Raw(querySql, qParams...).Scan(&dataLi).Error
		if err != nil && err != sql.ErrNoRows {
			return total, dataLi, err
		}
	}
	if withCount {
		err = tx.Raw(countSql, cParams...).Count(&total).Error
		if err != nil {
			return total, dataLi, err
		}
	}
	return total, dataLi, err
}

// 更新线索状态
func UpdateClueStatus(tx *gorm.DB, clueIds []int64, status int, opUser string) (err error) {
	upData := map[string]interface{}{
		"status":      status,
		"update_user": opUser,
		"update_time": time.Now().Format(constant.DateTime),
	}
	return OrmDB(tx).Model(&model.ConferenceClue{}).Where(`id in ?`, clueIds).Updates(upData).Error
}
