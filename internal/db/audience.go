package db

import (
	"conferencecenter/internal/model"
	"conferencecenter/internal/pkg/utils"
	"conferencecenter/internal/protocol"
	"fmt"

	"git.code.tencent.com/smmit/smmbase/logger"
	"github.com/pkg/errors"
	"gorm.io/gorm"
)

func CreateAudiencePreRegister(tx *gorm.DB, register model.AudiencePreRegistry) error {
	return OrmDB(tx).Save(&register).Error
}

func GetAudiencePreRegister(tx *gorm.DB, conferenceId int64) (registry model.AudiencePreRegistry, err error) {
	if conferenceId <= 0 {
		return model.AudiencePreRegistry{}, errors.New("展会id必须大于0")
	}
	err = OrmDB(tx).Where("conference_id=?", conferenceId).Where("is_deleted=0").Last(&registry).Error
	if err != nil {
		return model.AudiencePreRegistry{}, err
	}
	return
}

func RecordAudienceConferenceCompany(db *gorm.DB, management *model.AudienceConferenceCompany) error {
	return OrmDB(db).Create(management).Error
}

func GetAudienceConferenceCompany(db *gorm.DB, conferenceId int64, year string, page int, size int) (companyList []model.AudienceConferenceCompany, total int64, err error) {
	if conferenceId <= 0 {
		return nil, 0, errors.New("展会id必须大于0")
	}
	limit, offSet := utils.PageOffset(page, size)
	query := OrmDB(db).Table("audience_conference_company").Where("conference_id=?", conferenceId).Where("year=?", year).Where("is_deleted=0").Order("sort asc,id desc")
	query.Count(&total)
	err = query.Limit(limit).Offset(offSet).Find(&companyList).Error
	return companyList, total, err
}

func GetAudienceConferenceCompanyHaveNews(db *gorm.DB, conferenceId int64, page int, size int) (companyList []model.AudienceConferenceCompany, total int64, err error) {
	if conferenceId <= 0 {
		return nil, 0, errors.New("展会id必须大于0")
	}
	limit, offSet := utils.PageOffset(page, size)
	query := OrmDB(db).Table("audience_conference_company").Where("conference_id=?", conferenceId).Where("is_deleted=0").Where("cn_exhibitor_news_id >0").Order("sort asc,id desc")
	query.Count(&total)
	err = query.Limit(limit).Offset(offSet).Find(&companyList).Error
	return companyList, total, err
}

func GetAudienceConferenceCompanyByNewsId(db *gorm.DB, newsIDs []int64, page int, size int) (list []model.AudienceConferenceCompany, total int64, err error) {
	if len(newsIDs) == 0 {
		return nil, 0, err
	}
	query := OrmDB(db).Table("audience_conference_company").Where("cn_exhibitor_news_id in ?", newsIDs).Where("is_deleted=0").Order("sort asc,id desc")
	err = query.Count(&total).Error
	if err != nil {
		logger.Error(fmt.Sprintf("获取新闻总数失败：Err->%v", err))
		return nil, 0, err
	}
	if page > 0 && size > 0 {
		limit, offSet := utils.PageOffset(page, size)
		query = query.Limit(limit).Offset(offSet)
	}
	err = query.Find(&list).Error
	if err != nil {
		logger.Error(fmt.Sprintf("根据新闻id查询展商sql出错：Err->%v", err))
		return nil, 0, err
	}
	return

}

func GetAudienceConferenceCompany2(db *gorm.DB, page int, size int, meetingId int64) (list []model.AudienceConferenceCompany, total int64, err error) {

	if meetingId <= 0 {
		meetingId = 1
		logger.Warnning("会议id为空，默认查询clnb数据")
	}

	query := OrmDB(db).Table("audience_conference_company").Where("cn_exhibitor_news_id != 0").Where("is_deleted=0")

	if meetingId > 0 {
		query.Where("conference_id=?", meetingId)
	}

	query.Order("sort asc,cn_exhibitor_news_id desc")
	err = query.Count(&total).Error
	if err != nil {
		logger.Error(fmt.Sprintf("获取新闻总数失败：Err->%v", err))
		return nil, 0, err
	}
	if page > 0 && size > 0 {
		limit, offSet := utils.PageOffset(page, size)
		query = query.Limit(limit).Offset(offSet)
	}
	err = query.Find(&list).Error
	if err != nil {
		logger.Error(fmt.Sprintf("根据新闻id查询展商sql出错：Err->%v", err))
		return nil, 0, err
	}
	return

}

func GetIndustryList(db *gorm.DB) (list []model.Industry, err error) {
	err = OrmDB(db).Table("industry").Find(&list).Error
	if err != nil {
		logger.Error(fmt.Sprintf("查询行业信息sql出错：%v", err))
		return nil, err
	}
	return
}

func EditAudienceConferenceCompany(db *gorm.DB, management model.AudienceConferenceCompany) error {
	err := OrmDB(db).Table("audience_conference_company").Save(&management).Error
	return err
}

func GetVenueIdByKeyWord(db *gorm.DB, keyWord string, isCn bool, conferenceId int64) (list []int64, err error) {
	query := OrmDB(db).Table("audience_conference_company as acc").
		Select("distinct acc.cn_exhibitor_venue_id").
		Joins("left join venue v on v.id=acc.cn_exhibitor_venue_id and v.deleted=0 ").
		Joins("left join production_management pm on acc.id=pm.audience_company_id and pm.deleted=0").
		Where("acc.conference_id=?", conferenceId).Where("acc.is_deleted=0").Order("acc.sort asc,acc.id desc")
	if isCn {
		if keyWord != "" {
			query.Where("acc.cn_conference_company like ?", "%"+keyWord+"%").Or("v.cn_venue_name like ?", "%"+keyWord+"%").
				Or("cn_booth_number like ?", "%"+keyWord+"%").Or("pm.cn_name like ?", "%"+keyWord+"%")
		}
	} else {
		if keyWord != "" {
			query.Where("acc.en_conference_company like ?", "%"+keyWord+"%").Or("v.en_venue_name like ?", "%"+keyWord+"%").
				Or("en_booth_number like ?", "%"+keyWord+"%").Or("pm.en_name like ?", "%"+keyWord+"%")
		}
	}
	err = query.Distinct().Find(&list).Error
	if err != nil {
		logger.Error(fmt.Sprintf("查询展馆id数量sql错误：Err->%v;KeyWord->%v;isCn->%v;conferenceId->%v", err, keyWord, isCn, conferenceId))
		return nil, err
	}
	return
}

func GetAudienceConferenceCompanyByKeyWordFirstLetter(db *gorm.DB, keyWord string, isCn bool, page int, size int, conferenceId int64, venueId int64, year string) (companyList []model.AudienceConferenceCompanyDto, total int64, err error) {
	limit, offSet := utils.PageOffset(page, size)
	query := OrmDB(db).Table("audience_conference_company as acc").
		Select("distinct acc.id,acc.year,acc.conference_id,acc.cn_conference_company,acc.en_conference_company,acc.cn_booth_number,"+
			"acc.en_booth_number,acc.cn_url,acc.en_url,acc.logo,acc.cn_exhibitor_video_link,"+
			"acc.cn_exhibitor_video_cover,v.cn_venue_name,v.en_venue_name,acc.cn_exhibitor_introduction,acc.logo,acc.cn_exhibitor_venue_id").
		Joins("left join venue v on v.id=acc.cn_exhibitor_venue_id and v.deleted=0 ").
		Joins("left join production_management pm on acc.id=pm.audience_company_id and pm.deleted=0").
		Where("acc.conference_id=?", conferenceId).Where("acc.is_deleted=0").Order("acc.sort asc,acc.id desc")
	if venueId != 0 {
		query = query.Where("v.id=?", venueId)
	}
	if utils.NotEmpty(year) {
		query = query.Where("acc.year=?", year)
	}
	if isCn {
		if keyWord != "" {
			query.Where("acc.cn_conference_company like ?", "%"+keyWord+"%").Or("v.cn_venue_name like ?", "%"+keyWord+"%").
				Or("cn_booth_number like ?", "%"+keyWord+"%").Or("pm.cn_name like ?", "%"+keyWord+"%")
		}
	} else {
		if keyWord != "" {
			query.Where("acc.en_conference_company like ?", "%"+keyWord+"%").Or("v.en_venue_name like ?", "%"+keyWord+"%").
				Or("en_booth_number like ?", "%"+keyWord+"%").Or("pm.en_name like ?", "%"+keyWord+"%")
		}
	}
	query = query.Session(&gorm.Session{})
	query.Distinct("acc.id").Count(&total)
	err = query.Distinct().Limit(limit).Offset(offSet).Find(&companyList).Error
	return
}

func RecordFirstLetter(db *gorm.DB, letterList []model.ConferenceCompanyFirstLetter) (err error) {
	if letterList == nil {
		return nil
	}
	err = OrmDB(db).Create(letterList).Error
	return
}

func GetSingleAudienceConferenceCompany(db *gorm.DB, id int64) (company protocol.CompanyManagement, err error) {
	err = OrmDB(db).Table("audience_conference_company").Where("id=?", id).Where("is_deleted=?", 0).Last(&company).Error
	if err != nil && gorm.ErrRecordNotFound == err {
		return company, nil
	}
	return company, err
}

func DeleteFirstLetter(db *gorm.DB, audienceConferenceCompanyId int64) (err error) {
	err = OrmDB(db).Table("conference_company_first_letter").Where("audience_company_id=?", audienceConferenceCompanyId).Update("is_deleted", 1).Error
	return err
}

func RecordFloorGraph(db *gorm.DB, graph *model.ConferenceFloorGraph) error {
	err := OrmDB(db).Create(&graph).Error
	return err
}

func GetPagedFloorGraphListByConferenceId(db *gorm.DB, id int64, page int, size int) (graphs []model.ConferenceFloorGraph, total int64, err error) {
	limit, offSet := utils.PageOffset(page, size)
	query := OrmDB(db).Table("conference_floor_graph").Where("is_deleted=?", 0).Where("conference_id=?", id).Order("sort asc,id desc").Limit(limit).Offset(offSet)
	query.Count(&total)
	query.Find(&graphs)
	return
}

func GetFloorGraphListByConferenceId(db *gorm.DB, id int64) (graphs []model.ConferenceFloorGraph, err error) {
	query := OrmDB(db).Table("conference_floor_graph").Where("conference_id=?", id).Order("sort asc,id asc").Where("is_deleted=0")
	query.Find(&graphs)
	return
}

func GetSingleFloorGraph(db *gorm.DB, graphId int64) (graph model.ConferenceFloorGraph, err error) {
	err = OrmDB(db).Table("conference_floor_graph").Where("is_deleted=?", 0).Where("id=?", graphId).Where("is_deleted=0").Last(&graph).Error
	if err != nil && gorm.ErrRecordNotFound == err {
		return model.ConferenceFloorGraph{}, nil
	}
	return graph, nil

}

func UpdateFloorGraph(db *gorm.DB, graph model.ConferenceFloorGraph) error {
	err := OrmDB(db).Table("conference_floor_graph").Save(&graph).Error
	return err
}

func DeleteFloorGraph(db *gorm.DB, graphId int64) error {
	err := OrmDB(db).Model(&model.ConferenceFloorGraph{}).Where("id=?", graphId).Update("is_deleted", 1).Error
	return err
}

func RecordAudienceQuestionType(db *gorm.DB, questionType model.AudienceQuestionType) error {
	err := OrmDB(db).Table("audience_question_type").Create(&questionType).Error
	return err
}

func GetCommonQuestionType(db *gorm.DB, conferenceId int64) (list []model.AudienceQuestionType, err error) {
	err = OrmDB(db).Table("audience_question_type").Where("conference_id=?", conferenceId).Where("is_deleted=0").
		Order("sort asc,id desc").Find(&list).Error
	return
}

func UpdateAudienceCommonQuestionType(db *gorm.DB, questionType protocol.ReqUpdateQuestionType) error {
	err := OrmDB(db).Table("audience_question_type").Save(&questionType).Error
	if err != nil {
		return err
	}
	return nil
}

func DeleteAudienceCommonQuestionType(db *gorm.DB, typeId int64) error {
	err := OrmDB(db).Model(&model.AudienceQuestionType{}).Where("id=?", typeId).Update("is_deleted", 1).Error
	return err
}

func GetCommonQuestionTypeById(db *gorm.DB, typeId int64) (questionType model.CompanyQuestionType, err error) {
	err = OrmDB(db).Table("audience_question_type").Where("id=?", typeId).Where("is_deleted=?", 0).Last(&questionType).Error
	return
}
func GetCommonQuestionAnswerById(db *gorm.DB, answerId int64) (answer model.CommonQuestionContent, err error) {
	err = OrmDB(db).Table("audience_question_content aqc").
		Joins("left join audience_question_type aqt on aqc.question_type_id=aqt.id and aqt.is_deleted=0 and aqc.is_deleted=0").
		Select("aqt.id question_type_id,aqt.cn_question_type,aqt.en_question_type,aqc.cn_question_content,aqc.en_question_content,aqc.cn_question_answer,aqc.en_question_answer,aqc.sort").
		Where("aqc.id=?", answerId).Where("aqc.is_deleted=?", 0).Last(&answer).Error
	if err != nil && gorm.ErrRecordNotFound == err {
		return model.CommonQuestionContent{}, nil
	}
	return
}

func RecordCommonQuestionAnswer(db *gorm.DB, content model.CommonQuestionContent) error {
	err := OrmDB(db).Table("audience_question_content").Create(&content).Error
	return err
}

func GetAudienceCommonQuestionAnswer(db *gorm.DB, conferenceId int64) (content []model.AudienceQuestionContent, err error) {
	err = OrmDB(db).Table("audience_question_content aqc").
		Joins("left join audience_question_type aqt on aqc.question_type_id=aqt.id and aqt.is_deleted=0 and aqc.is_deleted=0").
		Select("aqt.id question_type_id,aqt.cn_question_type,aqt.en_question_type,aqc.cn_question_content,aqc.en_question_content,aqc.cn_question_answer,aqc.en_question_answer,aqc.sort").
		Where("aqt.conference_id=?", conferenceId).Where("aqc.is_deleted=0").Order("aqc.sort asc,aqc.id asc").Find(&content).Error
	return
}

func SaveAfterReport(db *gorm.DB, report *model.AudienceAfterReport) error {
	return OrmDB(db).Save(report).Error
}

func GetAfterReportById(db *gorm.DB, id int64) (report model.AudienceAfterReport, err error) {
	err = OrmDB(db).Model(&model.AudienceAfterReport{}).Where("id=?", id).Where("is_deleted=?", 0).Last(&report).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return model.AudienceAfterReport{}, nil
	}
	return
}
func GetAfterReportList(db *gorm.DB, conferenceId int64) (list []model.AudienceAfterReport, err error) {
	query := OrmDB(db).Model(&model.AudienceAfterReport{}).Where("conference_id=?", conferenceId).Where("is_deleted=?", 0).Order("sort asc")
	err = query.Find(&list).Error
	return
}
func DeleteAfterReport(db *gorm.DB, id int64) error {
	return OrmDB(db).Model(&model.AudienceAfterReport{}).Where("id=?", id).Update("is_deleted", 1).Error
}

func SavePaperCollection(db *gorm.DB, collection model.PaperCollection) error {
	return OrmDB(db).Save(&collection).Error
}

func GetPaperCollection(db *gorm.DB, conferenceId int64) (collection model.PaperCollection, err error) {
	err = OrmDB(db).Model(&model.PaperCollection{}).Where("conference_id=?", conferenceId).Last(&collection).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return model.PaperCollection{}, nil
	}
	return collection, err
}

func GetCompanyCommonQuestionAnswer(db *gorm.DB, conferenceId int64) (list []model.CompanyQuestionContent, err error) {
	err = OrmDB(db).Table("company_question_content cqc").
		Joins("left join company_question_type cqt on cqc.question_type_id=cqt.id and cqt.is_deleted=0 and cqc.is_deleted=0").
		Select("cqt.id question_type_id,cqt.cn_question_type,cqt.en_question_type,cqc.cn_question_content,cqc.en_question_content,cqc.cn_question_answer,cqc.en_question_answer,cqc.sort").
		Where("cqt.conference_id=?", conferenceId).Where("cqc.is_deleted=0").Order("cqc.sort asc,cqc.id asc").Find(&list).Error
	return
}

func GetAudienceCommonQuestionAnswerByTypeId(db *gorm.DB, typeId int64) (content []model.AudienceQuestionContent, err error) {
	err = OrmDB(db).Table("audience_question_content aqc").
		Joins("left join audience_question_type aqt on aqc.question_type_id=aqt.id and aqt.is_deleted=0 and aqc.is_deleted=0").
		Select("aqc.id id,aqt.id question_type_id,aqt.cn_question_type,aqt.en_question_type,aqc.cn_question_content,aqc.en_question_content,aqc.cn_question_answer,aqc.en_question_answer,aqc.sort").
		Where("aqc.question_type_id=?", typeId).Where("aqc.is_deleted=0").Order("aqc.sort asc,aqc.id asc").Find(&content).Error
	return
}

func UpdateAudienceQuestionAnswer(db *gorm.DB, content model.CompanyQuestionContent) error {
	err := OrmDB(db).Table("audience_question_content").
		Where("id=?", content.Id).
		Updates(&content).Error
	return err
}

func DeleteAudienceCommonQuestionAnswer(db *gorm.DB, id int64) error {
	err := OrmDB(db).Model(&model.AudienceQuestionContent{}).Where("id=?", id).Update("is_deleted", 1).Error
	return err
}

func DeleteAudienceConferenceCompany(db *gorm.DB, id int64) error {
	err := OrmDB(db).Model(&model.AudienceConferenceCompany{}).Where("id=?", id).Update("is_deleted", 1).Error
	return err
}

// 搜索展商
func QueryConferenceCompanyInfos(tx *gorm.DB, conferenceId int64, keyword, language string, page int, pageSize int) (int64, []model.AudienceConferenceCompanyData, error) {
	var (
		total         int64
		dataLi        = make([]model.AudienceConferenceCompanyData, 0)
		err           error
		queryCols     string
		limit, offset = utils.PageOffset(page, pageSize)
	)
	if language == "cn" {
		queryCols = "id,conference_id,cn_conference_company as conference_company,cn_booth_number as booth_number,cn_url as url,logo,is_deleted,create_user,update_user,create_time,update_time"
	} else {
		queryCols = "id,conference_id,en_conference_company as conference_company,en_booth_number as booth_number,en_url as url,logo,is_deleted,create_user,update_user,create_time,update_time"
	}
	query := OrmDB(tx).Table(`audience_conference_company`).Select(queryCols).Where(`is_deleted=0`)
	if conferenceId > 0 {
		query = query.Where(`conference_id=?`, conferenceId)
	}
	if utils.NotEmpty(keyword) {
		likeCon := "%" + keyword + "%"
		if language == "cn" {
			query = query.Where(`cn_conference_company LIKE ?`, likeCon)
		} else {
			query = query.Where(`en_conference_company LIKE ?`, likeCon)
		}
	}
	if err = query.Count(&total).Error; err != nil {
		return 0, dataLi, err
	}
	if total <= 0 {
		return 0, dataLi, nil
	}
	query = query.Order("sort asc,id asc")
	if page > 0 && pageSize > 0 {
		query = query.Limit(limit).Offset(offset)
	}
	err = query.Find(&dataLi).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return 0, dataLi, err
	}
	return total, dataLi, err
}

func SaveProduction(tx *gorm.DB, production *model.ProductionManagement) error {
	return OrmDB(tx).Table("production_management").Save(production).Error
}

func GetProductionById(tx *gorm.DB, id int64) (p model.ProductionManagement, err error) {
	err = OrmDB(tx).Where("id=?", id).Where("deleted=0").Last(&p).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Error(fmt.Sprintf("根据id查询产品信息出错：%v;id：%d", err, id))
		return model.ProductionManagement{}, err
	}
	return p, nil
}

func DeleteProduction(tx *gorm.DB, id int64) error {
	return OrmDB(tx).Model(&model.ProductionManagement{}).Where("id=?", id).Update("deleted", 1).Error
}

func GetProductionByCompanyId(tx *gorm.DB, audienceCompanyId []int64, page int, size int) (list []model.ProductionManagement, total int64, err error) {
	query := OrmDB(tx).Table("production_management").Where("audience_company_id in ?", audienceCompanyId).
		Where("deleted=0").Order("sorting asc")
	if page > 0 && size > 0 {
		limit, offSet := utils.PageOffset(page, size)
		err = query.Count(&total).Error
		if err != nil {
			logger.Error(fmt.Sprintf("查询产品管理总数失败:%v;audienceCompanyId->%d", err, audienceCompanyId))
			return
		}
		err = query.Limit(limit).Offset(offSet).Find(&list).Error
		if err != nil {
			logger.Error(fmt.Sprintf("分页查询产品管理失败:%v;audienceCompanyId->%d;limit->%d;offSet->%d", err, audienceCompanyId, limit, offSet))
			return
		}
		return
	}
	err = query.Find(&list).Error
	if err != nil {
		logger.Error(fmt.Sprintf("查询产品管理失败:%v;audienceCompanyId->%d", err, audienceCompanyId))
		return
	}
	return
}

func GetCnFreeVisitorFromId() ([]string, error) {
	list := make([]string, 0)
	err := OrmDB(nil).Model(model.CnFreeVisitor{}).Distinct("fromId").
		Where("fromId!=?", "0").Where("fromId !=?", "").Find(&list).Error
	if err != nil {
		logger.Error(fmt.Sprintf("查询渠道sql出错：Err->%v", err))
		return nil, err
	}
	return list, nil
}

func GetEnFreeVisitorFromId() ([]string, error) {
	list := make([]string, 0)
	err := OrmDB(nil).Model(model.EnFreeVisitor{}).Distinct("fromId").
		Where("fromId!=?", "0").Where("fromId !=?", "").Find(&list).Error
	if err != nil {
		logger.Error(fmt.Sprintf("查询渠道sql出错：Err->%v", err))
		return nil, err
	}
	return list, nil
}

func CreateCnFreeVisitor(tx *gorm.DB, cnFreeVisitor *model.CnFreeVisitor) error {
	return OrmDB(tx).Table("cn_free_visitor").Create(cnFreeVisitor).Error
}

func CreateEnFreeVisitor(tx *gorm.DB, enFreeVisitor *model.EnFreeVisitor) error {
	return OrmDB(tx).Table("en_free_visitor").Create(enFreeVisitor).Error
}

func GetCnFreeVisitorById(tx *gorm.DB, id int64) (model.CnFreeVisitor, error) {
	visitor := model.CnFreeVisitor{}
	err := OrmDB(tx).Where("id=?", id).Last(&visitor).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Error(fmt.Sprintf("查询中文免费观众信息sql错误：Err->%v;id->%d", err, id))
		return model.CnFreeVisitor{}, err
	}
	return visitor, nil
}

func GetCnFreeVisitorInfo(tx *gorm.DB, conferenceId int64, email string, fullName string, fromId string, fromName string, sourceId int, startTime string, endTime string, enterpriseType string, page int, size int, conferenceName string) (list []model.CnFreeVisitor, total int64, err error) {
	query := OrmDB(tx).Table("cn_free_visitor cfv").
		Select("cfv.id,cfv.conference_id,cfv.cn_conference_name,cfv.cn_name,cfv.cn_company,cfv.cn_department,cfv.cn_job_title," +
			"cfv.cn_email,cfv.cn_telephone,cfv.paper_answer,DATE_FORMAT(cfv.submit_time,'%Y-%m-%d %H:%i:%s') submit_time,cfv.willing,cfv.source,cfv.fromId,cfv.main_products,cfv.enterprise_type,cfv.procurement_items,cfv.order_quantity,cfv.from_name")
	if conferenceId != 0 {
		query = query.Where("cfv.conference_id = ?", conferenceId)
	}
	if conferenceName != "" {
		query = query.Where("cfv.cn_conference_name = ?", conferenceName)
	}
	if email != "" {
		query = query.Where("cfv.cn_email like ?", "%"+email+"%")
	}
	if fullName != "" {
		query = query.Where("cfv.cn_name like ?", "%"+fullName+"%")
	}
	if startTime != "" && endTime != "" {
		query = query.Where("cfv.submit_time between ? and ?", startTime, endTime)
	}
	if fromName != "" {
		query = query.Where("cfv.from_name =?", fromName)
	}
	if fromId != "" {
		query = query.Where("cfv.fromId like ?", "%"+fromId+"%")
	}
	if utils.NotEmpty(enterpriseType) {
		query = query.Where("cfv.enterprise_type = ?", enterpriseType)
	}
	if sourceId != 0 {
		query = query.Where("cfv.source=?", sourceId)
	}
	query.Order("id desc")
	err = query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}
	if page > 0 && size > 0 {
		limit, offSet := utils.PageOffset(page, size)
		query.Limit(limit).Offset(offSet)
	}
	err = query.Find(&list).Error
	if err != nil {
		logger.Error(fmt.Sprintf("查询中文免费观众信息失败：%v", err))
		return
	}
	return
}

func GetEnFreeVisitorInfo(tx *gorm.DB, conferenceId int64, email string, fullName string, fromId string, fromName string, sourceId int, startTime string, endTime string, enterpriseType string, page int, size int, confenreceName string) (list []model.EnFreeVisitor, total int64, err error) {
	query := OrmDB(tx).Table("en_free_visitor efv").
		Select("efv.id,efv.conference_id,efv.en_conference_name,efv.en_first_name,efv.en_last_name,efv.en_company,efv.en_job_title," +
			"efv.en_email,efv.en_telephone,efv.en_first_name,efv.en_last_name,efv.en_country_residence,efv.en_city,efv.en_phone,efv.en_nationality," +
			"efv.en_main_focus,DATE_FORMAT(efv.submit_time,'%Y-%m-%d %H:%i:%s') submit_time,efv.source,efv.fromId,efv.main_products,efv.enterprise_type,efv.procurement_items,efv.order_quantity,efv.from_name")

	if conferenceId != 0 {
		query = query.Where("efv.conference_id = ?", conferenceId)
	}
	if confenreceName != "" {
		query = query.Where("efv.en_conference_name = ?", confenreceName)
	}
	if email != "" {
		query = query.Where("en_email like ?", "%"+email+"%")
	}
	if fullName != "" {
		query = query.Where("en_first_name like ?", "%"+fullName+"%").Or("en_last_name like ?", "%"+fullName+"%")
	}
	if startTime != "" && endTime != "" {
		query = query.Where("efv.submit_time between ? and ?", startTime, endTime)
	}
	if fromId != "" {
		query = query.Where("efv.fromId like ?", "%"+fromId+"%")
	}
	if fromName != "" {
		query = query.Where("efv.fromName like ?", "%"+fromName+"%")
	}
	if sourceId != 0 {
		query = query.Where("efv.source=?", sourceId)
	}
	if utils.NotEmpty(enterpriseType) {
		query = query.Where("efv.enterprise_type = ?", enterpriseType)
	}

	query.Order("id desc")
	err = query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}
	if page > 0 && size > 0 {
		limit, offSet := utils.PageOffset(page, size)
		query.Limit(limit).Offset(offSet)
	}
	err = query.Find(&list).Error
	if err != nil {
		logger.Error(fmt.Sprintf("查询中文免费观众信息失败：%v", err))
		return nil, 0, err
	}
	return
}

func GetQuestionPaperExcel(tx *gorm.DB, conferenceId int64) (paper model.QuestionPaper, err error) {
	db := OrmDB(tx).Table("question_paper").Select("url")

	if conferenceId > 0 {
		db = db.Where("conference_id=?", conferenceId)
	} else {
		db = db.Where("conference_id=?", 1)
	}
	err = db.Last(&paper).Error
	if err != nil {
		return paper, err
	}
	return
}

func UpdateQuestionPaperAnswer(tx *gorm.DB, id int64, data map[string]interface{}) error {
	return OrmDB(tx).Table("cn_free_visitor").Where("id=?", id).Updates(data).Error
}

func GetConferenceNewsSet(db *gorm.DB, id int64) (company protocol.ConferenceNewsSet, err error) {
	err = OrmDB(db).Table("conference_news_set").Where("conference_id=?", id).Last(&company).Error
	if err != nil && gorm.ErrRecordNotFound == err {
		return company, nil
	}
	return company, err
}

func GetConferenceNewsGallery(db *gorm.DB) (companyList []model.NewsGallery, err error) {

	query := OrmDB(db).Table("news_gallery").Order("id asc")
	err = query.Find(&companyList).Error
	return companyList, err
}

func AdminEnterpriseTypeDownList(db *gorm.DB, conferenceId int64, page int, size int) (companyList []model.ConferenceEnterpriseType, total int64, err error) {
	if conferenceId <= 0 {
		return nil, 0, errors.New("展会id必须大于0")
	}
	limit, offSet := utils.PageOffset(page, size)
	query := OrmDB(db).Table("conference_enterprise_type").Where("conference_id=?", conferenceId)

	query.Order("id asc")
	query.Count(&total)
	err = query.Limit(limit).Offset(offSet).Find(&companyList).Error
	return companyList, total, err
}
