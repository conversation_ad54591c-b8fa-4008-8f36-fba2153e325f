package db

import (
	"conferencecenter/internal/constant"
	"conferencecenter/internal/model"
	"conferencecenter/internal/pkg/utils"
	"github.com/pkg/errors"
	"gorm.io/gorm"
	"time"
)

func QueryConferenceIntroductionInfo(tx *gorm.DB, confId int64) (confInfo *model.ConferenceIntroduction, err error) {
	err = OrmDB(tx).Model(&model.ConferenceIntroduction{}).Where("deleted=0").Where(`id = ?`, confId).First(&confInfo).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return confInfo, err
	}
	return confInfo, nil
}

func SaveConferenceIntroduction(tx *gorm.DB, confInfo *model.ConferenceIntroduction) (err error) {
	return OrmDB(tx).Save(confInfo).Error
}

func QueryConferenceDataList(db *gorm.DB, conferenceId int64, page int, size int) (companyList []model.ConferenceData, total int64, err error) {
	if conferenceId <= 0 {
		return nil, 0, errors.New("展会id必须大于0")
	}
	limit, offSet := utils.PageOffset(page, size)
	query := OrmDB(db).Table("conference_data").Where("deleted=0").Where("conference_id=?", conferenceId).Order("sorting asc").Order("id desc")
	query.Count(&total)
	err = query.Limit(limit).Offset(offSet).Find(&companyList).Error
	return companyList, total, err
}

func QueryConferenceDataInfo(tx *gorm.DB, confId int64) (confInfo model.ConferenceData, err error) {
	err = OrmDB(tx).Model(&model.ConferenceData{}).Where("deleted=0").Where(`id = ?`, confId).First(&confInfo).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return confInfo, err
	}
	return confInfo, nil
}

func SaveConferenceData(tx *gorm.DB, confInfo *model.ConferenceData) (err error) {
	return OrmDB(tx).Save(confInfo).Error
}

func UpdateConferenceData(tx *gorm.DB, confId int64, deleted int, opUser string) (err error) {
	upData := map[string]interface{}{
		"deleted":      deleted,
		"update_admin": opUser,
		"update_time":  time.Now().Format(constant.DateTime),
	}
	return OrmDB(tx).Model(&model.ConferenceData{}).Where(`id = ?`, confId).Updates(upData).Error
}

func QueryConferenceOrganizationList(db *gorm.DB, conferenceId int64, Type, page, size int) (companyList []model.ConferenceOrganization, total int64, err error) {
	if conferenceId <= 0 {
		return nil, 0, errors.New("展会id必须大于0")
	}
	limit, offSet := utils.PageOffset(page, size)
	query := OrmDB(db).Table("conference_organization").Where("deleted=0").Where("conference_id=?", conferenceId)
	if Type > 0 {
		query.Where("type=?", Type)
	}
	query.Order("sorting asc").Order("id desc")
	query.Count(&total)
	err = query.Limit(limit).Offset(offSet).Find(&companyList).Error
	return companyList, total, err
}

func QueryConferenceOrganizationInfo(tx *gorm.DB, confId int64) (confInfo model.ConferenceOrganization, err error) {
	err = OrmDB(tx).Model(&model.ConferenceOrganization{}).Where("deleted=0").Where(`id = ?`, confId).First(&confInfo).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return confInfo, err
	}
	return confInfo, nil
}

func QueryConferenceOrganizationByName(tx *gorm.DB, id, conferenceId int64, Type, cnName, enName string) (confInfo model.ConferenceOrganization, err error) {
	query := OrmDB(tx).Model(&model.ConferenceOrganization{}).Where("deleted=0")

	if id > 0 {
		query.Where(`id != ?`, id)
	}
	if conferenceId > 0 {
		query.Where(`conference_id = ?`, conferenceId)
	}
	if utils.NotEmpty(Type) {
		query.Where(`type = ?`, Type)
	}
	if utils.NotEmpty(cnName) {
		query.Where(`cn_name = ?`, cnName)
	}
	if utils.NotEmpty(enName) {
		query.Where(`en_name = ?`, enName)
	}
	err = query.First(&confInfo).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return confInfo, err
	}
	return confInfo, nil
}

func SaveConferenceOrganization(tx *gorm.DB, confInfo *model.ConferenceOrganization) (err error) {
	return OrmDB(tx).Save(confInfo).Error
}

func UpdateConferenceOrganization(tx *gorm.DB, confId int64, deleted int, opUser string) (err error) {
	upData := map[string]interface{}{
		"deleted":      deleted,
		"update_admin": opUser,
		"update_time":  time.Now().Format(constant.DateTime),
	}
	return OrmDB(tx).Model(&model.ConferenceOrganization{}).Where(`id = ?`, confId).Updates(upData).Error
}

func QueryContactInformationList(db *gorm.DB, conferenceId int64, page, size int, isAdmin bool) (companyList []model.ContactInformation, total int64, err error) {
	if conferenceId <= 0 {
		return nil, 0, errors.New("展会id必须大于0")
	}
	limit, offSet := utils.PageOffset(page, size)
	query := OrmDB(db).Table("contact_information").Where("deleted=0").Where("conference_id=?", conferenceId)
	//if !isAdmin {
	//	query.Where("is_displayed=1")
	//}
	query.Order("sorting asc").Order("id desc")
	query.Count(&total)
	err = query.Limit(limit).Offset(offSet).Find(&companyList).Error
	return companyList, total, err
}

func QueryContactInformationInfo(tx *gorm.DB, confId int64) (confInfo model.ContactInformation, err error) {
	err = OrmDB(tx).Model(&model.ContactInformation{}).Where("deleted=0").Where(`id = ?`, confId).First(&confInfo).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return confInfo, err
	}
	return confInfo, nil
}

func QueryContactInformationByName(tx *gorm.DB, id, conferenceId int64, Type, cnName, enName string) (confInfo model.ContactInformation, err error) {
	query := OrmDB(tx).Model(&model.ContactInformation{}).Where("deleted=0")

	if id > 0 {
		query.Where(`id != ?`, id)
	}
	if conferenceId > 0 {
		query.Where(`conference_id = ?`, conferenceId)
	}
	if utils.NotEmpty(cnName) {
		query.Where(`cn_name = ?`, cnName)
	}
	if utils.NotEmpty(enName) {
		query.Where(`en_name = ?`, enName)
	}
	err = query.First(&confInfo).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return confInfo, err
	}
	return confInfo, nil
}

func SaveContactInformation(tx *gorm.DB, confInfo *model.ContactInformation) (err error) {
	return OrmDB(tx).Save(confInfo).Error
}

func UpdateContactInformation(tx *gorm.DB, confId int64, deleted int, opUser string) (err error) {
	upData := map[string]interface{}{
		"deleted":      deleted,
		"update_admin": opUser,
		"update_time":  time.Now().Format(constant.DateTime),
	}
	return OrmDB(tx).Model(&model.ContactInformation{}).Where(`id = ?`, confId).Updates(upData).Error
}

func QueryConferenceBottomPageList(db *gorm.DB, conferenceId int64, sortingType, page, size int) (companyList []model.ConferenceBottomPage, total int64, err error) {
	if conferenceId <= 0 {
		return nil, 0, errors.New("展会id必须大于0")
	}
	limit, offSet := utils.PageOffset(page, size)
	query := OrmDB(db).Table("conference_bottom_page").Where("deleted=0").Where("conference_id=?", conferenceId)

	if sortingType > 0 {
		query.Order("en_sorting asc")
	} else {
		query.Order("cn_sorting asc")
	}
	query.Order("id desc")
	query.Count(&total)
	err = query.Limit(limit).Offset(offSet).Find(&companyList).Error
	return companyList, total, err
}

func QueryConferenceBottomPageInfo(tx *gorm.DB, confId int64) (confInfo model.ConferenceBottomPage, err error) {
	err = OrmDB(tx).Model(&model.ConferenceBottomPage{}).Where("deleted=0").Where(`id = ?`, confId).First(&confInfo).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return confInfo, err
	}
	return confInfo, nil
}

func QueryConferenceBottomPageByName(tx *gorm.DB, id, conferenceId int64, Type, cnName, enName string) (confInfo model.ConferenceBottomPage, err error) {
	query := OrmDB(tx).Model(&model.ConferenceBottomPage{}).Where("deleted=0")

	if id > 0 {
		query.Where(`id != ?`, id)
	}
	if conferenceId > 0 {
		query.Where(`conference_id = ?`, conferenceId)
	}
	if utils.NotEmpty(cnName) {
		query.Where(`cn_name = ?`, cnName)
	}
	if utils.NotEmpty(enName) {
		query.Where(`en_name = ?`, enName)
	}
	err = query.First(&confInfo).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return confInfo, err
	}
	return confInfo, nil
}

func SaveConferenceBottomPage(tx *gorm.DB, confInfo *model.ConferenceBottomPage) (err error) {
	return OrmDB(tx).Save(confInfo).Error
}

func UpdateConferenceBottomPage(tx *gorm.DB, confId int64, deleted int, opUser string) (err error) {
	upData := map[string]interface{}{
		"deleted":      deleted,
		"update_admin": opUser,
		"update_time":  time.Now().Format(constant.DateTime),
	}
	return OrmDB(tx).Model(&model.ConferenceBottomPage{}).Where(`id = ?`, confId).Updates(upData).Error
}
