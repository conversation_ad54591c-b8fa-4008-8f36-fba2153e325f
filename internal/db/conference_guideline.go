package db

import (
	"conferencecenter/internal/model"
	"gorm.io/gorm"
)

func SaveExhibitionGuideline(db *gorm.DB, guideline model.ExhibitionGuideline) error {
	err := OrmDB(db).Create(&guideline).Error
	return err
}

func FindExhibitionGuideline(db *gorm.DB, conferenceId int64) (list []model.ExhibitionGuideline, err error) {
	err = OrmDB(db).Where("conference_Id=?", conferenceId).Find(&list).Error
	return
}

//func UpdateExhibitionGuideline(db *gorm.DB, guideline model.ExhibitionGuideline) error {
//	err := OrmDB(db).Where("id=?", guideline.Id).Save(&guideline).Error
//	return err
//
//}

func UpdateExhibitionGuideline(db *gorm.DB, guideline model.ExhibitionGuideline) error {
	err := OrmDB(db).Table("exhibition_guideline").Save(&guideline).Error
	return err
}

func GetExhibitionGuideline(db *gorm.DB, conferenceId int64) (guideline model.ExhibitionGuideline, err error) {
	err = OrmDB(db).Where("conference_id=?", conferenceId).Where("is_deleted=?", 0).Last(&guideline).Error
	if err != nil && gorm.ErrRecordNotFound == err {
		return model.ExhibitionGuideline{}, nil
	}
	return guideline, err
}
