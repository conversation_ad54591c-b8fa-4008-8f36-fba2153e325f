package db

import (
	"conferencecenter/internal/constant"
	"conferencecenter/internal/model"
	"conferencecenter/internal/pkg/utils"
	"database/sql"
	"fmt"
	"gorm.io/gorm"
	"strconv"
)

// 展商手册订阅列表
func QueryHandBookSubscribeList(tx *gorm.DB, conferenceID int64, keyword, startTime, endTime string, page, pageSize int, withCount bool) (int64, []model.HandBookSubscribeInfo, error) {
	var (
		total         int64
		dataLi        = make([]model.HandBookSubscribeInfo, 0)
		limit, offset = utils.PageOffset(page, pageSize)
		qParams       = make([]interface{}, 0)
		cParams       = make([]interface{}, 0)
		err           error
	)
	tx = OrmDB(tx)
	querySql := fmt.Sprintf(" SELECT s.id,s.conference_id,s.cn_conference_name,s.en_conference_name,s.cellphone,s.email,s.create_time,s.update_time FROM `company_handbook_subscribe` s INNER JOIN `conference` c ON s.conference_id = c.id WHERE c.status != %d ", constant.ConferenceStatusDeleted)
	countSql := fmt.Sprintf(" SELECT count(s.id) FROM `company_handbook_subscribe` s INNER JOIN `conference` c ON s.conference_id = c.id WHERE c.status != %d ", constant.ConferenceStatusDeleted)
	if conferenceID > 0 {
		querySql += " AND c.id = ? "
		countSql += " AND c.id = ? "
		qParams = append(qParams, conferenceID)
		cParams = append(cParams, conferenceID)
	}
	if keyword != "" {
		keyLike := "%" + keyword + "%"
		keyId, err_ := strconv.Atoi(keyword)
		if err_ == nil && keyId > 0 {
			querySql += " AND (c.id = ? OR c.cn_name LIKE ? OR c.en_name LIKE ?) "
			countSql += " AND (c.id = ? OR c.cn_name LIKE ? OR c.en_name LIKE ?) "
			qParams = append(qParams, keyId, keyLike, keyLike)
			cParams = append(cParams, keyId, keyLike, keyLike)
		} else {
			querySql += " AND (c.cn_name LIKE ? OR c.en_name LIKE ?) "
			countSql += " AND (c.cn_name LIKE ? OR c.en_name LIKE ?) "
			qParams = append(qParams, keyLike, keyLike)
			cParams = append(cParams, keyLike, keyLike)
		}
	}
	if startTime != "" {
		querySql += " AND s.create_time >= ?"
		countSql += " AND s.create_time >= ?"
		qParams = append(qParams, startTime)
		cParams = append(cParams, startTime)
	}
	if endTime != "" {
		querySql += " AND s.create_time <= ?"
		countSql += " AND s.create_time <= ?"
		qParams = append(qParams, endTime)
		cParams = append(cParams, endTime)
	}
	querySql += " ORDER BY id DESC "
	if page > 0 && pageSize > 0 {
		querySql += " LIMIT ?,? "
		qParams = append(qParams, offset, limit)
	}
	{
		err = tx.Raw(querySql, qParams...).Scan(&dataLi).Error
		if err != nil && err != sql.ErrNoRows {
			return total, dataLi, err
		}
	}
	if withCount {
		err = tx.Raw(countSql, cParams...).Count(&total).Error
		if err != nil {
			return total, dataLi, err
		}
	}
	return total, dataLi, err
}

func CreateHandbookSubscribe(tx *gorm.DB, data *model.CompanyHandbookSubscribe) (err error) {
	return OrmDB(tx).Create(data).Error
}

// 查询展商手册信息
func QueryHandBookInfo(tx *gorm.DB, id int64) (handBook model.CompanyHandBook, err error) {
	if id <= 0 {
		return handBook, nil
	}
	query := OrmDB(tx).Model(&model.CompanyHandBook{})
	if id > 0 {
		query = query.Where(` id = ? `, id)
	}
	err = query.Last(&handBook).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return handBook, err
	}
	return handBook, nil
}

func FindConferenceCompany(db *gorm.DB, conferenceId int64) (list []model.ConferenceCompany, err error) {
	err = OrmDB(db).Where("conference_id=?", conferenceId).Find(&list).Error
	return
}

func SaveConferenceCompany(db *gorm.DB, company model.ConferenceCompany) error {
	err := OrmDB(db).Create(&company).Error
	return err
}

func UpdateConferenceCompany(db *gorm.DB, company model.ConferenceCompany) error {
	err := OrmDB(db).Updates(&company).Error
	return err
}

func GetConferenceCompany(db *gorm.DB, conferenceId int64) (company model.ConferenceCompany, err error) {
	err = OrmDB(db).Where("conference_id=?", conferenceId).Last(&company).Error
	//err处理
	if err != nil && gorm.ErrRecordNotFound == err {
		return model.ConferenceCompany{}, nil
	}
	return company, nil
}

func SaveCompanyHandbook(db *gorm.DB, book *model.CompanyHandBook) error {
	err := OrmDB(db).Create(book).Error
	return err
}

func GetCompanyHandbookList(db *gorm.DB, conferenceId int64) (list []model.CompanyHandBook, err error) {
	err = OrmDB(db).Where("is_deleted=?", 0).Where("conference_id=?", conferenceId).Order("sort asc,id desc").Find(&list).Error
	return
}

func GetCompanyHandbook(db *gorm.DB, handbookId int64) (handbook model.CompanyHandBook, err error) {
	err = OrmDB(db).Where("id=?", handbookId).Last(&handbook).Error
	if err != nil && gorm.ErrRecordNotFound == err {
		return model.CompanyHandBook{}, nil
	}
	return handbook, nil
}

func DeleteCompanyHandbook(db *gorm.DB, handbookId int64) error {
	err := OrmDB(db).Model(&model.CompanyHandBook{}).Where("id=?", handbookId).Update("is_deleted", 1).Error
	return err
}

func UpdateCompanyHandbook(db *gorm.DB, book model.CompanyHandBook) error {
	err := OrmDB(db).Table("company_handbook").Updates(&book).Error
	return err
}

func SaveCompanyQuestionType(db *gorm.DB, questionType *model.CompanyQuestionType) error {
	err := OrmDB(db).Table("company_question_type").Create(questionType).Error
	return err
}

func GetCompanyQuestionTypeList(db *gorm.DB, conferenceId int64) (list []model.CompanyQuestionType, err error) {
	err = OrmDB(db).Table("company_question_type").Order("sort asc,id desc").Where("conference_id=?", conferenceId).Where("is_deleted=0").Find(&list).Error
	return
}

func GetCompanyQuestionType(db *gorm.DB, typeId int64) (questionType model.CompanyQuestionType, err error) {
	err = OrmDB(db).Table("company_question_type").Where("id=?", typeId).Where("is_deleted=?", 0).Last(&questionType).Error
	if err != nil && gorm.ErrRecordNotFound == err {
		return model.CompanyQuestionType{}, nil
	}
	return questionType, err
}

func EditCompanyQuestionType(db *gorm.DB, questionType model.CompanyQuestionType) error {
	err := OrmDB(db).Table("company_question_type").Updates(&questionType).Error
	return err
}

func DeleteCompanyQuestionType(db *gorm.DB, questionTypeId int64) error {
	err := OrmDB(db).Model(&model.CompanyQuestionType{}).Where("id=?", questionTypeId).Update("is_deleted", 1).Error
	return err
}

func SaveCompanyQuestionContent(db *gorm.DB, content *model.CommonQuestionContent) error {
	err := OrmDB(db).Table("company_question_content").Create(content).Error
	return err
}

func GetCompanyQuestionAnswerByTypeId(db *gorm.DB, typeId int64) (list []model.CompanyQuestionContent, err error) {
	//test
	err = OrmDB(db).Table("company_question_content cqc").
		Joins("left join company_question_type cqt on cqc.question_type_id=cqt.id and cqt.is_deleted=0 and cqc.is_deleted=0").
		Select("cqc.id id,cqt.id question_type_id,cqt.cn_question_type,cqt.en_question_type,cqc.cn_question_content,cqc.en_question_content,cqc.cn_question_answer,cqc.en_question_answer,cqc.sort").
		Where("cqc.question_type_id=?", typeId).Where("cqc.is_deleted=0").Order("cqc.sort asc,cqc.id desc").Find(&list).Error
	return
}

func GetCompanyQuestionAnswerByAnswerId(db *gorm.DB, answerId int64) (content model.CommonQuestionContent, err error) {
	err = OrmDB(db).Table("company_question_content").Where("id=?", answerId).Last(&content).Error
	if err != nil && gorm.ErrRecordNotFound == err {
		return model.CommonQuestionContent{}, nil
	}
	return content, nil
}

func EditCompanyQuestionContent(db *gorm.DB, content model.CommonQuestionContent) error {
	err := OrmDB(db).Table("company_question_content").Updates(&content).Error
	return err
}

func DeleteCompanyQuestionContent(db *gorm.DB, contentId int64) error {
	err := OrmDB(db).Model(&model.CompanyQuestionContent{}).Where("id=?", contentId).Update("is_deleted", 1).Error
	return err
}

func QueryProduction(db *gorm.DB, conferenceID int64, keyword string, language string, page int, size int) (int64, []model.ProductionData, error) {
	var (
		total         int64
		dataLi        = make([]model.ProductionData, 0)
		err           error
		queryCols     string
		limit, offset = utils.PageOffset(page, size)
	)
	if language == "cn" {
		queryCols = "pm.id,pm.audience_company_id,pm.cn_name name,pm.en_name,pm.logo,pm.sorting"
	} else {
		queryCols = "pm.id,pm.audience_company_id,pm.en_name name,pm.logo,pm.sorting"
	}

	query := OrmDB(db).Table(`production_management pm`).Joins("join audience_conference_company acc on pm.audience_company_id=acc.id").
		Select(queryCols).Where(`deleted=0`)
	if conferenceID > 0 {
		query = query.Where(`acc.conference_id=?`, conferenceID)
	}
	if utils.NotEmpty(keyword) {
		likeCon := "%" + keyword + "%"
		if language == "cn" {
			query = query.Where(`pm.cn_name LIKE ?`, likeCon)
		} else {
			query = query.Where(`pm.en_name LIKE ?`, likeCon)
		}
	}
	if err = query.Count(&total).Error; err != nil {
		return 0, dataLi, err
	}
	if total <= 0 {
		return 0, dataLi, nil
	}
	query = query.Order("sorting asc,id asc")
	if page > 0 && size > 0 {
		query = query.Limit(limit).Offset(offset)
	}
	err = query.Find(&dataLi).Error
	if err != nil {
		return 0, dataLi, err
	}
	return total, dataLi, err
}
