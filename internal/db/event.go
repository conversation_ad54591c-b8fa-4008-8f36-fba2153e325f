package db

import (
	"conferencecenter/internal/constant"
	"conferencecenter/internal/model"
	"conferencecenter/internal/pkg/utils"
	"github.com/pkg/errors"
	"gorm.io/gorm"
	"time"
)

func QueryConferenceEventCategoryList(db *gorm.DB, conferenceId int64, page int, size int) (companyList []model.ConferenceEventCategory, total int64, err error) {
	if conferenceId <= 0 {
		return nil, 0, errors.New("展会id必须大于0")
	}
	limit, offSet := 0, 0
	if page > 0 && size > 0 {
		limit, offSet = utils.PageOffset(page, size)
	}
	query := OrmDB(db).Table("conference_event_category").Where("deleted=0").Where("conference_id=?", conferenceId)

	query.Order("sorting asc").Order("id desc")
	query.Count(&total)
	if limit >= 0 && offSet > 0 {
		query.Limit(limit).Offset(offSet)
	}
	err = query.Find(&companyList).Error
	return companyList, total, err
}

func QueryConferenceEventCategoryInfo(tx *gorm.DB, confId int64) (confInfo model.ConferenceEventCategory, err error) {
	err = OrmDB(tx).Model(&model.ConferenceEventCategory{}).Where("deleted=0").Where(`id = ?`, confId).First(&confInfo).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return confInfo, err
	}
	return confInfo, nil
}
func QueryConferenceEventCategory(tx *gorm.DB, confId int64, categoryType int) (confInfo model.ConferenceEventCategory, err error) {
	err = OrmDB(tx).Model(&model.ConferenceEventCategory{}).Where("deleted=0").Where(`conference_id = ?`, confId).Where("type", categoryType).First(&confInfo).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return confInfo, err
	}
	return confInfo, nil
}

func SaveConferenceEventCategoryInfo(tx *gorm.DB, confInfo *model.ConferenceEventCategory) (err error) {
	return OrmDB(tx).Save(confInfo).Error
}

func UpdateConferenceEventCategory(tx *gorm.DB, confId int64, deleted int, opUser string) (err error) {
	upData := map[string]interface{}{
		"deleted":      deleted,
		"update_admin": opUser,
		"update_time":  time.Now().Format(constant.DateTime),
	}
	return OrmDB(tx).Model(&model.ConferenceEventCategory{}).Where(`id = ?`, confId).Updates(upData).Error
}

func QueryConferenceEventList(db *gorm.DB, conferenceId int64, name string, page int, size int) (companyList []model.ConferenceEvent, total int64, err error) {
	if conferenceId <= 0 {
		return nil, 0, errors.New("展会id必须大于0")
	}
	limit, offSet := utils.PageOffset(page, size)
	query := OrmDB(db).Table("conference_event").Where("deleted=0").Where("conference_id=?", conferenceId)

	if utils.NotEmpty(name) {
		likeCon := "%" + name + "%"
		query = query.Where(` (cn_name LIKE ? OR en_name LIKE ? OR cn_place LIKE ? OR en_place LIKE ?) `, likeCon, likeCon, likeCon, likeCon)
	}
	//if categoryType > 0 {
	//	query.Where("category_type=?", categoryType)
	//}
	query.Order("sorting asc").Order("id desc")
	query.Count(&total)
	err = query.Limit(limit).Offset(offSet).Find(&companyList).Error
	return companyList, total, err
}

func QueryConferenceEventInfo(tx *gorm.DB, confId int64) (confInfo model.ConferenceEvent, err error) {
	err = OrmDB(tx).Model(&model.ConferenceEvent{}).Where("deleted=0").Where(`id = ?`, confId).First(&confInfo).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return confInfo, err
	}
	return confInfo, nil
}

// QueryConferenceEventByCode 根据活动编号查询活动信息
func QueryConferenceEventByCode(tx *gorm.DB, conferenceId int64, eventCode string) (confInfo model.ConferenceEvent, err error) {
	err = OrmDB(tx).Model(&model.ConferenceEvent{}).Where("deleted=0").Where("conference_id=? AND event_code=?", conferenceId, eventCode).First(&confInfo).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return confInfo, err
	}
	return confInfo, nil
}

func SaveConferenceEventInfo(tx *gorm.DB, confInfo *model.ConferenceEvent) (err error) {
	return OrmDB(tx).Save(confInfo).Error
}

func UpdateConferenceEvent(tx *gorm.DB, confId int64, deleted int, opUser string) (err error) {
	upData := map[string]interface{}{
		"deleted":      deleted,
		"update_admin": opUser,
		"update_time":  time.Now().Format(constant.DateTime),
	}
	return OrmDB(tx).Model(&model.ConferenceEvent{}).Where(`id = ?`, confId).Updates(upData).Error
}

func QueryEventScheduleDateList(db *gorm.DB, conferenceId, eventId int64, page int, size int) (companyList []model.EventScheduleDate, total int64, err error) {
	if conferenceId <= 0 && eventId <= 0 {
		return nil, 0, errors.New("展会id必须大于0")
	}
	limit, offSet := utils.PageOffset(page, size)
	query := OrmDB(db).Table("event_schedule_date").Where("deleted=0")
	if conferenceId > 0 {
		query.Where("conference_id=?", conferenceId)
	}
	if eventId > 0 {
		query.Where("event_id=?", eventId)
	}

	query.Order("date asc").Order("id desc")
	query.Count(&total)
	err = query.Limit(limit).Offset(offSet).Find(&companyList).Error
	return companyList, total, err
}

func UpdateConferenceEventDate(tx *gorm.DB, confId, deleted int64, opUser string) (err error) {
	upData := map[string]interface{}{
		"deleted":      deleted,
		"update_admin": opUser,
		"update_time":  time.Now().Format(constant.DateTime),
	}
	return OrmDB(tx).Model(&model.EventScheduleDate{}).Where(`event_id = ?`, confId).Updates(upData).Error
}

func QueryEventScheduleDateTimeList(db *gorm.DB, conferenceId, eventId int64, page int, size int) (companyList []model.EventScheduleDate, total int64, err error) {
	if conferenceId <= 0 && eventId <= 0 {
		return nil, 0, errors.New("展会id必须大于0")
	}
	limit, offSet := utils.PageOffset(page, size)
	query := OrmDB(db).Table("event_schedule_date").Where("deleted=0")
	if conferenceId > 0 {
		query.Where("conference_id=?", conferenceId)
	}
	if eventId > 0 {
		query.Where("event_id=?", eventId)
	}
	query.Group("event_id,start_date_time,end_date_time")
	query.Order("sorting asc").Order("id")
	query.Count(&total)
	err = query.Limit(limit).Offset(offSet).Find(&companyList).Error
	return companyList, total, err
}

func QueryEventScheduleDateInfo(tx *gorm.DB, confId int64) (confInfo model.EventScheduleDate, err error) {
	err = OrmDB(tx).Model(&model.EventScheduleDate{}).Where("deleted=0").Where(`id = ?`, confId).First(&confInfo).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return confInfo, err
	}
	return confInfo, nil
}

func SaveEventScheduleDateInfo(tx *gorm.DB, confInfo *model.EventScheduleDate) (err error) {
	return OrmDB(tx).Save(confInfo).Error
}

func QueryEventScheduleForumList(db *gorm.DB, dayId int64) (companyList []model.EventScheduleForum, total int64, err error) {
	if dayId <= 0 {
		return nil, 0, errors.New("展会id必须大于0")
	}
	query := OrmDB(db).Table("event_schedule_forum").Where("deleted=0").Where("day_id=?", dayId).Order("sorting asc").Order("id desc")
	query.Count(&total)
	err = query.Find(&companyList).Error
	return companyList, total, err
}

func QueryEventScheduleForumInfo(tx *gorm.DB, confId int64) (confInfo model.EventScheduleForum, err error) {
	err = OrmDB(tx).Model(&model.EventScheduleForum{}).Where("deleted=0").Where(`id = ?`, confId).First(&confInfo).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return confInfo, err
	}
	return confInfo, nil
}

func SaveEventScheduleForum(tx *gorm.DB, confInfo *model.EventScheduleForum) (err error) {
	return OrmDB(tx).Save(confInfo).Error
}

func UpdateEventScheduleForum(tx *gorm.DB, confId int64, deleted int, opUser string) (err error) {
	upData := map[string]interface{}{
		"deleted":      deleted,
		"update_admin": opUser,
		"update_time":  time.Now().Format(constant.DateTime),
	}
	return OrmDB(tx).Model(&model.EventScheduleForum{}).Where(`id = ?`, confId).Updates(upData).Error
}

func QueryEventScheduleList(db *gorm.DB, eventId, dayId, forumId int64, name string, page int, size int) (companyList []model.EventSchedule, total int64, err error) {
	if eventId <= 0 && eventId <= 0 && forumId <= 0 {
		return nil, 0, errors.New("展会id必须大于0")
	}
	limit, offSet := utils.PageOffset(page, size)
	query := OrmDB(db).Table("event_schedule").Where("deleted=0")
	if eventId > 0 {
		query.Where("event_id=?", eventId)
	}
	if dayId > 0 {
		query.Where("day_id=?", dayId)
	}
	if forumId > 0 {
		query.Where("forum_id=?", forumId)
	}
	if utils.NotEmpty(name) {
		if utils.NotEmpty(name) {
			likeCon := "%" + name + "%"
			query = query.Where(` (cn_title LIKE ? OR en_title LIKE ? OR cn_content LIKE ? OR en_content LIKE ?) `, likeCon, likeCon, likeCon, likeCon)
		}
	}
	query.Order("sorting asc").Order("id desc")
	query.Count(&total)
	err = query.Limit(limit).Offset(offSet).Find(&companyList).Error
	return companyList, total, err
}

func QueryEventScheduleInfo(tx *gorm.DB, confId int64) (confInfo model.EventSchedule, err error) {
	err = OrmDB(tx).Model(&model.EventSchedule{}).Where("deleted=0").Where(`id = ?`, confId).First(&confInfo).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return confInfo, err
	}
	return confInfo, nil
}

func SaveEventSchedule(tx *gorm.DB, confInfo *model.EventSchedule) (err error) {
	return OrmDB(tx).Save(confInfo).Error
}

func UpdateEventSchedule(tx *gorm.DB, confId int64, deleted int, opUser string) (err error) {
	upData := map[string]interface{}{
		"deleted":      deleted,
		"update_admin": opUser,
		"update_time":  time.Now().Format(constant.DateTime),
	}
	return OrmDB(tx).Model(&model.EventSchedule{}).Where(`id = ?`, confId).Updates(upData).Error
}

func QueryEventScheduleGuestList(db *gorm.DB, eventId, scheduleId int64, name string, page int, size int) (companyList []model.EventScheduleGuest, total int64, err error) {
	if scheduleId <= 0 && eventId <= 0 {
		return nil, 0, errors.New("展会id必须大于0")
	}
	limit, offSet := utils.PageOffset(page, size)
	query := OrmDB(db).Table("event_schedule_guest").Where("deleted=0")
	if eventId > 0 {
		query.Where("event_id=?", eventId)
	}
	if scheduleId > 0 {
		query.Where("schedule_id=?", scheduleId)
	}

	if utils.NotEmpty(name) {
		likeCon := "%" + name + "%"
		query = query.Where(` (cn_company LIKE ? OR en_company LIKE ? OR cn_position LIKE ? OR en_position LIKE ? OR cn_appellation LIKE ? OR en_appellation LIKE ?) `, likeCon, likeCon, likeCon, likeCon, likeCon, likeCon)
	}
	query.Order("sorting asc").Order("id desc")
	query.Count(&total)
	err = query.Limit(limit).Offset(offSet).Find(&companyList).Error
	return companyList, total, err
}

func QueryEventScheduleGuestInfo(tx *gorm.DB, confId int64) (confInfo model.EventScheduleGuest, err error) {
	err = OrmDB(tx).Model(&model.EventScheduleGuest{}).Where("deleted=0").Where(`id = ?`, confId).First(&confInfo).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return confInfo, err
	}
	return confInfo, nil
}

func SaveEventScheduleGuest(tx *gorm.DB, confInfo *model.EventScheduleGuest) (err error) {
	return OrmDB(tx).Save(confInfo).Error
}

func UpdateEventScheduleGuest(tx *gorm.DB, confId int64, deleted int, opUser string) (err error) {
	upData := map[string]interface{}{
		"deleted":      deleted,
		"update_admin": opUser,
		"update_time":  time.Now().Format(constant.DateTime),
	}
	return OrmDB(tx).Model(&model.EventScheduleGuest{}).Where(`id = ?`, confId).Updates(upData).Error
}

// SoftDeleteEventSchedulesByEventId 根据活动ID软删除所有日程
func SoftDeleteEventSchedulesByEventId(tx *gorm.DB, eventId int64, conferenceId int64) error {
	upData := map[string]interface{}{
		"deleted":     1,
		"update_time": time.Now(),
	}
	return OrmDB(tx).Model(&model.EventSchedule{}).
		Where("event_id = ? AND conference_id = ? AND deleted = 0", eventId, conferenceId).
		Updates(upData).Error
}

// SoftDeleteEventScheduleGuestsByEventId 根据活动ID软删除所有嘉宾
func SoftDeleteEventScheduleGuestsByEventId(tx *gorm.DB, eventId int64, conferenceId int64) error {
	upData := map[string]interface{}{
		"deleted":     "1",
		"update_time": time.Now(),
	}
	return OrmDB(tx).Model(&model.EventScheduleGuest{}).
		Where("event_id = ? AND conference_id = ? AND deleted = '0'", eventId, conferenceId).
		Updates(upData).Error
}

func UserEventScheduleDateList(db *gorm.DB, eventId int64) (companyList []model.EventScheduleDate, err error) {
	if eventId <= 0 {
		return nil, errors.New("活动id必须大于0")
	}
	query := OrmDB(db).Table("event_schedule_date").Where("deleted=0").Where("event_id=?", eventId).Order("sorting asc").Order("id desc")
	err = query.Find(&companyList).Error
	return companyList, err
}

// 搜索活动
func QueryConferenceEvents(tx *gorm.DB, conferenceId int64, keyword, language string, page int, pageSize int) (int64, []model.EventData, error) {
	var (
		total         int64
		dataLi        = make([]model.EventData, 0)
		err           error
		queryCols     string
		limit, offset = utils.PageOffset(page, pageSize)
	)
	if language == "cn" {
		queryCols = "id,cn_name as name,category_type"
	} else {
		queryCols = "id,en_name as name,category_type"
	}
	query := OrmDB(tx).Model(new(model.ConferenceEvent)).Select(queryCols).Where(`deleted=0`)
	if conferenceId > 0 {
		query = query.Where(`conference_id=?`, conferenceId)
	}
	if utils.NotEmpty(keyword) {
		likeCon := "%" + keyword + "%"
		if language == "cn" {
			query = query.Where(` cn_name LIKE ? `, likeCon)
		} else {
			query = query.Where(` en_name LIKE ? `, likeCon)
		}
	}
	if err = query.Count(&total).Error; err != nil {
		return 0, dataLi, err
	}
	if total <= 0 {
		return 0, dataLi, nil
	}
	query = query.Order("sorting asc,id asc")
	if page > 0 && pageSize > 0 {
		query = query.Limit(limit).Offset(offset)
	}
	err = query.Find(&dataLi).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return 0, dataLi, err
	}
	return total, dataLi, err
}

func UserEventDateList(db *gorm.DB, conferenceId int64, page int, size int) (companyList []model.EventScheduleDate, total int64, err error) {
	if conferenceId <= 0 {
		return nil, 0, errors.New("展会id必须大于0")
	}
	limit, offSet := utils.PageOffset(page, size)
	query := OrmDB(db).Table("event_schedule_date").Where("deleted=0")
	if conferenceId > 0 {
		query.Where("conference_id=?", conferenceId)
	}
	query.Group("event_id,date")
	query.Order("date asc").Order("time asc")
	query.Count(&total)
	err = query.Limit(limit).Offset(offSet).Find(&companyList).Error
	return companyList, total, err
}

func UserEventDateListG(db *gorm.DB, conferenceId int64, page int, size int) (companyList []model.EventScheduleDate, total int64, err error) {
	if conferenceId <= 0 {
		return nil, 0, errors.New("展会id必须大于0")
	}
	limit, offSet := utils.PageOffset(page, size)
	query := OrmDB(db).Table("event_schedule_date").Where("deleted=0")
	if conferenceId > 0 {
		query.Where("conference_id=?", conferenceId)
	}
	query.Group("date")
	query.Order("sorting asc").Order("time asc")
	query.Count(&total)
	err = query.Limit(limit).Offset(offSet).Find(&companyList).Error
	return companyList, total, err
}

func QueryForumCategoryConfigList(db *gorm.DB) (companyList []model.ForumCategoryConfig, total int64, err error) {

	query := OrmDB(db).Table("forum_category_config").Order("id asc")

	query.Count(&total)
	err = query.Find(&companyList).Error
	return companyList, total, err
}

func QueryExpertInfoV3List(db *gorm.DB, page, pageSize int) (companyList []model.EventExpertInfo, total int64, err error) {

	query := EventOrm(db).Table("expert_info_v3").Where("event_id", 26).Order("id asc")
	query.Count(&total)
	if page > 0 && pageSize > 0 {
		limit, offSet := utils.PageOffset(page, pageSize)
		query.Limit(limit).Offset(offSet)
	}
	err = query.Find(&companyList).Error
	return companyList, total, err
}

func QueryAnnualSelection(db *gorm.DB, page, pageSize int) (companyList []model.AnnualSelection, total int64, err error) {

	query := OrmDB(db).Table("annual_selection").Where("conference_id", 2).Order("id asc")
	if page > 0 && pageSize > 0 {
		query.Limit(pageSize).Offset((page - 1) * pageSize)
	}
	query.Count(&total)
	err = query.Find(&companyList).Error
	return companyList, total, err
}
