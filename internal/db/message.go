package db

import (
	"conferencecenter/internal/constant"
	"conferencecenter/internal/model"
	"gorm.io/gorm"
	"time"
)

// 保存发送记录
func SaveHandBookSendLog(tx *gorm.DB, sendLog *model.CompanyHandBookSendLog) (err error) {
	return OrmDB(tx).Save(sendLog).Error
}

// 更新往届展会
func UpdateHandBookSendLog(tx *gorm.DB, logId int64, status int) (err error) {
	upData := map[string]interface{}{
		"status":      status,
		"update_time": time.Now().Format(constant.DateTime),
	}
	return OrmDB(tx).Model(&model.CompanyHandBookSendLog{}).Where(`id = ?`, logId).Updates(upData).Error
}
