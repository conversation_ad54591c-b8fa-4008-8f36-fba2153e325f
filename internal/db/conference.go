package db

import (
	"conferencecenter/internal/constant"
	"conferencecenter/internal/model"
	"conferencecenter/internal/pkg/utils"
	"gorm.io/gorm"
	"time"
)

func QueryConferenceList(tx *gorm.DB, keyword string, filter bool, page, pageSize int) (int64, []model.Conference, error) {
	var (
		total         int64
		confLi        = make([]model.Conference, 0)
		err           error
		limit, offset = utils.PageOffset(page, pageSize)
	)
	query := OrmDB(tx).Model(&model.Conference{})
	if keyword != "" {
		likeCon := "%" + keyword + "%"
		query = query.Where(` (cn_name LIKE ? OR en_name LIKE ? OR cn_short_name LIKE ? OR en_short_name LIKE ? OR cn_location LIKE ? OR en_location LIKE ?) `, likeCon, likeCon, likeCon, likeCon, likeCon, likeCon)
	}
	if filter {
		query = query.Where(`status != ?`, constant.ConferenceStatusDeleted)
	}
	if err = query.Count(&total).Error; err != nil {
		return 0, confLi, err
	}
	if total <= 0 {
		return 0, confLi, nil
	}
	query = query.Order("update_time desc,id desc")
	if page > 0 && pageSize > 0 {
		query = query.Limit(limit).Offset(offset)
	}
	err = query.Find(&confLi).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return 0, confLi, err
	}
	return total, confLi, nil
}

func QueryConferenceInfo(tx *gorm.DB, confId int64) (confInfo model.Conference, err error) {
	err = OrmDB(tx).Model(&model.Conference{}).Where(`id = ?`, confId).First(&confInfo).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return confInfo, err
	}
	return confInfo, nil
}

func QueryConferenceInfoByName(tx *gorm.DB, confId string) (confInfo model.Conference, err error) {
	err = OrmDB(tx).Model(&model.Conference{}).Where(`cn_name = ?`, confId).First(&confInfo).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return confInfo, err
	}
	return confInfo, nil
}

func GetConferenceMeetingNo(tx *gorm.DB, confId string) (confInfo model.Conference, err error) {
	err = OrmDB(tx).Model(&model.Conference{}).Where(`meeting_sys_id = ?`, confId).First(&confInfo).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return confInfo, err
	}
	return confInfo, nil
}

func UpdateConferenceStatus(tx *gorm.DB, confId int64, status int, opUser string) (err error) {
	upData := map[string]interface{}{
		"status":      status,
		"update_user": opUser,
		"update_time": time.Now().Format(constant.DateTime),
	}
	return OrmDB(tx).Model(&model.Conference{}).Where(`id = ?`, confId).Updates(upData).Error
}

func UpdateConferenceTemplate(tx *gorm.DB, confId int64, status int, opUser string) (err error) {
	upData := map[string]interface{}{
		"template_type": status,
		"update_user":   opUser,
		"update_time":   time.Now().Format(constant.DateTime),
	}
	return OrmDB(tx).Model(&model.Conference{}).Where(`id = ?`, confId).Updates(upData).Error
}

func SaveConferenceInfo(tx *gorm.DB, confInfo *model.Conference) (err error) {
	return OrmDB(tx).Save(confInfo).Error
}

// 查询封面按钮
func QueryConferenceCoverButtons(tx *gorm.DB, confId int64, page, pageSize int) (int64, []model.ConferenceCoverButton, error) {
	var (
		total         int64
		buttonLi      = make([]model.ConferenceCoverButton, 0)
		err           error
		limit, offset = utils.PageOffset(page, pageSize)
	)
	query := OrmDB(tx).Model(&model.ConferenceCoverButton{})
	if confId > 0 {
		query = query.Where(`conference_id = ?`, confId)
	}
	if err = query.Count(&total).Error; err != nil {
		return 0, buttonLi, err
	}
	if total <= 0 {
		return 0, buttonLi, nil
	}
	query = query.Order("id asc")
	if page > 0 && pageSize > 0 {
		query = query.Limit(limit).Offset(offset)
	}
	err = query.Find(&buttonLi).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return 0, buttonLi, err
	}
	return total, buttonLi, nil
}

// 查询往届列表
func QueryPreviousList(tx *gorm.DB, confId int64, page, pageSize int) (int64, []model.ConferencePrevious, error) {
	var (
		total         int64
		confLi        = make([]model.ConferencePrevious, 0)
		err           error
		limit, offset = utils.PageOffset(page, pageSize)
	)
	query := OrmDB(tx).Model(&model.ConferencePrevious{}).Where(`status = ?`, constant.PreviousStatusEnable)
	if confId > 0 {
		query = query.Where(`conference_id`, confId)
	}
	if err = query.Count(&total).Error; err != nil {
		return 0, confLi, err
	}
	if total <= 0 {
		return 0, confLi, nil
	}
	query = query.Order("sort asc,id asc")
	if page > 0 && pageSize > 0 {
		query = query.Limit(limit).Offset(offset)
	}
	err = query.Find(&confLi).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return 0, confLi, err
	}
	return total, confLi, nil
}

// 更新往届展会
func UpdatePreviousStatus(tx *gorm.DB, infoId int64, status int, opUser string) (err error) {
	upData := map[string]interface{}{
		"status":      status,
		"update_user": opUser,
		"update_time": time.Now().Format(constant.DateTime),
	}
	return OrmDB(tx).Model(&model.ConferencePrevious{}).Where(`id = ?`, infoId).Updates(upData).Error
}

// 删除往届展会
func DelPreviousInfo(tx *gorm.DB, infoIdLi []int64) (err error) {
	if len(infoIdLi) <= 0 {
		return
	}
	tx = OrmDB(tx)
	if len(infoIdLi) > 0 {
		tx = tx.Where("id IN ?", infoIdLi)
	}
	return tx.Delete(model.ConferencePrevious{}).Error
}

// 保存往届展会
func SavePreviousInfo(tx *gorm.DB, previousInfo *model.ConferencePrevious) (err error) {
	return OrmDB(tx).Save(previousInfo).Error
}

// 查询往届展会
func QueryPreviousInfo(tx *gorm.DB, infoId int64) (previousInfo model.ConferencePrevious, err error) {
	err = OrmDB(tx).Model(&model.ConferencePrevious{}).Where(`id = ?`, infoId).First(&previousInfo).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return previousInfo, err
	}
	return previousInfo, nil
}

func CreateCoverButton(tx *gorm.DB, button *model.ConferenceCoverButton) (err error) {
	return OrmDB(tx).Create(button).Error
}

// 删除展会封面按钮
func DelCoverButton(tx *gorm.DB, confId int64, buttonIdLi []int64) (err error) {
	if confId <= 0 && len(buttonIdLi) <= 0 {
		return
	}
	tx = OrmDB(tx)
	if confId > 0 {
		tx = tx.Where("conference_id = ?", confId)
	}
	if len(buttonIdLi) > 0 {
		tx = tx.Where("id IN ?", buttonIdLi)
	}
	return tx.Delete(model.ConferenceCoverButton{}).Error
}

func QueryConferenceShortNameMaps(tx *gorm.DB, shortName string, page, pageSize int) (int64, []model.ConferenceShortNameMap, error) {
	var (
		total         int64
		dataLi        = make([]model.ConferenceShortNameMap, 0)
		err           error
		limit, offset = utils.PageOffset(page, pageSize)
	)
	query := OrmDB(tx).Model(&model.ConferenceShortNameMap{})
	if shortName != "" {
		query = query.Where(`show_name = ?`, shortName)
	}
	if err = query.Count(&total).Error; err != nil {
		return 0, dataLi, err
	}
	if total <= 0 {
		return 0, dataLi, nil
	}
	query = query.Order("id asc")
	if page > 0 && pageSize > 0 {
		query = query.Limit(limit).Offset(offset)
	}
	err = query.Find(&dataLi).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return 0, dataLi, err
	}
	return total, dataLi, nil
}

func CreateConferenceClue(tx *gorm.DB, data *model.ConferenceClue) (err error) {
	return OrmDB(tx).Create(data).Error
}

// 保存简称映射关系
func SaveShortNameMap(tx *gorm.DB, shortMap *model.ConferenceShortNameMap) (err error) {
	tx = OrmDB(tx)
	insertSql := " INSERT INTO `conference_shortname_map`(`short_name`,`conference_id`,`create_user`,`update_user`,`create_time`,`update_time`) VALUES (?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `conference_id`=?,`update_user`=?,`update_time`=?"
	params := []interface{}{shortMap.ShortName, shortMap.ConferenceId, shortMap.CreateUser, shortMap.UpdateUser, shortMap.CreateTime, shortMap.UpdateTime, shortMap.ConferenceId, shortMap.UpdateUser, shortMap.UpdateTime}
	return tx.Exec(insertSql, params...).Error
}

func GetAllMeetingSysList(tx *gorm.DB) ([]model.Conference, error) {

	var (
		confLi = make([]model.Conference, 0)
		err    error
	)
	query := OrmDB(tx).Model(&model.Conference{})

	query = query.Where(`meeting_sys_id!=''`)
	query = query.Group("meeting_sys_id").Order("id desc")
	err = query.Find(&confLi).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return confLi, err
	}
	return confLi, nil

}
