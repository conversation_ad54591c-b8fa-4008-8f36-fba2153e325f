package db

import (
	"conferencecenter/internal/model"
	"github.com/pkg/errors"
	"gorm.io/gorm"
)

func SaveVenue(tx *gorm.DB, venue *model.Venue) error {
	return OrmDB(tx).Table("venue").Save(&venue).Error
}

func GetVenueById(tx *gorm.DB, id int64) (venue model.Venue, err error) {
	err = OrmDB(tx).Model(&model.Venue{}).Where("id=?", id).Where("deleted=0").Last(&venue).Error
	if err != nil && !errors.Is(gorm.ErrRecordNotFound, err) {
		return model.Venue{}, err
	}
	return venue, nil
}

func GetVenueByConferenceId(tx *gorm.DB, conferenceId int64) (models []model.Venue, err error) {
	err = OrmDB(tx).Model(&model.Venue{}).Where("deleted=0").Where("conference_id=?", conferenceId).Order("sorting asc").Find(&models).Error
	if err != nil {
		return nil, err
	}
	return
}

func GetVenueMapByConferenceID(tx *gorm.DB, conferenceId int64) (map[int64]model.Venue, error) {
	models := make([]model.Venue, 0)
	venueMap := make(map[int64]model.Venue)
	err := OrmDB(tx).Model(&model.Venue{}).Where("deleted=0").Where("conference_id=?", conferenceId).Find(&models).Error
	if err != nil {
		return nil, err
	}
	for _, venue := range models {
		venueMap[venue.Id] = venue
	}
	return venueMap, nil
}

func DeleteVenue(tx *gorm.DB, venueId int64) error {
	return OrmDB(tx).Model(&model.Venue{}).Where("id=?", venueId).Update("deleted", 1).Error
}
