package db

import (
	"conferencecenter/internal/constant"
	"conferencecenter/internal/model"
	"conferencecenter/internal/pkg/utils"
	"conferencecenter/internal/protocol"
	"database/sql"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.code.tencent.com/smmit/smmbase/exception"
	"github.com/pkg/errors"
	"gorm.io/gorm"
)

func QueryConferenceTicketPriceList(db *gorm.DB, conferenceId int64, page int, size int) (companyList []model.ConferenceTicketPrice, total int64, err error) {
	if conferenceId <= 0 {
		return nil, 0, errors.New("展会id必须大于0")
	}
	limit, offSet := utils.PageOffset(page, size)
	query := OrmDB(db).Table("conference_ticket_price").Where("deleted=0 and cn_sorting != ''").Where("conference_id=?", conferenceId).Order("cn_sorting asc").Order("id desc")
	query.Count(&total)
	err = query.Limit(limit).Offset(offSet).Find(&companyList).Error
	return companyList, total, err
}

func QueryConferenceEnTicketPriceList(db *gorm.DB, conferenceId int64, page int, size int) (companyList []model.ConferenceTicketPrice, total int64, err error) {
	if conferenceId <= 0 {
		return nil, 0, errors.New("展会id必须大于0")
	}
	limit, offSet := utils.PageOffset(page, size)
	query := OrmDB(db).Table("conference_ticket_price").Where("deleted=0 and en_sorting != ''").Where("conference_id=?", conferenceId).Order("en_sorting asc").Order("id desc")
	query.Count(&total)
	err = query.Limit(limit).Offset(offSet).Find(&companyList).Error
	return companyList, total, err
}

func QueryConferenceTicketPriceInfo(tx *gorm.DB, confId int64) (confInfo *model.ConferenceTicketPrice, err error) {
	err = OrmDB(tx).Model(&model.ConferenceTicketPrice{}).Where("deleted=0").Where(`id = ?`, confId).First(&confInfo).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return confInfo, err
	}
	return confInfo, nil
}

func SaveConferenceTicketPriceInfo(tx *gorm.DB, confInfo *model.ConferenceTicketPrice) (err error) {
	return OrmDB(tx).Save(confInfo).Error
}

func UpdateConferenceTicketPrice(tx *gorm.DB, confId int64, deleted int, opUser string) (err error) {
	upData := map[string]interface{}{
		"deleted":      deleted,
		"update_admin": opUser,
		"update_time":  time.Now().Format(constant.DateTime),
	}
	return OrmDB(tx).Model(&model.ConferenceTicketPrice{}).Where(`id = ?`, confId).Updates(upData).Error
}

func QueryConferenceRightsInterestsList(db *gorm.DB, conferenceId int64, page int, size int) (companyList []model.ConferenceRightsInterests, total int64, err error) {
	if conferenceId <= 0 {
		return nil, 0, errors.New("展会id必须大于0")
	}
	limit, offSet := utils.PageOffset(page, size)
	query := OrmDB(db).Table("conference_rights_interests").Where("deleted=0 and cn_content!='' ").Where("conference_id=?", conferenceId).Order("cn_sorting asc").Order("id desc")
	query.Count(&total)
	err = query.Limit(limit).Offset(offSet).Find(&companyList).Error
	return companyList, total, err
}

func QueryConferenceEnRightsInterestsList(db *gorm.DB, conferenceId int64, page int, size int) (companyList []model.ConferenceRightsInterests, total int64, err error) {
	if conferenceId <= 0 {
		return nil, 0, errors.New("展会id必须大于0")
	}
	limit, offSet := utils.PageOffset(page, size)
	query := OrmDB(db).Table("conference_rights_interests").Where("deleted=0 and en_content!='' ").Where("conference_id=?", conferenceId).Order("en_sorting asc").Order("id desc")
	query.Count(&total)
	err = query.Limit(limit).Offset(offSet).Find(&companyList).Error
	return companyList, total, err
}

func QueryConferenceRightsInterestsInfo(tx *gorm.DB, confId int64) (confInfo model.ConferenceRightsInterests, err error) {
	err = OrmDB(tx).Model(&model.ConferenceRightsInterests{}).Where("deleted=0").Where(`id = ?`, confId).First(&confInfo).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return confInfo, err
	}
	return confInfo, nil
}

func SaveConferenceRightsInterests(tx *gorm.DB, confInfo *model.ConferenceRightsInterests) (err error) {
	return OrmDB(tx).Save(confInfo).Error
}

func UpdateConferenceRightsInterests(tx *gorm.DB, confId int64, deleted int, opUser string) (err error) {
	upData := map[string]interface{}{
		"deleted":      deleted,
		"update_admin": opUser,
		"update_time":  time.Now().Format(constant.DateTime),
	}
	return OrmDB(tx).Model(&model.ConferenceRightsInterests{}).Where(`id = ?`, confId).Updates(upData).Error
}

func GetConferenceTicketConfigInfo(tx *gorm.DB, confId int64) (confInfo model.ConferenceTicketConfig, err error) {
	err = OrmDB(tx).Model(&model.ConferenceTicketConfig{}).Where("deleted=0").Where(`conference_id = ?`, confId).Order("id").Limit(1).First(&confInfo).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return confInfo, err
	}
	return confInfo, nil
}

func SaveConferenceTicketConfig(tx *gorm.DB, confInfo *model.ConferenceTicketConfig) (err error) {
	return OrmDB(tx).Save(confInfo).Error
}

//
//func QueryConferenceRegisterFromIdList(db *gorm.DB, req protocol.ReqGetConferenceFromIdList) (companyList []protocol.ResConferenceRegisterFromList, err error) {
//
//	query := OrmDB(db).Table("conference_register_from").Where("deleted=0")
//	if req.ConferenceId > 0 {
//		query.Where("conference_id=?", req.ConferenceId)
//	}
//	if req.FromType > 0 {
//		query.Where("from_type=?", req.FromType)
//	}
//	query.Group("from_name")
//	query.Order("id desc")
//	err = query.Find(&companyList).Error
//	return companyList, err
//}

func QueryConferenceRegisterList(db *gorm.DB, req protocol.ReqGetConferenceRegisterList) (companyList []model.ConferenceRegister, total int64, err error) {

	limit, offSet := utils.PageOffset(req.Page, req.PageSize)
	query := OrmDB(db).Table("conference_register").Where("deleted=0")
	if req.ConferenceId > 0 {
		query.Where("conference_id=?", req.ConferenceId)
	}
	//if utils.NotEmpty(req.CnConferenceName) {
	//	query.Where("conference_name=?", req.CnConferenceName)
	//}
	if utils.NotEmpty(req.FirstName) {
		query.Where("first_name=?", req.FirstName)
	}
	if utils.NotEmpty(req.LastName) {
		query.Where("last_name=?", req.LastName)
	}
	if utils.NotEmpty(req.Email) {
		query.Where("email=?", req.Email)
	}
	if utils.NotEmpty(req.SourceId) {
		query.Where("source_id=?", req.SourceId)
	}
	if utils.NotEmpty(req.FromId) {
		query.Where("fromId=?", req.FromId)
	}
	if utils.NotEmpty(req.FromName) {
		query.Where("from_name=?", req.FromName)
	}
	if utils.NotEmpty(req.ConferenceName) {
		query.Where("conference_name=?", req.ConferenceName)
	}
	if utils.NotEmpty(req.EnterpriseType) {
		query.Where("enterprise_type=?", req.EnterpriseType)
	}

	if utils.NotEmpty(req.StartTime) {
		query.Where("create_time>=? and create_time<=? ", req.StartTime+" 00:00:00", req.EndTime+" 23:59:59")
	}
	if len(req.Ids) > 0 {
		if len(req.Ids) == 1 {
			query.Where("id=?", req.Ids[0])
		} else {
			query.Where("id in (?)", req.Ids)
		}
	}

	query.Order("id desc")
	query.Count(&total)
	err = query.Limit(limit).Offset(offSet).Find(&companyList).Error
	return companyList, total, err
}

func GetMeetingRegisterUserCountListIn(tx *gorm.DB, registerId []string) (companyList []protocol.RespMeetingRegisterUser, total int64, err error) {
	if len(registerId) <= 0 {
		return nil, 0, nil
	}
	tx = OrmDB(tx)

	querySql := fmt.Sprintf("SELECT register_id,count(*) count FROM conference_register_user WHERE  deleted=0 AND register_id ")
	if len(registerId) == 1 {
		querySql += " = " + registerId[0]
	}
	if len(registerId) > 1 {
		join := strings.Join(registerId, ",")
		querySql += " IN (" + join + ")"
	}
	querySql += " Group by register_id ORDER BY id"
	{
		err = tx.Raw(querySql).Scan(&companyList).Error
		if err != nil && err != sql.ErrNoRows {
			return companyList, 0, err
		}
	}
	return companyList, total, err
}

func GetMeetingRegisterUserListIn(tx *gorm.DB, registerId []string) (companyList []protocol.RespMeetingRegisterUserList, total int64, err error) {
	if len(registerId) <= 0 {
		return nil, 0, nil
	}
	tx = OrmDB(tx)

	if err != nil {
		return nil, 0, exception.NewSysErrorException(err.Error())
	}

	querySql := fmt.Sprintf("SELECT * FROM conference_register_user WHERE  deleted=0 AND register_id ")
	if len(registerId) == 1 {
		querySql += " = " + registerId[0]
	}
	if len(registerId) > 1 {
		join := strings.Join(registerId, ",")
		querySql += " IN (" + join + ")"
	}
	querySql += " ORDER BY register_id desc"
	{
		err = tx.Raw(querySql).Scan(&companyList).Error
		if err != nil && err != sql.ErrNoRows {
			return companyList, 0, err
		}
	}
	return companyList, total, err
}

func QueryConferenceRegisterUserList(db *gorm.DB, req protocol.ReqGetConferenceRegisterUserList) (companyList []model.ConferenceRegisterUser, total int64, err error) {
	if req.RegisterId <= 0 {
		return nil, 0, errors.New("报名信息id必须大于0")
	}
	query := OrmDB(db).Table("conference_register_user").Where("deleted=0").Where("register_id=?", req.RegisterId)
	query.Order("id desc")
	query.Count(&total)
	err = query.Find(&companyList).Error
	return companyList, total, err
}

func SaveConferenceRegister(tx *gorm.DB, confInfo *model.ConferenceRegister) (err error) {
	return OrmDB(tx).Save(confInfo).Error
}

func UpdateMeetingRegister(tx *gorm.DB, confInfo *model.ConferenceRegister) (err error) {
	return OrmDB(tx).Save(confInfo).Error
}

func GetConferenceRegisterInfo(tx *gorm.DB, confId int64) (confInfo model.ConferenceRegister, err error) {
	err = OrmDB(tx).Model(&model.ConferenceRegister{}).Where("deleted=0").Where(`id = ?`, confId).Limit(1).First(&confInfo).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return confInfo, err
	}
	return confInfo, nil
}

func AddConferenceRegisterUserList(tx *gorm.DB, registerId int64, userList []protocol.ReqConferenceRegisterUser) error {
	if registerId <= 0 {
		return nil
	}
	tx = OrmDB(tx)

	sql := fmt.Sprintf("INSERT INTO %v(register_id,first_name,last_name,email,mobile,company,job_title,create_time,deleted) VALUES", "conference_register_user")
	for i, user := range userList {
		if i != 0 {
			sql += ","
		}

		sql += "(" + strconv.FormatInt(registerId, 10) + "," + "'" + user.FirstName + "'" + "," + "'" + user.LastName + "'" + "," + "'" + user.Email + "'" + "," + "'" + user.Mobile + "'" + "," + "'" + user.Company + "'" + "," + "'" + user.JobTitle + "'" + "," + "'" + time.Now().Format(constant.DateTime) + "'" + "," + strconv.FormatInt(constant.ConferenceStatus, 10) + ")"
	}
	{
		err := tx.Exec(sql).Error
		if err != nil {
			return exception.NewSysErrorException(err.Error())
		}
	}
	return nil
}

func UpdateConferenceRegisterUser(tx *gorm.DB, confId int64, deleted int, opUser string) (err error) {
	upData := map[string]interface{}{
		"deleted":     deleted,
		"create_time": time.Now().Format(constant.DateTime),
	}
	return OrmDB(tx).Model(&model.ConferenceRegisterUser{}).Where(`register_id = ?`, confId).Updates(upData).Error
}

func QueryConferenceRightsTicketList(db *gorm.DB, conferenceId, ticketId int64) (companyList []model.ConferenceRightsTicket, total int64, err error) {
	if conferenceId <= 0 {
		return nil, 0, errors.New("展会id必须大于0")
	}
	query := OrmDB(db).Table("conference_rights_ticket").Where("deleted=0").Where("conference_id=?", conferenceId)
	if ticketId > 0 {
		query.Where("ticket_id=?", ticketId)
	}
	query.Order("id desc")
	query.Count(&total)
	err = query.Find(&companyList).Error
	return companyList, total, err
}

func AddMeetingTicketRightsList(tx *gorm.DB, conferenceId, isCn, ticketId int64, rightsList ...string) error {
	if len(rightsList) <= 0 {
		return nil
	}
	tx = OrmDB(tx)
	begin, err := tx.Begin().DB()

	delSql := fmt.Sprintf("UPDATE %v SET deleted =%v WHERE ticket_id=%v", "conference_rights_ticket", time.Now().Second(), ticketId)
	_, err = begin.Exec(delSql)
	if err != nil {
		return exception.NewSysErrorException(err.Error())
	}

	sql := fmt.Sprintf("INSERT INTO %v(conference_id,is_cn,ticket_id,rights_id,deleted) VALUES", "conference_rights_ticket")
	for i, rightsId := range rightsList {
		if i != 0 {
			sql += ","
		}
		sql += "(" + strconv.FormatInt(conferenceId, 10) + "," + strconv.FormatInt(isCn, 10) + "," + strconv.FormatInt(ticketId, 10) + "," + rightsId + ",0)"
	}
	_, err = begin.Exec(sql)
	if err != nil {
		return exception.NewSysErrorException(err.Error())
	}
	return nil
}

func UserConferenceTicketPriceList(db *gorm.DB, conferenceId, sortingType int64) (companyList []model.ConferenceTicketPrice, err error) {
	if conferenceId <= 0 {
		return nil, errors.New("展会id必须大于0")
	}
	query := OrmDB(db).Table("conference_ticket_price").Where("deleted=0").Where("conference_id=?", conferenceId)
	if sortingType == 1 {
		query.Where("cn_sorting != ''")
		query.Order("cn_sorting asc")
	} else if sortingType == 2 {
		query.Where("en_sorting != ''")
		query.Order("en_sorting asc")
	}
	query.Order("id desc")
	err = query.Find(&companyList).Error
	return companyList, err
}

func UserConferenceRightsInterestsList(db *gorm.DB, conferenceId, sortingType int64) (companyList []model.ConferenceRightsInterests, err error) {
	if conferenceId <= 0 {
		return nil, errors.New("展会id必须大于0")
	}
	query := OrmDB(db).Table("conference_rights_interests").Where("deleted=0").Where("conference_id=?", conferenceId)

	if sortingType == 1 {
		query.Where("cn_sorting != ''")
		query.Order("cn_sorting asc")
	} else if sortingType == 2 {
		query.Where("en_sorting != ''")
		query.Order("en_sorting asc")
	}
	query.Order("id desc")
	err = query.Find(&companyList).Error
	return companyList, err
}
func UserConferenceRightsTicketList(db *gorm.DB, conferenceId, isCn int64) (companyList []model.ConferenceRightsTicket, err error) {
	if conferenceId <= 0 {
		return nil, errors.New("展会id必须大于0")
	}
	query := OrmDB(db).Table("conference_rights_ticket").Where("deleted=0").Where("conference_id=?", conferenceId).Where("is_cn=?", isCn).Order("id desc")
	err = query.Find(&companyList).Error
	return companyList, err
}

func AddMeetingRegisterLog(tx *gorm.DB, confInfo *model.RegisterLog) (err error) {
	return OrmDB(tx).Save(confInfo).Error
}
func GetMeetingRegisterByOrderId(db *gorm.DB, recordId string) (companyList []model.ConferenceRegister, total int64, err error) {
	if utils.IsEmpty(recordId) {
		return nil, 0, errors.New("展会id必须大于0")
	}
	query := OrmDB(db).Table("conference_register").Where("deleted=0").Where("order_id=?", recordId)
	query.Order("id desc")
	query.Count(&total)
	err = query.Find(&companyList).Error
	return companyList, total, err
}

func GetMeetingRegisterUserList(db *gorm.DB, recordId int64) (companyList []model.ConferenceRegisterUser, total int64, err error) {
	if recordId <= 0 {
		return nil, 0, errors.New("展会id必须大于0")
	}
	query := OrmDB(db).Table("conference_register_user").Where("deleted=0").Where("register_id=?", recordId)
	query.Order("id desc")
	query.Count(&total)
	err = query.Find(&companyList).Error
	return companyList, total, err
}

func GetTicketPriceServiceIdList(db *gorm.DB) (companyList []model.ConferenceTicketPrice, total int64, err error) {
	query := OrmDB(db).Table("conference_ticket_price").Where("deleted=0 and service_id != '' ")
	query.Order("id desc")
	query.Count(&total)
	err = query.Find(&companyList).Error
	return companyList, total, err
}
func GetMeetingRegisterById(db *gorm.DB, recordId int64) (companyList []model.ConferenceRegister, total int64, err error) {
	if recordId <= 0 {
		return nil, 0, errors.New("展会id必须大于0")
	}
	query := OrmDB(db).Table("conference_register").Where("deleted=0").Where("id=?", recordId)
	query.Order("id desc")
	query.Count(&total)
	err = query.Find(&companyList).Error
	return companyList, total, err
}
func GetTicketPriceServiceIds(db *gorm.DB, serviceId string, appName string) (companyList []model.ConferenceTicketPrice, total int64, err error) {
	if utils.IsEmpty(serviceId) {
		return nil, 0, errors.New("服务ID为空")
	}
	query := OrmDB(db).Table("conference_ticket_price").Where("deleted=0")
	if appName == constant.AppConferenceEn {
		query.Where("en_service_id=?", serviceId)
	} else {
		query.Where("cn_service_id=?", serviceId)
	}
	query.Order("id desc")
	query.Count(&total)
	err = query.Find(&companyList).Error
	return companyList, total, err
}

//func QueryChannelSourceList(db *gorm.DB, id string) (companyList []model.ChannelSource, err error) {
//	query := OrmDB(db).Table("channel_source")
//	if utils.NotEmpty(id) {
//		query.Where("id=?", id)
//	}
//	query.Order("id asc")
//	err = query.Find(&companyList).Error
//	return companyList, err
//}

func UserForumConfigList(db *gorm.DB, conferenceId int64, typeId string) (companyList []model.ForumConfigList, total int64, err error) {

	query := OrmDB(db).Table("forum_config_list")
	if conferenceId > 0 {
		query.Where("conference_id=?", conferenceId)
	}
	if utils.NotEmpty(typeId) {
		query.Where("type=?", typeId)
	} else {
		query.Where("type=?", 0)
	}
	query.Order("id asc")
	err = query.Find(&companyList).Error
	return companyList, total, err
}

func UserForumConfigListById(db *gorm.DB, id string) (companyList []model.ForumConfigList, total int64, err error) {
	if len(id) <= 0 {
		return nil, 0, errors.New("展会id必须大于0")
	}
	query := OrmDB(db).Table("forum_config_list").Where("id in (?)", id)
	query.Order("id asc")
	err = query.Find(&companyList).Error
	return companyList, total, err
}

func SaveBdVidLog(tx *gorm.DB, confInfo *model.BdVidLog) (err error) {
	return OrmDB(tx).Save(confInfo).Error
}

//func GetConferenceFromConfigListById(db *gorm.DB) (companyList []model.ConferenceFromConfig, err error) {
//
//	query := OrmDB(db).Table("conference_from_config")
//	query.Order("id asc")
//	err = query.Find(&companyList).Error
//	return companyList, err
//}
