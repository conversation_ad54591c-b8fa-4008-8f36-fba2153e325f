package db

import (
	"conferencecenter/internal/constant"
	"conferencecenter/internal/model"
	"conferencecenter/internal/pkg/utils"
	"conferencecenter/internal/protocol"
	"github.com/pkg/errors"
	"gorm.io/gorm"
	"time"
)

func QueryConferenceColumnList(db *gorm.DB, req protocol.ReqColumnList) (companyList []model.ConferenceColumn, total int64, err error) {
	if req.ConferenceId <= 0 {
		return nil, 0, errors.New("展会id必须大于0")
	}
	limit, offSet := utils.PageOffset(req.Page, req.PageSize)
	query := OrmDB(db).Table("conference_column").Where("deleted=0").Where("conference_id=?", req.ConferenceId)
	if req.SubSectionId > 0 {
		query.Where("sub_section_id =?", req.SubSectionId)
	} else {
		query.Where("sub_section_id =0")
	}
	if utils.NotEmpty(req.Name) {
		likeCon := "%" + req.Name + "%"
		query = query.Where(` (cn_name LIKE ? OR en_name LIKE ?) `, likeCon, likeCon)
	}
	query.Order("sorting asc").Order("id desc")
	query.Count(&total)
	err = query.Limit(limit).Offset(offSet).Find(&companyList).Error
	return companyList, total, err
}

func UserConferenceColumnList(db *gorm.DB, conferenceId, subSectionId int64, Type int) (companyList []model.ConferenceColumn, err error) {
	if conferenceId <= 0 {
		return nil, errors.New("展会id必须大于0")
	}
	query := OrmDB(db).Table("conference_column").Where("deleted=0")
	if conferenceId > 0 {
		query.Where("conference_id=?", conferenceId)
	}
	if Type > 0 {
		query.Where("type=?", Type)
	}
	if subSectionId > 0 {
		query.Where("sub_section_id=?", subSectionId)
	} else if subSectionId == -1 {
		query.Where("sub_section_id!=0")
	} else {
		query.Where("sub_section_id=0")
	}
	query.Order("sorting asc").Order("id desc")
	err = query.Find(&companyList).Error
	return companyList, err
}

func QueryConferenceColumnInfo(tx *gorm.DB, confId int64) (confInfo model.ConferenceColumn, err error) {
	err = OrmDB(tx).Model(&model.ConferenceColumn{}).Where("deleted=0").Where(`id = ?`, confId).First(&confInfo).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return confInfo, err
	}
	return confInfo, nil
}

func SaveConferenceColumnInfo(tx *gorm.DB, confInfo *model.ConferenceColumn) (err error) {
	return OrmDB(tx).Save(confInfo).Error
}

func UpdateConferenceColumn(tx *gorm.DB, confId int64, deleted int, opUser string) (err error) {
	upData := map[string]interface{}{
		"deleted":      deleted,
		"update_admin": opUser,
		"update_time":  time.Now().Format(constant.DateTime),
	}
	return OrmDB(tx).Model(&model.ConferenceColumn{}).Where(`id = ?`, confId).Updates(upData).Error
}

func QueryConferenceInformationList(db *gorm.DB, req protocol.ReqColumnInformationList) (companyList []model.ConferenceInformation, total int64, err error) {
	if req.ConferenceId <= 0 {
		return nil, 0, errors.New("展会id必须大于0")
	}
	limit, offSet := utils.PageOffset(req.Page, req.PageSize)
	query := OrmDB(db).Table("conference_information").Where("deleted=0").Where("conference_id=?", req.ConferenceId)
	if req.Type > 0 {
		if req.Type == 1 && req.IsAdmin {
			query.Where("type!=?", 2)
		} else {
			query.Where("type=?", req.Type)
		}
	}
	if req.ColumnId > 0 {
		query.Where("column_id =?", req.ColumnId)
	}
	query.Order("cn_sorting asc").Order("id desc")
	query.Count(&total)
	err = query.Limit(limit).Offset(offSet).Find(&companyList).Error
	return companyList, total, err
}

func QueryConferenceInformationInfo(tx *gorm.DB, confId int64) (confInfo model.ConferenceInformation, err error) {
	err = OrmDB(tx).Model(&model.ConferenceInformation{}).Where("deleted=0").Where(`id = ?`, confId).First(&confInfo).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return confInfo, err
	}
	return confInfo, nil
}

func SaveConferenceInformation(tx *gorm.DB, confInfo *model.ConferenceInformation) (err error) {
	return OrmDB(tx).Save(confInfo).Error
}

//func QueryConferenceInformationEventList(db *gorm.DB, informationId int64) (companyList []model.ConferenceInformationEvent, err error) {
//	if informationId <= 0 {
//		return nil, errors.New("展会id必须大于0")
//	}
//	query := OrmDB(db).Table("conference_information_event").Where("deleted=0").Where("information_id=?", informationId)
//	query.Order("id desc")
//	err = query.Find(&companyList).Error
//	return companyList, err
//}
//
//func AddConferenceInformationEventList(tx *gorm.DB, conferenceId, informationId int64, eventsList ...string) error {
//	if len(eventsList) <= 0 {
//		return nil
//	}
//	params := make([]interface{}, 0)
//	tx = OrmDB(tx)
//	begin, err := tx.Begin().DB()
//	if err != nil {
//		return exception.NewSysErrorException(err.Error())
//	}
//	delSql := fmt.Sprintf("UPDATE %v SET deleted =%v WHERE information_id=%v", "conference_information_event", time.Now().Second(), informationId)
//	_, err = begin.Exec(delSql)
//	if err != nil {
//		return exception.NewSysErrorException(err.Error())
//	}
//	sql := fmt.Sprintf("INSERT INTO %v(conference_id,information_id,event_id,deleted) VALUES", "conference_information_event")
//	for i, eventId := range eventsList {
//		if i != 0 {
//			sql += ","
//		}
//
//		sql += "(" + strconv.FormatInt(conferenceId, 10) + "," + strconv.FormatInt(informationId, 10) + "," + eventId + "," + strconv.FormatInt(constant.ConferenceStatus, 10) + ")"
//		params = append(params, conferenceId, informationId, eventId, constant.ConferenceStatus)
//	}
//	{
//		_, err = begin.Exec(sql)
//		if err != nil {
//			return exception.NewSysErrorException(err.Error())
//		}
//	}
//	return nil
//}

func UpdateConferenceInformation(tx *gorm.DB, confId int64, deleted int, opUser string) (err error) {
	upData := map[string]interface{}{
		"deleted":      deleted,
		"update_admin": opUser,
		"update_time":  time.Now().Format(constant.DateTime),
	}
	return OrmDB(tx).Model(&model.ConferenceInformation{}).Where(`id = ?`, confId).Updates(upData).Error
}

func UserConferenceInformationEventList(db *gorm.DB, conferenceId int64, Type int, IsEn bool) (companyList []model.ConferenceInformation, err error) {
	if conferenceId <= 0 {
		return nil, nil
	}
	query := OrmDB(db).Table("conference_information").Where("deleted=0")

	if conferenceId > 0 {
		query.Where("conference_id=?", conferenceId)
	}
	if Type > 0 {
		query.Where("type=?", Type)
	} else {
		query.Where("type!=2")
	}
	//if len(informationIds) > 0 {
	//	if len(informationIds) == 1 {
	//		query.Where("id =?", informationIds)
	//	} else {
	//		query.Where("id in (?)", informationIds)
	//	}
	//}
	if IsEn {
		query.Where("en_picture!=''").Order("en_sorting asc,id desc")
	} else {
		query.Where("cn_picture!=''").Order("cn_sorting asc,id desc")
	}
	err = query.Find(&companyList).Error
	return companyList, err
}

func UserConferenceInformationList(db *gorm.DB, conferenceId int64, Type int, IsEn bool) (companyList []model.ConferenceInformation, err error) {
	if conferenceId <= 0 {
		return nil, nil
	}
	query := OrmDB(db).Table("conference_information").Where("deleted=0")

	if conferenceId > 0 {
		query.Where("conference_id=?", conferenceId)
	}
	if Type > 0 {
		query.Where("type=?", Type)
	} else {
		query.Where("type!=2")
	}
	if IsEn {
		query.Order("en_sorting asc,id desc")
	} else {
		query.Order("cn_sorting asc,id desc")
	}
	err = query.Find(&companyList).Error
	return companyList, err
}

//func UserConferenceInformationEventList(db *gorm.DB, conferenceId, eventId int64) (companyList []model.ConferenceInformationEvent, err error) {
//	if conferenceId <= 0 && eventId <= 0 {
//		return nil, errors.New("展会id必须大于0")
//	}
//	query := OrmDB(db).Table("conference_information_event").Where("deleted=0")
//	if conferenceId > 0 {
//		query.Where("event_id=0 and conference_id=?", conferenceId)
//	}
//	if eventId > 0 {
//		query.Where("event_id=?", eventId)
//	}
//
//	query.Group("information_id")
//	query.Order("id desc")
//	err = query.Find(&companyList).Error
//	return companyList, err
//}

// 搜索嘉宾
func QueryConferenceGuests(tx *gorm.DB, conferenceId int64, keyword, language string, page int, pageSize int) (int64, []model.ConferenceGuestData, error) {
	var (
		total         int64
		dataLi        = make([]model.ConferenceGuestData, 0)
		err           error
		queryCols     string
		limit, offset = utils.PageOffset(page, pageSize)
	)
	if language == "cn" {
		queryCols = "id,conference_id,column_id,cn_appellation as appellation,cn_position as position,cn_company as company,cn_link as link,picture,cn_content as content,type,sorting,deleted,create_time,update_time,create_admin,update_admin"
	} else {
		queryCols = "id,conference_id,column_id,en_appellation as appellation,en_position as position,en_company as company,en_link as link,picture,en_content as content,type,sorting,deleted,create_time,update_time,create_admin,update_admin"
	}
	query := OrmDB(tx).Table(`conference_information`).Select(queryCols).Where(`deleted=0`)
	if conferenceId > 0 {
		query = query.Where(`conference_id=?`, conferenceId)
	}
	if utils.NotEmpty(keyword) {
		likeCon := "%" + keyword + "%"
		if language == "cn" {
			query = query.Where(`cn_appellation LIKE ?`, likeCon)
		} else {
			query = query.Where(`en_appellation LIKE ?`, likeCon)
		}
	}
	if err = query.Count(&total).Error; err != nil {
		return 0, dataLi, err
	}
	if total <= 0 {
		return 0, dataLi, nil
	}
	query = query.Order("sorting asc,id asc")
	if page > 0 && pageSize > 0 {
		query = query.Limit(limit).Offset(offset)
	}
	err = query.Find(&dataLi).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return 0, dataLi, err
	}
	return total, dataLi, err
}
