package db

import (
	"conferencecenter/internal/model"
	"gorm.io/gorm"
)

func EditConferenceSponsor(db *gorm.DB, sponsor model.ConferenceSponsor) error {
	err := OrmDB(db).Where("conference_id=?", sponsor.ConferenceId).Updates(&sponsor).Error
	return err

}

func SaveConferenceSponsor(db *gorm.DB, sponsor *model.ConferenceSponsor) error {
	err := OrmDB(db).Create(sponsor).Error
	return err

}

func GetConferenceSponsorById(db *gorm.DB, id string) (sponsor model.ConferenceSponsor, err error) {
	err = OrmDB(db).Where("conference_id=?", id).Last(&sponsor).Error
	if err != nil && gorm.ErrRecordNotFound == err {
		return model.ConferenceSponsor{}, err
	}
	return sponsor, err
}
