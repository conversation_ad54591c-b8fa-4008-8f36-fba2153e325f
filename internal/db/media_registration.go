package db

import (
	"conferencecenter/internal/constant"
	"conferencecenter/internal/model"
	"conferencecenter/internal/pkg/utils"
	"conferencecenter/internal/protocol"
	"time"

	"gorm.io/gorm"
)

func QueryMediaRegistrationConfigInfo(tx *gorm.DB, confId int64) (confInfo *model.MediaRegistrationConfig, err error) {
	err = OrmDB(tx).Model(&model.MediaRegistrationConfig{}).Where("deleted=0").Where(`conference_id = ?`, confId).First(&confInfo).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return confInfo, err
	}
	return confInfo, nil
}

func QueryMediaRegistrationInfo(tx *gorm.DB, confId int64) (confInfo *model.MediaRegistration, err error) {
	err = OrmDB(tx).Model(&model.MediaRegistration{}).Where("deleted=0").Where(`id = ?`, confId).First(&confInfo).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return confInfo, err
	}
	return confInfo, nil
}

func SaveMediaRegistrationConfig(tx *gorm.DB, confInfo *model.MediaRegistrationConfig) (err error) {
	return OrmDB(tx).Save(confInfo).Error
}

func SaveMediaRegistration(tx *gorm.DB, confInfo *model.MediaRegistration) (err error) {
	return OrmDB(tx).Save(confInfo).Error
}

func UpdateMediaRegistration(tx *gorm.DB, confId int64, deleted int, opUser string) (err error) {
	upData := map[string]interface{}{
		"deleted":      deleted,
		"update_admin": opUser,
		"update_time":  time.Now().Format(constant.DateTime),
	}
	return OrmDB(tx).Model(&model.MediaRegistrationConfig{}).Where(`id = ?`, confId).Updates(upData).Error
}

func QueryMediaRegistrationList(db *gorm.DB, req protocol.ReqGetMediaRegistrationList) (companyList []model.MediaRegistration, total int64, err error) {
	limit, offSet := utils.PageOffset(req.Page, req.PageSize)
	query := OrmDB(db).Table("media_registration").Where("deleted=0")

	if req.ConferenceId > 0 {
		query.Where("conference_id=?", req.ConferenceId)
	}
	if utils.NotEmpty(req.ConferenceName) {
		query.Where("conference_name=?", req.ConferenceName)
	}
	if utils.NotEmpty(req.Language) {
		query.Where("language=?", req.Language)
	}
	if utils.NotEmpty(req.MediaName) {
		query.Where("organisation like ?", "%"+req.MediaName+"%")
	}
	if utils.NotEmpty(req.Name) {
		query.Where("name like ?", "%"+req.Name+"%")
	}
	if utils.NotEmpty(req.Email) {
		query.Where("email=?", req.Email)
	}
	if utils.NotEmpty(req.Phone) {
		query.Where("phone like ?", "%"+req.Phone+"%")
	}
	if utils.NotEmpty(req.Country) {
		query.Where("country=?", req.Country)
	}
	if utils.NotEmpty(req.FromId) {
		query.Where("from_id=?", req.FromId)
	}
	if utils.NotEmpty(req.FromName) {
		query.Where("from_name=?", req.FromName)
	}
	if utils.NotEmpty(req.SourceID) {
		query.Where("source_id=?", req.SourceID)
	}
	if utils.NotEmpty(req.StartTime) && utils.NotEmpty(req.EndTime) {
		query.Where("create_time>=?", req.StartTime+" 00:00:00")
		query.Where("create_time<=?", req.EndTime+" 23:59:59")
	}

	query.Order("id desc")
	query.Count(&total)
	err = query.Limit(limit).Offset(offSet).Find(&companyList).Error
	return companyList, total, err
}

func QueryMediaRegistrationByName(tx *gorm.DB, id, conferenceId int64, Type, cnName, enName string) (confInfo model.ConferenceOrganization, err error) {
	query := OrmDB(tx).Model(&model.ConferenceOrganization{}).Where("deleted=0")

	if id > 0 {
		query.Where(`id != ?`, id)
	}
	if conferenceId > 0 {
		query.Where(`conference_id = ?`, conferenceId)
	}
	if utils.NotEmpty(Type) {
		query.Where(`type = ?`, Type)
	}
	if utils.NotEmpty(cnName) {
		query.Where(`cn_name = ?`, cnName)
	}
	if utils.NotEmpty(enName) {
		query.Where(`en_name = ?`, enName)
	}
	err = query.First(&confInfo).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return confInfo, err
	}
	return confInfo, nil
}
