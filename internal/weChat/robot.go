package weChat

import (
	"bytes"
	"conferencecenter/internal/pkg/utils"
	"encoding/json"
	"fmt"
	"git.code.tencent.com/smmit/smmbase/logger"
	"git.code.tencent.com/smmit/smmbase/webhook/feishu"
	"github.com/levigross/grequests"
	"os"
	"time"
)

const (
	WxUrl = "https://open.feishu.cn/open-apis/bot/v2/hook/698f54b6-f280-4bad-b14a-07488a31565a"

	WxUrl2 = "https://open.feishu.cn/open-apis/bot/v2/hook/e4c9d298-f1e6-4ba1-8123-0c0fe03e9671"

	LandingPageWxUrl  = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2176d4e7-a887-4064-9be7-2ef1b20c3030"
	LandingPageWxUrl2 = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d1c62400-8981-435b-abc8-25ea2a3b5820"
)

type wxWorkRobotMsg struct {
	MsgType  string                 `json:"msgtype"`
	Text     wxWorkRobotMsgText     `json:"text"`
	Markdown wxWorkRobotMsgMarkdown `json:"markdown"`
}

type wxWorkRobotMsgText struct {
	Content             string   `json:"content"`
	MentionedList       []string `json:"mentioned_list"`
	MentionedMobileList []string `json:"mentioned_mobile_list"`
}

type wxWorkRobotMsgMarkdown struct {
	Content             string   `json:"content"`
	MentionedList       []string `json:"mentioned_list"`
	MentionedMobileList []string `json:"mentioned_mobile_list"`
}

func Send(msg, url string) {
	if utils.IsEmpty(url) {
		if !utils.IsTestEnv() {
			url = WxUrl2
		} else {
			url = WxUrl
		}
	}
	params := wxWorkRobotMsg{
		MsgType:  "markdown",
		Markdown: wxWorkRobotMsgMarkdown{"", nil, nil},
	}
	params.Markdown.Content += fmt.Sprintf("环境：%v\n%v", os.Getenv("SMM_SERVER_ENV"), msg)

	bs, err := json.Marshal(params)
	if err != nil {
		logger.Error(err.Error())
		return
	}

	op := grequests.RequestOptions{
		DialTimeout:         10 * time.Second,
		TLSHandshakeTimeout: 10 * time.Second,
		RequestTimeout:      10 * time.Second,
		InsecureSkipVerify:  true,
	}
	op.Headers = map[string]string{"Content-Type": "application/json;charset=UTF-8"}
	op.RequestBody = bytes.NewReader(bs)

	var resp *grequests.Response
	resp, err = grequests.Post(url, &op)
	if err != nil {
		logger.Error(err.Error())
		return
	}
	if !resp.Ok {
		logger.Error(err.Error())
		return
	}
}

func Send2(cardMsg *feishu.CardMsg, url string) {

	if utils.IsEmpty(url) {
		if utils.IsTestEnv() {
			url = WxUrl
		} else {
			url = WxUrl2
		}
	}

	//使用URL发送
	err := feishu.NewFeishuRobotURL(url).Send(cardMsg, false)
	if err != nil {
		logger.Error(err.Error())
		return
	}

}
