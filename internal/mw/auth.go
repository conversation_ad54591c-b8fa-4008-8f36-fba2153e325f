package mw

import (
	"conferencecenter/internal/constant"
	"conferencecenter/internal/corecontext"
	"conferencecenter/internal/errcode"
	"conferencecenter/internal/protocol"
	"errors"
	"git.code.tencent.com/smmit/smmbase/admin"
	"git.code.tencent.com/smmit/smmbase/logger"
	"git.code.tencent.com/smmit/smmbase/service"
	"github.com/gin-gonic/gin"
	"net/http"
)

func AdminAuthArgs() admin.AuthDecoratorArgs {
	url := corecontext.Config().RPC.AdminCenter
	Fail := func(c *gin.Context) {
		err := protocol.NewResErr(errcode.RESPONSE_CODE_PERMISSION_ERR, nil)
		c.<PERSON>(http.StatusOK, err.Info)
	}
	return admin.AuthDecoratorArgs{AdminCenterHost: url, FailedHandler: Fail}
}

func AdminAuth(module string, perm int64) gin.HandlerFunc {
	Fail := func(c *gin.Context) {
		err := protocol.NewResErr(errcode.RESPONSE_CODE_PERMISSION_ERR, nil)
		c.<PERSON>(http.StatusOK, err.Info)
	}
	return admin.PermDecorator(&admin.Perm{AppName: constant.AppConference, ModName: module, Value: perm}, Fail)
}

func UserAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		reqUserId := int64(0)
		token := getUserToken(c)
		if token != "" {
			uInfo, err := ParseTokenByUserCenter(token)
			if err != nil {
				_ = logger.Warnning("UserAuth:Err->", err, ",token->", token)
				c.Set(constant.UserLoginStatus, constant.UserStatusNotLogin)
			} else if uInfo != nil {
				uid := int64(uInfo.UserId)
				if uid <= 0 {
					c.Set(constant.UserLoginStatus, constant.UserStatusNotLogin)
				} else {
					c.Set(constant.UserToken, token)
				}
				reqUserId = uid
			} else {
				c.Set(constant.UserLoginStatus, constant.UserStatusNotLogin)
			}
		} else {
			c.Set(constant.UserLoginStatus, constant.UserStatusNotLogin)
		}
		c.Set(constant.UserId, reqUserId)
		c.Next()
	}
}

// 解析用户ID
func GetUserId(c *gin.Context) (userId int64) {
	idData, exists := c.Get(constant.UserId)
	if exists {
		return idData.(int64)
	}
	return 0
}

func CheckLogin() gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.GetString(constant.UserLoginStatus) == constant.UserStatusNotLogin {
			err := protocol.NewResErr(errcode.RESPONSE_CODE_TOKEN_EXPIRED, nil)
			c.JSON(http.StatusOK, err.Info)
			c.Abort()
		}
		// 用户已经登录，继续执行业务流程
		c.Next()
	}
}

func getUserToken(c *gin.Context) (t string) {
	if c.GetHeader("SMM_auth_token") != "" {
		t = c.GetHeader("SMM_auth_token")
	} else if c.GetHeader("token") != "" {
		t = c.GetHeader("token")
	} else if c.GetHeader("user_token") != "" {
		t = c.GetHeader("user_token")
	} else if token, ok := c.GetQuery("SMM_auth_token"); ok {
		t = token
	} else if token, ok = c.GetQuery("token"); ok {
		t = token
	} else if token, ok = c.GetQuery("user_token"); ok {
		t = token
	} else if token, ok = c.GetPostForm("SMM_auth_token"); ok {
		t = token
	} else if token, ok = c.GetPostForm("token"); ok {
		t = token
	} else if token, ok = c.GetPostForm("user_token"); ok {
		t = token
	} else if cookie, err := c.Request.Cookie("SMM_auth_token"); err == nil {
		t = cookie.Value
	}
	return
}

func ParseTokenByUserCenter(tokenStr string) (uInfo *service.UserInfo, err error) {
	userCenter := service.NewUsercenterService(corecontext.Config().RPC.UserCenter + "/4.0")
	resp, err_ := userCenter.GetUserInfo(tokenStr)
	if err_ != nil {
		_ = logger.Debug(err_)
		return nil, err_
	}
	if resp.Code != 0 {
		_ = logger.Debug(resp.Code, resp.Msg)
		return nil, errors.New(resp.Msg)
	}
	uInfo = &resp.Data.User
	return
}
