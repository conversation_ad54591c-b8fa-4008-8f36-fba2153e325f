package mw

import (
	"conferencecenter/internal/constant"
	"conferencecenter/internal/corecontext"
	"conferencecenter/internal/pkg/utils"
	"github.com/gin-gonic/gin"
)

func ClientIp() gin.HandlerFunc {
	return func(c *gin.Context) {
		ip := utils.GetClientIp(c)

		c.Set(constant.UserClientIp, ip)
		corecontext.Log().Infow("访问IP", "clientIp", ip)
		c.Next()
	}
}

func GetUserClientIp(c *gin.Context) string {
	return c.GetString(constant.UserClientIp)
}
