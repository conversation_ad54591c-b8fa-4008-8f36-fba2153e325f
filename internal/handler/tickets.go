package handler

import (
	"bytes"
	"conferencecenter/internal/constant"
	"conferencecenter/internal/db"
	"conferencecenter/internal/errcode"
	"conferencecenter/internal/model"
	"conferencecenter/internal/pkg/utils"
	"conferencecenter/internal/protocol"
	"conferencecenter/internal/rpc"
	"conferencecenter/internal/service"
	"conferencecenter/internal/weChat"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.code.tencent.com/smmit/smmbase/admin"
	"git.code.tencent.com/smmit/smmbase/logger"
	"git.code.tencent.com/smmit/smmbase/webhook/feishu"
	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
)

// @Summary		    查询票种列表
// @Description	    查询票种列表API
// @Tags			管理后台-票种
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqGetTicketPriceList					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.ResAdminTicketPriceList}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						                "failed，code != 0"
// @Router			/admin/tickets/list  [get]
func AdminTicketPriceList(c *gin.Context) {
	var (
		req   protocol.ReqGetTicketPriceList
		res   protocol.ResAdminTicketPriceList
		err   protocol.ResError
		list  []protocol.ConferenceTicketPrice
		total int64
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if req.IsCn > 0 {
		list, total, err_ = service.QueryEnConferenceTicketPriceList(req)
		if err_ != nil {
			err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
			return
		}
	} else {
		list, total, err_ = service.QueryConferenceTicketPriceList(req)
		if err_ != nil {
			err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
			return
		}
	}

	res.Info.InitPageInfo(total, req.Page, req.PageSize)
	res.List = list

	return
}

// @Summary		    新增或修改票种
// @Description	    新增或修改票种API
// @Tags			管理后台-票种
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string				                true    "SMM-ADMIN-TOKEN"
// @Param			payload		formData	protocol.ReqAdminSaveTicketPrice     true    "json body"
// @Success		    200			{object}	protocol.Response{}                 "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                 "failed，code != 0"
// @Router	        /admin/tickets/add_edit  [post]
func AdminAddEditTicketPrice(c *gin.Context) {
	var (
		req protocol.ReqAdminSaveTicketPrice
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	adminInfo, exist := c.Get(constant.AdminInfoKey)
	if exist {
		if utils.NotEmpty(adminInfo.(*admin.User).FullName) {
			req.AdminEmail = adminInfo.(*admin.User).FullName
		} else if utils.NotEmpty(adminInfo.(*admin.User).Email) {
			req.AdminEmail = adminInfo.(*admin.User).Email
		}
	}

	_, err_ = service.AddEditConferenceTicketPrice(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}

	return
}

// @Summary		    查询票种详情信息
// @Description	    查询票种详情信息API
// @Tags			管理后台-票种
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										false   "auth_token"
// @Param			request		query		protocol.ReqTicketPriceId					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.RespTicketPriceInfo}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						            "failed，code != 0"
// @Router			/admin/tickets/detail/id    [get]
func AdminQueryTicketPriceInfo(c *gin.Context) {
	var (
		req protocol.ReqTicketPriceId
		res protocol.RespTicketPriceInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetConferenceTicketPriceInfo(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	return
}

// @Summary		    删除票种信息
// @Description	    删除票种API
// @Tags			管理后台-票种
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string				        true    "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqRightInterestsId					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{}                 "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                 "failed，code != 0"
// @Router	        /admin/tickets/delete/id  [post]
func AdminDeleteTicketPrice(c *gin.Context) {
	var (
		req protocol.ReqRightInterestsId
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	adminInfo, exist := c.Get(constant.AdminInfoKey)
	if exist {
		if utils.NotEmpty(adminInfo.(*admin.User).FullName) {
			req.AdminEmail = adminInfo.(*admin.User).FullName
		} else if utils.NotEmpty(adminInfo.(*admin.User).Email) {
			req.AdminEmail = adminInfo.(*admin.User).Email
		}
	}
	_, err_ = service.DeleteConferenceTicketPrice(req.Id, req.AdminEmail)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	return
}

// @Summary		    查询票种权益列表
// @Description	    查询票种权益列表API
// @Tags			管理后台-票种权益
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqGetRightsInterestsList					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.ResAdminRightsInterestsList}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						                "failed，code != 0"
// @Router			/admin/tickets/rights_interests/list  [get]
func AdminRightsInterestsList(c *gin.Context) {
	var (
		req   protocol.ReqGetRightsInterestsList
		res   protocol.ResAdminRightsInterestsList
		err   protocol.ResError
		list  []model.ConferenceRightsInterests
		total int64
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}

	if req.IsCn > 0 {
		list, total, err_ = service.QueryEnRightsInterestsList(req)
		if err_ != nil {
			err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
			return
		}
	} else {
		list, total, err_ = service.QueryRightsInterestsList(req)
		if err_ != nil {
			err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
			return
		}
	}
	res.Info.InitPageInfo(total, req.Page, req.PageSize)
	res.List = list

	return
}

// @Summary		    查询票种权益列表
// @Description	    查询票种权益列表API
// @Tags			管理后台-票种权益
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqGetRightsInterestsList					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.ResAdminRightsInterestsList}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						                "failed，code != 0"
// @Router			/admin/tickets/rights_interests/en_list  [get]
func AdminEnRightsInterestsList(c *gin.Context) {
	var (
		req protocol.ReqGetRightsInterestsList
		res protocol.ResAdminRightsInterestsList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}

	list, total, err_ := service.QueryEnRightsInterestsList(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res.Info.InitPageInfo(total, req.Page, req.PageSize)
	res.List = list

	return
}

// @Summary		    新增或修改票种权益
// @Description	    新增或修改票种权益API
// @Tags			管理后台-票种权益
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string				                true    "SMM-ADMIN-TOKEN"
// @Param			payload		formData	protocol.ReqAdminSaveRightInterests     true    "json body"
// @Success		    200			{object}	protocol.Response{}                 "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                 "failed，code != 0"
// @Router	        /admin/tickets/rights_interests/add_edit  [post]
func AdminAddEditRightInterests(c *gin.Context) {
	var (
		req protocol.ReqAdminSaveRightInterests
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	_, err_ = service.AddEditConferenceRightsInterests(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	return
}

// @Summary		    查询票种权益详情信息
// @Description	    查询票种权益详情信息API
// @Tags			管理后台-票种权益
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										false   "auth_token"
// @Param			payload		formData	protocol.ReqRightInterestsId     true    "json body"
// @Success		    200			{object}	protocol.Response{data=protocol.ResConferenceRightsInterestsInfo}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						            "failed，code != 0"
// @Router			/admin/tickets/rights_interests/detail/id    [get]
func AdminQueryRightInterestsInfo(c *gin.Context) {
	var (
		req protocol.ReqRightInterestsId
		res protocol.ResConferenceRightsInterestsInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetConferenceRightsInterestsInfo(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
	}
	return
}

// @Summary		    删除票种权益信息
// @Description	    删除票种权益API
// @Tags			管理后台-票种权益
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			payload		formData	protocol.ReqRightInterestsId     true    "json body"
// @Success		    200			{object}	protocol.Response{}                 "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                 "failed，code != 0"
// @Router	        /admin/tickets/rights_interests/delete/id  [post]
func AdminDeleteRightInterests(c *gin.Context) {
	var (
		req protocol.ReqRightInterestsId
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	_, err_ = service.DeleteRightsInterests(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
	}

	return
}

// @Summary		    查询票种模版类型信息
// @Description	    查询票种模版类型信息API
// @Tags			管理后台-票种
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										false   "auth_token"
// @Param			payload		formData	protocol.ReqWebConferenceList     true    "json body"
// @Success		    200			{object}	protocol.Response{data=protocol.ResConferenceTicketConfigInfo}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						            "failed，code != 0"
// @Router			/admin/tickets/config/detail/id    [get]
func AdminQueryTicketConfigInfo(c *gin.Context) {
	var (
		req protocol.ReqWebConferenceList
		res protocol.ResConferenceTicketConfigInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetConferenceTicketConfigInfo(req.ConferenceId)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
	}
	return
}

// @Summary		    新增或修改票种模版类型
// @Description	    新增或修改票种模版类型API
// @Tags			管理后台-票种
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string				                true    "SMM-ADMIN-TOKEN"
// @Param			payload		formData	protocol.ReqAdminSaveTicketConfig     true    "json body"
// @Success		    200			{object}	protocol.Response{}                 "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                 "failed，code != 0"
// @Router	        /admin/tickets/config/add_edit  [post]
func AdminAddEditTicketConfig(c *gin.Context) {
	var (
		req protocol.ReqAdminSaveTicketConfig
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	_, err_ = service.AddEditConferenceTicketConfig(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	return
}

// @Summary		    查询票种权益绑定列表
// @Description	    查询票种权益绑定列表API
// @Tags			管理后台-票种权益绑定
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqGetRightsTicketList					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.ResAdminRightsTicketList}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						                "failed，code != 0"
// @Router			/admin/tickets/rights/list  [get]
func AdminGetMeetingRightsTicketList(c *gin.Context) {
	var (
		req protocol.ReqGetRightsTicketList
		res protocol.ResAdminRightsTicketList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}

	list, total, err_ := service.QueryRightsTicketList(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res.Info.InitPageInfo(total, req.Page, req.PageSize)
	res.List = list

	return
}

// @Summary		    新增或修改票种权益绑定
// @Description	    新增或修改票种权益绑定API
// @Tags			管理后台-票种权益绑定
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string				                true    "admin_token"
// @Param			payload		formData	protocol.ReqAdminSaveRightTicket     true    "json body"
// @Success		    200			{object}	protocol.Response{}                 "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                 "failed，code != 0"
// @Router	        /admin/tickets/rights/add  [post]
func AdminAddEditRightTicket(c *gin.Context) {
	var (
		req protocol.ReqAdminSaveRightTicket
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}

	_, err_ = service.AdminSaveMeetingRightTicket(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	return
}

// @Summary		    查询报名信息列表
// @Description	    查询报名信息列表API
// @Tags			管理后台-报名信息
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqGetConferenceRegisterList					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.ResAdminConferenceRegisterList}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						                "failed，code != 0"
// @Router			/admin/register/list  [get]
func AdminGetConferenceRegisterList(c *gin.Context) {
	var (
		req protocol.ReqGetConferenceRegisterList
		res protocol.ResAdminConferenceRegisterList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	list, total, err_ := service.QueryConferenceRegisterList(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res.Info.InitPageInfo(total, req.Page, req.PageSize)
	res.List = list
	return
}

// @Summary		    导出报名信息列表
// @Description	    导出报名信息列表API
// @Tags			管理后台-报名信息
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string						    true    "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqGetConferenceRegisterList  	false	"Query Params"
// @Success		    200					                                                "excel文件"
// @Failure		    500			{object}	protocol.Response{}						    "failed，code != 0"
// @Router			/admin/register/export  [get]
func AdminExportConferenceRegisterList(c *gin.Context) {
	var (
		req        protocol.ReqGetConferenceRegisterList
		err        protocol.ResError
		exlData    *bytes.Reader
		dataLength int64
		fileName   = "报名信息列表"
	)
	defer func() {
		wrapExcelResp(c, fmt.Sprintf("%s.xlsx", fileName), dataLength, exlData, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	exlData, err_ = service.ExportRegisterList(req)
	if err_ != nil || exlData == nil {
		err = protocol.NewResErrWithMsg(errcode.RESPONSE_CODE_PARAM_ERR, "参数错误", err_)
		return
	}
	dataLength = int64(exlData.Len())
	return
}

// @Summary		    查询报名-报名人信息列表
// @Description	    查询报名-报名人信息列表API
// @Tags			管理后台-报名信息
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqGetConferenceRegisterUserList					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.ResAdminConferenceRegisterUserList}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						                "failed，code != 0"
// @Router			/admin/register/user/list  [get]
func AdminGetConferenceRegisterUserList(c *gin.Context) {
	var (
		req protocol.ReqGetConferenceRegisterUserList
		res protocol.ResAdminConferenceRegisterUserList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	list, total, err_ := service.QueryConferenceRegisterUserList(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res.Info.InitPageInfo(total, 0, 0)
	res.List = list
	return
}

// @Summary		    感兴趣的会议列表
// @Description	    感兴趣的会议列表API
// @Tags			前台-票和报名
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqUserForumConfigList					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.ResUserForumConfigList}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						                "failed，code != 0"
// @Router			/user/forum_config/list  [get]
func UserForumConfigList(c *gin.Context) {
	var (
		req protocol.ReqUserForumConfigList
		res protocol.ResUserForumConfigList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.UserForumConfigList(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	return
}

// @Summary		    渠道映射列表
// @Description	    渠道映射列表API
// @Tags			管理后台-渠道映射
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqGetIntroductionInfo					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.ResConferenceFromConfigList}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						                "failed，code != 0"
// @Router			/admin/from/config/list  [get]
func AdminConferenceFromConfigList(c *gin.Context) {
	var (
		req protocol.ReqGetIntroductionInfo
		res protocol.ResConferenceFromConfigList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetConferenceFromConfigList(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	return
}

// @Summary		    渠道映射列表v2(根据数据查询)
// @Description	    渠道映射列表v2API
// @Tags			管理后台-渠道映射v2
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqGetIntroductionInfo					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.ResConferenceFromConfigList}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						                "failed，code != 0"
// @Router			/admin/from/config/list/v2  [get]
func AdminConferenceFromConfigListV2(c *gin.Context) {
	var (
		req protocol.ReqGetIntroductionInfo
		res protocol.ResConferenceFromConfigList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	list, err_ := service.GetConferenceFromConfigListV2(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res.List = list
	return
}

// @Summary		    用户填写报名信息 （2.0修改报名信息和参会人信息分开填写）
// @Description	    用户填写报名信息API
// @Tags			前台-票和报名
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM_auth_token	header		string				                false    "SMM_auth_token"
// @Param			payload		body	protocol.ReqAddConferenceRegister     true    "json body"
// @Success		    200			{object}	protocol.Response{}                 "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                 "failed，code != 0"
// @Router	        /user/register/add  [post]
func UserAddConferenceRegister(c *gin.Context) {
	var (
		req protocol.ReqAddConferenceRegister
		res protocol.RespConferenceRegisterInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}

	res, _, err_ = service.UserAddConferenceRegister(req)
	if err_ != nil {
		err = protocol.NewResErrWithMsg(errcode.RESPONSE_CODE_PARAM_ERR, err_.Error(), err_)
		return
	}

	return
}

// @Summary		    用户填写报名信息详情 （2.0新增 购买参会时第二步获取报名信息使用）
// @Description	    用户填写报名信息详情API
// @Tags			前台-报名信息详情
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqGetConferenceRegisterUserList					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.ResConferenceRegister}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						                "failed，code != 0"
// @Router			/user/register/info  [get]
func UserConferenceRegisterInfo(c *gin.Context) {
	var (
		req protocol.ReqGetConferenceRegisterUserList
		res protocol.ResConferenceRegister
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetUserConferenceRegisterInfo(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	return
}

// @Summary		    订单购买状态回调
// @Description	    订单购买状态回调API
// @Tags			内网接口-订单回调
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM_auth_token	header		string				                false    "SMM_auth_token"
// @Param			payload		body	protocol.ReqAdminUpdConferenceRegister     true    "json body"
// @Success		    200			{object}	protocol.Response{}                 "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                 "failed，code != 0"
// @Router	        /conference_register/upd  [post]
func UpdateMeetingRegister(c *gin.Context) {
	var (
		ReqLog     []byte
		RespLog    []byte
		enMeeting  = model.Conference{}
		req        protocol.ReqAdminUpdConferenceRegister
		reqAPiSign protocol.ReqAddAPiSignUpAddEn
		WxUrl2     string
		err        protocol.ResError
	)
	defer func() {
		wrapResp(c, 0, addCallback, protocol.ResError{})
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	ReqLog, _ = json.Marshal(req)
	RegisterLog := model.RegisterLog{
		RegisterId: req.Id,
		ReqLog:     string(ReqLog),
		RespLog:    string(RespLog),
		LogType:    1,
		CreateTime: time.Now(),
	}
	defer func() {
		if err_ != nil {
			RegisterLog.ErrLog = err_.Error()
			RegisterLog.CreateTime = time.Now()
			markdown := fmt.Sprintf(`
				## 英文会议订单错误提醒
			>报名记录ID：%v
			>订单号：%s
			>购买数量：%v
			>订单状态：%v
			>错误信息：%s
			>时间：%s`, req.Id, req.OrderNumber, req.Quantity, req.OrderStatus, err_.Error(), utils.GetCurDateTime())

			weChat.Send(markdown, weChat.WxUrl)

		}
	}()

	if !utils.IsTestEnv() {
		if enMeeting.ID <= 0 {
			WxUrl2 = weChat.WxUrl2
		} else {
			if utils.NotEmpty(enMeeting.QwxUrl) {
				WxUrl2 = enMeeting.QwxUrl
			} else {
				WxUrl2 = weChat.WxUrl2
			}

		}
	} else {
		if enMeeting.ID <= 0 || utils.IsEmpty(enMeeting.QwxUrl) {
			WxUrl2 = weChat.WxUrl
		} else {
			WxUrl2 = enMeeting.QwxUrl
		}
	}

	now := time.Now().In(time.Local)
	if req.Id <= 0 {
		// 创建报名信息
		enMeetingsRegister := model.ConferenceRegister{
			ServiceId:      req.ServiceID,
			ConferenceName: req.ServiceName,
			OrderId:        req.OrderNumber,
			OrderNum:       req.Quantity,
			OrderStatus:    req.OrderStatus,
			CreateTime:     now,
			UpdateTime:     now,
			Deleted:        0,
		}

		if utils.NotEmpty(req.CustomInfo) {
			var customInfo protocol.RespCustomInfo
			err := json.Unmarshal([]byte(req.CustomInfo), &customInfo)
			if err != nil {
				logger.Error("CustomInfo 转化失败", req.CustomInfo, err.Error())
			}
			if &customInfo != nil {
				enMeetingsRegister.FirstName = customInfo.Name
				enMeetingsRegister.Email = customInfo.Email
			}
		}

		ReqLog, err_ = json.Marshal(req)
		RespLog, err_ = json.Marshal("")
		RegisterLog = model.RegisterLog{
			RegisterId: req.Id,
			ReqLog:     string(ReqLog),
			RespLog:    string(RespLog),
			LogType:    1,
			CreateTime: now,
		}

		CurrencyUnit := " 元"
		if req.CurrencyUnit == 2 {
			CurrencyUnit = " 美元"
		}
		if req.CurrencyUnit == 3 {
			CurrencyUnit = " 欧元"
		}
		registers, _, err_ := db.GetMeetingRegisterByOrderId(nil, req.OrderNumber)
		if err_ != nil {
			err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
			return
		}
		if len(registers) > 0 {
			orderId := &registers[0]
			if orderId != nil {
				enMeetingsRegister.FirstName = orderId.FirstName
				enMeetingsRegister.Email = orderId.Email
				orderId.OrderId = req.OrderNumber
				orderId.OrderNum = req.Quantity
				orderId.OrderStatus = req.OrderStatus
				orderId.ServiceId = req.ServiceID
				orderId.UpdateTime = now
				db.UpdateMeetingRegister(nil, orderId)
				if err_ != nil {
					err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
					return
				}

				if orderId.ConferenceId > 0 {
					enMeeting, err_ = db.QueryConferenceInfo(nil, orderId.ConferenceId)
					if err_ != nil {
						err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
						return
					}
					if utils.NotEmpty(enMeeting.MeetingSysId) {
						if req.OrderStatus == 2 {
							reqAPiSign.MeetingNo = enMeeting.MeetingSysId
							list, _, err_ := db.GetMeetingRegisterUserList(nil, orderId.Id)
							if err_ != nil {
								err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
								return
							}
							Amount := decimal.NewFromFloat(req.Amount)
							div := Amount.Div(decimal.NewFromFloat(float64(req.Quantity)))
							f, _ := div.Float64()
							s := strconv.FormatFloat(f, 'f', 2, 64)

							if req.Quantity > len(list) {
								if utils.IsEmpty(orderId.LastName) {
									markdown := fmt.Sprintf(`
						## 会议购买人数多于报名人数
							>会议名称：%v
							>购票人姓名：%s
							>购票人手机号：%v
							>订单号：%v
							>会议系统编号：%v
							>报错信息：%v
							>时间：%s`, enMeeting.CnName, orderId.FirstName, orderId.Mobile, orderId.OrderId, reqAPiSign.MeetingNo, "购买人数多于报名人数", utils.GetCurDateTime())
									weChat.Send(markdown, WxUrl2)

									cardMsg := feishu.NewMdCardMsg("会议购买人数多于报名人数", "blue")
									cardMsg.AppendMd(`>会议名称：%v
>购票人姓名：%s
>报名人联系邮箱：%v
>订单号：%v
>会议系统编号：%v
>报错信息：%v
>时间: %s`, enMeeting.CnName, orderId.FirstName, orderId.Mobile, orderId.OrderId, reqAPiSign.MeetingNo, "购买人数多于报名人数", utils.GetCurDateTime())

									if strings.Contains(WxUrl2, "open.feishu.cn") {
										weChat.Send2(cardMsg, WxUrl2)
									} else {
										weChat.Send(markdown, WxUrl2)
									}

								} else {
									markdown := fmt.Sprintf(`
						## 会议购买人数多于报名人数
							>会议名称：%v
							>购票人first name：%s 
							>购票人last name：%s 
							>报名人联系邮箱：%v
							>订单号：%v
							>会议系统编号：%v
							>报错信息：%v
							>时间：%s`, enMeeting.CnName, orderId.FirstName, orderId.LastName, orderId.Email, orderId.OrderId, reqAPiSign.MeetingNo, "购买人数多于报名人数", utils.GetCurDateTime())

									cardMsg := feishu.NewMdCardMsg("会议购买人数多于报名人数", "blue")
									cardMsg.AppendMd(`>会议名称：%v
>购票人first name：%s 
>购票人last name：%s 
>报名人联系邮箱：%v
>订单号：%v
>会议系统编号：%v
>报错信息：%v
>时间: %s`, enMeeting.CnName, orderId.FirstName, orderId.LastName, orderId.Email, orderId.OrderId, reqAPiSign.MeetingNo, "购买人数多于报名人数", utils.GetCurDateTime())

									if strings.Contains(WxUrl2, "open.feishu.cn") {
										weChat.Send2(cardMsg, WxUrl2)
									} else {
										weChat.Send(markdown, WxUrl2)
									}
								}
								logger.Warnning("购买人数多于报名人数：")
							}

							register, _ := db.GetConferenceRegisterInfo(nil, req.Id)
							reqAPiSign.FromId = register.FromId

							for i, registerUser := range list {
								if i+1 > req.Quantity {
									logger.Info("i：", i)
									logger.Info("Quantity：", req.Quantity)

									if utils.IsEmpty(orderId.LastName) {
										markdown := fmt.Sprintf(`
						## 会议购买人数少于报名人数
							>会议名称：%v
							>购票人姓名：%s 
							>购票人手机号：%v
							>订单号：%v
							>会议系统编号：%v
							>报错信息：%v
							>时间：%s`, enMeeting.CnName, orderId.FirstName, orderId.Mobile, orderId.OrderId, reqAPiSign.MeetingNo, "购买人数少于报名人数", utils.GetCurDateTime())

										cardMsg := feishu.NewMdCardMsg("会议购买人数少于报名人数", "blue")
										cardMsg.AppendMd(`>会议名称：%v
>购票人姓名：%s 
>购票人手机号：%v
>订单号：%v
>会议系统编号：%v
>报错信息：%v
>时间: %s`, enMeeting.CnName, orderId.FirstName, orderId.Mobile, orderId.OrderId, reqAPiSign.MeetingNo, "购买人数少于报名人数", utils.GetCurDateTime())

										if strings.Contains(WxUrl2, "open.feishu.cn") {
											weChat.Send2(cardMsg, WxUrl2)
										} else {
											weChat.Send(markdown, WxUrl2)
										}
									} else {
										markdown := fmt.Sprintf(`
						## 会议购买人数少于报名人数
							>会议名称：%v
							>购票人first name：%s 
							>购票人last name：%s 
							>报名人联系邮箱：%v
							>订单号：%v
							>会议系统编号：%v
							>报错信息：%v
							>时间：%s`, enMeeting.CnName, orderId.FirstName, orderId.LastName, orderId.Email, orderId.OrderId, reqAPiSign.MeetingNo, "购买人数少于报名人数", utils.GetCurDateTime())

										cardMsg := feishu.NewMdCardMsg("英文会议订单错误提醒", "blue")
										cardMsg.AppendMd(`>会议名称：%v
>购票人first name：%s 
>购票人last name：%s 
>报名人联系邮箱：%v
>订单号：%v
>会议系统编号：%v
>报错信息：%v
>时间: %s`, enMeeting.CnName, orderId.FirstName, orderId.LastName, orderId.Email, orderId.OrderId, reqAPiSign.MeetingNo, "购买人数少于报名人数", utils.GetCurDateTime())

										if strings.Contains(WxUrl2, "open.feishu.cn") {
											weChat.Send2(cardMsg, WxUrl2)
										} else {
											weChat.Send(markdown, WxUrl2)
										}
									}
									logger.Warnning("购买人数少于报名人数：")
									break
								}

								reqAPiSign.CnOrEn = orderId.CnOrEn
								reqAPiSign.PlatformId = orderId.SourceID
								reqAPiSign.UserId = orderId.UserId
								reqAPiSign.FirstName = registerUser.FirstName
								reqAPiSign.LastName = registerUser.LastName
								reqAPiSign.Email = registerUser.Email
								reqAPiSign.Mobile = registerUser.Mobile
								reqAPiSign.Company = registerUser.Company
								reqAPiSign.JobTitle = registerUser.JobTitle
								reqAPiSign.Identity = "1"
								reqAPiSign.ServiceId = req.ServiceID
								reqAPiSign.OrderId = req.OrderNumber
								reqAPiSign.OrderCount = strconv.Itoa(len(list))
								reqAPiSign.OrderStatus = strconv.Itoa(req.OrderStatus)
								reqAPiSign.PayAmount = s
								reqAPiSign.Source = req.Source
								reqAPiSign.PayMethod = req.PayMethod
								reqAPiSign.CurrencyUnit = req.CurrencyUnit
								m, apiErr := rpc.AddAPiSignUpOrderChange(reqAPiSign)

								ReqLog, err_ = json.Marshal(reqAPiSign)
								RespLog, err_ = json.Marshal(m)
								RegisterLog = model.RegisterLog{
									RegisterId: req.Id,
									ReqLog:     string(ReqLog),
									RespLog:    string(RespLog),
									LogType:    2,
									CreateTime: now,
								}
								if apiErr != nil {
									logger.Warnning("MeetingNo: ", reqAPiSign.MeetingNo)
									RegisterLog.ErrLog = apiErr.Error()
									markdown := fmt.Sprintf(`
						## 英文会议报名同步会议系统失败
							>会议名称：%v
							>购票人first name：%s 
							>购票人last name：%s 
							>报名人联系邮箱：%v
							>订单号：%v
							>会议系统编号：%v
							>报错信息：%v
							>时间：%s`, enMeeting.CnName, orderId.FirstName, orderId.LastName, orderId.Email, orderId.OrderId, reqAPiSign.MeetingNo, apiErr.Error(), utils.GetCurDateTime())
									weChat.Send(markdown, WxUrl2)
								}
								if m.Code != 0 {
									logger.Warnning("MeetingNo: ", reqAPiSign.MeetingNo)
									RegisterLog.ErrLog = m.Msg
									markdown := fmt.Sprintf(`
						## 英文会议报名同步会议系统失败
							>会议名称：%v
							>购票人first name：%s 
							>购票人last name：%s 
							>购票人联系邮箱：%v
							>订单号：%v
							>会议系统编号：%v
							>报错信息：%v
							>时间：%s`, enMeeting.CnName, orderId.FirstName, orderId.LastName, orderId.Email, orderId.OrderId, reqAPiSign.MeetingNo, m.Msg, utils.GetCurDateTime())
									weChat.Send(markdown, WxUrl2)
								}
								RegisterLog.CreateTime = time.Now()
								db.AddMeetingRegisterLog(nil, &RegisterLog)
							}
						}
					} else {
						logger.Error(enMeeting.CnName + "会议未设置绑定会议系统")
					}
				}
			} else {

				if utils.NotEmpty(req.CustomInfo) {
					var customInfo protocol.RespCustomInfo
					err := json.Unmarshal([]byte(req.CustomInfo), &customInfo)
					if err != nil {
						logger.Error("CustomInfo 转化失败", req.CustomInfo, err.Error())
					}
					if &customInfo != nil {
						enMeetingsRegister.FirstName = customInfo.Name
						enMeetingsRegister.Email = customInfo.Email
					}
				}
				meetingId := int64(0)
				serviceList, _, err_ := db.GetTicketPriceServiceIdList(nil)
				if err_ != nil {
					err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
					return
				}
				for _, service := range serviceList {
					if utils.NotEmpty(service.CnServiceId) {
						split := strings.Split(service.CnServiceId, ",")
						for _, serviceId := range split {
							if serviceId == req.ServiceID {
								if service.ConferenceId > 0 {
									meetingId = service.ConferenceId
								}
							}
						}
					}
				}
				if meetingId > 0 {
					enMeeting, err_ = db.QueryConferenceInfo(nil, meetingId)
					if err_ != nil {
						err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
						return
					}
					if enMeeting.ID > 0 {
						enMeetingsRegister.ConferenceId = enMeeting.ID
						enMeetingsRegister.ConferenceName = enMeeting.CnName
					}
				}
				err_ = db.SaveConferenceRegister(nil, &enMeetingsRegister)
				if err_ != nil {
					err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
					return
				}
			}
		}

		if req.OrderStatus == 1 {
			markdown2 := fmt.Sprintf(`## <font color="red">已创建订单通知 </font>
			>会议/服务名称：%s
			>购票人 name：%s 
			>购票人邮箱：%v
			>购票人手机号：%v
			>订单号：%v 
			>订单状态：%v 
			>订单数量：%v 
			>订单金额：%v
			>时间：%s 
					`, enMeetingsRegister.ConferenceName, enMeetingsRegister.FirstName, enMeetingsRegister.Email, enMeetingsRegister.Mobile, req.OrderNumber, "待支付", req.Quantity, strconv.FormatFloat(req.Amount, 'f', 2, 64)+CurrencyUnit, utils.GetCurDateTime())

			if utils.NotEmpty(req.CouponCode) {
				markdown2 = fmt.Sprintf(`## <font color="red">已创建订单通知 </font>
				>会议/服务名称：%s
				>购票人 name：%s 
				>购票人邮箱：%v
				>购票人手机号：%v
				>订单号：%v 
				>订单状态：%v 
				>订单数量：%v 
				>订单金额：%v 
				>优惠码：%v 
				>优惠折扣：%v
				>时间：%s 
					`, enMeetingsRegister.ConferenceName, enMeetingsRegister.FirstName, enMeetingsRegister.Email, enMeetingsRegister.Mobile, req.OrderNumber, "待支付", req.Quantity, strconv.FormatFloat(req.Amount, 'f', 2, 64)+CurrencyUnit, req.CouponCode, req.OffVaule, utils.GetCurDateTime())
			}

			cardMsg := feishu.NewMdCardMsg("已创建订单通知", "red")
			cardMsg.AppendMd(`>会议/服务名称：%v
>购票人 name：%s 
>购票人邮箱：%v
>购票人手机号：%v
>订单号：%v 
>订单状态：%v 
>订单数量：%v 
>订单金额：%v 
>优惠码：%v 
>优惠折扣：%v
>时间: %s`, enMeetingsRegister.ConferenceName, enMeetingsRegister.FirstName, enMeetingsRegister.Email, enMeetingsRegister.Mobile, req.OrderNumber, "待支付", req.Quantity, strconv.FormatFloat(req.Amount, 'f', 2, 64)+CurrencyUnit, req.CouponCode, req.OffVaule, utils.GetCurDateTime())

			if strings.Contains(WxUrl2, "open.feishu.cn") {
				weChat.Send2(cardMsg, WxUrl2)
			} else {
				weChat.Send(markdown2, WxUrl2)
			}

		} else if req.OrderStatus == 2 {

			cardMsg := feishu.NewMdCardMsg("申请参会已支付通知", "green")
			cardMsg.AppendMd(`>会议/服务名称：%s
>购票人姓名：%s 
>购票人邮箱：%s`, req.ServiceName, enMeetingsRegister.FirstName, enMeetingsRegister.Email)

			markdown2 := fmt.Sprintf(`## <font color="green">申请参会已支付通知 </font>
				>会议/服务名称：%s
				>购票人姓名：%s 
				>购票人邮箱：%v`, enMeetingsRegister.ConferenceName, enMeetingsRegister.FirstName, enMeetingsRegister.Email)

			if utils.NotEmpty(enMeetingsRegister.LastName) {
				markdown2 += fmt.Sprintf(`
					>参会人first name：%s 
					>参会人last name：%s 
					>参会人邮箱：%s`, enMeetingsRegister.FirstName, enMeetingsRegister.LastName, enMeetingsRegister.Email)

				cardMsg.AppendMd(`
					>参会人first name：%s 
					>参会人last name：%s 
					>参会人邮箱：%s`, enMeetingsRegister.FirstName, enMeetingsRegister.LastName, enMeetingsRegister.Email)
			} else {
				markdown2 += fmt.Sprintf(`
					>参会人姓名：%s 
					>参会人邮箱：%s`, enMeetingsRegister.FirstName, enMeetingsRegister.Email)

				cardMsg.AppendMd(`
					>参会人姓名：%s 
					>参会人邮箱：%s`, enMeetingsRegister.FirstName, enMeetingsRegister.Email)
			}

			if utils.NotEmpty(req.CouponCode) {
				markdown2 = fmt.Sprintf(`
				>订单号：%v 
				>订单状态：%v 
				>订单数量：%v 
				>订单金额：%v 
				>优惠码：%v 
				>优惠折扣：%v
				>时间：%s`, req.OrderNumber, "已支付", req.Quantity, strconv.FormatFloat(req.Amount, 'f', 2, 64)+CurrencyUnit, req.CouponCode, req.OffVaule, utils.GetCurDateTime())

				cardMsg.AppendMd(`
				>订单号：%s
				>订单状态：%s 
				>订单数量：%v 
				>订单金额：%v 
				>优惠码：%v 
				>优惠折扣：%v
				>时间：%s`, req.OrderNumber, "已支付", req.Quantity, strconv.FormatFloat(req.Amount, 'f', 2, 64)+CurrencyUnit, req.CouponCode, req.OffVaule, utils.GetCurDateTime())
			} else {
				markdown2 += fmt.Sprintf(`
				>订单号：%v 
				>订单状态：%v 
				>订单数量：%v 
				>订单金额：%v
				>时间：%s`, req.OrderNumber, "已支付", req.Quantity, strconv.FormatFloat(req.Amount, 'f', 2, 64)+CurrencyUnit, utils.GetCurDateTime())

				cardMsg.AppendMd(`
				>订单号：%s 
				>订单状态：%s
				>订单数量：%v 
				>订单金额：%v
				>时间：%s`, req.OrderNumber, "已支付", req.Quantity, strconv.FormatFloat(req.Amount, 'f', 2, 64)+CurrencyUnit, utils.GetCurDateTime())

			}

			if strings.Contains(WxUrl2, "open.feishu.cn") {
				weChat.Send2(cardMsg, WxUrl2)
			} else {
				weChat.Send(markdown2, WxUrl2)
			}

		}
		RegisterLog.CreateTime = time.Now()
		err_ = db.AddMeetingRegisterLog(nil, &RegisterLog)
		if err_ != nil {
			err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
			return
		}
		return
	}

	userList, _, err_ := db.GetMeetingRegisterUserList(nil, req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}

	registerList, _, err_ := db.GetMeetingRegisterById(nil, req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if len(registerList) == 0 {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}

	enMeeting, err_ = db.QueryConferenceInfo(nil, registerList[0].ConferenceId)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}

	if len(registerList) > 0 {
		registerList[0].OrderId = req.OrderNumber
		registerList[0].OrderNum = req.Quantity
		registerList[0].OrderStatus = req.OrderStatus
		registerList[0].ServiceId = req.ServiceID
		registerList[0].UpdateTime = now
		err_ = db.UpdateMeetingRegister(nil, &registerList[0])
		if err_ != nil {
			err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
			return
		}

		ReqLog, err_ = json.Marshal(req)
		RespLog, err_ = json.Marshal("")
		RegisterLog = model.RegisterLog{
			RegisterId: req.Id,
			ReqLog:     string(ReqLog),
			RespLog:    string(RespLog),
			LogType:    1,
		}
		RegisterLog.CreateTime = time.Now()
		err_ = db.AddMeetingRegisterLog(nil, &RegisterLog)
		if err_ != nil {
			err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
			return
		}

		Amount := decimal.NewFromFloat(req.Amount)
		div := Amount.Div(decimal.NewFromFloat(float64(req.Quantity)))
		f, _ := div.Float64()
		s := strconv.FormatFloat(f, 'f', 2, 64)

		if !utils.IsTestEnv() {
			if enMeeting.ID <= 0 {
				WxUrl2 = weChat.WxUrl2
				if req.OrderStatus == 2 {
					markdown := fmt.Sprintf(`
				## 英文会议报名同步会议系统失败
					>会议名称：%v
					>购票人first name：%s 
					>购票人last name：%s 
					>报名人联系邮箱：%v
					>订单号：%v
					>会议系统编号：%v
					>报错信息：%v
					>时间：%s`, enMeeting.CnName, registerList[0].FirstName, registerList[0].LastName, registerList[0].Email, registerList[0].OrderId, reqAPiSign.MeetingNo, "该会议未设置绑定会议系统", utils.GetCurDateTime())

					cardMsg := feishu.NewMdCardMsg("英文会议报名同步会议系统失败", "blue")
					cardMsg.AppendMd(`>会议名称：%v
>购票人first name：%s 
>购票人last name：%s 
>报名人联系邮箱：%v
>订单号：%v
>会议系统编号：%v
>报错信息：%v
>时间: %s`, enMeeting.CnName, registerList[0].FirstName, registerList[0].LastName, registerList[0].Email, registerList[0].OrderId, reqAPiSign.MeetingNo, "该会议未设置绑定会议系统", utils.GetCurDateTime())

					if strings.Contains(WxUrl2, "open.feishu.cn") {
						weChat.Send2(cardMsg, WxUrl2)
					} else {
						weChat.Send(markdown, WxUrl2)
					}

				}

			} else {
				if utils.NotEmpty(enMeeting.QwxUrl) {
					WxUrl2 = enMeeting.QwxUrl
				} else {
					WxUrl2 = weChat.WxUrl2
				}
				if utils.NotEmpty(enMeeting.MeetingSysId) {
					reqAPiSign.MeetingNo = enMeeting.MeetingSysId
				} else {
					if req.OrderStatus == 2 {

						if strings.Contains(WxUrl2, "open.feishu.cn") {
							cardMsg := feishu.NewMdCardMsg("英文会议报名同步会议系统失败", "blue")
							cardMsg.AppendMd(`>会议名称：%v
>购票人first name：%s 
>购票人last name：%s 
>报名人联系邮箱：%v
>订单号：%v
>会议系统编号：%v
>报错信息：%v
>时间: %s`, enMeeting.CnName, registerList[0].FirstName, registerList[0].LastName, registerList[0].Email, registerList[0].OrderId, reqAPiSign.MeetingNo, "该会议未设置绑定会议系统", utils.GetCurDateTime())
							weChat.Send2(cardMsg, WxUrl2)
						} else {
							markdown := fmt.Sprintf(`
						## 英文会议报名同步会议系统失败
							>会议名称：%v
							>购票人first name：%s 
							>购票人last name：%s 
							>报名人联系邮箱：%v
							>订单号：%v
							>会议系统编号：%v
							>报错信息：%v
							>时间：%s`, enMeeting.CnName, registerList[0].FirstName, registerList[0].LastName, registerList[0].Email, registerList[0].OrderId, reqAPiSign.MeetingNo, "该会议未设置绑定会议系统", utils.GetCurDateTime())

							weChat.Send(markdown, WxUrl2)
						}
					}
				}
			}
		} else {
			if enMeeting.ID <= 0 {
				WxUrl2 = weChat.WxUrl
				if req.OrderStatus == 2 {
					markdown := fmt.Sprintf(`
					## 英文会议报名同步会议系统失败
						>会议名称：%v
						>购票人first name：%s 
						>购票人last name：%s 
						>报名人联系邮箱：%v
						>订单号：%v
						>会议系统编号：%v
						>报错信息：%v
						>时间：%s`, enMeeting.CnName, registerList[0].FirstName, registerList[0].LastName, registerList[0].Email, registerList[0].OrderId, reqAPiSign.MeetingNo, "该会议未设置绑定会议系统", utils.GetCurDateTime())

					cardMsg := feishu.NewMdCardMsg("英文会议报名同步会议系统失败", "blue")
					cardMsg.AppendMd(`>会议名称：%v
>购票人first name：%s 
>购票人last name：%s 
>报名人联系邮箱：%v
>订单号：%v
>会议系统编号：%v
>报错信息：%v
>时间: %s`, enMeeting.CnName, registerList[0].FirstName, registerList[0].LastName, registerList[0].Email, registerList[0].OrderId, reqAPiSign.MeetingNo, "该会议未设置绑定会议系统", utils.GetCurDateTime())

					if strings.Contains(WxUrl2, "open.feishu.cn") {
						weChat.Send2(cardMsg, WxUrl2)
					} else {
						weChat.Send(markdown, WxUrl2)
					}
				}

			} else {
				if utils.NotEmpty(enMeeting.QwxUrl) {
					WxUrl2 = enMeeting.QwxUrl
				} else {
					WxUrl2 = weChat.WxUrl
				}
				if utils.NotEmpty(enMeeting.MeetingSysId) {
					reqAPiSign.MeetingNo = enMeeting.MeetingSysId
				} else {
					if req.OrderStatus == 2 {
						markdown := fmt.Sprintf(`
						## 英文会议报名同步会议系统失败
							>会议名称：%v
							>购票人first name：%s 
							>购票人last name：%s 
							>报名人联系邮箱：%v
							>订单号：%v
							>会议系统编号：%v
							>报错信息：%v
							>时间：%s`, enMeeting.CnName, registerList[0].FirstName, registerList[0].LastName, registerList[0].Email, registerList[0].OrderId, reqAPiSign.MeetingNo, "该会议未设置绑定会议系统", utils.GetCurDateTime())

						cardMsg := feishu.NewMdCardMsg("英文会议报名同步会议系统失败", "blue")
						cardMsg.AppendMd(`>会议名称：%v
>购票人first name：%s 
>购票人last name：%s 
>报名人联系邮箱：%v
>订单号：%v
>会议系统编号：%v
>报错信息：%v
>时间: %s`, enMeeting.CnName, registerList[0].FirstName, registerList[0].LastName, registerList[0].Email, registerList[0].OrderId, reqAPiSign.MeetingNo, "该会议未设置绑定会议系统", utils.GetCurDateTime())

						if strings.Contains(WxUrl2, "open.feishu.cn") {
							weChat.Send2(cardMsg, WxUrl2)
						} else {
							weChat.Send(markdown, WxUrl2)
						}
					}
				}
			}
		}

		if utils.NotEmpty(reqAPiSign.MeetingNo) {
			if req.OrderStatus == 2 {
				if req.Quantity > len(userList) {
					if utils.IsEmpty(reqAPiSign.LastName) {
						markdown := fmt.Sprintf(`
						## 会议购买人数多于报名人数
							>会议名称：%v
							>购票人姓名：%s
							>报名人手机号：%v
							>订单号：%v
							>会议系统编号：%v
							>报错信息：%v
							>时间：%s`, enMeeting.CnName, registerList[0].FirstName, registerList[0].Mobile, registerList[0].OrderId, reqAPiSign.MeetingNo, "购买人数多于报名人数", utils.GetCurDateTime())

						cardMsg := feishu.NewMdCardMsg("会议购买人数多于报名人数", "blue")
						cardMsg.AppendMd(`>会议名称：%v
>购票人姓名：%s
>报名人手机号：%v
>订单号：%v
>会议系统编号：%v
>报错信息：%v
>时间: %s`, enMeeting.CnName, registerList[0].FirstName, registerList[0].Mobile, registerList[0].OrderId, reqAPiSign.MeetingNo, "购买人数多于报名人数", utils.GetCurDateTime())

						if strings.Contains(WxUrl2, "open.feishu.cn") {
							weChat.Send2(cardMsg, WxUrl2)
						} else {
							weChat.Send(markdown, WxUrl2)
						}

					} else {
						markdown := fmt.Sprintf(`
						## 会议购买人数多于报名人数
							>会议名称：%v
							>购票人first name：%s 
							>购票人last name：%s 
							>报名人联系邮箱：%v
							>订单号：%v
							>会议系统编号：%v
							>报错信息：%v
							>时间：%s`, enMeeting.CnName, registerList[0].FirstName, registerList[0].Email, registerList[0].LastName, registerList[0].OrderId, reqAPiSign.MeetingNo, "购买人数多于报名人数", utils.GetCurDateTime())

						cardMsg := feishu.NewMdCardMsg("会议购买人数多于报名人数", "blue")
						cardMsg.AppendMd(`>会议名称：%v
>购票人first name：%s 
>购票人last name：%s 
>报名人联系邮箱：%v
>订单号：%v
>会议系统编号：%v
>报错信息：%v
>时间: %s`, enMeeting.CnName, registerList[0].FirstName, registerList[0].Email, registerList[0].LastName, registerList[0].OrderId, reqAPiSign.MeetingNo, "购买人数多于报名人数", utils.GetCurDateTime())

						if strings.Contains(WxUrl2, "open.feishu.cn") {
							weChat.Send2(cardMsg, WxUrl2)
						} else {
							weChat.Send(markdown, WxUrl2)
						}

					}
					logger.Warnning("购买人数多于报名人数：")
				}

				for i, registerUser := range userList {
					logger.Info("k：", i)
					logger.Info("Quantity：", req.Quantity)

					if i+1 > req.Quantity {
						logger.Info("i：", i)
						logger.Info("Quantity：", req.Quantity)

						if utils.IsEmpty(registerUser.LastName) {
							markdown := fmt.Sprintf(`
						## 会议购买人数少于报名人数
							>会议名称：%v
							>购票人姓名：%s 
							>报名人手机号：%v
							>订单号：%v
							>会议系统编号：%v
							>报错信息：%v
							>时间：%s`, enMeeting.CnName, registerList[0].FirstName, registerList[0].Mobile, registerList[0].OrderId, reqAPiSign.MeetingNo, "购买人数少于报名人数", utils.GetCurDateTime())

							cardMsg := feishu.NewMdCardMsg("会议购买人数少于报名人数", "blue")
							cardMsg.AppendMd(`>会议名称：%v
>购票人姓名：%s 
>报名人手机号：%v
>订单号：%v
>会议系统编号：%v
>报错信息：%v
>时间: %s`, enMeeting.CnName, registerList[0].FirstName, registerList[0].Mobile, registerList[0].OrderId, reqAPiSign.MeetingNo, "购买人数少于报名人数", utils.GetCurDateTime())

							if strings.Contains(WxUrl2, "open.feishu.cn") {
								weChat.Send2(cardMsg, WxUrl2)
							} else {
								weChat.Send(markdown, WxUrl2)
							}

						} else {
							markdown := fmt.Sprintf(`
						## 会议购买人数少于报名人数
							>会议名称：%v
							>购票人first name：%s 
							>购票人last name：%s 
							>报名人联系邮箱：%v
							>订单号：%v
							>会议系统编号：%v
							>报错信息：%v
							>时间：%s`, enMeeting.CnName, registerList[0].FirstName, registerList[0].LastName, registerList[0].Email, registerList[0].OrderId, reqAPiSign.MeetingNo, "购买人数少于报名人数", utils.GetCurDateTime())

							cardMsg := feishu.NewMdCardMsg("会议购买人数少于报名人数", "blue")
							cardMsg.AppendMd(`>会议名称：%v
>购票人first name：%s 
>购票人last name：%s 
>报名人手机号：%v
>订单号：%v
>会议系统编号：%v
>报错信息：%v
>时间: %s`, enMeeting.CnName, registerList[0].FirstName, registerList[0].LastName, registerList[0].Mobile, registerList[0].OrderId, reqAPiSign.MeetingNo, "购买人数少于报名人数", utils.GetCurDateTime())

							if strings.Contains(WxUrl2, "open.feishu.cn") {
								weChat.Send2(cardMsg, WxUrl2)
							} else {
								weChat.Send(markdown, WxUrl2)
							}

						}
						logger.Warnning("购买人数少于报名人数：")
						break
					}

					reqAPiSign.UserId = registerList[0].UserId
					reqAPiSign.FirstName = registerUser.FirstName
					reqAPiSign.LastName = registerUser.LastName
					reqAPiSign.Email = registerUser.Email
					reqAPiSign.Mobile = registerUser.Mobile
					reqAPiSign.Company = registerUser.Company
					reqAPiSign.JobTitle = registerUser.JobTitle
					reqAPiSign.Identity = "1"

					reqAPiSign.ServiceId = req.ServiceID
					reqAPiSign.OrderId = req.OrderNumber
					reqAPiSign.OrderCount = strconv.Itoa(len(userList))
					reqAPiSign.OrderStatus = strconv.Itoa(req.OrderStatus)
					reqAPiSign.PayAmount = s
					reqAPiSign.Source = req.Source
					reqAPiSign.PayMethod = req.PayMethod
					reqAPiSign.CurrencyUnit = req.CurrencyUnit
					m, apiErr := rpc.AddAPiSignUpOrderChange(reqAPiSign)

					ReqLog, err_ = json.Marshal(reqAPiSign)
					RespLog, err_ = json.Marshal(m)
					RegisterLog = model.RegisterLog{
						RegisterId: req.Id,
						ReqLog:     string(ReqLog),
						RespLog:    string(RespLog),
						LogType:    2,
					}
					if apiErr != nil {
						logger.Warnning("MeetingNo: ", reqAPiSign.MeetingNo)
						RegisterLog.ErrLog = apiErr.Error()
						markdown := fmt.Sprintf(`
						## 英文会议报名同步会议系统失败
							>会议名称：%v
							>购票人first name：%s 
							>购票人last name：%s 
							>报名人联系邮箱：%v
							>订单号：%v
							>会议系统编号：%v
							>报错信息：%v
							>时间：%s`, enMeeting.CnName, registerList[0].FirstName, registerList[0].LastName, registerList[0].Email, registerList[0].OrderId, reqAPiSign.MeetingNo, apiErr.Error(), utils.GetCurDateTime())

						cardMsg := feishu.NewMdCardMsg("英文会议报名同步会议系统失败", "blue")
						cardMsg.AppendMd(`>会议名称：%v
>购票人first name：%s 
>购票人last name：%s 
>报名人联系邮箱：%v
>订单号：%v
>会议系统编号：%v
>报错信息：%v
>时间: %s`, enMeeting.CnName, registerList[0].FirstName, registerList[0].LastName, registerList[0].Email, registerList[0].OrderId, reqAPiSign.MeetingNo, apiErr.Error(), utils.GetCurDateTime())

						if strings.Contains(WxUrl2, "open.feishu.cn") {
							weChat.Send2(cardMsg, WxUrl2)
						} else {
							weChat.Send(markdown, WxUrl2)
						}

					}
					if m.Code != 0 {
						logger.Warnning("MeetingNo: ", reqAPiSign.MeetingNo)
						RegisterLog.ErrLog = m.Msg
						markdown := fmt.Sprintf(`
						## 英文会议报名同步会议系统失败
							>会议名称：%v
							>购票人first name：%s 
							>购票人last name：%s 
							>购票人联系邮箱：%v【
							>订单号：%v
							>会议系统编号：%v
							>报错信息：%v
							>时间：%s`, enMeeting.CnName, registerList[0].FirstName, registerList[0].LastName, registerList[0].Email, registerList[0].OrderId, reqAPiSign.MeetingNo, m.Msg, utils.GetCurDateTime())

						cardMsg := feishu.NewMdCardMsg("英文会议报名同步会议系统失败", "blue")
						cardMsg.AppendMd(`>会议名称：%v
>购票人first name：%s 
>购票人last name：%s 
>购票人联系邮箱：%v
>订单号：%v
>会议系统编号：%v
>报错信息：%v
>时间: %s`, enMeeting.CnName, registerList[0].FirstName, registerList[0].LastName, registerList[0].Email, registerList[0].OrderId, reqAPiSign.MeetingNo, m.Msg, utils.GetCurDateTime())

						if strings.Contains(WxUrl2, "open.feishu.cn") {
							weChat.Send2(cardMsg, WxUrl2)
						} else {
							weChat.Send(markdown, WxUrl2)
						}
					}
					RegisterLog.CreateTime = time.Now()
					db.AddMeetingRegisterLog(nil, &RegisterLog)
				}
			}
		} else {
			logger.Error(enMeeting.CnName + "会议未设置绑定会议系统")
		}

	} else {
		logger.Warnning("nil req:", req)
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}

	CurrencyUnit := " 元"
	if req.CurrencyUnit == 2 {
		CurrencyUnit = " 美元"
	}
	if req.CurrencyUnit == 3 {
		CurrencyUnit = " 欧元"
	}
	if req.OrderStatus == 1 {
		markdown := fmt.Sprintf(`
				## 英文会议订单创建提醒 
			>报名记录ID：%v
			>订单号：%s
			>购买数量：%v
			>订单状态：%v
			>时间：%s`, req.Id, req.OrderNumber, req.Quantity, req.OrderStatus, utils.GetCurDateTime())
		weChat.Send(markdown, weChat.WxUrl)

		if utils.IsEmpty(registerList[0].LastName) {
			markdown2 := fmt.Sprintf(`
				## <font color="red">已创建订单通知 </font>
					>会议名：%v
					>购票人姓名：%s 
					>购票人手机号：%v
					>购票人公司：%v
					>报名数量：%v
					>订单号：%v
					>订单状态：%v
					>订单数量：%v
					>订单金额：%s
					>时间：%s`, enMeeting.CnName, registerList[0].FirstName, registerList[0].Mobile, registerList[0].Company, len(userList), req.OrderNumber, "待支付", req.Quantity, strconv.FormatFloat(req.Amount, 'f', 2, 64)+CurrencyUnit, utils.GetCurDateTime())

			cardMsg := feishu.NewMdCardMsg("已创建订单通知", "blue")
			cardMsg.AppendMd(`>会议名：%s
>购票人姓名：%s 
>购票人手机号：%v
>购票人公司：%v
>报名数量：%v
>订单号：%v
>订单状态：%v
>订单数量：%v
>订单金额：%s
>时间：%s`, enMeeting.CnName, registerList[0].FirstName, registerList[0].Mobile, registerList[0].Company, len(userList), req.OrderNumber, "待支付", req.Quantity, strconv.FormatFloat(req.Amount, 'f', 2, 64)+CurrencyUnit, utils.GetCurDateTime())

			if strings.Contains(WxUrl2, "open.feishu.cn") {
				weChat.Send2(cardMsg, WxUrl2)
			} else {
				weChat.Send(markdown2, WxUrl2)
			}

		} else {
			markdown2 := fmt.Sprintf(`
				## <font color="red">已创建订单通知 </font>
					>会议名：%v
					>购票人first name：%s 
					>购票人last name：%s 
					>购票人邮箱：%s
					>购票人公司：%s
					>报名数量：%v
					>订单号：%v
					>订单状态：%v
					>订单数量：%v
					>订单金额：%s
					>时间：%s`, enMeeting.CnName, registerList[0].FirstName, registerList[0].LastName, registerList[0].Email, registerList[0].Company, len(userList), req.OrderNumber, "待支付", req.Quantity, strconv.FormatFloat(req.Amount, 'f', 2, 64)+CurrencyUnit, utils.GetCurDateTime())

			cardMsg := feishu.NewMdCardMsg("已创建订单通知", "blue")
			cardMsg.AppendMd(`>会议名：%s
>购票人first name：%s 
>购票人last name：%s 
>购票人邮箱：%s
>购票人公司：%s
>报名数量：%v
>订单号：%v
>订单状态：%v
>订单数量：%v
>订单金额：%s
>时间：%s`, enMeeting.CnName, registerList[0].FirstName, registerList[0].LastName, registerList[0].Email, registerList[0].Company, len(userList), req.OrderNumber, "待支付", req.Quantity, strconv.FormatFloat(req.Amount, 'f', 2, 64)+CurrencyUnit, utils.GetCurDateTime())

			if strings.Contains(WxUrl2, "open.feishu.cn") {
				weChat.Send2(cardMsg, WxUrl2)
			} else {
				weChat.Send(markdown2, WxUrl2)
			}
		}

	} else {
		markdown := fmt.Sprintf(`
				## 英文会议订单完成提醒
			>报名记录ID：%v
			>订单号：%s
			>购买数量：%v
			>订单状态：%v
			>时间：%s`, req.Id, req.OrderNumber, req.Quantity, req.OrderStatus, utils.GetCurDateTime())
		weChat.Send(markdown, weChat.WxUrl)

		if utils.IsEmpty(registerList[0].LastName) {

			cardMsg := feishu.NewMdCardMsg("申请参会已支付通知", "blue")
			cardMsg.AppendMd(`>会议名：%s
>购票人姓名：%s 
>购票人手机号：%v`, enMeeting.CnName, registerList[0].FirstName, registerList[0].Mobile)

			markdown2 := fmt.Sprintf(`## <font color="green">申请参会已支付通知 </font>
			>会议名：%s 
			>购票人姓名：%s 
			>购票人手机号：%v`, enMeeting.CnName, registerList[0].FirstName, registerList[0].Mobile)

			for _, registerUser := range userList {
				markdown2 += fmt.Sprintf(`
			>参会人姓名：%s 
			>参会人手机号：%s 
			>参会人邮箱：%s
			>参会人公司：%s`, registerUser.FirstName, registerUser.Mobile, registerUser.Email, registerUser.Company)

				cardMsg.AppendMd(`
>参会人姓名：%s 
>参会人手机号：%s 
>参会人邮箱：%s
>参会人公司：%s`, registerUser.FirstName, registerUser.Mobile, registerUser.Email, registerUser.Company)

			}

			markdown2 += fmt.Sprintf(`
			>报名数量：%v 
			>订单号：%v 
			>订单状态：%v 
			>订单数量：%v 、
			>订单金额：%v 
			>时间：%s`, len(userList), req.OrderNumber, "已支付", req.Quantity, strconv.FormatFloat(req.Amount, 'f', 2, 64)+CurrencyUnit, utils.GetCurDateTime())

			cardMsg.AppendMd(`
>报名数量：%v 
>订单号：%v 
>订单状态：%v 
>订单数量：%v 、
>订单金额：%v 
>时间：%s`, len(userList), req.OrderNumber, "已支付", req.Quantity, strconv.FormatFloat(req.Amount, 'f', 2, 64)+CurrencyUnit, utils.GetCurDateTime())

			if strings.Contains(WxUrl2, "open.feishu.cn") {
				weChat.Send2(cardMsg, WxUrl2)
			} else {
				weChat.Send(markdown2, WxUrl2)
			}

		} else {

			cardMsg := feishu.NewMdCardMsg("申请参会已支付通知", "blue")
			cardMsg.AppendMd(`>会议名：%s
>购票人first name：%s 
>购票人last name：%s 
>购票人手机号：%s 
>购票人邮箱：%s`, enMeeting.CnName, registerList[0].FirstName, registerList[0].LastName, registerList[0].Mobile, registerList[0].Email)

			markdown2 := fmt.Sprintf(`## <font color="green">申请参会已支付通知 </font>
			>会议名：%s
			>购票人first name：%s 
			>购票人last name：%s 
			>购票人手机号：%s 
			>购票人邮箱：%s`, enMeeting.CnName, registerList[0].FirstName, registerList[0].LastName, registerList[0].Mobile, registerList[0].Email)

			for _, registerUser := range userList {
				markdown2 += fmt.Sprintf(`
				>参会人first name：%s 
				>参会人last name：%s 
				>参会人手机号：%s 
				>参会人邮箱：%s
				>参会人公司：%s`, registerUser.FirstName, registerUser.LastName, registerUser.Mobile, registerUser.Email, registerUser.Company)

				cardMsg.AppendMd(`
>参会人first name：%s 
>参会人last name：%s 
>参会人手机号：%s 
>参会人邮箱：%s
>参会人公司：%s`, registerUser.FirstName, registerUser.LastName, registerUser.Mobile, registerUser.Email, registerUser.Company)

			}
			markdown2 += fmt.Sprintf(`
			>报名数量：%v 
			>订单号：%v 
			>订单状态：%v 
			>订单数量：%v 
			>订单金额：%v 
			>时间：%s`, len(userList), req.OrderNumber, "已支付", req.Quantity, strconv.FormatFloat(req.Amount, 'f', 2, 64)+CurrencyUnit, utils.GetCurDateTime())

			cardMsg.AppendMd(`
>报名数量：%v 
>订单号：%v 
>订单状态：%v 
>订单数量：%v 
>订单金额：%v 
>时间：%s`, len(userList), req.OrderNumber, "已支付", req.Quantity, strconv.FormatFloat(req.Amount, 'f', 2, 64)+CurrencyUnit, utils.GetCurDateTime())

			if strings.Contains(WxUrl2, "open.feishu.cn") {
				weChat.Send2(cardMsg, WxUrl2)
			} else {
				weChat.Send(markdown2, WxUrl2)
			}

		}

		logger.Warnning(err)
	}

}

// @Summary		    服务修改回调通知
// @Description	    服务修改回调通知API
// @Tags			内网接口-订单回调
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM_auth_token	header		string				                false    "SMM_auth_token"
// @Param			payload		body	protocol.ReqAddConferenceRegister     true    "json body"
// @Success		    200			{object}	protocol.Response{}                 "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                 "failed，code != 0"
// @Router	        /conference_ticket_price/update  [post]
func AdminUpdMeetingTicketPrice(c *gin.Context) {
	var (
		req protocol.ReqAdminSaveMeetingTicketPrice
		err protocol.ResError
	)
	defer func() {
		logger.Warnning("conference_ticket_price:", err)
		wrapResp(c, 0, addCallback, protocol.ResError{})
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}

	adminInfo, exist := c.Get(constant.AdminInfoKey)
	if exist {
		req.AdminEmail = adminInfo.(*admin.User).FullName
	}
	if utils.NotEmpty(req.ServiceId) {
		resp, err_ := rpc.GetServiceMeetingTicketPrice(req.ServiceId)
		if err_ != nil {
			err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
			return
		}
		if len(resp.ServiceList) > 0 {
			priceServices, _, err_ := db.GetTicketPriceServiceIds(nil, req.ServiceId, resp.ServiceList[0].AppName)
			if err_ != nil {
				logger.Error("GetServiceMeetingTicketPrice is err ", err_.Error())
				err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
				return
			}
			for _, service := range priceServices {

				var standards []protocol.Standards

				if len(resp.ServiceList[0].PriceTypeList) > 0 {
					standard := protocol.Standards{
						Delegates:    "1 Delegate",
						CurrencyUnit: resp.ServiceList[0].CurrencyUnit,
						EarlyBird:    resp.ServiceList[0].PriceTypeList[0].Price,
					}
					if service.CnServiceId == req.ServiceId {
						standard.Delegates = strconv.Itoa(1) + "人"
					}
					standards = append(standards, standard)
				}

				if service.Type == 0 {
					kNum := -1
					for i := 2; i < service.CnMaximum+1; i++ {
						isTrue := false
						if len(resp.ServiceList[0].SalesStrategyList) > 0 {

							for k, salesStrategy := range resp.ServiceList[0].SalesStrategyList {

								if i == salesStrategy.RequireNum {

									price := decimal.NewFromFloat(resp.ServiceList[0].SalesStrategyList[k].UnitPrice)

									fromInt := decimal.NewFromInt(int64(i))

									sumPrice := price.Mul(fromInt)
									floatResult, _ := sumPrice.Float64()

									standard := protocol.Standards{
										Delegates:    strconv.Itoa(salesStrategy.RequireNum) + " Delegates",
										CurrencyUnit: resp.ServiceList[0].CurrencyUnit,
										EarlyBird:    floatResult,
									}
									if service.CnServiceId == req.ServiceId {
										standard.Delegates = strconv.Itoa(salesStrategy.RequireNum) + "人"
									}
									standards = append(standards, standard)
									isTrue = true
									kNum = k
								}
							}

							if !isTrue {
								if i == 2 || kNum == -1 {
									price := decimal.NewFromFloat(resp.ServiceList[0].PriceTypeList[0].Price)

									fromInt := decimal.NewFromInt(int64(i))

									sumPrice := price.Mul(fromInt)
									floatResult, _ := sumPrice.Float64()

									standard := protocol.Standards{
										Delegates:    strconv.Itoa(i) + " Delegates",
										CurrencyUnit: resp.ServiceList[0].CurrencyUnit,
										EarlyBird:    floatResult,
									}
									if service.CnServiceId == req.ServiceId {
										standard.Delegates = strconv.Itoa(i) + "人"
									}
									standards = append(standards, standard)
								} else {
									price := decimal.NewFromFloat(resp.ServiceList[0].SalesStrategyList[kNum].UnitPrice)

									fromInt := decimal.NewFromInt(int64(i))

									sumPrice := price.Mul(fromInt)
									floatResult, _ := sumPrice.Float64()

									standard := protocol.Standards{
										Delegates:    strconv.Itoa(i) + " Delegates",
										CurrencyUnit: resp.ServiceList[0].CurrencyUnit,
										EarlyBird:    floatResult,
									}
									if service.CnServiceId == req.ServiceId {
										standard.Delegates = strconv.Itoa(i) + "人"
									}
									standards = append(standards, standard)
								}
							}

						} else {

							fromInt := decimal.NewFromInt(int64(i))

							price := decimal.NewFromFloat(resp.ServiceList[0].PriceTypeList[0].Price)

							sumPrice := price.Mul(fromInt)
							floatResult, _ := sumPrice.Float64()

							standard := protocol.Standards{
								Delegates:    strconv.Itoa(i) + " Delegates",
								CurrencyUnit: resp.ServiceList[0].CurrencyUnit,
								EarlyBird:    floatResult,
							}
							if service.CnServiceId == req.ServiceId {
								standard.Delegates = strconv.Itoa(i) + "人"
							}
							standards = append(standards, standard)
						}
					}
				} else {
					for k, salesStrategy := range resp.ServiceList[0].SalesStrategyList {
						if 3 == salesStrategy.RequireNum {
							standard := protocol.Standards{
								Delegates:    strconv.Itoa(salesStrategy.RequireNum) + " Delegates",
								EarlyBird:    resp.ServiceList[0].SalesStrategyList[k].UnitPrice,
								CurrencyUnit: resp.ServiceList[0].CurrencyUnit,
							}
							if service.CnServiceId == req.ServiceId {
								standard.Delegates = strconv.Itoa(salesStrategy.RequireNum) + "人"
							}
							standards = append(standards, standard)
						}
					}
				}
				marshal, err_ := json.Marshal(standards)
				if err_ != nil {
					logger.Error("UpdateMeetingTicketPriceFee standards Marshal ", err_.Error())
					err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
					return
				}
				if utils.NotEmpty(string(marshal)) {
					if service.CnServiceId == req.ServiceId {
						service.CnStandardFee = string(marshal)
					} else {
						service.EnStandardFee = string(marshal)
					}
					err_ = db.SaveConferenceTicketPriceInfo(nil, &service)
					if err_ != nil {
						logger.Error("inner UpdateMeetingTicketPriceFee ", err_.Error())
						err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
						return
					}
				}
			}
		}
	}
}

// @Summary		    查询票种信息列表
// @Description	    查询票种信息列表API
// @Tags			前台-票和报名
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqWebConferenceTicketList					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.ResUserTicketPriceList}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						                "failed，code != 0"
// @Router			/user/tickets/list  [get]
func UserTicketPriceList(c *gin.Context) {
	var (
		req protocol.ReqWebConferenceTicketList
		res protocol.ResUserTicketPriceList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.UserConferenceTicketPriceList(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	return
}
