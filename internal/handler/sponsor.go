package handler

import (
	"conferencecenter/internal/errcode"
	"conferencecenter/internal/protocol"
	"conferencecenter/internal/service"
	"github.com/asaskevich/govalidator"
	"github.com/gin-gonic/gin"
)

// @Summary		    新增或修改大会赞助
// @Description	    新增或修改大会赞助API
// @Tags			管理后台-赞助
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			payload		formData	protocol.ReqAdminSaveSponsor     true    "json body"
// @Success		    200			{object}	protocol.Response{}                 "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                 "failed，code != 0"
// @Router	        /admin/sponsor/add_edit  [post]
func AdminAddEditConferenceSponsor(c *gin.Context) {
	var (
		req protocol.ReqAdminSaveSponsor
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if _, err_ = govalidator.ValidateStruct(req); err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	err_ = service.AdminAddEditConferenceSponsor(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
	return
}

// @Summary		    大会赞助详情
// @Description	    查询大会赞助API
// @Tags			管理后台-赞助
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			id   		path		int							                true	"展会id"
// @Success		    200			{object}	protocol.Response{data=protocol.RespConferenceSponsorInfo}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						            "failed，code != 0"
// @Router			/admin/sponsor/detail/{id}    [get]
func AdminQueryConferenceSponsorInfo(c *gin.Context) {
	var (
		res protocol.RespConferenceSponsorInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	paramId := c.Param("id")
	if paramId == "" {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, nil)
		return
	}
	res, err_ := service.QueryConferenceSponsorInfo(paramId)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
	return
}

// @Summary		    大会赞助详情
// @Description	    查询大会赞助API
// @Tags			前台-赞助
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM_auth_token	header		string										false   "SMM_auth_token"
// @Param			id   		path		int							                true	"展会id"
// @Success		    200			{object}	protocol.Response{data=protocol.RespConferenceSponsorInfo}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						            "failed，code != 0"
// @Router			/user/sponsor/detail/{id}    [get]
func UserQueryConferenceSponsorInfo(c *gin.Context) {
	var (
		res protocol.RespConferenceSponsorInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	paramId := c.Param("id")
	if paramId == "" {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, nil)
		return
	}
	res, err_ := service.QueryConferenceSponsorInfo(paramId)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
	return
}
