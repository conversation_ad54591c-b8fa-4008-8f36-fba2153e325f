package handler

import (
	"conferencecenter/internal/constant"
	"conferencecenter/internal/corecontext"
	"conferencecenter/internal/msgqueue"
	"conferencecenter/internal/rpc"
	"conferencecenter/internal/service"
	"context"
	"fmt"
	"git.code.tencent.com/smmit/smmbase/logger"
	"github.com/Shopify/sarama"
	"sync"
	"time"
)

// kafka 生产初始化
func InitKafkaProducer() {
	msgqueue.NewKafkaClient()
	return
}

// kafka 消费注册
func StartKafkaConsumer() {
	kfkConference := corecontext.Config().Kafka.Conference
	if kfkConference.Topics != nil && len(kfkConference.Topics) > 0 {
		for _, oneTopic := range kfkConference.Topics {
			if !service.GetConferenceProcessSwitch(kfkConference, oneTopic) {
				continue
			}
			gp := "conference_" + oneTopic
			lg := "conference_" + oneTopic
			switch oneTopic {
			case constant.TopicHandBookData:
				go KafkaConsumerProcessMTopic(kfkConference.Brokers, gp, []string{oneTopic}, service.HandBookDataProcess, lg)
			}
		}
	}
}

type KfConsumer struct {
	ready   chan bool
	process func(msg *sarama.ConsumerMessage) (string, error)
	logPre  string //用于标记消费
}

func (consumer *KfConsumer) Setup(sarama.ConsumerGroupSession) error {
	close(consumer.ready)
	return nil
}
func (consumer *KfConsumer) Cleanup(sarama.ConsumerGroupSession) error {
	_ = rpc.SendWechatMsg(corecontext.Config().NotifyConf.KafkaAddr, "clean up:"+consumer.logPre, nil)
	return nil
}
func (consumer *KfConsumer) ConsumeClaim(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	for message := range claim.Messages() {
		_ = logger.Debugf("%s,kafka: timestamp = %v, topic = %s", consumer.logPre, message.Timestamp, message.Topic)
		var (
			procState string
			err       error
		)
		if procState, err = consumer.process(message); err != nil {
			_ = logger.Error("notify kafka msg failure", err)
		} else {
			session.MarkMessage(message, "")
			session.Commit()
		}
		if err != nil {
			nMsg := fmt.Sprintf("%s, timestamp = %v, topic = %s \n result:%v", consumer.logPre, message.Timestamp, message.Topic, procState)
			go func() {
				_ = rpc.SendWechatMsg(corecontext.Config().NotifyConf.KafkaAddr, nMsg, err)
			}()
		}
	}
	return nil
}

func KafkaConsumerProcessMTopic(
	brokers []string,
	groupId string,
	topics []string,
	proc func(message *sarama.ConsumerMessage) (string, error),
	logTip string) {

	defer func() { recover() }()

	kafkaConfig := sarama.NewConfig()
	kafkaConfig.Consumer.Group.Rebalance.Strategy = sarama.BalanceStrategyRange
	kafkaConfig.Consumer.Group.Session.Timeout = 20 * time.Second
	kafkaConfig.Version = sarama.V1_0_0_0
	kafkaConfig.Consumer.Offsets.AutoCommit.Enable = false
	kafkaConfig.Consumer.Offsets.Initial = sarama.OffsetNewest //默认启动时从最新offset消费
	kafkaConfig.Consumer.Return.Errors = true

	ctx, _ := context.WithCancel(context.Background())
	consumer := KfConsumer{ready: make(chan bool), process: proc, logPre: logTip}

	_ = logger.Debug("kafka brokers:", brokers)
	kfClient, err := sarama.NewConsumerGroup(brokers, groupId, kafkaConfig)
	if err != nil {
		_ = logger.Error(logTip, "start consumer failure==>", err)
		return
	}

	go func() {
		for er := range kfClient.Errors() {
			_ = logger.Warnning("kafka error:", er)
			_ = rpc.SendWechatMsg(corecontext.Config().NotifyConf.KafkaAddr, "kafka error", er)
		}
	}()

	wg := &sync.WaitGroup{}
	wg.Add(1)
	go func() {
		defer wg.Done()
		for {
			if err := kfClient.Consume(ctx, topics, &consumer); err != nil {
				_ = logger.Warnning(logTip, "consumer error", err)
			}

			if ctx.Err() != nil {
				_ = logger.Debug(ctx.Err())
				_ = rpc.SendWechatMsg(corecontext.Config().NotifyConf.KafkaAddr, "ctx error", err)
				return
			}
			consumer.ready = make(chan bool)
		}
	}()

	rd := <-consumer.ready
	_ = logger.Debug(logTip, "consumer up and running...", rd, "group =>", groupId, "topic==>", topics)
	wg.Wait()
	if err = kfClient.Close(); err != nil {
		_ = logger.Error(logTip, "close consumer error", err)
	}
}
