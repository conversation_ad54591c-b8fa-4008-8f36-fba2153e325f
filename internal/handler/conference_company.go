package handler

import (
	"bytes"
	"conferencecenter/internal/constant"
	"conferencecenter/internal/errcode"
	"conferencecenter/internal/pkg/utils"
	"conferencecenter/internal/protocol"
	"conferencecenter/internal/service"
	"fmt"
	"git.code.tencent.com/smmit/smmbase/logger"
	"github.com/asaskevich/govalidator"
	"github.com/gin-gonic/gin"
	"github.com/jinzhu/now"
	"github.com/pkg/errors"
	"strconv"
)

// @Summary      获取展会价值数据 （2.0修改 ，之前页面叫申请参展）
// @Description     获取展会价值数据
// @Tags          管理后台-展商-展会价值
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		query		protocol.ReqConferenceId	        true	"展会id"
// @Success		200			{object}	protocol.Response{data=protocol.ApplyConference}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/company/apply/conference [GET]
func GetApplyConference(c *gin.Context) {
	var (
		req protocol.ReqConferenceId
		res protocol.ApplyConference
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if _, reqErr := govalidator.ValidateStruct(req); reqErr != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, reqErr)
		return
	}
	res, err_ = service.GetApplyConference(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary      获取展会价值数据 （2.0修改 ，之前页面叫申请参展）
// @Description     获取展会价值数据
// @Tags          前台-展商-展会价值
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		query		protocol.ReqConferenceId	        true	"展会id"
// @Success		200			{object}	protocol.Response{data=protocol.ApplyConference}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/user/company/apply/conference [GET]
func UserGetApplyConference(c *gin.Context) {
	var (
		req protocol.ReqConferenceId
		res protocol.ApplyConference
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetApplyConference(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary			保存展商展会价值 （2.0修改 ，之前页面叫申请参展）
// @Description		保存展商展会价值
// @Tags			管理后台-展商-展会价值
// @Accept			multipart/form-data
// @Produce			json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		formData		protocol.ApplyConference				        true	"申请参展"
// @Success		200			{object}	protocol.Response{}	   "ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/company/save/apply/conference [POST]
func SaveApplyConference(c *gin.Context) {
	var (
		req protocol.ApplyConference
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	req.OpUser = getAdminUser(c).FullName
	err_ = service.SaveApplyConference(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary      获取申请参展成功提示
// @Description     获取申请参展成功提示
// @Tags          管理后台-展商-申请参展
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		query		protocol.ReqConferenceId	        true	"展会id"
// @Success		200			{object}	protocol.Response{data=protocol.ExhibitionSuccess}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/company/exhibition/success [GET]
func GetExhibitionSuccess(c *gin.Context) {
	var (
		req protocol.ReqConferenceId
		res protocol.ExhibitionSuccess
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if _, reqErr := govalidator.ValidateStruct(req); reqErr != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, reqErr)
		return
	}
	res, err_ = service.GetExhibitionSuccess(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary			保存申请参展成功提示
// @Description		保存申请参展成功提示
// @Tags			管理后台-展商-申请参展
// @Accept			multipart/form-data
// @Produce			json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		formData		protocol.ExhibitionSuccess				        true	"申请参展"
// @Success		200			{object}	protocol.Response{}	   "ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/company/save/exhibition/success [POST]
func SaveExhibitionSuccess(c *gin.Context) {
	var (
		req protocol.ExhibitionSuccess
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	req.OpUser = getAdminUser(c).FullName
	err_ = service.SaveExhibitionSuccess(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary      获取申请参展成功提示
// @Description     获取申请参展成功提示
// @Tags          前台-展商-申请参展
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		query		protocol.ReqConferenceId	        true	"展会id"
// @Success		200			{object}	protocol.Response{data=protocol.ExhibitionSuccess}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/user/company/exhibition/success [GET]
func UserGetExhibitionSuccess(c *gin.Context) {
	var (
		req protocol.ReqConferenceId
		res protocol.ExhibitionSuccess
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetExhibitionSuccess(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary  		保存展商在线登记数据
// @Description  	保存展商在线登记数据
// @Tags  			管理后台-展商在线登记
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		query				protocol.OnlineRegister		        true	"Query Params"
// @Success		200			{object}	protocol.Response{}	  "ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/company/save/online/register [POST]
func SaveOnlineRegister(c *gin.Context) {
	var (
		req protocol.OnlineRegister
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	req.OpUser = getAdminUser(c).FullName
	err_ = service.SaveOnlineRegister(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		获取展商在线登记数据
// @Description	获取展商在线登记数据
// @Tags			管理后台-展商在线登记
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		query		protocol.ReqConferenceId				        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=protocol.OnlineRegister}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/company/online/register [GET]
func GetOnlineRegister(c *gin.Context) {
	var (
		req protocol.ReqConferenceId
		res protocol.OnlineRegister
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetOnlineRegister(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// 获取展商在线登记数据
//
//	@Summary		获取展商在线登记数据
//	@Description	获取展商在线登记数据
//	@Tags			前台-展商在线登记
//	@Accept			multipart/form-data
//	@Produce		json
//	@Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
//	@Param			req		query		protocol.ReqConferenceId				        true	"Query Params"
//	@Success		200			{object}	protocol.Response{data=protocol.OnlineRegister}	"ok，code = 0"
//	@Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
//	@Router			/user/company/online/register [GET]
func UserGetOnlineRegister(c *gin.Context) {
	var (
		req protocol.ReqConferenceId
		res protocol.OnlineRegister
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}

	res, err_ = service.GetOnlineRegister(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary	  添加展商手册
// @Description	添加展商手册
// @Tags			管理后台-展商手册
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req  	 query			protocol.CompanyHandbook			        true	"Query Params"
// @Success		200			{object}	protocol.Response{}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/company/save/handbook [POST]
func SaveCompanyHandbook(c *gin.Context) {
	var (
		req protocol.CompanyHandbook
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	req.OpUser = getAdminUser(c).FullName
	err_ = service.AddCompanyHandbook(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		获取展商手册列表
// @Description	获取展商手册列表
// @Tags			管理后台-展商手册
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		query		protocol.ReqConferenceId				        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=protocol.ResCompanyHandbook}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/company/handbook/list [GET]
func GetCompanyHandbookList(c *gin.Context) {
	var (
		req protocol.ReqConferenceId
		res protocol.ResCompanyHandbookList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	list, err_ := service.GetCompanyHandbookList(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
	res.List = list
}

// @Summary		根据id获取展商手册
// @Description	根据id获取展商手册
// @Tags			管理后台-展商手册
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"admin_token"
// @Param			req		query		protocol.ReqCompanyId				        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=protocol.CompanyHandbook}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/company/get/handbook [GET]
func GetCompanyHandbook(c *gin.Context) {
	var (
		req protocol.ReqCompanyId
		res protocol.CompanyHandbook
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetCompanyHandbook(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		获取展商手册列表
// @Description	获取展商手册列表
// @Tags			前台-展商手册
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		query		protocol.ReqConferenceId				        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=protocol.ResCompanyHandbookList}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/user/company/handbook/list [GET]
func UserGetCompanyHandbookList(c *gin.Context) {
	var (
		req protocol.ReqConferenceId
		res protocol.ResCompanyHandbookList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	list, err_ := service.GetCompanyHandbookList(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
	res.List = list
}

// @Summary		删除展商手册
// @Description	删除展商手册
// @Tags			管理后台-展商手册
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			id	query		protocol.ReqHandbookId				        true	"Query Params"
// @Success		200			{object}	protocol.Response{}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/company/delete/handbook [POST]
func DeleteCompanyHandbook(c *gin.Context) {
	var (
		req protocol.ReqHandbookId
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	user := getAdminUser(c).FullName
	err_ = service.DeleteCompanyHandbook(req.Id, user)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		更新展商手册
// @Description	更新展商手册
// @Tags			管理后台-展商手册
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req	 query	protocol.CompanyHandbook				        true	"Query Params"
// @Success		200			{object}	protocol.Response{}	   "ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/company/update/handbook [POST]
func UpdateCompanyHandbook(c *gin.Context) {
	var (
		req protocol.CompanyHandbook
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	req.OpUser = getAdminUser(c).FullName
	err_ = service.UpdateCompanyHandbook(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		保存展商手册页面内容
// @Description	保存展商手册页面内容
// @Tags			管理后台-展商手册
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		query		protocol.HandbookContent				        true	"Query Params"
// @Success		200			{object}	protocol.Response{}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/company/save/handbook/content [POST]
func SaveHandbookContent(c *gin.Context) {
	var (
		req protocol.HandbookContent
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	req.OpUser = getAdminUser(c).FullName
	err_ = service.SaveHandbookContent(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		获取展商手册页面内容
// @Description	获取展商手册页面内容
// @Tags			管理后台-展商手册
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"admin_token"
// @Param			req		query		protocol.ReqConferenceId				        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=protocol.HandbookContent}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/company/get/handbook/content [GET]
func GetHandbookContent(c *gin.Context) {
	var (
		req protocol.ReqConferenceId
		res protocol.HandbookContent
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if _, err_ = govalidator.ValidateStruct(req); err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetHandbookContent(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		前台获取展商手册页面内容
// @Description	前台获取展商手册页面内容
// @Tags			前台-展商手册
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"admin_token"
// @Param			req		query		protocol.ReqConferenceId				        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=protocol.HandbookContent}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			 /user/company/get/handbook/content [GET]
func UserGetHandbookContent(c *gin.Context) {
	var (
		req protocol.ReqConferenceId
		res protocol.HandbookContent
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetHandbookContent(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		保存现场搭建
// @Description	保存现场搭建
// @Tags			管理后台-展商现场搭建
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		query		protocol.SetUp				        true	"Query Params"
// @Success		200			{object}	protocol.Response{}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/company/save/set/up [POST]
func SaveSetUp(c *gin.Context) {
	var (
		req protocol.SetUp
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	req.OpUser = getAdminUser(c).FullName
	err_ = service.SaveSetUp(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		获取现场搭建时间
// @Description	获取现场搭建时间
// @Tags			管理后台-展商现场搭建
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		query			protocol.ReqConferenceId			        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=protocol.SetUp}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/company/get/set/up [GET]
func GetSetUp(c *gin.Context) {
	var (
		req protocol.ReqConferenceId
		res protocol.SetUp
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if _, err_ = govalidator.ValidateStruct(req); err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetSetUp(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		获取现场搭建时间
// @Description	获取现场搭建时间
// @Tags			前台-展商现场搭建
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		query			protocol.ReqConferenceId			        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=protocol.SetUp}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/user/company/get/set/up [GET]
func UserGetSetUp(c *gin.Context) {
	var (
		req protocol.ReqConferenceId
		res protocol.SetUp
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if _, err_ = govalidator.ValidateStruct(req); err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetSetUp(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		保存观展邀请函
// @Description	保存观展邀请函
// @Tags			管理后台-展商观展邀请函
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		query		protocol.Invitation				        true	"Query Params"
// @Success		200			{object}	protocol.Response{}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/company/save/invitation [POST]
func SaveInvitation(c *gin.Context) {
	var (
		req protocol.Invitation
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if _, err_ = govalidator.ValidateStruct(req); err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	req.OpUser = getAdminUser(c).FullName
	err_ = service.SaveInvitation(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		获取观展邀请函
// @Description	获取观展邀请函
// @Tags		管理后台-展商观展邀请函
// @Accept		multipart/form-data
// @Produce		json
// @Param		SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param		req		        query		protocol.ReqConferenceId				        true	"Query Params"
// @Success		200			    {object}	protocol.Response{data=protocol.Invitation}	            "ok，code = 0"
// @Failure		500			    {object}	protocol.Response{}						                "failed，code != 0"
// @Router			/admin/company/get/invitation [GET]
func GetInvitation(c *gin.Context) {
	var (
		req protocol.ReqConferenceId
		res protocol.Invitation
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if _, err_ = govalidator.ValidateStruct(req); err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetInvitation(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		获取观展邀请函
// @Description	获取观展邀请函
// @Tags		前台-展商观展邀请函
// @Accept		multipart/form-data
// @Produce		json
// @Param		SMM_auth_token	header		string									false	"SMM_auth_token"
// @Param		req		query		protocol.ReqConferenceId				        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=protocol.Invitation}	        "ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						            "failed，code != 0"
// @Router		/user/company/get/invitation [GET]
func UserGetInvitation(c *gin.Context) {
	var (
		req protocol.ReqConferenceId
		res protocol.Invitation
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if _, err_ = govalidator.ValidateStruct(req); err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetInvitation(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		保存展商常见问题类型
// @Description	保存展商常见问题类型
// @Tags		管理后台-展商常见问题
// @Accept		multipart/form-data
// @Produce		json
// @Param		SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param		req		        query		protocol.CommonQuestionType				        true	"Query Params"
// @Success		200			    {object}	protocol.Response{}	"ok，code = 0"
// @Failure		500			    {object}	protocol.Response{}						        "failed，code != 0"
// @Router		/admin/company/save/question/type [POST]
func SaveCompanyQuestionType(c *gin.Context) {
	var (
		req protocol.CommonQuestionType
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if _, err_ = govalidator.ValidateStruct(req); err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	req.OpUser = getAdminUser(c).FullName
	err_ = service.SaveCompanyQuestionType(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		获取展商常见问题类型列表
// @Description	获取展商常见问题类型列表
// @Tags		管理后台-展商常见问题
// @Accept		multipart/form-data
// @Produce		json
// @Param		SMM-ADMIN-TOKEN	header			string										    true	"SMM-ADMIN-TOKEN"
// @Param		req		query					protocol.ReqConferenceId				        true	"Query Params"
// @Success		200			{object}			protocol.Response{data=protocol.ResCommonQuestionType}	"ok，code = 0"
// @Failure		500			{object}			protocol.Response{}						        "failed，code != 0"
// @Router		/admin/company/get/question/type/list 	[GET]
func GetCompanyQuestionTypeList(c *gin.Context) {
	var (
		req protocol.ReqConferenceId
		res protocol.ResCommonQuestionType
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if _, err_ = govalidator.ValidateStruct(req); err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	list, err_ := service.GetCompanyQuestionTypeList(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
	res.List = list
}

// @Summary		根据id获取展商问题类型
// @Description	根据id获取展商问题类型
// @Tags		管理后台-展商常见问题
// @Accept		multipart/form-data
// @Produce		json
// @Param		SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param		req		        query		protocol.ReqQuestionTypeId				        true	"Query Params"
// @Success		200			    {object}	protocol.Response{data=protocol.CommonQuestionType}	    "ok，code = 0"
// @Failure		500			    {object}	protocol.Response{}						                "failed，code != 0"
// @Router		/admin/company/get/question/type	[GET]
func GetCompanyQuestionType(c *gin.Context) {
	var (
		req protocol.ReqQuestionTypeId
		res protocol.CommonQuestionType
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetCompanyQuestionType(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		删除展商常见问题类型
// @Description	删除展商常见问题类型
// @Tags		管理后台-展商常见问题
// @Accept		multipart/form-data
// @Produce		json
// @Param		SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param		req		        query		protocol.ReqQuestionTypeId				        true	"Query Params"
// @Success		200			    {object}	protocol.Response{}	                                    "ok，code = 0"
// @Failure		500			    {object}	protocol.Response{}						                "failed，code != 0"
// @Router		/admin/company/delete/question/type [POST]
func DeleteCompanyQuestionType(c *gin.Context) {
	var (
		req protocol.ReqQuestionTypeId
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if _, err_ = govalidator.ValidateStruct(req); err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	req.OpUser = getAdminUser(c).FullName
	err_ = service.DeleteCompanyQuestionType(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		编辑展商常见问题类型
// @Description	编辑展商常见问题类型
// @Tags		管理后台-展商常见问题
// @Accept		multipart/form-data
// @Produce		json
// @Param		SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param		req		        query		protocol.ReqEditCompanyQuestionType				        true	"Query Params"
// @Success		200			    {object}	protocol.Response{}	"ok，code = 0"
// @Failure		500			    {object}	protocol.Response{}						        "failed，code != 0"
// @Router		/admin/company/edit/question/type [POST]
func EditCompanyQuestionType(c *gin.Context) {
	var (
		req protocol.ReqEditCompanyQuestionType
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if _, err_ = govalidator.ValidateStruct(req); err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	req.OpUser = getAdminUser(c).FullName
	err_ = service.EditCompanyQuestionType(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		保存展商常见问题内容
// @Description	保存展商常见问题内容
// @Tags			管理后台-展商常见问题
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		query			protocol.QuestionContent			        true	"Query Params"
// @Success		200			{object}	protocol.Response{}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/company/save/question/content [POST]
func SaveCompanyQuestionContent(c *gin.Context) {
	var (
		req protocol.QuestionContent
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	req.OpUser = getAdminUser(c).FullName
	err_ = service.SaveCompanyQuestionContent(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		获得展商常见问题内容列表
// @Description	获得展商常见问题内容列表
// @Tags			管理后台-展商常见问题
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			questionTypeId		query			protocol.ReqQuestionTypeId			        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=protocol.ResCommonQuestionAnswer}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/company/get/question/content/list [GET]
func GetCompanyQuestionAnswerList(c *gin.Context) {
	var (
		questionTypeId protocol.ReqQuestionTypeId
		res            protocol.ResCommonQuestionAnswer
		err            protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&questionTypeId)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if _, err_ = govalidator.ValidateStruct(questionTypeId); err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	list, err_ := service.GetCompanyQuestionAnswerList(questionTypeId.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
	res.List = list
}

// @Summary		根据id获取展商问题答案
// @Description	根据id获取展商问题答案
// @Tags			管理后台-展商常见问题
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"admin_token"
// @Param			req		query		protocol.ReqQuestionContentId				        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=protocol.QuestionContent}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/company/get/question/content [GET]
func GetCompanyQuestionAnswer(c *gin.Context) {
	var (
		req protocol.ReqQuestionContentId
		res protocol.QuestionContent
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetCompanyQuestionAnswer(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		编辑展商常见问题内容
// @Description	编辑展商常见问题内容
// @Tags			管理后台-展商常见问题
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		query			protocol.CommonQuestionAnswerUpdate			        true	"Query Params"
// @Success		200			{object}	protocol.Response{}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/company/edit/question/content [POST]
func EditCompanyQuestionContent(c *gin.Context) {
	var (
		req protocol.CommonQuestionAnswerUpdate
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	req.OpUser = getAdminUser(c).FullName
	err_ = service.EditCompanyQuestionContent(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		删除展商常见问题内容
// @Description	删除展商常见问题内容
// @Tags		管理后台-展商常见问题
// @Accept		multipart/form-data
// @Produce		json
// @Param		SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param		req		        query		protocol.ReqQuestionContentId				    true	"Query Params"
// @Success		200			    {object}	protocol.Response{}	"ok，code = 0"
// @Failure		500			    {object}	protocol.Response{}						        "failed，code != 0"
// @Router		/admin/company/delete/question/content [POST]
func DeleteCompanyQuestionContent(c *gin.Context) {
	var (
		req protocol.ReqQuestionContentId
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if _, err_ = govalidator.ValidateStruct(req); err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	req.OpUser = getAdminUser(c).FullName
	err_ = service.DeleteCompanyQuestionContent(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary	获取展商常见问题
// @Description	获取展商常见问题
// @Tags		前台-展商常见问题
// @Accept		multipart/form-data
// @Produce		json
// @Param		SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param		req	            query 	    protocol.ReqConferenceId						true	"Query Params"
// @Success		200			    {object}	protocol.Response{data=protocol.ResCommonQA}	"ok，code = 0"
// @Failure		500			    {object}	protocol.Response{}						        "failed，code != 0"
// @Router		/user/company/qa/list [GET]
func UserGetCompanyQA(c *gin.Context) {
	var (
		req protocol.ReqConferenceId
		res []protocol.QuestionType
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if _, err_ = govalidator.ValidateStruct(req); err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetCompanyCommonQA(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// 发送展商手册变更通知
func SendHandBookNotify(c *gin.Context) {
	var (
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	idParam := c.PostForm("id")
	if idParam == "" {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, errors.New("展商手册ID必填"))
		return
	}
	id, err_ := strconv.Atoi(idParam)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	go func() {
		_ = service.SendHandBookChgKfkNotify(int64(id))
	}()
}

// @Summary		    查询订阅列表
// @Description	    查询订阅列表API
// @Tags			管理后台-通用
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										    true    "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqAdminSubscribeList  				false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.ResAdminSubscribeList}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						            "failed，code != 0"
// @Router			/admin/handbook/subscribe/list  [get]
func AdminQuerySubscribeList(c *gin.Context) {
	var (
		req protocol.ReqAdminSubscribeList
		res protocol.ResAdminSubscribeList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if req.StartTime != "" {
		startTime, err_ := utils.ParseDateAuto(req.StartTime)
		if err_ != nil {
			err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
			return
		}
		req.StartTime = now.New(startTime).BeginningOfDay().Format(constant.DateTime)
	}
	if req.EndTime != "" {
		endTime, err_ := utils.ParseDateAuto(req.EndTime)
		if err_ != nil {
			err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
			return
		}
		req.EndTime = now.New(endTime).EndOfDay().Format(constant.DateTime)
	}
	req.DefaultPage()
	total, dataLi, err_ := service.GetHandBookSubscribeList(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_DB_ERR, err_)
		return
	}
	res.List = dataLi
	res.Info.InitPageInfo(total, req.Page, req.PageSize)
	return
}

// @Summary		    导出订阅列表
// @Description	    导出订阅列表API
// @Tags			管理后台-通用
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string						    true    "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqAdminSubscribeList  	false	"Query Params"
// @Success		    200					                                                "excel文件"
// @Failure		    500			{object}	protocol.Response{}						    "failed，code != 0"
// @Router			/admin/handbook/subscribe/export  [get]
func AdminExportSubscribeList(c *gin.Context) {
	var (
		req        protocol.ReqAdminSubscribeList
		err        protocol.ResError
		exlData    *bytes.Reader
		dataLength int64
		fileName   = "订阅列表"
	)
	defer func() {
		wrapExcelResp(c, fmt.Sprintf("%s.xlsx", fileName), dataLength, exlData, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if req.StartTime != "" {
		startTime, err_ := utils.ParseDateAuto(req.StartTime)
		if err_ != nil {
			err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
			return
		}
		req.StartTime = now.New(startTime).BeginningOfDay().Format(constant.DateTime)
	}
	if req.EndTime != "" {
		endTime, err_ := utils.ParseDateAuto(req.EndTime)
		if err_ != nil {
			err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
			return
		}
		req.EndTime = now.New(endTime).EndOfDay().Format(constant.DateTime)
	}
	exlData, err_ = service.ExportHandBookSubscribeList(req)
	if err_ != nil || exlData == nil {
		err = protocol.NewResErrWithMsg(errcode.RESPONSE_CODE_PARAM_ERR, "参数错误", err_)
		return
	}
	dataLength = int64(exlData.Len())
	return
}

// @Summary		    订阅展商手册
// @Description	    订阅展商手册API
// @Tags			前台-展商
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM_auth_token	header		string						false   "SMM_auth_token"
// @Param			payload		formData	protocol.ReqUserSubscribe   true    "json body"
// @Success		    200			{object}	protocol.Response{}         "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}			"failed，code != 0"
// @Router			/user/handbook/subscribe [post]
func UserSubscribeHandBook(c *gin.Context) {
	var (
		req protocol.ReqUserSubscribe
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if req.Cellphone == "" && req.Email == "" {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, errors.New("请填写手机或邮箱"))
		return
	}
	if req.Cellphone != "" && req.Email != "" {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, errors.New("手机或邮箱只能填一个"))
	}
	if req.Email != "" {
		ok, err_ := utils.EmailValid(req.Email)
		if !ok {
			err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
			return
		}
	}
	err_ = service.SaveHandBookSubscribe(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_FAILED, err_)
	}

	return
}

// @Summary		获取问卷内容
// @Description	获取问卷内容
// @Tags			前台-观众
// @Accept			multipart/form-data
// @Produce		json
// @Param			AUTH-TOKEN	header		string										    true	"SMM_auth_token"
// @Param			payload		formData	protocol.ReqUserSubscribe   true    "json body"
// @Success		200			{object}	protocol.Response{data=protocol.QuestionPaper}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/user/audience/paper [GET]
func GetQuestionPaper(c *gin.Context) {
	var (
		req protocol.ReqUserGuestInformationList
		res protocol.QuestionPaper
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetQuestionPaper(req.ConferenceId)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary        提交问卷
// @Description	提交问卷
// @Tags			前台-观众
// @Accept			application/json
// @Produce		json
// @Param			AUTH-TOKEN	header		string										    true	"admin_token"
// @Param			req			body			protocol.ReqSaveQuestionPaper			        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=protocol.ResSubmitQuestionPaper}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/user/audience/submit/paper [POST]
func SubmitQuestionPaper(c *gin.Context) {
	var (
		req protocol.ReqSaveQuestionPaper
		res protocol.ResSubmitQuestionPaper
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.SubmitQuestionPaper(req)
	if err_ != nil {
		err = protocol.NewResErrWithMsg(errcode.RESPONSE_CODE_SYSTEM_ERR, res.ErrorString, err_)
		return
	}
}

// @Summary		提交免费观众信息
// @Description	提交免费观众信息
// @Tags			前台-观众
// @Accept			application/json
// @Produce		json
// @Param			AUTH-TOKEN	header		string										    true	"admin_token"
// @Param			req			body			protocol.ReqSaveFreeVisitorInfo			        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=protocol.ResSubmitFreeVisitor}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/user/audience/submit/free/visitor [POST]
func FreeVisitorSubmit(c *gin.Context) {
	var (
		req protocol.ReqSaveFreeVisitorInfo
		res protocol.ResSubmitFreeVisitor
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if req.CnOrEn == constant.Cn {
		if _, reqErr := govalidator.ValidateStruct(req.CnVisitor); reqErr != nil {
			err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, reqErr)
			return
		}
	}
	if req.CnOrEn == constant.En {
		if _, reqErr := govalidator.ValidateStruct(req.EnVisitor); reqErr != nil {
			err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, reqErr)
			return
		}
	}
	res, err_ = service.SubmitFreeVisitor(req)
	if err_ != nil {
		if err_.Error() == "验证码错误" {
			err = protocol.NewResErr(errcode.RESPONSE_CODE_CODE_INVALID, err_)
			logger.Warnning(err_)
			return
		}
		err = protocol.NewResErrWithMsg(errcode.RESPONSE_CODE_SYSTEM_ERR, res.ErrorString, err_)
		return
	}
}

// @Summary		获取所有渠道
// @Description	获取所有渠道
// @Tags			管理后台-免费观众
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"admin_token"
// @Success		200			{object}	protocol.Response{data=[]string}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/audience/fromId [GET]
func GetAllFromID(c *gin.Context) {
	var (
		res []string
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()

	res, err_ := service.GetAllFromID()
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		获取免费观众信息
// @Description	获取免费观众信息
// @Tags			管理后台-免费观众
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"admin_token"
// @Param			req		query			protocol.ReqVisitorID			        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=protocol.ResFreeVisitorInfo}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/audience/free/visitor [GET]
func GetConferenceFreeVisitorInfo(c *gin.Context) {
	var (
		req protocol.ReqVisitorID
		res protocol.ResFreeVisitorInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	errTemp := c.ShouldBind(&req)
	if errTemp != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, errTemp)
		return
	}
	req.ReqPage.DefaultPage()
	list, total, errTemp := service.GetFreeVisitorInfo(req)
	if errTemp != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, errTemp)
		return
	}
	res.Info.InitPageInfo(total, req.Page, req.PageSize)
	res.List = list
}

// @Summary		导出免费观众
// @Description	导出免费观众
// @Tags			管理后台-免费观众
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"admin_token"
// @Param			req		query		protocol.ReqVisitorID				        true	"Query Params"
// @Success		200			{object}	protocol.Response{}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/audience/export/free/visitor [GET]
func ExportFreeVisitorInfo(c *gin.Context) {
	var (
		req        protocol.ReqVisitorID
		err        protocol.ResError
		exlData    *bytes.Reader
		dataLength int64
		fileName   = "免费观众信息"
	)
	defer func() {
		wrapExcelResp(c, fmt.Sprintf("%s.xlsx", fileName), dataLength, exlData, err)
	}()
	errTemp := c.ShouldBind(&req)
	if errTemp != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, errTemp)
		return
	}
	if req.CnOrEn == constant.Cn {
		fileName = "国内免费观众信息"
	}
	if req.CnOrEn == constant.En {
		fileName = "海外免费观众信息"
	}
	req.ReqPage.DefaultPage()
	exlData, err_ := service.ExportFreeVisitorInfo(req)
	if err_ != nil || exlData == nil {
		err = protocol.NewResErrWithMsg(errcode.RESPONSE_CODE_PARAM_ERR, "参数错误", err_)
		return
	}
	dataLength = int64(exlData.Len())

}
