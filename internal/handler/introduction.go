package handler

import (
	"conferencecenter/internal/errcode"
	"conferencecenter/internal/protocol"
	"conferencecenter/internal/service"
	"github.com/asaskevich/govalidator"
	"github.com/gin-gonic/gin"
)

// @Summary		    大会介绍和介绍视频详情
// @Description	    查询大会介绍和介绍视频API
// @Tags			管理后台-首页其他-大会介绍和介绍视频
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										false   "auth_token"
// @Param			payload		formData	protocol.ReqGetIntroductionInfo     true    "json body"
// @Success		    200			{object}	protocol.Response{data=protocol.RespIntroductionInfo}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						            "failed，code != 0"
// @Router			/admin/introduction/detail/id    [get]
func AdminQueryConferenceIntroductionInfo(c *gin.Context) {
	var (
		req protocol.ReqGetIntroductionInfo
		res protocol.RespIntroductionInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetConferenceIntroductionInfo(req.ConferenceId)
	return
}

// @Summary		    新增或修改大会介绍和介绍视频
// @Description	    新增或修改大会介绍和介绍视频API
// @Tags			管理后台-首页其他-大会介绍和介绍视频
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string				                true    "SMM-ADMIN-TOKEN"
// @Param			payload		formData	protocol.ReqAdminSaveIntroduction     true    "json body"
// @Success		    200			{object}	protocol.Response{}                 "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                 "failed，code != 0"
// @Router	        /admin/introduction/add_edit  [post]
func AdminAddEditConferenceIntroduction(c *gin.Context) {
	var (
		req protocol.ReqAdminSaveIntroduction
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if _, reqErr := govalidator.ValidateStruct(req); reqErr != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, reqErr)
		return
	}
	_, err_ = service.AddEditConferenceIntroduction(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		    大会介绍数据列表
// @Description	    大会介绍数据列表API
// @Tags			管理后台-首页其他-数据
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqGetIntroductionList					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.ResAdminConferenceDataList}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						                "failed，code != 0"
// @Router			/admin/data/list  [get]
func AdminConferenceIntroductionDataList(c *gin.Context) {
	var (
		req protocol.ReqGetIntroductionList
		res protocol.ResAdminConferenceDataList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	list, total, err_ := service.QueryConferenceDataList(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
	res.List = list
	res.Info.InitPageInfo(total, req.Page, req.PageSize)
	return
}

// @Summary		    大会介绍数据详情
// @Description	    查询大会介绍数据API
// @Tags			管理后台-首页其他-数据
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										false   "auth_token"
// @Param			payload		    formData	protocol.ReqGetIntroductionDataInfo     true    "json body"
// @Success		    200			    {object}	protocol.Response{data=protocol.RespIntroductionDataInfo}	"ok，code = 0"
// @Failure		    500			    {object}	protocol.Response{}						            "failed，code != 0"
// @Router			/admin/data/detail/id    [get]
func AdminQueryIntroductionDataInfo(c *gin.Context) {
	var (
		req protocol.ReqGetIntroductionDataInfo
		res protocol.RespIntroductionDataInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetConferenceDataInfo(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
	return
}

// @Summary		    新增或修改大会介绍数据
// @Description	    新增或修改大会介绍数据API
// @Tags			管理后台-首页其他-数据
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string				                true    "SMM-ADMIN-TOKEN"
// @Param			payload		formData	protocol.ReqAdminSaveIntroductionData     true    "json body"
// @Success		    200			{object}	protocol.Response{}                 "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                 "failed，code != 0"
// @Router	        /admin/data/add_edit  [post]
func AdminAddEditIntroductionData(c *gin.Context) {
	var (
		req protocol.ReqAdminSaveIntroductionData
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	_, err_ = service.AddEditConferenceData(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
	return
}

// @Summary		    删除大会介绍数据
// @Description	    删除大会介绍数据API
// @Tags			管理后台-首页其他-数据
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string				        true    "SMM-ADMIN-TOKEN"
// @Param			payload		formData	protocol.ReqGetIntroductionDataInfo     true    "json body"
// @Success		    200			{object}	protocol.Response{}                 "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                 "failed，code != 0"
// @Router	        /admin/data/delete/id  [post]
func AdminDeleteIntroductionData(c *gin.Context) {
	var (
		req protocol.ReqGetIntroductionDataInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	_, err_ = service.DeleteConferenceData(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}

	return
}

// @Summary		    组织架构列表
// @Description	    组织架构列表API
// @Tags			管理后台-首页其他-组织架构
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqGetIntroductionOrganizationList					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.ResAdminConferenceOrganizationList}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						                "failed，code != 0"
// @Router			/admin/organization/list  [get]
func AdminConferenceOrganizationList(c *gin.Context) {
	var (
		req protocol.ReqGetIntroductionOrganizationList
		res protocol.ResAdminConferenceOrganizationList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	list, total, err_ := service.QueryConferenceOrganizationList(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
	res.List = list
	res.Info.InitPageInfo(total, req.Page, req.PageSize)
	return
}

// @Summary		    新增或修改组织架构
// @Description	    新增或修改组织架构API
// @Tags			管理后台-首页其他-组织架构
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string				                true    "SMM-ADMIN-TOKEN"
// @Param			payload		formData	protocol.ReqAdminSaveOrganization     true    "json body"
// @Success		    200			{object}	protocol.Response{}                 "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                 "failed，code != 0"
// @Router	        /admin/organization/add_edit  [post]
func AdminAddEditOrganization(c *gin.Context) {
	var (
		req protocol.ReqAdminSaveOrganization
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}

	_, err_ = service.AddEditConferenceOrganization(req)
	if err_ != nil {
		err = protocol.NewResErrWithMsg(errcode.RESPONSE_CODE_SYSTEM_ERR, err_.Error(), err_)
		return
	}

	return
}

// @Summary		  组织架构详情
// @Description	    查询组织架构API
// @Tags			管理后台-首页其他-组织架构
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			payload		formData	protocol.ReqGetIntroductionOrganizationInfo     true    "json body"
// @Success		    200			{object}	protocol.Response{data=protocol.RespOrganizationInfo}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						            "failed，code != 0"
// @Router			/admin/organization/detail/id    [get]
func AdminQueryOrganizationInfo(c *gin.Context) {
	var (
		req protocol.ReqGetIntroductionOrganizationInfo
		res protocol.RespOrganizationInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetConferenceOrganizationInfo(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
	return
}

// @Summary		    删除组织架构
// @Description	    删除组织架构API
// @Tags			管理后台-首页其他-组织架构
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string				        true    "SMM-ADMIN-TOKEN"
// @Param			payload		formData	protocol.ReqGetIntroductionOrganizationInfo     true    "json body"
// @Success		    200			{object}	protocol.Response{}                 "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                 "failed，code != 0"
// @Router	        /admin/organization/delete/id  [post]
func AdminDeleteOrganization(c *gin.Context) {
	var (
		req protocol.ReqGetIntroductionOrganizationInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	_, err_ = service.DeleteConferenceOrganization(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}

	return
}

// @Summary		    组织架构联系信息列表（2.0新增）
// @Description	    组织架构联系信息列表API
// @Tags			管理后台-组织架构-联系信息
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqGetIntroductionList					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.ResAdminContactInformationList}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						                "failed，code != 0"
// @Router			/admin/contact_information/list  [get]
func AdminContactInformationList(c *gin.Context) {
	var (
		req protocol.ReqGetIntroductionList
		res protocol.ResAdminContactInformationList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	list, total, err_ := service.QueryContactInformationList(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
	res.List = list
	res.Info.InitPageInfo(total, req.Page, req.PageSize)
	return
}

// @Summary		    新增或修改组织架构联系信息（2.0新增）
// @Description	    新增或修改组织架构联系信息API
// @Tags			管理后台-组织架构-联系信息
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string				                true    "SMM-ADMIN-TOKEN"
// @Param			payload		formData	protocol.ReqAdminSaveContactInformation     true    "json body"
// @Success		    200			{object}	protocol.Response{}                 "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                 "failed，code != 0"
// @Router	        /admin/contact_information/add_edit  [post]
func AdminAddEditContactInformation(c *gin.Context) {
	var (
		req protocol.ReqAdminSaveContactInformation
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}

	_, err_ = service.AddEditContactInformation(req)
	if err_ != nil {
		err = protocol.NewResErrWithMsg(errcode.RESPONSE_CODE_SYSTEM_ERR, err_.Error(), err_)
		return
	}

	return
}

// @Summary		  	组织架构联系信息详情（2.0新增）
// @Description	    查询组织架构联系信息API
// @Tags			管理后台-组织架构-联系信息
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			payload		formData	protocol.ReqGetIntroductionOrganizationInfo     true    "json body"
// @Success		    200			{object}	protocol.Response{data=protocol.RespBottomPageInfo}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						            "failed，code != 0"
// @Router			/admin/contact_information/detail/id    [get]
func AdminQueryContactInformationInfo(c *gin.Context) {
	var (
		req protocol.ReqGetIntroductionOrganizationInfo
		res protocol.RespContactInformationInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetContactInformationInfo(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
	return
}

// @Summary		    删除组织架构联系信息（2.0新增）
// @Description	    删除组织架构联系信息API
// @Tags			管理后台-组织架构-联系信息
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string				        true    "SMM-ADMIN-TOKEN"
// @Param			payload		formData	protocol.ReqGetIntroductionOrganizationInfo     true    "json body"
// @Success		    200			{object}	protocol.Response{}                 "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                 "failed，code != 0"
// @Router	        /admin/contact_information/delete/id  [post]
func AdminDeleteContactInformation(c *gin.Context) {
	var (
		req protocol.ReqGetIntroductionOrganizationInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	_, err_ = service.DeleteContactInformation(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}

	return
}

// @Summary		    组织架构底部页面二维码列表
// @Description	    组织架构底部页面二维码列表API
// @Tags			管理后台-首页其他-组织架构
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqGetIntroductionList					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.ResAdminConferenceBottomPageList}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						                "failed，code != 0"
// @Router			/admin/bottom_page/list  [get]
func AdminConferenceBottomPageList(c *gin.Context) {
	var (
		req protocol.ReqGetIntroductionList
		res protocol.ResAdminConferenceBottomPageList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	list, total, err_ := service.QueryConferenceBottomPageList(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
	res.List = list
	res.Info.InitPageInfo(total, req.Page, req.PageSize)
	return
}

// @Summary		    新增或修改组织架构底部页面二维码
// @Description	    新增或修改组织架构底部页面二维码API
// @Tags			管理后台-首页其他-组织架构
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string				                true    "SMM-ADMIN-TOKEN"
// @Param			payload		formData	protocol.ReqAdminSaveBottomPage     true    "json body"
// @Success		    200			{object}	protocol.Response{}                 "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                 "failed，code != 0"
// @Router	        /admin/bottom_page/add_edit  [post]
func AdminAddEditBottomPage(c *gin.Context) {
	var (
		req protocol.ReqAdminSaveBottomPage
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}

	_, err_ = service.AddEditConferenceBottomPage(req)
	if err_ != nil {
		err = protocol.NewResErrWithMsg(errcode.RESPONSE_CODE_SYSTEM_ERR, err_.Error(), err_)
		return
	}

	return
}

// @Summary		  组织架构底部页面二维码详情
// @Description	    查询组织架构底部页面二维码API
// @Tags			管理后台-首页其他-组织架构
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			payload		formData	protocol.ReqGetIntroductionOrganizationInfo     true    "json body"
// @Success		    200			{object}	protocol.Response{data=protocol.RespBottomPageInfo}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						            "failed，code != 0"
// @Router			/admin/bottom_page/detail/id    [get]
func AdminQueryBottomPageInfo(c *gin.Context) {
	var (
		req protocol.ReqGetIntroductionOrganizationInfo
		res protocol.RespBottomPageInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetConferenceBottomPageInfo(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
	return
}

// @Summary		    删除组织架构底部页面二维码
// @Description	    删除组织架构底部页面二维码API
// @Tags			管理后台-首页其他-组织架构
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string				        true    "SMM-ADMIN-TOKEN"
// @Param			payload		formData	protocol.ReqGetIntroductionOrganizationInfo     true    "json body"
// @Success		    200			{object}	protocol.Response{}                 "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                 "failed，code != 0"
// @Router	        /admin/bottom_page/delete/id  [post]
func AdminDeleteBottomPage(c *gin.Context) {
	var (
		req protocol.ReqGetIntroductionOrganizationInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	_, err_ = service.DeleteConferenceBottomPage(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}

	return
}

// @Summary		    大会介绍信息列表
// @Description	    大会介绍信息列表API
// @Tags			前台-首页其他信息集合
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM_auth_token	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.UserGetIntroductionList					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.ResWebConferenceIntroductionList}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						                "failed，code != 0"
// @Router			/user/introduction/list  [get]
func UserConferenceIntroductionList(c *gin.Context) {
	var (
		req protocol.UserGetIntroductionList
		res protocol.ResWebConferenceIntroductionList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if req.ConferenceId <= 0 {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetUserConferenceIntroductionList(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
	return
}
