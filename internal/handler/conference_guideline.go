package handler

import (
	"conferencecenter/internal/errcode"
	"conferencecenter/internal/protocol"
	"conferencecenter/internal/service"
	"github.com/asaskevich/govalidator"
	"github.com/gin-gonic/gin"
)

// @Summary		保存展馆概况
// @Description	保存展馆概况
// @Tags			管理后台-展馆概况
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		query		protocol.HallInfo				        true	"展馆概况"
// @Success		200			{object}	protocol.Response{}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/save/hall/info [POST]
func SaveHallInfo(c *gin.Context) {
	var (
		req protocol.HallInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	req.OpUser = getAdminUser(c).FullName
	err_ = service.SaveHallInfo(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		获取展馆概况
// @Description	获取展馆概况
// @Tags			管理后台-展馆概况
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		query		protocol.ReqConferenceId				        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=protocol.HallInfo}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/get/hall/info [GET]
func GetHallInfo(c *gin.Context) {
	var (
		req protocol.ReqConferenceId
		res protocol.HallInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetHallInfo(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		获取展馆概况
// @Description	获取展馆概况
// @Tags		前台-参会指南
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		query		protocol.ReqConferenceId				        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=protocol.HallInfo}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/user/get/hall/info [GET]
func UserGetHallInfo(c *gin.Context) {
	var (
		req protocol.ReqConferenceId
		res protocol.HallInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetHallInfo(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		保存酒店住宿页面数据
// @Description	保存酒店住宿页面数据
// @Tags			管理后台-酒店住宿
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		query		protocol.HotelPage				        true	"Query Params"
// @Success		200			{object}	protocol.Response{}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/save/hotel [POST]
func SaveHotel(c *gin.Context) {
	var (
		req protocol.HotelPage
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	req.OpUser = getAdminUser(c).FullName
	err_ = service.SaveHotelPage(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		获取酒店住宿页面数据
// @Description	获取酒店住宿页面数据
// @Tags			管理后台-酒店住宿
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		query		protocol.ReqConferenceId			        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=protocol.HotelPage}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/get/hotel [GET]
func GetHotel(c *gin.Context) {
	var (
		req protocol.ReqConferenceId
		res protocol.HotelPage
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetHotelPage(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		获取酒店住宿页面数据
// @Description	获取酒店住宿页面数据
// @Tags			前台-酒店住宿
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		query		protocol.ReqConferenceId			        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=protocol.HotelPage}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/user/get/hotel [GET]
func UserGetHotel(c *gin.Context) {
	var (
		req protocol.ReqConferenceId
		res protocol.HotelPage
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetHotelPage(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary			保存交通信息
// @Description		保存交通信息
// @Tags			管理后台-交通服务
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		query		protocol.TrafficService				        true	"Query Params"
// @Success		200			{object}	protocol.Response{}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/save/traffic/service [POST]
func SaveTrafficService(c *gin.Context) {
	var (
		req protocol.TrafficService
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	req.OpUser = getAdminUser(c).FullName
	err_ = service.SaveTrafficService(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary			获得交通服务信息
// @Description		获得交通服务信息
// @Tags			管理后台-交通服务
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		query		protocol.ReqConferenceId				        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=protocol.TrafficService}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router		/admin/get/traffic/service [GET]
func GetTrafficService(c *gin.Context) {
	var (
		req protocol.ReqConferenceId
		res protocol.TrafficService
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetTrafficService(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		前台获得交通服务信息
// @Description	前台获得交通服务信息
// @Tags			前台-交通服务
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		query		protocol.ReqConferenceId				        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=protocol.TrafficService}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/user/get/traffic/service [GET]
func UserGetTrafficService(c *gin.Context) {
	var (
		req protocol.ReqConferenceId
		res protocol.TrafficService
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetTrafficService(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		保存城市概况
// @Description	保存城市概况
// @Tags			管理后台-城市概况
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		query		protocol.CityInfo				        true	"Query Params"
// @Success		200			{object}	protocol.Response{}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/save/city/info [POST]
func SaveCityInfo(c *gin.Context) {
	var (
		req protocol.CityInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if _, err_ = govalidator.ValidateStruct(req); err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	req.OpUser = getAdminUser(c).FullName
	err_ = service.SaveCityInfo(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		获得城市概况
// @Description	获得城市概况
// @Tags			管理后台-城市概况
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		query		protocol.ReqConferenceId				        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=protocol.CityInfo}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/get/city/info [GET]
func GetCityInfo(c *gin.Context) {
	var (
		req protocol.ReqConferenceId
		res protocol.CityInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetCityInfo(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		前台获得城市概况
// @Description	前台获得城市概况
// @Tags			前台-城市概况
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		query		protocol.ReqConferenceId				        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=protocol.CityInfo}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/user/get/city/info [GET]
func UserGetCityInfo(c *gin.Context) {
	var (
		req protocol.ReqConferenceId
		res protocol.CityInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}

	res, err_ = service.GetCityInfo(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		获取场馆信息（2.0修改 ，新增接口）
// @Description	获取场馆信息
// @Tags			后台-场馆信息
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"admin_token"
// @Param			req		query			protocol.ReqConferenceId			        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=[]protocol.Venue}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/get/venues [GET]
func AdminGetVenues(c *gin.Context) {
	var (
		req protocol.ReqConferenceId
		res []protocol.Venue
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetVenues(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		新增、编辑场馆信息（2.0修改 ，新增接口）
// @Description	新增、编辑场馆信息
// @Tags			后台-场馆信息
// @Accept			application/json
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"admin_token"
// @Param			req		body		protocol.ReqSaveVenue				        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=string}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/add_edit/venue [POST]
func SaveVenue(c *gin.Context) {
	var (
		req protocol.ReqSaveVenue
		res string
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if _, reqErr := govalidator.ValidateStruct(req); reqErr != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, reqErr)
		return
	}
	req.UserName = getAdminUser(c).FullName
	res, err_ = service.SaveVenue(req)
	if err_ != nil {
		err = protocol.NewResErrWithMsg(errcode.RESPONSE_CODE_SYSTEM_ERR, res, err_)
		return
	}
}

// @Summary		删除场馆（2.0修改 ，新增接口）
// @Description	删除场馆
// @Tags			后台-场馆信息
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"admin_token"
// @Param			req		query			protocol.ReqVenueId				        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=string}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/delete/venue [POST]
func DeleteVenue(c *gin.Context) {
	var (
		req protocol.ReqVenueId
		res string
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if _, reqErr := govalidator.ValidateStruct(req); reqErr != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, reqErr)
		return
	}
	res, err_ = service.DeleteVenue(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}
