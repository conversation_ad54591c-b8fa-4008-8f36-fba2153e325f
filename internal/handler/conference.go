package handler

import (
	"bytes"
	"conferencecenter/internal/constant"
	"conferencecenter/internal/errcode"
	"conferencecenter/internal/mw"
	"conferencecenter/internal/pkg/utils"
	"conferencecenter/internal/protocol"
	"conferencecenter/internal/service"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.code.tencent.com/smmit/smmbase/logger"
	"github.com/asaskevich/govalidator"
	"github.com/gin-gonic/gin"
	"github.com/jinzhu/now"
	"github.com/pkg/errors"
)

// @Summary		    查询展会列表
// @Description	    查询展会列表API
// @Tags			管理后台-首页
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										  true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqAdminConferenceList					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.ResAdminConferenceList}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						                "failed，code != 0"
// @Router			/admin/conference/list  [get]
func AdminQueryConferences(c *gin.Context) {
	var (
		req protocol.ReqAdminConferenceList
		res protocol.ResAdminConferenceList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	req.DefaultPage()
	total, dataLi, err_ := service.GetConferenceList(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_DB_ERR, err_)
		return
	}
	res.List = dataLi
	res.Info.InitPageInfo(total, req.Page, req.PageSize)
	return
}

// @Summary		    查询展会列表
// @Description	    查询展会列表API
// @Tags			管理后台-首页
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										  true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqAdminConferenceNameList				  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.ResAdminConferenceNameList}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}					                "failed，code != 0"
// @Router			/admin/conference/name/list  [get]
func AdminQueryConferenceNameList(c *gin.Context) {
	var (
		req protocol.ReqAdminConferenceNameList
		res protocol.ResAdminConferenceNameList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetConferenceNameByType(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_DB_ERR, err_)
		return
	}
	return
}

// @Summary		    查询展会基本信息
// @Description	    查询展会基本信息API
// @Tags			管理后台-首页
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										  true  "SMM-ADMIN-TOKEN"
// @Param			req  		    query		protocol.ReqConferenceId                      true	"Query Params"
// @Success		    200			    {object}	protocol.Response{data=protocol.ResConferenceInfo}	"ok，code = 0"
// @Failure		    500			    {object}	protocol.Response{}						            "failed，code != 0"
// @Router			/admin/conference/detail    [get]
func AdminQueryConferenceInfo(c *gin.Context) {
	var (
		req  protocol.ReqConferenceId
		res  *protocol.ResConferenceInfo
		err  protocol.ResError
		code int
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()

	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	_, err_ = govalidator.ValidateStruct(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, code, err_ = service.GetConferenceInfo(req.Id)
	if code != errcode.RESPONSE_CODE_SUCCESS {
		err = protocol.NewResErr(code, err_)
		return
	}
	return
}

// @Summary		    新增或修改基本信息
// @Description	    新增或修改基本信息API
// @Tags			管理后台-首页
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string				                true    "SMM-ADMIN-TOKEN"
// @Param			payload		    body	    protocol.ReqAdminConferenceInfo     true    "json body"
// @Success		    200			    {object}	protocol.Response{}                 "ok，code = 0"
// @Failure		    500			    {object}	protocol.Response{}                 "failed，code != 0"
// @Router	        /admin/conference/add_edit  [post]
func AdminAddEditConferenceInfo(c *gin.Context) {
	var (
		req protocol.ReqAdminConferenceInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	// 检查跳转按钮
	for i, button := range req.ButtonLi {
		if utils.IsEmpty(button.CnName) {
			err = protocol.NewResErrWithMsg(errcode.RESPONSE_CODE_PARAM_ERR, fmt.Sprintf("第%d个跳转按钮中文名称不能为空", i+1), nil)
			return
		} else if utils.IsEmpty(button.CnURL) {
			err = protocol.NewResErrWithMsg(errcode.RESPONSE_CODE_PARAM_ERR, fmt.Sprintf("第%d个跳转按钮中文链接不能为空", i+1), nil)
			return
		} else if utils.IsEmpty(button.EnName) && !utils.IsEmpty(button.EnURL) {
			err = protocol.NewResErrWithMsg(errcode.RESPONSE_CODE_PARAM_ERR, fmt.Sprintf("第%d个跳转按钮英文名称不能为空", i+1), nil)
			return
		} else if utils.IsEmpty(button.EnURL) && !utils.IsEmpty(button.EnName) {
			err = protocol.NewResErrWithMsg(errcode.RESPONSE_CODE_PARAM_ERR, fmt.Sprintf("第%d个跳转按钮英文链接不能为空", i+1), nil)
			return
		}
		button.CnName = strings.Trim(button.CnName, " ")
		button.CnURL = strings.Trim(button.CnURL, " ")
		button.EnName = strings.Trim(button.EnName, " ")
		button.EnURL = strings.Trim(button.EnURL, " ")
		req.ButtonLi[i] = button
	}
	req.Start = time.Unix(req.StartTime, 0)
	req.End = time.Unix(req.EndTime, 0)
	req.OpUser = getAdminUser(c).FullName
	resMsg, err_ := service.AddEditConferenceInfo(req)
	if resMsg != "" {
		err = protocol.NewResErrWithMsg(errcode.RESPONSE_CODE_FAILED, resMsg, err_)
	} else if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_FAILED, err_)
	}
	// 操作日志
	if !err.HasError() {
		var opType, content string
		if req.ID > 0 {
			opType = "UPDATE"
			content = fmt.Sprintf("修改展会信息，展会ID:%d", req.ID)
		} else {
			opType = "CREATE"
			content = fmt.Sprintf("新增展会信息，展会名称:%s", req.CnName)
		}
		go service.SaveOpLog(constant.AuthModuleInfo, opType, content, c.ClientIP(), req.OpUser)
	}
	return
}

// @Summary		设置展会状态
// @Description	设置展会状态API
// @Tags		管理后台-首页
// @Accept		multipart/form-data
// @Produce		json
// @Param		SMM-ADMIN-TOKEN	header		string			                     true "SMM-ADMIN-TOKEN"
// @Param		payload		    formData	protocol.ReqAdminSetConferenceStatus true "json body"
// @Success		200			    {object}	protocol.Response{}                  "ok，code = 0"
// @Failure		500			    {object}	protocol.Response{}                  "failed，code != 0"
// @Router		/admin/conference/set_status [POST]
func AdminSetConferenceStatus(c *gin.Context) {
	var (
		req protocol.ReqAdminSetConferenceStatus
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	req.OpUser = getAdminUser(c).FullName
	resMsg, err_ := service.UpdateConferenceStatus(req.ConferenceId, req.Status, req.OpUser)
	if resMsg != "" {
		err = protocol.NewResErrWithMsg(errcode.RESPONSE_CODE_FAILED, resMsg, err_)
	} else if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_FAILED, err_)
	}
	// 操作日志
	if !err.HasError() {
		content := fmt.Sprintf("设置展会状态，展会ID:%d，状态:%d", req.ConferenceId, req.Status)
		go service.SaveOpLog(constant.AuthModuleInfo, "UPDATE", content, c.ClientIP(), req.OpUser)
	}

	return
}

// @Summary		设置展会日程模版类型
// @Description	设置日程模版类型API
// @Tags		管理后台-首页
// @Accept		multipart/form-data
// @Produce		json
// @Param		SMM-ADMIN-TOKEN	header		string			                     true "SMM-ADMIN-TOKEN"
// @Param		payload		    formData	protocol.ReqAdminSetConferenceTemplate true "json body"
// @Success		200			    {object}	protocol.Response{}                  "ok，code = 0"
// @Failure		500			    {object}	protocol.Response{}                  "failed，code != 0"
// @Router		/admin/conference/set_template [POST]
func AdminSetConferenceTemplate(c *gin.Context) {
	var (
		req protocol.ReqAdminSetConferenceTemplate
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	req.OpUser = getAdminUser(c).FullName
	resMsg, err_ := service.UpdateConferenceTemplate(req.ConferenceId, req.TemplateType, req.OpUser)
	if resMsg != "" {
		err = protocol.NewResErrWithMsg(errcode.RESPONSE_CODE_FAILED, resMsg, err_)
	} else if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_FAILED, err_)
	}
	// 操作日志
	if !err.HasError() {
		content := fmt.Sprintf("设置展会状态，展会ID:%d，模版类型:%d", req.ConferenceId, req.TemplateType)
		go service.SaveOpLog(constant.AuthModuleInfo, "UPDATE", content, c.ClientIP(), req.OpUser)
	}

	return
}

// @Summary		    查询往届列表
// @Description	    查询往届列表API
// @Tags			管理后台-往届
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										    true    "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqAdminPreviousList					false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.ResAdminPreviousList}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						            "failed，code != 0"
// @Router			/admin/previous/list    [get]
func AdminQueryPreviousList(c *gin.Context) {
	var (
		req protocol.ReqAdminPreviousList
		res protocol.ResAdminPreviousList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	total, dataLi, err_ := service.AdminGetPreviousList(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_DB_ERR, err_)
		return
	}
	res.List = dataLi
	res.Info.InitPageInfo(total, req.Page, req.PageSize)
	return
}

// @Summary		    新增或修改往届信息
// @Description	    新增或修改往届信息API
// @Tags			管理后台-往届
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string				        true    "SMM-ADMIN-TOKEN"
// @Param			payload		formData	protocol.ReqAdminPreviousInfo   true    "json body"
// @Success		    200			{object}	protocol.Response{}                     "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                     "failed，code != 0"
// @Router	        /admin/previous/add_edit  [post]
func AdminAddEditPreviousInfo(c *gin.Context) {
	var (
		req protocol.ReqAdminPreviousInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	req.OpUser = getAdminUser(c).FullName
	resMsg, err_ := service.AddEditPreviousInfo(req)
	if resMsg != "" {
		err = protocol.NewResErrWithMsg(errcode.RESPONSE_CODE_FAILED, resMsg, err_)
	} else if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_FAILED, err_)
	}
	// 操作日志
	if !err.HasError() {
		var opType, content string
		if req.ID > 0 {
			opType = "UPDATE"
			content = fmt.Sprintf("修改往届展会，往届ID:%d，展会ID:%d", req.ID, req.ConferenceID)
		} else {
			opType = "CREATE"
			content = fmt.Sprintf("新增往届展会，往届名称:%s，展会ID:%d", req.CnName, req.ConferenceID)
		}
		go service.SaveOpLog(constant.AuthModuleInfo, opType, content, c.ClientIP(), req.OpUser)
	}
	return
}

// @Summary		    删除往届信息
// @Description	    删除往届信息API
// @Tags			管理后台-往届
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string				    true    "SMM-ADMIN-TOKEN"
// @Param			id		    path    	int                         true    "Path Params"
// @Success		    200			{object}	protocol.Response{}                 "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                 "failed，code != 0"
// @Router	        /admin/previous/delete/:id  [post]
func AdminDeletePreviousInfo(c *gin.Context) {
	var (
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	paramId := c.Param("id")
	if paramId == "" {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, nil)
		return
	}
	infoId, err_ := strconv.Atoi(paramId)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if infoId <= 0 {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, nil)
		return
	}
	opUser := getAdminUser(c).FullName
	resMsg, err_ := service.DeletePreviousInfo(int64(infoId), opUser)
	if resMsg != "" {
		err = protocol.NewResErrWithMsg(errcode.RESPONSE_CODE_FAILED, resMsg, err_)
	} else if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_FAILED, err_)
	}
	// 操作日志
	if !err.HasError() {
		content := fmt.Sprintf("删除往届展会，往届ID:%d", infoId)
		go service.SaveOpLog(constant.AuthModuleInfo, "DELETE", content, c.ClientIP(), opUser)
	}
	return
}

// @Summary		    新增或修改简称映射关系
// @Description	    新增或修改简称映射关系API
// @Tags			管理后台-通用
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string				        true    "SMM-ADMIN-TOKEN"
// @Param			payload		formData	protocol.ReqAdminShortMap       true    "json body"
// @Success		    200			{object}	protocol.Response{}                     "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                     "failed，code != 0"
// @Router	        /admin/short_map/add_edit  [post]
func AdminAddEditShortMap(c *gin.Context) {
	var (
		req protocol.ReqAdminShortMap
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	req.OpUser = getAdminUser(c).FullName
	err_ = service.AddEditShortMap(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_FAILED, err_)
		return
	}
	// 操作日志
	if !err.HasError() {
		content := fmt.Sprintf("更新简称映射关系，简称:%s，展会ID:%d", req.ShortName, req.ConferenceID)
		go service.SaveOpLog(constant.AuthModuleInfo, "AddEdit", content, c.ClientIP(), req.OpUser)
	}
	return
}

// @Summary		    查询展会基本信息
// @Description	    查询展会基本信息API
// @Tags			前台-首页
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM_auth_token	header		string									false   "SMM_auth_token"
// @Param			id   		path		int							                true	"Path Params"
// @Success		    200			{object}	protocol.Response{data=protocol.ResConferenceInfo}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						            "failed，code != 0"
// @Router			/user/conference/detail/:id    [get]
func UserQueryConferenceInfo(c *gin.Context) {
	var (
		res  *protocol.ResConferenceInfo
		err  protocol.ResError
		code int
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	paramId := c.Param("id")
	if paramId == "" {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, nil)
		return
	}
	infoId, err_ := strconv.Atoi(paramId)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if infoId <= 0 {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, nil)
		return
	}
	res, code, err_ = service.GetConferenceInfo(int64(infoId))
	if code != errcode.RESPONSE_CODE_SUCCESS {
		err = protocol.NewResErr(code, err_)
		return
	}
	return
}

// @Summary		    查询展会基本信息
// @Description	    查询展会基本信息API
// @Tags			前台-首页
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM_auth_token	header		string									false   "SMM_auth_token"
// @Param			payload		formData	protocol.ReqUserMeetingNo                 true    "json body"
// @Success		    200			{object}	protocol.Response{data=protocol.ResConferenceInfo}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						            "failed，code != 0"
// @Router			/user/conference/info    [get]
func UserGetConferenceMeetingNo(c *gin.Context) {
	var (
		req  protocol.ReqUserMeetingNo
		res  *protocol.ResConferenceInfo
		err  protocol.ResError
		code int
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, code, err_ = service.GetConferenceByMeetingNo(req.MeetingNo)
	if code != errcode.RESPONSE_CODE_SUCCESS {
		err = protocol.NewResErr(code, err_)
		return
	}
	return
}

// @Summary		    查询展会映射关系
// @Description	    查询展会映射关系API
// @Tags			前台-首页
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM_auth_token	header		string										false   "SMM_auth_token"
// @Param			short_name  query		string							            false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=[]protocol.ResMapInfo}	    "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						            "failed，code != 0"
// @Router			/user/conference/mapping    [get]
func UserQueryConferenceMap(c *gin.Context) {
	var (
		req protocol.ReqonferenceMap
		res = make([]protocol.ResMapInfo, 0)
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()

	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res = service.GetConferenceMapping(req.ShortName)
	return
}

// @Summary		    查询往届展会
// @Description	    查询往届展会API
// @Tags			前台-通用
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM_auth_token	header		string										 false   "SMM_auth_token"
// @Param			payload		formData	protocol.ReqUserPreviousList                 true    "json body"
// @Success		    200			{object}	protocol.Response{data=protocol.ResUserPreviousList} "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						             "failed，code != 0"
// @Router			/user/previous/list     [get]
func UserQueryPreviousList(c *gin.Context) {
	var (
		req protocol.ReqUserPreviousList
		res protocol.ResUserPreviousList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	total, dataLi, err_ := service.UserGetPreviousList(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
	res.List = dataLi
	res.Info.InitPageInfo(total, req.Page, req.PageSize)
	return
}

// @Summary		获取展会线索列表
// @Description	获取展会线索列表
// @Tags		管理后台-展会线索
// @Accept		multipart/form-data
// @Produce		json
// @Param		SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param		request		    query		protocol.ReqAdminClueList					    false	"Query Params"
// @Success		200			    {object}	protocol.Response{data=protocol.ResConferenceClueList}	"ok，code = 0"
// @Failure		500			    {object}	protocol.Response{}						                "failed，code != 0"
// @Router		/admin/clue/list [GET]
func GetConferenceClueList(c *gin.Context) {
	var (
		req protocol.ReqAdminClueList
		res protocol.ResConferenceClueList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if req.StartTime != "" {
		startTime, err_ := utils.ParseDateAuto(req.StartTime)
		if err_ != nil {
			err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
			return
		}
		req.StartTime = now.New(startTime).BeginningOfDay().Format(constant.DateTime)
	}
	if req.EndTime != "" {
		endTime, err_ := utils.ParseDateAuto(req.EndTime)
		if err_ != nil {
			err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
			return
		}
		req.EndTime = now.New(endTime).EndOfDay().Format(constant.DateTime)
	}
	req.DefaultPage()
	total, dataLi, err_ := service.GetConferenceClueList(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_DB_ERR, err_)
		return
	}
	res.List = dataLi
	res.Info.InitPageInfo(total, req.Page, req.PageSize)
	return
}

// @Summary		    表单查询来源列表
// @Description	    表单查询来源列表API
// @Tags			管理后台-来源列表
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqGetConferenceSourceList					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.ResAdminConferenceChannelSourceList}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						                "failed，code != 0"
// @Router			/admin/register/source/list  [get]
func AdminGetConferenceSourceList(c *gin.Context) {
	var (
		req protocol.ReqGetConferenceSourceList
		res protocol.ResAdminConferenceChannelSourceList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	list, err_ := service.QueryQueryChannelSourceList(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res.List = list
	return
}

// @Summary		获取参展信息列表
// @Description	获取参展信息列表
// @Tags		管理后台-参展信息
// @Accept		multipart/form-data
// @Produce		json
// @Param		SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param		request		    query		protocol.ReqAdminClueList					    false	"Query Params"
// @Success		200			    {object}	protocol.Response{data=protocol.ResConferenceExhibitionList}	"ok，code = 0"
// @Failure		500			    {object}	protocol.Response{}						                "failed，code != 0"
// @Router		/admin/exhibition/list [GET]
func GetConferenceExhibitionList(c *gin.Context) {
	var (
		req protocol.ReqAdminClueList
		res protocol.ResConferenceExhibitionList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if req.StartTime != "" {
		startTime, err_ := utils.ParseDateAuto(req.StartTime)
		if err_ != nil {
			err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
			return
		}
		req.StartTime = now.New(startTime).BeginningOfDay().Format(constant.DateTime)
	}
	if req.EndTime != "" {
		endTime, err_ := utils.ParseDateAuto(req.EndTime)
		if err_ != nil {
			err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
			return
		}
		req.EndTime = now.New(endTime).EndOfDay().Format(constant.DateTime)
	}
	req.Type = 0
	req.DefaultPage()
	total, dataLi, err_ := service.GetConferenceExhibitionList(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_DB_ERR, err_)
		return
	}
	res.List = dataLi
	res.Info.InitPageInfo(total, req.Page, req.PageSize)
	return
}

// @Summary		导出参展信息列表
// @Description	导出参展信息列表
// @Tags		管理后台-参展信息
// @Accept		multipart/form-data
// @Produce		json
// @Param		SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param		request		    query		protocol.ReqAdminClueList					    false	"Query Params"
// @Success		200			                                                                        "excel文件"
// @Failure		500			    {object}	protocol.Response{}						                "failed，code != 0"
// @Router		/admin/exhibition/export [GET]
func ExportConferenceExhibitionList(c *gin.Context) {
	var (
		req        protocol.ReqAdminClueList
		err        protocol.ResError
		clueIds    = make([]int64, 0)
		exlData    *bytes.Reader
		dataLength int64
		fileName   = "线索列表"
	)
	defer func() {
		wrapExcelResp(c, fmt.Sprintf("%s.xlsx", fileName), dataLength, exlData, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if req.StartTime != "" {
		startTime, err_ := utils.ParseDateAuto(req.StartTime)
		if err_ != nil {
			err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
			return
		}
		req.StartTime = now.New(startTime).BeginningOfDay().Format(constant.DateTime)
	}
	if req.EndTime != "" {
		endTime, err_ := utils.ParseDateAuto(req.EndTime)
		if err_ != nil {
			err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
			return
		}
		req.EndTime = now.New(endTime).EndOfDay().Format(constant.DateTime)
	}
	req.Type = 0
	clueIds, exlData, err_ = service.ExportConferenceExhibitionList(req)
	if err_ != nil || exlData == nil {
		err = protocol.NewResErrWithMsg(errcode.RESPONSE_CODE_PARAM_ERR, "参数错误", err_)
		return
	}
	dataLength = int64(exlData.Len())
	go func() {
		opUser := getAdminUser(c).FullName
		_ = service.UpdateConferenceClueStatus(clueIds, constant.ClueStatusLocated, opUser)
		content := fmt.Sprintf("设置线索已分配，Ids：%v", clueIds)
		service.SaveOpLog(constant.AuthModuleClue, "UPDATE", content, c.ClientIP(), opUser)
	}()
	return
}

// @Summary		获取赞助信息列表
// @Description	获取赞助信息列表
// @Tags		管理后台-赞助信息
// @Accept		multipart/form-data
// @Produce		json
// @Param		SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param		request		    query		protocol.ReqAdminClueList					    false	"Query Params"
// @Success		200			    {object}	protocol.Response{data=protocol.ResConferenceExhibitionList}	"ok，code = 0"
// @Failure		500			    {object}	protocol.Response{}						                "failed，code != 0"
// @Router		/admin/sponsor/list [GET]
func GetConferenceSponsorList(c *gin.Context) {
	var (
		req protocol.ReqAdminClueList
		res protocol.ResConferenceExhibitionList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if req.StartTime != "" {
		startTime, err_ := utils.ParseDateAuto(req.StartTime)
		if err_ != nil {
			err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
			return
		}
		req.StartTime = now.New(startTime).BeginningOfDay().Format(constant.DateTime)
	}
	if req.EndTime != "" {
		endTime, err_ := utils.ParseDateAuto(req.EndTime)
		if err_ != nil {
			err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
			return
		}
		req.EndTime = now.New(endTime).EndOfDay().Format(constant.DateTime)
	}
	req.DefaultPage()
	req.Type = 1
	total, dataLi, err_ := service.GetConferenceSponsorList(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_DB_ERR, err_)
		return
	}
	res.List = dataLi
	res.Info.InitPageInfo(total, req.Page, req.PageSize)
	return
}

// @Summary		导出赞助信息列表
// @Description	导出赞助信息列表
// @Tags		管理后台-赞助信息
// @Accept		multipart/form-data
// @Produce		json
// @Param		SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param		request		    query		protocol.ReqAdminClueList					    false	"Query Params"
// @Success		200			                                                                        "excel文件"
// @Failure		500			    {object}	protocol.Response{}						                "failed，code != 0"
// @Router		/admin/sponsor/export [GET]
func ExportConferenceSponsorList(c *gin.Context) {
	var (
		req        protocol.ReqAdminClueList
		err        protocol.ResError
		clueIds    = make([]int64, 0)
		exlData    *bytes.Reader
		dataLength int64
		fileName   = "线索列表"
	)
	defer func() {
		wrapExcelResp(c, fmt.Sprintf("%s.xlsx", fileName), dataLength, exlData, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if req.StartTime != "" {
		startTime, err_ := utils.ParseDateAuto(req.StartTime)
		if err_ != nil {
			err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
			return
		}
		req.StartTime = now.New(startTime).BeginningOfDay().Format(constant.DateTime)
	}
	if req.EndTime != "" {
		endTime, err_ := utils.ParseDateAuto(req.EndTime)
		if err_ != nil {
			err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
			return
		}
		req.EndTime = now.New(endTime).EndOfDay().Format(constant.DateTime)
	}
	req.Type = 1
	clueIds, exlData, err_ = service.ExportConferenceSponsorList(req)
	if err_ != nil || exlData == nil {
		err = protocol.NewResErrWithMsg(errcode.RESPONSE_CODE_PARAM_ERR, "参数错误", err_)
		return
	}
	dataLength = int64(exlData.Len())
	go func() {
		opUser := getAdminUser(c).FullName
		_ = service.UpdateConferenceClueStatus(clueIds, constant.ClueStatusLocated, opUser)
		content := fmt.Sprintf("设置线索已分配，Ids：%v", clueIds)
		service.SaveOpLog(constant.AuthModuleClue, "UPDATE", content, c.ClientIP(), opUser)
	}()
	return
}

// @Summary		导出展会线索列表
// @Description	导出展会线索列表
// @Tags		管理后台-展会线索
// @Accept		multipart/form-data
// @Produce		json
// @Param		SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param		request		    query		protocol.ReqAdminClueList					    false	"Query Params"
// @Success		200			                                                                        "excel文件"
// @Failure		500			    {object}	protocol.Response{}						                "failed，code != 0"
// @Router		/admin/clue/export [GET]
func ExportConferenceClueList(c *gin.Context) {
	var (
		req        protocol.ReqAdminClueList
		err        protocol.ResError
		clueIds    = make([]int64, 0)
		exlData    *bytes.Reader
		dataLength int64
		fileName   = "线索列表"
	)
	defer func() {
		wrapExcelResp(c, fmt.Sprintf("%s.xlsx", fileName), dataLength, exlData, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if req.StartTime != "" {
		startTime, err_ := utils.ParseDateAuto(req.StartTime)
		if err_ != nil {
			err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
			return
		}
		req.StartTime = now.New(startTime).BeginningOfDay().Format(constant.DateTime)
	}
	if req.EndTime != "" {
		endTime, err_ := utils.ParseDateAuto(req.EndTime)
		if err_ != nil {
			err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
			return
		}
		req.EndTime = now.New(endTime).EndOfDay().Format(constant.DateTime)
	}
	clueIds, exlData, err_ = service.ExportConferenceClueList(req)
	if err_ != nil || exlData == nil {
		err = protocol.NewResErrWithMsg(errcode.RESPONSE_CODE_PARAM_ERR, "参数错误", err_)
		return
	}
	dataLength = int64(exlData.Len())
	go func() {
		opUser := getAdminUser(c).FullName
		_ = service.UpdateConferenceClueStatus(clueIds, constant.ClueStatusLocated, opUser)
		content := fmt.Sprintf("设置线索已分配，Ids：%v", clueIds)
		service.SaveOpLog(constant.AuthModuleClue, "UPDATE", content, c.ClientIP(), opUser)
	}()
	return
}

// @Summary		    申请参展或赞助
// @Description	    申请参展或赞助API
// @Tags			前台-通用
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM_auth_token	header		string						false   "SMM_auth_token"
// @Param			payload		formData	protocol.ReqUserApplySubmit true    "form data"
// @Success		    200			{object}	protocol.Response{}         "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}			"failed，code != 0"
// @Router			/user/apply/submit [post]
func UserApplySubmit(c *gin.Context) {
	var (
		req protocol.ReqUserApplySubmit
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if req.InterestLevel == "" && req.BoothType == "" {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, errors.New("请填写兴趣等级或展位类型"))
		return
	}
	if req.InterestLevel != "" && req.BoothType != "" {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, errors.New("兴趣等级或展位类型只能填一个"))
		return
	}
	req.UserId = mw.GetUserId(c)
	err_ = service.SaveUserApply(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_FAILED, err_)
	}
	return
}

// @Summary		    购买展览展位
// @Description	    购买展览展位API
// @Tags			表单-购买展览展位
// @Accept			multipart/form-data
// @Produce		    json
// @Param			payload		formData	protocol.ReqUserApplyPurchaseBoothSubmit true    "form data"
// @Success		    200			{object}	protocol.Response{}         "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}			"failed，code != 0"
// @Router			/user/apply/purchase_booth/submit [post]
func UserApplyPurchaseBoothSubmit(c *gin.Context) {
	var (
		req protocol.ReqUserApplyPurchaseBoothSubmit
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if req.CnOrEn == 0 && utils.IsEmpty(req.CellPhone) {
		err = protocol.NewResErrWithMsg(errcode.RESPONSE_CODE_PARAM_ERR, "手机号不能为空", errors.New("手机号不能为空"))
		return
	}
	if req.CnOrEn == 1 && utils.IsEmpty(req.Email) {
		err = protocol.NewResErrWithMsg(errcode.RESPONSE_CODE_PARAM_ERR, "Email cannot be empty", errors.New("Email cannot be empty"))
		return
	}

	if utils.NotEmpty(req.Email) {
		if req.CnOrEn == 1 && !utils.IsEmailValid(req.Email) {
			err = protocol.NewResErrWithMsg(errcode.RESPONSE_CODE_PARAM_ERR, "Email format error", errors.New("Email format error"))
			return
		}
	}

	req.UserId = mw.GetUserId(c)
	err_ = service.SaveUserPurchaseBooth(req)
	if err_ != nil {
		if err_.Error() == "验证码错误" {
			err = protocol.NewResErr(errcode.RESPONSE_CODE_CODE_INVALID, err_)
			logger.Warnning(err_)
			return
		}
		if err_.Error() == "手机号已存在" || err_.Error() == "邮箱已存在" {
			err = protocol.NewResErrWithMsg(errcode.RESPONSE_CODE_CELLPHONE_EXISTS, err_.Error(), err_)
			logger.Warnning(fmt.Sprintf("同步参展信息出错：Err->%v;", err))
			return
		}
		err = protocol.NewResErrWithMsg(errcode.RESPONSE_CODE_PARAM_ERR, err_.Error(), err_)
		return
	}

	return
}

// @Summary		    会议赞助及广告机会
// @Description	    会议赞助及广告机会API
// @Tags			表单-会议赞助及广告机会
// @Accept			multipart/form-data
// @Produce		    json
// @Param			payload		formData	protocol.ReqUserApplySponsorSubmit true    "form data"
// @Success		    200			{object}	protocol.Response{}         "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}			"failed，code != 0"
// @Router			/user/apply/sponsor/submit [post]
func UserApplySponsorSubmit(c *gin.Context) {
	var (
		req protocol.ReqUserApplySponsorSubmit
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if req.CnOrEn == 0 && utils.IsEmpty(req.CellPhone) {
		err = protocol.NewResErrWithMsg(errcode.RESPONSE_CODE_PARAM_ERR, "手机号不能为空", errors.New("手机号不能为空"))
		return
	}
	if req.CnOrEn == 1 && utils.IsEmpty(req.Email) {
		err = protocol.NewResErrWithMsg(errcode.RESPONSE_CODE_PARAM_ERR, "Email cannot be empty", errors.New("Email cannot be empty"))
		return
	}
	if utils.NotEmpty(req.Email) {
		if req.CnOrEn == 1 && !utils.IsEmailValid(req.Email) {
			err = protocol.NewResErrWithMsg(errcode.RESPONSE_CODE_PARAM_ERR, "Email format error", errors.New("Email format error"))
			return
		}
	}

	req.UserId = mw.GetUserId(c)
	err_ = service.SaveUserApplySponsor(req)
	if err_ != nil {
		if err_.Error() == "验证码错误" {
			err = protocol.NewResErr(errcode.RESPONSE_CODE_CODE_INVALID, err_)
			logger.Warnning(err_)
			return
		}
		if err_.Error() == "手机号已存在" || err_.Error() == "邮箱已存在" {
			err = protocol.NewResErrWithMsg(errcode.RESPONSE_CODE_CELLPHONE_EXISTS, err_.Error(), err_)
			logger.Warnning(fmt.Sprintf("同步参展信息出错：Err->%v;", err))
			return
		}
		err = protocol.NewResErrWithMsg(errcode.RESPONSE_CODE_PARAM_ERR, err_.Error(), err_)
	}

	return
}

// @Summary         搜索
// @Description	    搜索API
// @Tags			前台-通用
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM_auth_token	header		string										 false   "SMM_auth_token"
// @Param			req		        query	    protocol.ReqSearchData                       true    "Query Params"
// @Success		    200			    {object}	protocol.Response{data=protocol.ResSearchDataList}   "ok，code = 0"
// @Failure		    500			    {object}	protocol.Response{}						             "failed，code != 0"
// @Router			/user/common/search     [get]
func UserCommonSearch(c *gin.Context) {
	var (
		req protocol.ReqSearchData
		res protocol.ResSearchDataList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	req.DefaultPage()
	total, dataLi, err_ := service.SearchDataList(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_FAILED, err_)
		return
	}
	res.List = dataLi
	res.Info.InitPageInfo(int64(total), req.Page, req.PageSize)
	return
}

// @Summary			获取场馆信息
// @Description		获取场馆信息
// @Tags			前台-通用
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM_auth_token	header		string										    false	"admin_token"
// @Param			req		query			protocol.ReqConferenceId			        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=[]protocol.Venue}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/user/get/venues [GET]
func UserGetVenues(c *gin.Context) {
	var (
		req protocol.ReqConferenceId
		res []protocol.Venue
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetVenues(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}
