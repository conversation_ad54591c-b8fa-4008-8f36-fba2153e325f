package handler

import (
	"conferencecenter/internal/constant"
	"conferencecenter/internal/errcode"
	"conferencecenter/internal/protocol"
	"conferencecenter/internal/service"
	"fmt"
	"github.com/asaskevich/govalidator"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"strconv"
)

// SaveAboutUs 保存关于我们
//
// @Summary		保存关于我们
// @Description	保存关于我们
// @Tags		管理后台-关于我们
// @Accept		multipart/form-data
// @Produce		json
// @Param		SMM-ADMIN-TOKEN	header		string				true	"SMM-ADMIN-TOKEN"
// @Param		req		        query		protocol.ReqAboutUs	true	"Query Params"
// @Success		200			    {object}	protocol.Response{}	"ok，code = 0"
// @Failure		500			    {object}	protocol.Response{}	"failed，code != 0"
// @Router		/admin/save/about/us [POST]
func SaveAboutUs(c *gin.Context) {
	var (
		req protocol.ReqAboutUs
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, add<PERSON><PERSON>back, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if _, err_ = govalidator.ValidateStruct(req); err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	req.OpUser = getAdminUser(c).FullName
	err_ = service.SaveAboutUs(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_FAILED, err_)
		return
	}
	// 操作日志
	{
		content := fmt.Sprintf("存储关于我们，展会ID:%d，中文内容:%s，英文内容:%s", req.ConferenceId, req.CnAboutUs, req.EnAboutUs)
		go service.SaveOpLog(constant.AuthModuleInfo, "CREATE", content, c.ClientIP(), req.OpUser)
	}
	return
}

// 获得关于我们
//
// @Summary		获得关于我们
// @Description	获得关于我们
// @Tags		管理后台-关于我们
// @Accept		multipart/form-data
// @Produce		json
// @Param		SMM-ADMIN-TOKEN	header		string								 true	"SMM-ADMIN-TOKEN"
// @Param		conference_id   query		int							         true	"Query Params"
// @Success		200			    {object}	protocol.Response{data=protocol.ResAboutUs}	"ok，code = 0"
// @Failure		500			    {object}	protocol.Response{}						    "failed，code != 0"
// @Router		/admin/get/about/us [GET]
func AdminGetAboutUs(c *gin.Context) {
	var (
		res *protocol.ResAboutUs
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	idParam := c.Query("conference_id")
	if idParam == "" {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, nil)
		return
	}
	conferenceId, err_ := strconv.Atoi(idParam)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if conferenceId <= 0 {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.AdminGetAboutUs(int64(conferenceId))
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// 获得关于我们
//
// @Summary		获得关于我们
// @Description	获得关于我们
// @Tags		前台-关于我们
// @Accept		multipart/form-data
// @Produce		json
// @Param		SMM_auth_token  	header		string								 false	"SMM_auth_token"
// @Param		conference_id   query		int							         true	"Query Params"
// @Success		200			    {object}	protocol.Response{data=protocol.ResAboutUs}	"ok，code = 0"
// @Failure		500			    {object}	protocol.Response{}						    "failed，code != 0"
// @Router		/user/get/about/us [GET]
func UserGetAboutUs(c *gin.Context) {
	var (
		res *protocol.ResAboutUs
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	idParam := c.Query("conference_id")
	if idParam == "" {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, nil)
		return
	}
	conferenceId, err_ := strconv.Atoi(idParam)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if conferenceId <= 0 {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.UserGetAboutUs(int64(conferenceId))
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// SaveContactUs 保存联系我们
//
// @Summary		保存联系我们
// @Description	保存联系我们
// @Tags		管理后台-关于我们
// @Accept		multipart/form-data
// @Produce		json
// @Param		SMM-ADMIN-TOKEN	header		string			        true	"SMM-ADMIN-TOKEN"
// @Param		req		        query		protocol.ReqContactUs   true	"Query Params"
// @Success		200			    {object}	protocol.Response{}         "ok，code = 0"
// @Failure		500			    {object}	protocol.Response{}		    "failed，code != 0"
// @Router		/admin/save/contact/us [POST]
func SaveContactUs(c *gin.Context) {
	var (
		req protocol.ReqContactUs
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if req.ConferenceId <= 0 {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, errors.New("展会ID异常"))
		return
	}
	req.ID = 0
	req.OpUser = getAdminUser(c).FullName
	resMsg, err_ := service.AddEditContactUs(req)
	if resMsg != "" {
		err = protocol.NewResErrWithMsg(errcode.RESPONSE_CODE_FAILED, resMsg, err_)
	} else if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_FAILED, err_)
	}
	// 操作日志
	if !err.HasError() {
		content := fmt.Sprintf("新增联系我们，展会ID:%d，中文模块:%s，英文模块:%s", req.ConferenceId, req.CnContact, req.EnContact)
		go service.SaveOpLog(constant.AuthModuleInfo, "CREATE", content, c.ClientIP(), req.OpUser)
	}
	return
}

// 获得联系我们
//
// @Summary		获得联系我们
// @Description	获得联系我们
// @Tags		管理后台-关于我们
// @Accept		multipart/form-data
// @Produce		json
// @Param		SMM-ADMIN-TOKEN	header		string								 true	"SMM-ADMIN-TOKEN"
// @Param		req         	query		protocol.ReqContactUsList			 true	"Query Params"
// @Success		200			    {object}	protocol.Response{data=protocol.ResContactUsList}	"ok，code = 0"
// @Failure		500			    {object}	protocol.Response{}						    "failed，code != 0"
// @Router		/admin/get/contact/us [GET]
func GetContactUsList(c *gin.Context) {
	var (
		req protocol.ReqContactUsList
		res protocol.ResContactUs
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if _, err_ = govalidator.ValidateStruct(req); err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.AdminGetContactUsList(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_DB_ERR, err_)
		return
	}
	return
}

// 获得联系我们
//
// @Summary		获得联系我们
// @Description	获得联系我们
// @Tags		前台-关于我们
// @Accept		multipart/form-data
// @Produce		json
// @Param		SMM_auth_token  	    header		string								false	"SMM_auth_token"
// @Param		conference_id	    query		int				                    true	"Query Params"
// @Success		200			        {object}	protocol.Response{data=protocol.ResContactUsList}	"ok，code = 0"
// @Failure		500			        {object}	protocol.Response{}						    "failed，code != 0"
// @Router		/user/get/contact/us [GET]
func UserGetContactUs(c *gin.Context) {
	var (
		res protocol.ResContactUs
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	idParam := c.Query("conference_id")
	if idParam == "" {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, nil)
		return
	}
	conferenceId, err_ := strconv.Atoi(idParam)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if conferenceId <= 0 {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.UserGetContactUsList(protocol.ReqContactUsList{ConferenceId: int64(conferenceId)})
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
	return
}
