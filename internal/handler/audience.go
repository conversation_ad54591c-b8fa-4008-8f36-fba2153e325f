package handler

import (
	"conferencecenter/internal/constant"
	"conferencecenter/internal/errcode"
	"conferencecenter/internal/pkg/utils"
	"conferencecenter/internal/protocol"
	"conferencecenter/internal/service"
	"git.code.tencent.com/smmit/smmbase/admin"
	"github.com/asaskevich/govalidator"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

// @Summary			获取观众预登记记录
// @Description  	获取观众预登记记录api
// @Tags			管理后台-观众预登记
// @Accept			json
// @Produce			json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		query		 protocol.ReqConferenceId       true	"展会id"
// @Success		200			{object}	protocol.Response{data=protocol.ResAudiencePreRegister}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			 /admin/audience/pre/register [GET]
func GetAudiencePreRegister(c *gin.Context) {
	var (
		req protocol.ReqConferenceId
		res protocol.ResAudiencePreRegister
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if req.Id <= 0 {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, errors.New("展会id错误"))
	}
	res, err_ = service.GetAudiencePreRegister(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
	}
}

// @Summary	获取观众预登记记录
// @Description  获取观众预登记记录api
// @Tags			前台-观众预登记
// @Accept			multipart/form-data
// @Produce		json
// @Param		SMM-ADMIN-TOKEN		header		string					true	"SMM-ADMIN-TOKEN"
// @Param		req		query		 protocol.ReqConferenceId        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=protocol.ResAudiencePreRegister}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			 /user/audience/pre/register [GET]
func UserGetAudiencePreRegister(c *gin.Context) {
	var (
		req protocol.ReqConferenceId
		res protocol.ResAudiencePreRegister
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetAudiencePreRegister(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
	}
}

// @Summary		记录观众预登记记录
// @Description	记录观众预登记记录
// @Tags			管理后台-观众预登记
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		formData		protocol.ReqAudiencePreRegister				        true	"Query Params"
// @Success		200			{object}	protocol.Response{}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/audience/record/register [POST]
func RecordAudiencePreRegister(c *gin.Context) {
	var (
		req protocol.ReqAudiencePreRegister
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if _, reqErr := govalidator.ValidateStruct(req); reqErr != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, reqErr)
		return
	}
	req.OpUser = getAdminUser(c).FullName
	resErr := service.RecordAudiencePreRegister(req)
	if resErr != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, resErr)
	}
}

// @Summary			获取参观价值
// @Description  	获取参观价值api
// @Tags			管理后台-观众-参观价值
// @Accept			json
// @Produce			json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		query		 protocol.ReqConferenceId       true	"Query"
// @Success		200			{object}	protocol.Response{data=protocol.ResAudienceVisitingInfo}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			 /admin/audience/visiting/info [GET]
func GetAudienceVisitingValue(c *gin.Context) {
	var (
		req protocol.ReqConferenceId
		res protocol.ResAudienceVisitingInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if req.Id <= 0 {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, errors.New("展会id错误"))
	}
	res, err_ = service.ResAudienceVisitingInfo(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
	}
}

// @Summary			参观价值 （2.0新增）
// @Description		参观价值
// @Tags			管理后台-观众-参观价值
// @Accept			multipart/form-data
// @Produce			json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		formData		protocol.ReqAudienceVisitingValue				        true	"Query Params"
// @Success		200			{object}	protocol.Response{}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/audience/visiting/value [POST]
func RecordAudienceVisitingValue(c *gin.Context) {
	var (
		req protocol.ReqAudienceVisitingValue
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if _, reqErr := govalidator.ValidateStruct(req); reqErr != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, reqErr)
		return
	}
	req.OpUser = getAdminUser(c).FullName
	resErr := service.RecordAudienceVisitingValue(req)
	if resErr != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, resErr)
	}
}

// @Summary		获取展商管理后台（2.0修改 ，新增字段）
// @Description	获取展商管理后台
// @Tags			管理后台-展商管理
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		query		protocol.PageReqConferenceId				        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=protocol.CompanyManagement}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/audience/company/management [GET]
func GetCompanyManagement(c *gin.Context) {
	var (
		res protocol.ResCompanyManagement
		err protocol.ResError
		req protocol.PageReqConferenceId
	)
	req.DefaultPage()
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	newErr := c.ShouldBind(&req)
	if newErr != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, newErr)
		return
	}
	if _, reqErr := govalidator.ValidateStruct(req); reqErr != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, reqErr)
		return
	}
	req.ReqPage.DefaultPage()
	list, total, err_ := service.GetCompanyManagement(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
	res.List = list
	res.Info.InitPageInfo(total, req.Page, req.PageSize)
}

// @Summary		记录展商管理后台 （2.0修改 ，新增字段）
// @Description	记录展商管理后台
// @Tags			管理后台-展商管理
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		formData		protocol.ReqCompanyManagement				        true	"Query Params"
// @Success		200			{object}	protocol.Response{}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			 /admin/audience/record/company [POST]
func RecordCompany(c *gin.Context) {
	var (
		req protocol.ReqCompanyManagement
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if _, reqErr := govalidator.ValidateStruct(req); reqErr != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, reqErr)
		return
	}
	req.OpUser = getAdminUser(c).FullName
	err_ = service.RecordCompany(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		编辑展商管理后台 （2.0修改 ，新增字段）
// @Description	编辑展商管理后台
// @Tags			管理后台-展商管理
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		formData		protocol.ReqCompanyManagement				        true	"Query Params"
// @Success		200			{object}	protocol.Response{}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			 /admin/audience/edit/company [POST]
func EditAudienceConferenceCompany(c *gin.Context) {
	var (
		req protocol.ReqCompanyManagement
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if _, reqErr := govalidator.ValidateStruct(req); reqErr != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, reqErr)
		return
	}
	req.OpUser = getAdminUser(c).FullName
	err_ = service.EditAudienceConferenceCompany(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
	return
}

// @Summary		删除观众展商管理后台
// @Description	删除观众展商管理后台
// @Tags			管理后台-展商管理
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"admin_token"
// @Param			req		query				protocol.ReqConferenceId		        true	"Query Params"
// @Success		200			{object}	protocol.Response{}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router		/admin/audience/delete/company	 [POST]
func DeleteAudienceConferenceCompany(c *gin.Context) {
	var (
		req protocol.ReqCompanyId
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if _, err_ = govalidator.ValidateStruct(req); err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	opUser := getAdminUser(c).FullName
	err_ = service.DeleteAudienceConferenceCompany(req, opUser)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		根据id获取展商信息 （2.0修改 ，新增展商相关字段）
// @Description	根据id获取展商信息
// @Tags			管理后台-展商管理
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		query		protocol.ReqCompanyId				        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=protocol.CompanyManagement}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/audience/single/company [GET]
func GetSingleAudienceConferenceCompany(c *gin.Context) {
	var (
		req protocol.ReqCompanyId
		res protocol.CompanyManagement
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetSingleAudienceConferenceCompany(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		前台获取展商名录（2.0修改 ，接口传参、出参修改）
// @Description	前台获取展商名录
// @Tags			前台-展商名录
// @Accept			multipart/form-data
// @Produce		json
// @Param			AUTH-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		query		protocol.ReqCompanyDirectories				        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=protocol.CompanyDirectories}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/user/audience/company/directories [GET]
func GetCompanyDirectories(c *gin.Context) {
	var (
		res protocol.ResCompanyDirectories
		req protocol.ReqCompanyDirectories
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	req.DefaultPage()
	directories, total, err2 := service.GetCompanyDirectories(req)
	if err2 != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err2)
		return
	}
	res.List = directories
	res.Info.InitPageInfo(total, req.Page, req.PageSize)
	return
}

// @Summary		前台获取展商风采数据（展商风采  （2.0修改 ，新增接口）
// @Description	前台获取展商风采数据
// @Tags			前台-展商
// @Accept			multipart/form-data
// @Produce		json
// @Param			AUTH-TOKEN	header		string										    true	"admin_token"
// @Param			req		query			protocol.PageReqConferenceId			        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=protocol.ResCompanyBearingList}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/user/audience/company/bearing/list [GET]
func UserGetCompanyBearing(c *gin.Context) {
	var (
		req protocol.PageReqConferenceId
		res protocol.ResCompanyBearingList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	list, total, err_ := service.GetCompanyBearing(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
	res.List = list
	res.Info.InitPageInfo(total, req.Page, req.PageSize)
}

// @Summary		根据id获取风采详情（2.0修改 ，新增接口）
// @Description	根据id获取风采详情
// @Tags			前台-展商
// @Accept			multipart/form-data
// @Produce		json
// @Param			AUTH-TOKEN	header		string										    true	"admin_token"
// @Param			req		query		protocol.ReqCompanyBearingID				        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=protocol.CompanyBearing}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/user/audience/company/bearing [GET]
func UserGetCompanyBearingByID(c *gin.Context) {
	var (
		req protocol.ReqCompanyBearingID
		res protocol.CompanyBearing
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetCompanyBearingById(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		获取行业资讯
// @Description	获取行业资讯
// @Tags			前台-首页
// @Accept			multipart/form-data
// @Produce		json
// @Param			AUTH-TOKEN	header		string										    true	"admin_token"
// @Param			req		query		protocol.ReqConferenceId				        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=protocol.ResIndustryInfo}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/user/audience/industry/info [GET]
func GetIndustryInfo(c *gin.Context) {
	var (
		req protocol.ReqConferenceId
		res protocol.ResIndustryInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetIndustryInfo(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		记录展览平面图
// @Description	记录展览平面图
// @Tags			管理后台-展览平面图
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		query		protocol.ReqFloorGraph			        true	"Query Params"
// @Success		200			{object}	protocol.Response{}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/audience/record/floor/graph [POST]
func RecordFloorGraph(c *gin.Context) {
	var (
		req protocol.ReqFloorGraph
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if _, reqErr := govalidator.ValidateStruct(req); reqErr != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, reqErr)
		return
	}
	req.OpUser = getAdminUser(c).FullName
	err_ = service.RecordFloorGraph(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
	}
}

// @Summary		管理后台查询展览平面图
// @Description	管理后台查询展览平面图
// @Tags			管理后台-展览平面图
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		query		protocol.ReqConferenceId				     true	"Query Params"
// @Success		200			{object}	protocol.Response{data=protocol.ResPageFloorGraph}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/audience/floor/graph [GET]
func GetFloorGraphList(c *gin.Context) {
	var (
		req protocol.ReqConferenceId
		res protocol.ResPageFloorGraph
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if _, reqErr := govalidator.ValidateStruct(req); reqErr != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, reqErr)
		return
	}
	list, _, err_ := service.GetPageFloorGraphList(req.Id, 1, 999)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
	}
	res.List = list
}

// @Summary		根据id获取展览平面图信息
// @Description	根据id获取展览平面图信息
// @Tags			管理后台-展览平面图
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		query		protocol.ReqFloorGraphID				        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=protocol.FloorGraph}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/audience/single/floor/graph [GET]
func GetSingleFloorGraph(c *gin.Context) {
	var (
		req protocol.ReqFloorGraphID
		res protocol.FloorGraph
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if _, reqErr := govalidator.ValidateStruct(req); reqErr != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, reqErr)
		return
	}
	res, err_ = service.GetSingleFloorGraph(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
}

// @Summary		查询展览平面图
// @Description	查询展览平面图
// @Tags			前台-观众
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		query		protocol.ReqConferenceId				        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=protocol.ResPageFloorGraph}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/user/audience/floor/graph [GET]
func UserGetFloorGraph(c *gin.Context) {
	var (
		req protocol.ReqConferenceId
		res []protocol.FloorGraph
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetFloorGraphList(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
	}
}

// @Summary		更新展览平面图
// @Description	更新展览平面图
// @Tags			管理后台-展览平面图
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		query		protocol.ReqUpdateFloorGraph				        true	"Query Params"
// @Success		200			{object}	protocol.Response{}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/audience/update/floor/graph [POST]
func UpdateFloorGraph(c *gin.Context) {
	var (
		req protocol.ReqUpdateFloorGraph
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if _, reqErr := govalidator.ValidateStruct(req); reqErr != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, reqErr)
		return
	}
	req.OpUser = getAdminUser(c).FullName
	err_ = service.UpdateFloorGraph(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		删除展览平面图
// @Description	删除展览平面图
// @Tags			管理后台-展览平面图
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"SMM-ADMIN-TOKEN"
// @Param			req		query		protocol.ReqFloorGraphID			        true	"Query Params"
// @Success		200			{object}	protocol.Response{}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/audience/delete/floor/graph [POST]
func DeleteFloorGraph(c *gin.Context) {
	var (
		req protocol.ReqFloorGraphID
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		return
	}
	if _, reqErr := govalidator.ValidateStruct(req); reqErr != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, reqErr)
		return
	}
	req.OpUser = getAdminUser(c).FullName
	err_ = service.DeleteFloorGraph(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		新增或编辑展后报告
// @Description	新增或编辑展后报告
// @Tags			管理后台-展后报告
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"admin_token"
// @Param			req		formData		protocol.ReqSaveAfterReport				        true	"Query Params"
// @Success		200			{object}	protocol.Response{}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/audience/save/after/report [POST]
func SaveAfterReport(c *gin.Context) {
	var (
		req protocol.ReqSaveAfterReport
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if _, err_ = govalidator.ValidateStruct(req); err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	err_ = service.SaveAfterReport(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		获取展后报告
// @Description	获取展后报告
// @Tags			管理后台-展后报告
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"admin_token"
// @Param			req		query			protocol.ReqAfterReportID			        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=protocol.AfterReport}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router		/admin/audience/after/report	 [GET]
func GetAfterReport(c *gin.Context) {
	var (
		req protocol.ReqAfterReportID
		res protocol.AfterReport
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetAfterReportById(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		获取展后报告列表
// @Description	获取展后报告列表
// @Tags			管理后台-展后报告
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"admin_token"
// @Param			req		query		protocol.ReqConferenceId				        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=[]protocol.AfterReport}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/audience/after/report/list [GET]
func GetAfterReportList(c *gin.Context) {
	var (
		req protocol.ReqConferenceId
		res []protocol.AfterReport
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetAfterReportList(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		前台获取展后报告列表
// @Description	前台获取展后报告列表
// @Tags			前台-展后报告
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"admin_token"
// @Param			req		query		protocol.ReqConferenceId				        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=[]protocol.AfterReport}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/user/audience/after/report/list [GET]
func UserGetAfterReportList(c *gin.Context) {
	var (
		req protocol.ReqConferenceId
		res []protocol.AfterReport
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetAfterReportList(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		删除展后报告
// @Description	删除展后报告
// @Tags			管理后台-展后报告
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"admin_token"
// @Param		req			query			protocol.ReqAfterReportID			        true	"Query Params"
// @Success		200			{object}	protocol.Response{}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router		/admin/audience/delete	 [POST]
func DeleteAfterReport(c *gin.Context) {
	var (
		req protocol.ReqAfterReportID
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	err_ = service.DeleteAfterReport(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary  新增编辑论文征集
// @Description   新增编辑论文征集
// @Tags		管理后台-论文征集
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"admin_token"
// @Param			req		formData			protocol.ReqSavePaperCollection			        true	"Query Params"
// @Success		200			{object}	protocol.Response{}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router		/admin/audience/save/paper	 [POST]
func SavePaperCollection(c *gin.Context) {
	var (
		req protocol.ReqSavePaperCollection
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if _, err_ = govalidator.ValidateStruct(req); err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	err_ = service.SavePaperCollection(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		获取论文征集数据
// @Description	获取论文征集数据
// @Tags			管理后台-论文征集
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"admin_token"
// @Param			req		query		protocol.ReqConferenceId				        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=protocol.PaperCollection}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/audience/paper/collection [GET]
func GetPaperCollection(c *gin.Context) {
	var (
		req protocol.ReqConferenceId
		res protocol.PaperCollection
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetPaperCollection(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		前台获取论文征集数据
// @Description	前台获取论文征集数据
// @Tags			前台-论文征集
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"admin_token"
// @Param			req		query		protocol.ReqConferenceId				        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=protocol.PaperCollection}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/user/audience/paper/collection [GET]
func UserGetPaperCollection(c *gin.Context) {
	var (
		req protocol.ReqConferenceId
		res protocol.PaperCollection
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetPaperCollection(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary			后台获取产品管理数据（2.0新增）
// @Description		后台获取产品管理数据
// @Tags			后台-产品管理
// @Accept			multipart/form-data
// @Produce			json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"admin_token"
// @Param			req		query			protocol.ReqCompanyId			        true	"Query Params"
// @Success			200			{object}	protocol.Response{data=protocol.ResPageProductionManagement}	"ok，code = 0"
// @Failure			500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/audience/production/management [GET]
func AdminGetProductionManagement(c *gin.Context) {
	var (
		req protocol.ReqCompanyId
		res protocol.ResPageProductionManagement
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if _, reqErr := govalidator.ValidateStruct(req); reqErr != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, reqErr)
		return
	}
	req.ReqPage.DefaultPage()
	list, total, err_ := service.GetProductionManagement(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
	res.Info.InitPageInfo(total, req.Page, req.PageSize)
	res.List = list
}

// @Summary		保存或编辑产品信息 （2.0新增）
// @Description	保存或编辑产品信息
// @Tags			后台-产品管理
// @Accept			json
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"admin_token"
// @Param			req		body		protocol.ReqSaveProductionManagement				        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=string}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/audience/add_edit/production [POST]
func AdminSaveProduction(c *gin.Context) {
	var (
		req protocol.ReqSaveProductionManagement
		res string
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	req.UserName = getAdminUser(c).FullName
	if _, reqErr := govalidator.ValidateStruct(req); reqErr != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, reqErr)
		return
	}
	res, err_ = service.SaveProduction(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		删除产品信息（2.0新增）
// @Description	删除产品信息
// @Tags			后台-产品管理
// @Accept			multipart/form-data
// @Produce		json
// @Param			SMM-ADMIN-TOKEN 	header		string										    true	"admin_token"
// @Param			req		query			protocol.ReqProductionId			        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=string}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/audience/delete/production [POST]
func AdminDeleteProduction(c *gin.Context) {
	var (
		req protocol.ReqProductionId
		res string
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if _, reqErr := govalidator.ValidateStruct(req); reqErr != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, reqErr)
		return
	}
	res, err_ = service.DeleteProduction(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

//TODO 不知道需要吗

// @Summary		根据id获取产品信息 （2.0新增）
// @Description	根据id获取产品信息
// @Tags			后台-根据id获取产品信息
// @Accept			multipart/form-data
// @Produce		json
// @Param			AUTH-TOKEN	header		string										    true	"admin_token"
// @Param			req		query				protocol.ReqProductionId		        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=protocol.ProductionManagement}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/production [GET]
func AdminGetProduction(c *gin.Context) {
	var (
		req protocol.ReqProductionId
		res protocol.ProductionManagement
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetProductionById(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		海外观众邮箱导入
// @Description	海外观众邮箱导入
// @Tags			后台-观众-海外观众邮箱导入发送邮件
// @Accept			json
// @Produce		json
// @Param			SMM-ADMIN-TOKEN	header		string										    true	"admin_token"
// @Param			req		body		protocol.ReqSaveProductionManagement				        true	"Query Params"
// @Success		200			{object}	protocol.Response{data=string}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/admin/audience/import [POST]
func AdminAudienceImport(c *gin.Context) {
	var (
		req protocol.ReqBatchImportAudience
		res string
		err protocol.ResError
	)

	defer func() {
		wrapResp(c, res, addCallback, err)
	}()

	file, _, err_ := c.Request.FormFile("file")
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	defer file.Close()
	req.File = file

	adminInfo, exist := c.Get(constant.AdminInfoKey)
	if exist {
		if utils.NotEmpty(adminInfo.(*admin.User).FullName) {
			req.AdminEmail = adminInfo.(*admin.User).FullName
		} else if utils.NotEmpty(adminInfo.(*admin.User).Email) {
			req.AdminEmail = adminInfo.(*admin.User).Email
		}
	}

	res, err_ = service.AdminAudienceImport(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}

}
