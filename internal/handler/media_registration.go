package handler

import (
	"bytes"
	"conferencecenter/internal/errcode"
	"conferencecenter/internal/protocol"
	"conferencecenter/internal/service"
	"fmt"
	"github.com/asaskevich/govalidator"
	"github.com/gin-gonic/gin"
)

// @Summary		    大会介绍数据列表
// @Description	    大会介绍数据列表API
// @Tags			管理后台-首页其他-数据
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqGetMediaRegistrationList					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.ResAdminMediaRegistrationList}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						                "failed，code != 0"
// @Router			/admin/media_registration/list  [get]
func AdminMediaRegistrationList(c *gin.Context) {
	var (
		req protocol.ReqGetMediaRegistrationList
		res protocol.ResAdminMediaRegistrationList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	list, total, err_ := service.QueryConferenceMediaRegistrationList(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
	res.List = list
	res.Info.InitPageInfo(total, req.Page, req.PageSize)
	return
}

// @Summary		    导出报名信息列表
// @Description	    导出报名信息列表API
// @Tags			管理后台-报名信息
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string						    true    "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqGetMediaRegistrationList  	false	"Query Params"
// @Success		    200					                                                "excel文件"
// @Failure		    500			{object}	protocol.Response{}						    "failed，code != 0"
// @Router			/admin/register/export  [get]
func CnAdminExportMediaRegistrationList(c *gin.Context) {
	var (
		req        protocol.ReqGetMediaRegistrationList
		err        protocol.ResError
		exlData    *bytes.Reader
		dataLength int64
		fileName   = "中文媒体报名信息列表"
	)
	defer func() {
		wrapExcelResp(c, fmt.Sprintf("%s.xlsx", fileName), dataLength, exlData, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	req.Language = "cn"
	exlData, err_ = service.CnExportMediaRegistrationList(req)
	if err_ != nil || exlData == nil {
		err = protocol.NewResErrWithMsg(errcode.RESPONSE_CODE_PARAM_ERR, "参数错误", err_)
		return
	}
	dataLength = int64(exlData.Len())
	return
}

// @Summary		    导出报名信息列表
// @Description	    导出报名信息列表API
// @Tags			管理后台-报名信息
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string						    true    "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqGetMediaRegistrationList  	false	"Query Params"
// @Success		    200					                                                "excel文件"
// @Failure		    500			{object}	protocol.Response{}						    "failed，code != 0"
// @Router			/admin/register/export  [get]
func EnAdminExportMediaRegistrationList(c *gin.Context) {
	var (
		req        protocol.ReqGetMediaRegistrationList
		err        protocol.ResError
		exlData    *bytes.Reader
		dataLength int64
		fileName   = "英文媒体报名信息列表"
	)
	defer func() {
		wrapExcelResp(c, fmt.Sprintf("%s.xlsx", fileName), dataLength, exlData, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	req.Language = "en"
	exlData, err_ = service.EnExportMediaRegistrationList(req)
	if err_ != nil || exlData == nil {
		err = protocol.NewResErrWithMsg(errcode.RESPONSE_CODE_PARAM_ERR, "参数错误", err_)
		return
	}
	dataLength = int64(exlData.Len())
	return
}

// @Summary		    大会介绍和介绍视频详情
// @Description	    查询大会介绍和介绍视频API
// @Tags			管理后台-首页其他-大会介绍和介绍视频
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										false   "auth_token"
// @Param			payload		formData	protocol.ReqGetIntroductionInfo     true    "json body"
// @Success		    200			{object}	protocol.Response{data=protocol.RespIntroductionInfo}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						            "failed，code != 0"
// @Router			/admin/media_registration/config/detail    [get]
func AdminQueryMediaRegistrationInfo(c *gin.Context) {
	var (
		req protocol.ReqGetIntroductionInfo
		res protocol.RespMediaRegistrationConfigInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetMediaRegistrationConfigInfo(req.ConferenceId)
	return
}

// @Summary		    新增或修改大会介绍和介绍视频
// @Description	    新增或修改大会介绍和介绍视频API
// @Tags			管理后台-首页其他-大会介绍和介绍视频
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string				                true    "SMM-ADMIN-TOKEN"
// @Param			payload		formData	protocol.ReqAdminSaveIntroduction     true    "json body"
// @Success		    200			{object}	protocol.Response{}                 "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                 "failed，code != 0"
// @Router	        /admin/media_registration/config/add_edit  [post]
func AdminAddEditMediaRegistrationConfig(c *gin.Context) {
	var (
		req protocol.ReqAdminSaveMediaRegistrationConfig
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if _, reqErr := govalidator.ValidateStruct(req); reqErr != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, reqErr)
		return
	}
	_, err_ = service.AddEditMediaRegistrationConfig(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		    新增或修改大会介绍和介绍视频
// @Description	    新增或修改大会介绍和介绍视频API
// @Tags			管理后台-首页其他-大会介绍和介绍视频
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string				                true    "SMM-ADMIN-TOKEN"
// @Param			payload		formData	protocol.ReqAdminSaveIntroduction     true    "json body"
// @Success		    200			{object}	protocol.Response{}                 "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                 "failed，code != 0"
// @Router	        /user/cn/media_registration/add  [post]
func CnUserAddMediaRegistration(c *gin.Context) {
	var (
		req protocol.ReqAdminSaveMediaRegistration
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if _, reqErr := govalidator.ValidateStruct(req); reqErr != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, reqErr)
		return
	}
	_, err_ = service.CnUserAddMediaRegistration(req)
	if err_ != nil {
		err = protocol.NewResErrWithMsg(errcode.RESPONSE_CODE_SYSTEM_ERR, err_.Error(), err_)
		return
	}
}

// @Summary		    新增或修改大会介绍和介绍视频
// @Description	    新增或修改大会介绍和介绍视频API
// @Tags			管理后台-首页其他-大会介绍和介绍视频
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string				                true    "SMM-ADMIN-TOKEN"
// @Param			payload		formData	protocol.ReqAdminSaveIntroduction     true    "json body"
// @Success		    200			{object}	protocol.Response{}                 "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                 "failed，code != 0"
// @Router	        /user/en/media_registration/add  [post]
func EnUserAddMediaRegistration(c *gin.Context) {
	var (
		req protocol.ReqAdminSaveMediaRegistration
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if _, reqErr := govalidator.ValidateStruct(req); reqErr != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, reqErr)
		return
	}
	_, err_ = service.EnUserAddMediaRegistration(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}
