package handler

import (
	"strconv"

	"conferencecenter/internal/errcode"
	"conferencecenter/internal/protocol"
	"conferencecenter/internal/service"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

// @Summary		    查询活动分类列表(2.0新增)
// @Description	    活动分类管理API
// @Tags			管理后台-活动分类管理
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqGetConferenceEventCategoryList					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.ResAdminConferenceEventCategoryList}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						                "failed，code != 0"
// @Router			/admin/event/category/list  [get]
func AdminConferenceEventCategoryList(c *gin.Context) {
	var (
		req protocol.ReqGetConferenceEventCategoryList
		res protocol.ResAdminConferenceEventCategoryList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	list, total, err2 := service.QueryConferenceEventCategoryList(req)
	if err2 != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err2)
		return
	}
	res.List = list
	res.Info.InitPageInfo(total, req.Page, req.PageSize)
	return
}

// @Summary		    新增或修改活动分类管理(2.0新增)
// @Description	    新增或修改活动分类管理API
// @Tags			管理后台-活动分类管理
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string				                true    "SMM-ADMIN-TOKEN"
// @Param			payload		body	protocol.ReqAdminSaveConferenceEventCategory     true    "json body"
// @Success		    200			{object}	protocol.Response{}                 "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                 "failed，code != 0"
// @Router	        /admin/event/category/add_edit  [post]
func AdminAddEditConferenceEventCategory(c *gin.Context) {
	var (
		req protocol.ReqAdminSaveConferenceEventCategory
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	_, err_ = service.AddEditConferenceEventCategory(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	return
}

// @Summary		    活动分类详情(2.0新增)
// @Description	    查询活动分类详情API
// @Tags			管理后台-活动分类管理
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqConferenceEventInfo					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.RespAdminConferenceEventCategoryInfo}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						            "failed，code != 0"
// @Router			/admin/event/category/detail/id    [get]
func AdminQueryConferenceEventCategoryInfo(c *gin.Context) {
	var (
		req protocol.ReqConferenceEventInfo
		res protocol.RespAdminConferenceEventCategoryInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetConferenceEventCategoryInfo(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	return
}

// @Summary		    删除活动分类(2.0新增)
// @Description	    删除活动分类API
// @Tags			管理后台-活动分类管理
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string				        true    "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqConferenceEventInfo					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{}                 "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                 "failed，code != 0"
// @Router	        /admin/event/category/delete/id  [post]
func AdminDeleteConferenceEventCategory(c *gin.Context) {
	var (
		req protocol.ReqConferenceEventInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	resMsg, err_ := service.DeleteConferenceEventCategory(req.Id)
	if err_ != nil {
		err = protocol.NewResErrWithMsg(errcode.RESPONSE_CODE_PARAM_ERR, resMsg, err_)
		return
	}
	return
}

// @Summary		    查询活动一览表列表
// @Description	    查询活动列表API
// @Tags			管理后台-活动一览表
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqGetConferenceEventList					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.ResAdminConferenceEventList}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						                "failed，code != 0"
// @Router			/admin/event/list  [get]
func AdminConferenceEventList(c *gin.Context) {
	var (
		req protocol.ReqGetConferenceEventList
		res protocol.ResAdminConferenceEventList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	list, total, err2 := service.QueryConferenceEventList(req)
	if err2 != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err2)
		return
	}
	res.List = list
	res.Info.InitPageInfo(total, req.Page, req.PageSize)
	return
}

// @Summary		    新增或修改活动一览表(2.0添加活动分类字段)
// @Description	    新增或修改活动一览表API
// @Tags			管理后台-活动一览表
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string				                true    "SMM-ADMIN-TOKEN"
// @Param			payload		body	protocol.ReqAdminSaveConferenceEvent     true    "json body"
// @Success		    200			{object}	protocol.Response{}                 "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                 "failed，code != 0"
// @Router	        /admin/event/add_edit  [post]
func AdminAddEditConferenceEvent(c *gin.Context) {
	var (
		req protocol.ReqAdminSaveConferenceEvent
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	_, err_ = service.AddEditConferenceEvent(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	return
}

// @Summary		    活动一览表详情
// @Description	    查询活动一览表API
// @Tags			管理后台-活动一览表
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqConferenceEventInfo					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.RespAdminConferenceEventInfo}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						            "failed，code != 0"
// @Router			/admin/event/detail/id    [get]
func AdminQueryConferenceEventInfo(c *gin.Context) {
	var (
		req protocol.ReqConferenceEventInfo
		res protocol.RespAdminConferenceEventInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetConferenceEventInfo(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	return
}

// @Summary		    删除活动一览表
// @Description	    删除活动一览表API
// @Tags			管理后台-活动一览表
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string				        true    "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqConferenceEventInfo					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{}                 "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                 "failed，code != 0"
// @Router	        /admin/event/delete/id  [post]
func AdminDeleteConferenceEvent(c *gin.Context) {
	var (
		req protocol.ReqConferenceEventInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	_, err_ = service.DeleteConferenceEvent(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	return
}

// @Summary		    修改活动日程日期显示名称和排序
// @Description	    修改活动日程日期显示名称和排序API
// @Tags			管理后台-活动日程
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string				                true    "SMM-ADMIN-TOKEN"
// @Param			payload		formData	protocol.ReqAdminSaveEventScheduleDate     true    "json body"
// @Success		    200			{object}	protocol.Response{}                 "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                 "failed，code != 0"
// @Router	        /admin/schedule/date/add_edit  [post]
func AdminAddEditEventScheduleDate(c *gin.Context) {
	var (
		req protocol.ReqAdminSaveEventScheduleDate
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	_, err_ = service.AddEditEventScheduleDate(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	return
}

// @Summary		    查询活动分论坛日程列表
// @Description	    查询活动分论坛日程列表API
// @Tags			管理后台-活动日程-分论坛
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqGetConferenceForumList					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.ResAdminEventScheduleForumList}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						                "failed，code != 0"
// @Router			/admin/schedule/forum/list  [get]
func AdminEventScheduleForumList(c *gin.Context) {
	var (
		req protocol.ReqGetConferenceForumList
		res protocol.ResAdminEventScheduleForumList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	list, total, err2 := service.QueryEventScheduleForumList(req)
	if err2 != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err2)
		return
	}
	res.List = list
	res.Info.InitPageInfo(total, 0, 0)

	return
}

// @Summary		    新增或修改活动分论坛日程
// @Description	    新增或修改活动分论坛日程API
// @Tags			管理后台-活动日程-分论坛
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string				                true    "SMM-ADMIN-TOKEN"
// @Param			payload		formData	protocol.ReqAdminSaveEventScheduleForum     true    "json body"
// @Success		    200			{object}	protocol.Response{}                 "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                 "failed，code != 0"
// @Router	        /admin/schedule/forum/add_edit  [post]
func AdminAddEditEventScheduleForum(c *gin.Context) {
	var (
		req protocol.ReqAdminSaveEventScheduleForum
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	_, err_ = service.AddEditEventScheduleForum(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	return
}

// @Summary		    活动分论坛日程详情
// @Description	    查询活动分论坛日程API
// @Tags			管理后台-活动日程-分论坛
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqGetEventScheduleForumInfo					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.RespEventScheduleInfo}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						            "failed，code != 0"
// @Router			/admin/schedule/forum/detail/id    [get]
func AdminQueryEventScheduleForumInfo(c *gin.Context) {
	var (
		req protocol.ReqGetEventScheduleForumInfo
		res protocol.RespEventScheduleForumInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetEventScheduleForumInfo(req.ForumId)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	return
}

// @Summary		    删除活动分论坛日程
// @Description	    删除活动分论坛日程API
// @Tags			管理后台-活动日程-分论坛
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string				        true    "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqGetEventScheduleForumInfo					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{}                 "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                 "failed，code != 0"
// @Router	        /admin/schedule/forum/delete/id  [post]
func AdminDeleteEventScheduleForum(c *gin.Context) {
	var (
		req protocol.ReqGetEventScheduleForumInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	_, err_ = service.DeleteEventScheduleForum(req.ForumId)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	return
}

// @Summary		    查询活动日程列表
// @Description	    查询活动日程列表API
// @Tags			管理后台-活动日程
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqGetEventScheduleList					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.ResAdminEventScheduleList}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						                "failed，code != 0"
// @Router			/admin/schedule/list  [get]
func AdminEventScheduleList(c *gin.Context) {
	var (
		req protocol.ReqGetEventScheduleList
		res protocol.ResAdminEventScheduleList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	list, total, err2 := service.QueryEventScheduleList(req)
	if err2 != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err2)
		return
	}
	res.List = list
	res.Info.InitPageInfo(total, req.Page, req.PageSize)

	return
}

// @Summary		    新增或修改活动日程
// @Description	    新增或修改活动日程API
// @Tags			管理后台-活动日程
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string				                true    "SMM-ADMIN-TOKEN"
// @Param			payload		formData	protocol.ReqAdminSaveEventSchedule     true    "json body"
// @Success		    200			{object}	protocol.Response{}                 "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                 "failed，code != 0"
// @Router	        /admin/schedule/add_edit  [post]
func AdminAddEditEventSchedule(c *gin.Context) {
	var (
		req protocol.ReqAdminSaveEventSchedule
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	_, err_ = service.AddEditEventSchedule(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	return
}

// @Summary		    删除活动日程
// @Description	    删除活动日程API
// @Tags			管理后台-活动日程
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string				        true    "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqGetEventScheduleInfo					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{}                 "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                 "failed，code != 0"
// @Router	        /admin/schedule/delete/id  [post]
func AdminDeleteEventSchedule(c *gin.Context) {
	var (
		req protocol.ReqGetEventScheduleInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	_, err_ = service.DeleteEventSchedule(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	return
}

// @Summary		    活动日程详情
// @Description	    查询活动日程API
// @Tags			管理后台-活动日程
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqGetEventScheduleInfo					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.RespEventScheduleInfo}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						            "failed，code != 0"
// @Router			/admin/schedule/detail/id    [get]
func AdminQueryEventScheduleInfo(c *gin.Context) {
	var (
		req protocol.ReqGetEventScheduleInfo
		res protocol.RespEventScheduleInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetEventScheduleInfo(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	return
}

// @Summary		    查询活动日程嘉宾列表
// @Description	    查询活动日程嘉宾列表API
// @Tags			管理后台-活动日程-嘉宾
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqGetEventScheduleGuestList					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.ResAdminEventScheduleGuestList}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						                "failed，code != 0"
// @Router			/admin/schedule/guest/list  [get]
func AdminEventScheduleGuestList(c *gin.Context) {
	var (
		req protocol.ReqGetEventScheduleGuestList
		res protocol.ResAdminEventScheduleGuestList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	list, total, err2 := service.QueryEventScheduleGuestList(req)
	if err2 != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err2)
		return
	}
	res.List = list
	res.Info.InitPageInfo(total, req.Page, req.PageSize)

	return
}

// @Summary		    新增或修改活动日程嘉宾
// @Description	    新增或修改活动日程嘉宾API
// @Tags			管理后台-活动日程-嘉宾
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string				                true    "SMM-ADMIN-TOKEN"
// @Param			payload		formData	protocol.ReqAdminSaveEventScheduleGuest     true    "json body"
// @Success		    200			{object}	protocol.Response{}                 "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                 "failed，code != 0"
// @Router	        /admin/schedule/guest/add_edit  [post]
func AdminAddEditEventScheduleGuest(c *gin.Context) {
	var (
		req protocol.ReqAdminSaveEventScheduleGuest
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	_, err_ = service.AddEditEventScheduleGuest(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	return
}

// @Summary		    删除活动日程嘉宾嘉宾
// @Description	    删除活动日程嘉宾嘉宾API
// @Tags			管理后台-活动日程-嘉宾
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string				        true    "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqEventScheduleGuestInfo					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{}                 "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                 "failed，code != 0"
// @Router	        /admin/schedule/guest/delete/id  [post]
func AdminDeleteEventScheduleGuest(c *gin.Context) {
	var (
		req protocol.ReqEventScheduleGuestInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	_, err_ = service.DeleteEventScheduleGuest(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	return
}

// @Summary		    活动日程嘉宾详情
// @Description	    查询活动日程嘉宾详情API
// @Tags			管理后台-活动日程-嘉宾
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqEventScheduleGuestInfo					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.RespEventScheduleGuestInfo}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						            "failed，code != 0"
// @Router			/admin/schedule/guest/detail/id    [get]
func AdminQueryEventScheduleGuestInfo(c *gin.Context) {
	var (
		req protocol.ReqEventScheduleGuestInfo
		res protocol.RespEventScheduleGuestInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetEventScheduleGuestInfo(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	return
}

// @Summary		    查询活动一览表列表 (2.0添加活动分类字段)
// @Description	    查询活动列表API
// @Tags			前台-活动一览表
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM_auth_token	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqWebConferenceList					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.ResUserConferenceEventList}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						                "failed，code != 0"
// @Router			/user/event/list  [get]
func UserConferenceEventList(c *gin.Context) {
	var (
		req protocol.ReqWebConferenceList
		res protocol.ResUserConferenceEventList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if req.ConferenceId <= 0 {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, errors.New("展会ID不能为空"))
		return
	}
	//if req.CategoryType <= 0 {
	//	err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, errors.New("活动分类不能为空"))
	//	return
	//}
	res, err_ = service.UserConferenceEventList(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	return
}

// @Summary		    活动日程详情
// @Description	    查询活动详情API
// @Tags			前台-活动一览表
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM_auth_token	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqWebConferenceEventInfo					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.RespUserConferenceEventInfo}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						            "failed，code != 0"
// @Router			/user/schedule/detail    [get]
func UserQueryConferenceEventInfo(c *gin.Context) {
	var (
		req protocol.ReqWebConferenceEventInfo
		res protocol.RespUserConferenceEventInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetUserConferenceEventInfo(req.EventId)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	return
}

// @Summary		    查询活动日程分论坛列表
// @Description	    查询活动日程分论坛列表API
// @Tags			前台-活动一览表
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM_auth_token	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqGetConferenceForumList					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.ResUserEventScheduleForumList}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						                "failed，code != 0"
// @Router			/user/schedule_forum/list  [get]
func UserEventScheduleForumList(c *gin.Context) {
	var (
		req protocol.ReqGetConferenceForumList
		res protocol.ResUserEventScheduleForumList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	list, total, err2 := service.QueryEventScheduleForumList(req)
	if err2 != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err2)
		return
	}
	res.List = list
	res.Info.InitPageInfo(total, 0, 0)
	return
}

// @Summary		    查询活动日程列表
// @Description	    查询活动日程列表API
// @Tags			前台-活动一览表
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM_auth_token	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqWebEventScheduleList					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.ResUserEventScheduleList}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						                "failed，code != 0"
// @Router			/user/schedule/list  [get]
func UserEventScheduleList(c *gin.Context) {
	var (
		req protocol.ReqGetEventScheduleList
		res protocol.ResUserEventScheduleList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.UserEventScheduleList(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	return
}

// @Summary		    活动类别关联导航菜单
// @Description	    活动类别关联导航菜单列表API
// @Tags			前台-活动一览表
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM_auth_token	header		string										      true  "SMM-ADMIN-TOKEN"
// @Success		    200			{object}	protocol.Response{data=protocol.ResForumCategoryConfigList}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						                "failed，code != 0"
// @Router			/user/forum_category/list  [get]
func UserForumCategoryConfigList(c *gin.Context) {
	var (
		res protocol.ResForumCategoryConfigList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()

	list, total, err_ := service.UserForumCategoryConfigList()
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}

	res.List = list
	res.Info.InitPageInfo(total, 0, 0)
	return
}

// @Summary		    评选活动评委列表公示
// @Description	    评选活动评委列表公示API
// @Tags			前台-年度评选
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM_auth_token	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqGetConferenceEventCategoryList					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.ResEventExpertInfoList}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						                "failed，code != 0"
// @Router			/user/event/expert/list  [get]
func UserQueryExpertInfoV3List(c *gin.Context) {
	var (
		req protocol.ReqGetConferenceEventCategoryList
		res protocol.ResEventExpertInfoList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	list, total, err_ := service.UserQueryExpertInfoV3List(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}

	res.List = list
	res.Info.InitPageInfo(total, req.Page, req.PageSize)
	return
}

// @Summary		    评选活动评选介绍和流程
// @Description	    评选活动评选介绍和流程API
// @Tags			前台-年度评选
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM_auth_token	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqGetConferenceEventCategoryList					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.ResAnnualSelectionList}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						                "failed，code != 0"
// @Router			/user/event/annual_selection  [get]
func UserAnnualSelectionList(c *gin.Context) {
	var (
		req protocol.ReqGetConferenceEventCategoryList
		res protocol.ResAnnualSelectionList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	list, total, err_ := service.UserAnnualSelectionList(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res.List = list
	res.Info.InitPageInfo(total, req.Page, req.PageSize)
	return
}

// @Summary		    批量导入活动日程EXCEL
// @Description	    批量导入活动日程EXCEL文件，支持批量创建活动、日程和嘉宾信息
// @Tags			管理后台-活动日程
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string	true	"管理员认证token"
// @Param			conference_id	query		int64	true	"展会ID"
// @Param			file			formData	file	true	"Excel文件，包含活动日程数据"
// @Success		    200			{object}	protocol.Response{data=string}	"导入成功，返回导入结果信息"
// @Failure		    400			{object}	protocol.Response{}				"参数错误或文件格式错误"
// @Failure		    500			{object}	protocol.Response{}				"服务器内部错误"
// @Router	        /admin/event/schedule/import  [post]
func AdminImportEventSchedule(c *gin.Context) {
	var res protocol.Response
	var err protocol.ResError
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	s := c.Query("conference_id")
	conferenceId, err_ := strconv.ParseInt(s, 10, 64)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	if conferenceId == 0 {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, errors.New("展会ID不能为空"))
		return
	}
	// 权限校验由路由中间件完成

	file, err_ := c.FormFile("file")
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	openedFile, err_ := file.Open()
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	defer openedFile.Close()

	// 调用 service 层处理导入
	result, err_ := service.AdminImportEventSchedule(openedFile, file.Filename, conferenceId)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res.Data = result
	return
}
