package handler

import (
	"conferencecenter/internal/constant"
	"conferencecenter/internal/errcode"
	"conferencecenter/internal/pkg/utils"
	"conferencecenter/internal/protocol"
	"conferencecenter/internal/service"
	"git.code.tencent.com/smmit/smmbase/admin"
	"github.com/gin-gonic/gin"
)

// @Summary		    查询栏目信息列表
// @Description	    查询栏目信息列表API
// @Tags			管理后台-通用模块
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqColumnList					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.ResAdminColumnList}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						                "failed，code != 0"
// @Router			/admin/column/list  [get]
func AdminQueryConferenceColumn(c *gin.Context) {
	var (
		req protocol.ReqColumnList
		res protocol.ResAdminColumnList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	list, total, err_ := service.QueryConferenceColumnList(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res.List = list
	res.Info.InitPageInfo(total, req.Page, req.PageSize)

	return
}

// @Summary		    新增或修改通用模块
// @Description	    新增或修改通用模块API
// @Tags			管理后台-通用模块
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string				                true    "SMM-ADMIN-TOKEN"
// @Param			payload		formData	protocol.ReqAdminSaveColumn     true    "json body"
// @Success		    200			{object}	protocol.Response{}                 "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                 "failed，code != 0"
// @Router	        /admin/column/add_edit  [post]
func AdminAddEditColumn(c *gin.Context) {
	var (
		req protocol.ReqAdminSaveColumn
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	adminInfo, exist := c.Get(constant.AdminInfoKey)
	if exist {
		if utils.NotEmpty(adminInfo.(*admin.User).FullName) {
			req.AdminEmail = adminInfo.(*admin.User).FullName
		} else if utils.NotEmpty(adminInfo.(*admin.User).Email) {
			req.AdminEmail = adminInfo.(*admin.User).Email
		}
	}

	_, err_ = service.AddEditConferenceColumn(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	return
}

// @Summary		    查询通用模块详情信息
// @Description	    查询展会通用模块详情API
// @Tags			管理后台-通用模块
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqAdminColumnInfo					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.RespConferenceColumnInfo}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						            "failed，code != 0"
// @Router			/admin/column/detail/id    [get]
func AdminQueryColumnInfo(c *gin.Context) {
	var (
		req protocol.ReqAdminColumnInfo
		res protocol.RespConferenceColumnInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetConferenceColumnInfo(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	return
}

// @Summary		    删除通用模块信息
// @Description	    删除通用模块API
// @Tags			管理后台-通用模块
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string				        true    "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqAdminColumnInfo					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{}                 "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                 "failed，code != 0"
// @Router	        /admin/column/delete/id  [post]
func AdminDeleteColumnInfo(c *gin.Context) {
	var (
		req protocol.ReqAdminColumnInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	_, err_ = service.DeleteConferenceColumn(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	return
}

// @Summary		    查询公司信息（嘉宾列表）列表（2.0修改 赞助商，嘉宾都加上中英文图片和排序）
// @Description	    查询公司信息（嘉宾列表）列表API
// @Tags			管理后台-通用模块
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqColumnInformationList					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.ResAdminInformationList}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						                "failed，code != 0"
// @Router			/admin/information/list  [get]
func AdminQueryInformationList(c *gin.Context) {
	var (
		req protocol.ReqColumnInformationList
		res protocol.ResAdminInformationList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	req.IsAdmin = true
	list, total, err_ := service.QueryConferenceInformationList(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res.List = list
	res.Info.InitPageInfo(total, req.Page, req.PageSize)
	return
}

// @Summary		    新增或修改公司信息（嘉宾）（2.0修改 赞助商，嘉宾都加上中英文图片和排序）
// @Description	    新增或修改公司信息（嘉宾）API
// @Tags			管理后台-通用模块
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string				                true    "SMM-ADMIN-TOKEN"
// @Param			payload		formData	protocol.ReqAdminSaveInformation     true    "json body"
// @Success		    200			{object}	protocol.Response{}                 "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                 "failed，code != 0"
// @Router	        /admin/information/add_edit  [post]
func AdminAddEditInformation(c *gin.Context) {
	var (
		req protocol.ReqAdminSaveInformation
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	adminInfo, exist := c.Get(constant.AdminInfoKey)
	if exist {
		if utils.NotEmpty(adminInfo.(*admin.User).FullName) {
			req.AdminEmail = adminInfo.(*admin.User).FullName
		} else if utils.NotEmpty(adminInfo.(*admin.User).Email) {
			req.AdminEmail = adminInfo.(*admin.User).Email
		}
	}
	_, err_ = service.AddEditConferenceInformation(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	return
}

// @Summary		    删除公司信息（嘉宾）
// @Description	    删除公司信息（嘉宾）API
// @Tags			管理后台-通用模块
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string				        true    "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqAdminInformationInfo					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{}                 "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}                 "failed，code != 0"
// @Router	        /admin/information/delete/id  [post]
func AdminDeleteInformationInfo(c *gin.Context) {
	var (
		req protocol.ReqAdminInformationInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	_, err_ = service.DeleteConferenceInformation(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}

	return
}

// @Summary		    查询公司信息（嘉宾）详情 （2.0修改 赞助商，嘉宾都加上中英文图片和排序）
// @Description	    查询公司信息（嘉宾）详情API
// @Tags			管理后台-通用模块
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqAdminInformationInfo					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.RespConferenceInformationInfo}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						            "failed，code != 0"
// @Router			/admin/information/detail/id    [get]
func AdminQueryInformationInfo(c *gin.Context) {
	var (
		req protocol.ReqAdminInformationInfo
		res protocol.RespConferenceInformationInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetConferenceInformationInfo(req.Id)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}

	return
}

// @Summary		    企业类型下拉列表
// @Description	    企业类型下拉列表API
// @Tags			管理后台-通用模块
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM-ADMIN-TOKEN	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqGetConferenceInformationEventList					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.ResAdminEnterpriseTypeList}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						                "failed，code != 0"
// @Router			/user/enterprise_type/list  [get]
func UserEnterpriseTypeDownList(c *gin.Context) {
	var (
		req protocol.ReqGetConferenceInformationEventList
		res protocol.ResAdminEnterpriseTypeList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}

	if req.ConferenceId == 0 {
		req.ConferenceId = 1
	}

	list, total, err2 := service.AdminEnterpriseTypeDownList(req)
	if err2 != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err2)
		return
	}
	res.List = list
	res.Info.InitPageInfo(total, 0, 0)
	return
}

//// @Summary		    赞助商，嘉宾绑定活动
//// @Description	    赞助商，嘉宾绑定活动API
//// @Tags			管理后台-通用模块
//// @Accept			multipart/form-data
//// @Produce		    json
//// @Param			SMM-ADMIN-TOKEN	header		string				                true    "SMM-ADMIN-TOKEN"
//// @Param			payload		formData	protocol.ReqAdminSaveInformationEvent     true    "json body"
//// @Success		    200			{object}	protocol.Response{}                 "ok，code = 0"
//// @Failure		    500			{object}	protocol.Response{}                 "failed，code != 0"
//// @Router	        /admin/information/event/add_edit  [post]
//func AdminAddEditEventInformation(c *gin.Context) {
//	var (
//		req protocol.ReqAdminSaveInformationEvent
//		err protocol.ResError
//	)
//	defer func() {
//		wrapResp(c, nil, addCallback, err)
//	}()
//	err_ := c.ShouldBind(&req)
//	if err_ != nil {
//		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
//		return
//	}
//	_, err_ = service.AddEditConferenceInformationEvent(req)
//	if err_ != nil {
//		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
//		return
//	}
//	return
//}

// @Summary		    查询栏目信息列表
// @Description	    查询栏目信息列表API
// @Tags			前台-通用模块
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM_auth_token	header		string										false   "SMM_auth_token"
// @Param			request		query		protocol.ReqUserColumnInformationList					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.RespWebColumnInformationList}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						                "failed，code != 0"
// @Router			/user/column_information/list  [get]
func UserQueryColumnInformation(c *gin.Context) {
	var (
		req protocol.ReqUserColumnInformationList
		res protocol.RespWebColumnInformationList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.UserConferenceInformationList(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	return
}

// @Summary		    查询嘉宾信息列表
// @Description	    查询嘉宾信息列表API
// @Tags			前台-通用模块
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM_auth_token	header		string										false   "SMM_auth_token"
// @Param			request		query		protocol.ReqUserGuestInformationList					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.ResWebGuestInformationList}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						                "failed，code != 0"
// @Router			/user/guest_information/list  [get]
func UserQueryGuestInformation(c *gin.Context) {
	var (
		req protocol.ReqColumnInformationList
		res protocol.ResWebGuestInformationList
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	req.Type = 2
	req.Page = 1
	req.PageSize = 1000
	list, _, err_ := service.QueryConferenceInformationList(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res.GuestList = list
	return
}

// @Summary		    查询嘉宾信息详情
// @Description	    查询嘉宾信息详情API
// @Tags			前台-通用模块
// @Accept			multipart/form-data
// @Produce		    json
// @Param			SMM_auth_token	header		string										      true  "SMM-ADMIN-TOKEN"
// @Param			request		query		protocol.ReqUserGuestInformationInfo					  false	"Query Params"
// @Success		    200			{object}	protocol.Response{data=protocol.RespConferenceInformationInfo}	"ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}						            "failed，code != 0"
// @Router			/user/guest_information/detail/id    [get]
func UserQueryInformationInfo(c *gin.Context) {
	var (
		req protocol.ReqUserGuestInformationInfo
		res protocol.RespConferenceInformationInfo
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	res, err_ = service.GetConferenceInformationInfo(req.InformationId)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}

	return
}
