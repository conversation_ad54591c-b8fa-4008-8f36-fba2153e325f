package handler

import (
	"conferencecenter/internal/constant"
	"conferencecenter/internal/corecontext"
	"conferencecenter/internal/errcode"
	"conferencecenter/internal/protocol"
	"conferencecenter/internal/service"
	"fmt"
	"git.code.tencent.com/smmit/smmbase/admin"
	"github.com/pkg/errors"
	"io"
	"net/http"
	"net/url"
	"strings"

	"git.code.tencent.com/smmit/smmbase/logger"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
)

func addCallback(c *gin.Context, result interface{}) (res string) {
	var (
		jResult  []byte
		JsonIter = jsoniter.ConfigCompatibleWithStandardLibrary
	)
	jResult, err := JsonIter.Marshal(result)
	if err != nil {
		_ = logger.Warnning("json marshal failed :", err.Error())
		return
	}

	jQuery := c.Query("callback")
	if jQuery != "" {
		res = jQuery + "(" + string(jResult) + ")"
	} else {
		res = string(jResult)
	}

	c.Writer.Header().Set("Content-Type", "application/json; charset=UTF-8")
	c.Header("Access-Control-Allow-Origin", "*")
	return
}

func lineError(err error) string {
	var errMsg string
	for _, data := range strings.Split(fmt.Sprintf("%+v", err), "\n") {
		if strings.Contains(data, "gin-gonic/gin") {
			break
		}
		errMsg += strings.Trim(data, "") + "&"
	}

	return strings.Trim(errMsg, "&")
}

func wrapResp(c *gin.Context, resp interface{}, callback func(c *gin.Context, result interface{}) string, err protocol.ResError) {
	var result protocol.Response
	if err.HasError() {
		if err.Info == nil {
			_ = logger.Warnning(err.E)
		} else if err.Info.Code == errcode.RESPONSE_CODE_PARAM_ERR || strings.Contains(err.Info.Msg, "参数错误") {
			_ = logger.Warnning(err.E)
		} else {
			_ = logger.Error(lineError(errors.Cause(err.E)))
		}
		if err.Info == nil {
			result = protocol.Response{
				Code: errcode.RESPONSE_CODE_SYSTEM_ERR,
				Msg:  errcode.CodeMsg[errcode.RESPONSE_CODE_SYSTEM_ERR],
				Data: nil,
			}
		} else {
			result = protocol.Response{
				Code:      err.Info.Code,
				Msg:       err.Info.Msg,
				ErrorDesc: err.Info.ErrMsg,
				Data:      nil,
			}
		}
	} else {
		result = protocol.Response{
			Code: errcode.RESPONSE_CODE_SUCCESS,
			Msg:  errcode.CodeMsg[errcode.RESPONSE_CODE_SUCCESS],
			Data: resp,
		}
	}
	c.String(http.StatusOK, callback(c, result))
}

/**
 * 列表excel文件下载
 */
func wrapExcelResp(c *gin.Context, fileName string, contentLen int64, reader io.Reader, err protocol.ResError) {
	if len(fileName) == 0 {
		fileName = "导出数据.xlsx"
	}
	if err.HasError() {
		wrapResp(c, "导出数据异常", addCallback, err)
	} else {
		if !strings.HasSuffix(fileName, ".xlsx") {
			fileName = fileName + ".xlsx"
		}
		//解决中文乱码 //https://zhuanlan.zhihu.com/p/586350394
		fileName = url.QueryEscape(fileName)

		fName := fmt.Sprintf(`attachment; filename*=utf-8''%s`, fileName)
		extraHeaders := map[string]string{
			"Content-Disposition": fName,
		}
		c.DataFromReader(http.StatusOK, contentLen, "application/octet-stream", reader, extraHeaders)
	}
}

func AdminAuthArgs() admin.AuthDecoratorArgs {
	adminUrl := corecontext.Config().RPC.AdminCenter
	Fail := func(c *gin.Context) {
		c.JSON(http.StatusOK,
			map[string]interface{}{
				"code": 100106,
				"msg":  "权限错误",
				"data": nil,
			})
	}

	return admin.AuthDecoratorArgs{AdminCenterHost: adminUrl, FailedHandler: Fail}
}

func AdminAuth(module string, perm int64) gin.HandlerFunc {
	Fail := func(c *gin.Context) {
		c.JSON(http.StatusOK,
			map[string]interface{}{
				"code": 100106,
				"msg":  "权限错误",
				"data": nil,
			})
	}
	return admin.PermDecorator(&admin.Perm{AppName: constant.AppConference, ModName: module, Value: perm}, Fail)
}

func getAdminUser(c *gin.Context) *admin.User {
	a, _ := c.Get("admin_user_info")
	user, _ := a.(*admin.User)
	return user
}

// @Summary		发送验证码
// @Description	发送验证码
// @Tags			前台-通用
// @Accept			multipart/form-data
// @Produce		json
// @Param			AUTH-TOKEN	header		string										    true	"admin_token"
// @Param			req		query			protocol.ReqAddAPiSendSms			        true	"Query Params"
// @Success		200			{object}	protocol.Response{}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/user/send_sms [POST]
func SendSms(c *gin.Context) {
	var (
		req protocol.ReqAddAPiSendSms
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	req.Source = "conference_free_visitor"
	req.CodeType = "conference_from_phone"
	_, err_ = service.SendSms(req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		    表单发送手机验证码
// @Description	    表单发送手机验证码API
// @Tags			表单-申请参观-发送验证码
// @Accept			multipart/form-data
// @Produce		    json
// @Param			payload		formData	protocol.ReqUserAddAPiSendSms true    "form data"
// @Success		    200			{object}	protocol.Response{}         "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}			"failed，code != 0"
// @Router			/user/send/code [post]
func UserAddAPiSendSms(c *gin.Context) {
	var (
		req protocol.ReqUserAddAPiSendSms
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	err_ = service.SaveUserSendSms(req)
	if err_ != nil {
		err = protocol.NewResErrWithMsg(errcode.RESPONSE_CODE_PARAM_ERR, err_.Error(), err_)
	}
	return
}

// @Summary		    表单申请参观
// @Description	    表单申请参观API
// @Tags			表单-申请参观
// @Accept			multipart/form-data
// @Produce		    json
// @Param			payload		formData	protocol.ReqUserApplyVisitSubmit true    "form data"
// @Success		    200			{object}	protocol.Response{}         "ok，code = 0"
// @Failure		    500			{object}	protocol.Response{}			"failed，code != 0"
// @Router			/user/apply/visit/submit [post]
func UserApplyVisitSubmit(c *gin.Context) {
	var (
		req protocol.ReqUserApplyVisitSubmit
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, nil, addCallback, err)
	}()
	err_ := c.ShouldBind(&req)
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_PARAM_ERR, err_)
		return
	}
	err_ = service.SaveUserApplyVisit(req)
	if err_ != nil {
		err = protocol.NewResErrWithMsg(errcode.RESPONSE_CODE_PARAM_ERR, err_.Error(), err_)
	}
	return
}

// @Summary		获取行业信息
// @Description	获取行业信息
// @Tags			通用
// @Accept			multipart/form-data
// @Produce		json
// @Param			AUTH-TOKEN	header		string										    true	"admin_token"
// @Success		200			{object}	protocol.Response{data=[]protocol.IndustryFocus}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/user/get/industry/list [GET]
func UserGetIndustry(c *gin.Context) {
	var (
		res []protocol.IndustryFocus
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()

	res, err_ := service.GetIndustryList()
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		获取行业信息
// @Description	获取行业信息
// @Tags			通用
// @Accept			multipart/form-data
// @Produce		json
// @Param			AUTH-TOKEN	header		string										    true	"admin_token"
// @Success		200			{object}	protocol.Response{data=[]protocol.IndustryFocus}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/user/get/apply/purchase_booth/list [GET]
func UserGetApplyPurchaseBooth(c *gin.Context) {
	var (
		res []protocol.IndustryFocus
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()

	res, err_ := service.GetGetApplyPurchaseBoothList()
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}

// @Summary		获取来源列表
// @Description	获取来源列表
// @Tags			通用-获取来源列表
// @Accept			multipart/form-data
// @Produce		json
// @Param			AUTH-TOKEN	header		string										    true	"admin_token"
// @Success		200			{object}	protocol.Response{data=[]protocol.IndustryFocus}	"ok，code = 0"
// @Failure		500			{object}	protocol.Response{}						        "failed，code != 0"
// @Router			/user/get/apply/sponsor/list [GET]
func UserGetApplySponsorList(c *gin.Context) {
	var (
		res []protocol.IndustryFocus
		err protocol.ResError
	)
	defer func() {
		wrapResp(c, res, addCallback, err)
	}()

	res, err_ := service.GetApplySponsorList()
	if err_ != nil {
		err = protocol.NewResErr(errcode.RESPONSE_CODE_SYSTEM_ERR, err_)
		return
	}
}
