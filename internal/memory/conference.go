package memory

import (
	"conferencecenter/internal/db"
	"conferencecenter/internal/model"
	"conferencecenter/internal/rpc"
	"git.code.tencent.com/smmit/smmbase/logger"
	"github.com/pkg/errors"
	"strconv"
	"sync"
)

var (
	MemConferenceShortNameMap sync.Map //简称映射关系

	MemChannelSourceNameMap sync.Map //平台映射关系

	MemMeetingFromNameMap sync.Map //渠道映射关系

	MemConferenceNameMap sync.Map //简称映射关系
)

// 加载追踪渠道信息内存
func LoadMeetingFromNameMap() {
	_ = logger.Debug("加载追踪渠道信息")

	list, err := db.GetAllMeetingSysList(nil)
	if err != nil {
		logger.Warnning(err.Error())
		return
	}

	for _, meetingSys := range list {
		dataList, err := rpc.GetMeetingChannelList(meetingSys.MeetingSysId)
		if err != nil {
			_ = logger.Error("GetMeetingChannelList:", err)
			continue
		}
		if len(dataList) > 0 {
			UpdateMeetingFromNameMap(meetingSys.ID, dataList)
		}
	}
}

// 更新追踪渠道信息内存
func UpdateMeetingFromNameMap(meetingId int64, dataList []model.MeetingChannel) {
	if len(dataList) == 0 {
		return
	}
	for _, v := range dataList {
		MemMeetingFromNameMap.Store(strconv.FormatInt(meetingId, 10)+"_"+v.ChannelId, v)
	}
}

// 清空追踪渠道信息内存
func ClearMeetingFromNameMap() {
	MemMeetingFromNameMap = sync.Map{}
}

// 查询追踪渠道信息
func GetMeetingFromNameMap(shortName string) model.MeetingChannel {
	value, ok := MemMeetingFromNameMap.Load(shortName)
	if ok {
		pInfo, valid := value.(model.MeetingChannel)
		if valid {
			return pInfo
		} else {
			return model.MeetingChannel{}
		}
	}
	return model.MeetingChannel{}
}

// 加载简称映射关系内存
func LoadConferenceShortNameMap() {
	_ = logger.Debug("加载价格点信息")
	_, dataList, err := db.QueryConferenceShortNameMaps(nil, "", 0, 0)
	if err != nil {
		_ = logger.Error("LoadProductMap:", err)
		return
	}
	ClearConferenceShortNameMap()
	UpdateConferenceShortNameMap(dataList)
}

// 更新简称映射关系内存
func UpdateConferenceShortNameMap(dataList []model.ConferenceShortNameMap) {
	if len(dataList) == 0 {
		return
	}
	for _, v := range dataList {
		MemConferenceShortNameMap.Store(v.ShortName, v)
	}
}

// 清空简称映射关系内存
func ClearConferenceShortNameMap() {
	MemConferenceShortNameMap = sync.Map{}
}

// 查询简称映射关系
func GetConferenceShortNameMap(shortName string, withDb bool) *model.ConferenceShortNameMap {
	value, ok := MemConferenceShortNameMap.Load(shortName)
	if ok {
		pInfo, valid := value.(model.ConferenceShortNameMap)
		if valid {
			return &pInfo
		} else {
			return nil
		}
	} else if withDb {
		total, dataList, err := db.QueryConferenceShortNameMaps(nil, shortName, 1, 1)
		if err != nil {
			_ = logger.Error(errors.WithStack(err), "简称:"+shortName)
			return nil
		} else if total > 0 && len(dataList) <= 0 {
			return &dataList[0]
		}
		return nil
	}
	return nil
}

// 加载渠道追踪来源内存
func LoadChannelSourceNameMap() {
	_ = logger.Debug("加载渠道追踪来源信息")
	dataList, err := rpc.GetMeetingChannelPlatformList()
	if err != nil {
		_ = logger.Error("LoadProductMap:", err)
		return
	}
	UpdateChannelSourceNameMap(dataList)
}

// 更新简称映射关系内存
func UpdateChannelSourceNameMap(dataList []model.MeetingPlatform) {
	if len(dataList) == 0 {
		return
	}
	for _, v := range dataList {
		MemChannelSourceNameMap.Store(v.PlatformId, v.PlatformName)
	}
}

// 查询简称映射关系
func GetChannelSourceNameMap(sourceId string, withDb bool) string {
	value, ok := MemChannelSourceNameMap.Load(sourceId)
	if ok {
		pInfo, valid := value.(string)
		if valid {
			return pInfo
		} else {
			return ""
		}
	} else if withDb {
		dataList, err := rpc.GetMeetingChannelPlatformList()
		if err != nil {
			_ = logger.Error("LoadProductMap:", err)
			return ""
		}
		for _, v := range dataList {
			if v.PlatformId == sourceId {
				return v.PlatformName
			}
			MemChannelSourceNameMap.Store(v.PlatformId, v.PlatformName)
		}
		return ""
	}
	return ""
}

// 加载渠道追踪来源内存
func LoadConferenceNameMap() {
	_ = logger.Debug("加载渠道追踪来源信息")
	_, dataList, err := db.QueryConferenceList(nil, "", true, 1, 5000)
	if err != nil {
		_ = logger.Error("LoadProductMap:", err)
		return
	}
	ClearConferenceNameMap()
	UpdateConferenceNameMap(dataList)
}

// 清空简称映射关系内存
func ClearConferenceNameMap() {
	MemConferenceNameMap = sync.Map{}
}

// 更新简称映射关系内存
func UpdateConferenceNameMap(dataList []model.Conference) {
	if len(dataList) == 0 {
		return
	}
	for _, v := range dataList {
		MemConferenceNameMap.Store(v.ID, v.CnName)
	}
}

// 查询简称映射关系
func GetConferenceNameMap(conferenceId int64, withDb bool) string {
	value, ok := MemConferenceNameMap.Load(conferenceId)
	if ok {
		pInfo, valid := value.(string)
		if valid {
			return pInfo
		} else {
			return ""
		}
	} else if withDb {
		_, dataList, err := db.QueryConferenceList(nil, "", true, 1, 5000)
		if err != nil {
			_ = logger.Error("LoadProductMap:", err)
			return ""
		}
		for _, v := range dataList {
			MemConferenceNameMap.Store(v.ID, v.CnName)
		}
		return ""
	}
	return ""
}
