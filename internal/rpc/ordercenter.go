package rpc

import (
	"conferencecenter/internal/corecontext"
	"conferencecenter/internal/pkg/utils"
	"conferencecenter/internal/protocol"
	"encoding/json"
	"git.code.tencent.com/smmit/smmbase/logger"
)

func GetServiceMeetingTicketPrice(serviceId string) (resp protocol.RespService, err error) {
	// 编辑

	var m protocol.Message

	url := corecontext.Config().RPC.OrderCenter + "/v2/service_list?app_name=&service_ids=" + serviceId
	err = utils.HttpGetCall(url, nil, nil, &m)
	if err != nil {
		return resp, err
	}

	if err = json.Unmarshal(*m.Data, &resp); err != nil {
		logger.Warnning(err.Error())
		return resp, err
	}
	if m.Code != 0 {
		return resp, err
	}
	return resp, nil
}
