package rpc

import (
	"fmt"
	"github.com/go-resty/resty/v2"
	"github.com/pkg/errors"
)

func PostJson(uri string, paramJson interface{}) ([]byte, error) {
	client := resty.New()
	resp, err := client.R().
		SetHeader("Content-Type", "application/json;charset=UTF-8").
		SetBody(paramJson).
		Post(uri)
	if err != nil {
		return nil, errors.New(err.Error())
	}
	if !resp.IsSuccess() {
		return nil, errors.New(fmt.Sprintf("PostJson,StatusCode:%d", resp.StatusCode()))
	}
	return resp.Body(), nil
}

func Get(url string, param map[string]string) ([]byte, error) {
	client := resty.New()
	resp, err := client.R().
		SetQueryParams(param).
		SetHeader("Accept", "application/json").
		Get(url)
	if err != nil {
		return nil, errors.New(err.Error())
	}
	if !resp.IsSuccess() {
		return nil, errors.New(fmt.Sprintf("HttpGet,Statuscode:%d", resp.StatusCode()))
	}
	return resp.Body(), nil
}

func Post(uri string, formData map[string]string) ([]byte, error) {
	client := resty.New()
	resp, err := client.R().
		SetHeader("Content-Type", "multipart/form-data").
		SetFormData(formData).
		Post(uri)
	if err != nil {
		return nil, errors.New(err.Error())
	}
	if !resp.IsSuccess() {
		return nil, errors.New(fmt.Sprintf("HttpPost,StatusCode:%d", resp.StatusCode()))
	}
	return resp.Body(), nil
}
