package rpc

import (
	"bytes"
	"conferencecenter/internal/corecontext"
	"conferencecenter/internal/pkg/utils"
	"conferencecenter/internal/protocol"
	"encoding/json"
	"fmt"
	"git.code.tencent.com/smmit/smmbase/exception"
	"git.code.tencent.com/smmit/smmbase/logger"
	"io/ioutil"
	"net/http"
)

const (
	APiSendSms   = "/v3/send_sms"
	APiCheckCode = "/v3/check_code"
)

func SendSms(req protocol.ReqAddAPiSendSms) (m *protocol.Message, err error) {

	url := corecontext.Config().RPC.VcodeCenter + APiSendSms
	if utils.IsEmpty(corecontext.Config().RPC.VcodeCenter) {
		err = exception.NewException(exception.CodeParamError, "会议系统配置获取失败")
		logger.Error(err)
		return nil, err
	}

	data, err := json.Marshal(req)
	if err != nil {
		fmt.Println("marshal failed!", err)
		return nil, err
	}

	request, err := http.NewRequest("POST", url, bytes.NewBuffer(data))
	if err != nil {
		logger.Warnning(err)
		return m, err
	}
	request.Header.Set("Content-Type", "application/json")
	var client = http.DefaultClient
	response, err := client.Do(request)
	if err != nil {
		logger.Warnning(err)
		return m, err
	}
	defer response.Body.Close()
	bodyData, err := ioutil.ReadAll(response.Body)
	if err != nil {
		logger.Warnning(err)
		return m, err
	}
	err = json.Unmarshal(bodyData, &m)
	if err != nil {
		logger.Warnning("MeetingCenter error:", err.Error())
		return m, err
	}
	return m, nil

}

func CheckCode(req protocol.ReqCheckCode) (m *protocol.Message, err error) {

	url := corecontext.Config().RPC.VcodeCenter + APiCheckCode
	if utils.IsEmpty(corecontext.Config().RPC.MeetingCenter) {
		err = exception.NewException(exception.CodeParamError, "会议系统配置获取失败")
		logger.Error(err)
		return nil, err
	}
	logger.Warnning("MeetingCenter url ------ ", url)

	data, err := json.Marshal(req)
	if err != nil {
		fmt.Println("marshal failed!", err)
		return nil, err
	}

	request, err := http.NewRequest("POST", url, bytes.NewBuffer(data))
	if err != nil {
		logger.Warnning(err)
		return m, err
	}
	request.Header.Set("Content-Type", "application/json")
	var client = http.DefaultClient
	response, err := client.Do(request)
	if err != nil {
		logger.Warnning(err)
		return m, err
	}
	defer response.Body.Close()
	BodyData, err := ioutil.ReadAll(response.Body)
	if err != nil {
		logger.Warnning(err)
		return m, err
	}
	err = json.Unmarshal(BodyData, &m)
	if err != nil {
		logger.Warnning("MeetingCenter error:", err.Error())
		return m, err
	}
	return m, nil

}
