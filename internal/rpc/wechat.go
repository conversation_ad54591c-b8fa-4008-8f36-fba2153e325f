package rpc

import (
	"conferencecenter/internal/db"
	"conferencecenter/internal/model"
	"conferencecenter/internal/pkg/utils"
	"encoding/json"
	"fmt"
	"git.code.tencent.com/smmit/smmbase/logger"
	"github.com/pkg/errors"
	"net/url"
	"os"
	"time"
)

func SendWechatMsg(hookUrl, msg string, err error) error {
	var (
		content = msg
	)
	if err != nil {
		content += "\n>异常:" + err.Error()
	}
	return SendWechatMsgDirect(hookUrl, content)
}

func SendWechatMsgDirect(hookUrl, msg string) error {
	var env = ""
	if os.Getenv("SMM_SERVER_ENV") == "production" {
		env = ">生产"
	} else {
		env = ">测试"
	}
	mdContent := model.WechatContent{
		Content: env + fmt.Sprintf("(时间:%s)\n>消息:", time.Now().Format(`2006-01-02 15:04:05`)) + msg,
	}
	mdMsg := model.WechatMessage{
		MsgType:  "markdown",
		Markdown: mdContent,
	}
	rep, err := PostJson(hookUrl, mdMsg)
	if err != nil {
		_ = logger.Error("wechat_hook_msg_err:", hookUrl, err, msg)
		err = errors.WithMessagef(err, "push wechat msg error for(%s)", hookUrl)
	} else {
		respMsg := struct {
			ErrCode int    `json:"errcode"`
			ErrMsg  string `json:"errmsg"`
		}{}
		err = json.Unmarshal(rep, &respMsg)
		if err != nil {
			_ = logger.Error("wechat_hook_msg_err:", hookUrl, err, msg)
			err = errors.WithMessage(err, "JsonUnmarshal")
		}
	}
	return err
}

func UploadBdVid(pageUrl, token string, formType int) {

	//从url中获取指定参数
	purl, err := url.Parse(pageUrl)
	bdVid := ""
	if err == nil {
		bdVid = purl.Query().Get("bd_vid")
	}

	link := "https://ocpc.baidu.com/ocpcapi/api/uploadConvertData"

	req := UploadDataReq{
		Token: token,
	}
	req.ConversionTypes = append(req.ConversionTypes, ConversionTypeInfo{
		LogidUrl: pageUrl,
		NewType:  3,
	})
	returnMsg := ""
	reqDatas, _ := json.Marshal(&req)
	post, b := utils.JsonPost(link, "", nil, reqDatas)
	if !b {
		if post != nil {
			returnMsg = string(post)
			logger.Error("BdVid err:", string(post))
		}
		logger.Error("BdVid err: post is nil")
	}
	if post != nil {
		returnMsg = string(post)
	}

	err = db.SaveBdVidLog(nil, &model.BdVidLog{
		Vid:        bdVid,
		PageBdUrl:  pageUrl,
		NewsType:   "3",
		FormType:   formType,
		CreateTime: time.Now(),
		ReturnMsg:  returnMsg,
	})
	if err != nil {
		logger.Error(err)
	}

}

type UploadDataReq struct {
	Token           string               `json:"token"`
	ConversionTypes []ConversionTypeInfo `json:"conversionTypes"`
}

type ConversionTypeInfo struct {
	LogidUrl string `json:"logidUrl"`
	NewType  int    `json:"newType"`
}
