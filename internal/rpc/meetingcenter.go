package rpc

import (
	"bytes"
	"conferencecenter/internal/constant"
	"conferencecenter/internal/corecontext"
	"conferencecenter/internal/model"
	"conferencecenter/internal/pkg/utils"
	"conferencecenter/internal/protocol"
	"encoding/json"
	"errors"
	"fmt"
	"git.code.tencent.com/smmit/smmbase/exception"
	"git.code.tencent.com/smmit/smmbase/logger"
	"io/ioutil"
	"net/http"
)

const (
	APiSignUpADDEN                 = "/api/signUp/add_en"
	APiSignUpInterestedParticipant = "/api/signUp/interested_participant"
	APISignUpViewer                = "/api/signUp/viewer"
	PicChannelListByMeeting        = "/api/picChannelListByMeeting"
	PicChannelPlatformList         = "/api/platformList"
	ApiMediaSignUp                 = "/api/media/signUp" //媒体报名
)

type ReqAddAPiSignUpAddEn struct {
	MeetingId int    `json:"meeting_id"`
	UserId    int    `json:"user_id"`
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
	Email     string `json:"email"`
	Mobile    string `json:"mobile"`
	Company   string `json:"company"`
	JobTitle  string `json:"job_title"`
	Country   string `json:"country"`
	Identity  string `json:"identity"`
}

type ReqAPiSignUpOrderChange struct {
	MeetingId int    `json:"meeting_id"`
	UserId    int    `json:"user_id"`
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
	Email     string `json:"email"`
	Mobile    string `json:"mobile"`
	Company   string `json:"company"`
	JobTitle  string `json:"job_title"`
	Country   string `json:"country"`
	Identity  string `json:"identity"`

	PayAmount string `json:"pay_amount"`
	OrderId   string `json:"order_id"`
	ServiceId string `json:"service_id"`
	PayMethod string `json:"pay_method"`

	OrderCount  string `json:"order_count"`
	OrderStatus string `json:"order_status"`
	Source      string `json:"source"`
}

func AddAPiSignUpAddEn(req protocol.ReqAddAPiSignAddEn) (m *protocol.Message, err error) {

	if utils.IsEmpty(corecontext.Config().RPC.MeetingCenter) {
		err = exception.NewException(exception.CodeParamError, "会议系统配置获取失败")
		logger.Error(err)
		return nil, err
	}
	url := corecontext.Config().RPC.MeetingCenter + APiSignUpADDEN

	logger.Warnning("MeetingCenter url ------ ", url)

	data, err := json.Marshal(req)
	if err != nil {
		fmt.Println("marshal failed!", err)
		return nil, err
	}

	request, err := http.NewRequest("POST", url, bytes.NewBuffer(data))
	if err != nil {
		logger.Warnning(err)
		return m, err
	}
	request.Header.Set("Content-Type", "application/json")
	var client = http.DefaultClient
	response, err := client.Do(request)
	if err != nil {
		logger.Warnning(err)
		return m, err
	}
	defer response.Body.Close()
	BodyData, err := ioutil.ReadAll(response.Body)
	if err != nil {
		logger.Warnning(err)
		return m, err
	}
	err = json.Unmarshal(BodyData, &m)
	if err != nil {
		logger.Warnning("MeetingCenter error:", err.Error())
		return m, err
	}
	return m, nil
}

func AddAPiSignUpOrderChange(req protocol.ReqAddAPiSignUpAddEn) (m *protocol.Message, err error) {

	url := corecontext.Config().RPC.MeetingCenter + APiSignUpADDEN
	if utils.IsEmpty(corecontext.Config().RPC.MeetingCenter) {
		err = exception.NewException(exception.CodeParamError, "会议系统配置获取失败")
		logger.Error(err)
		return nil, err
	}
	logger.Warnning("MeetingCenter url ------ ", url)

	data, err := json.Marshal(req)
	if err != nil {
		fmt.Println("marshal failed!", err)
		return nil, err
	}

	request, err := http.NewRequest("POST", url, bytes.NewBuffer(data))
	if err != nil {
		logger.Warnning(err)
		return m, err
	}
	request.Header.Set("Content-Type", "application/json")
	var client = http.DefaultClient
	response, err := client.Do(request)
	if err != nil {
		logger.Warnning(err)
		return m, err
	}
	defer response.Body.Close()
	BodyData, err := ioutil.ReadAll(response.Body)
	if err != nil {
		logger.Warnning(err)
		return m, err
	}
	err = json.Unmarshal(BodyData, &m)
	if err != nil {
		logger.Warnning("MeetingCenter error:", err.Error())
		return m, err
	}
	return m, nil

}

func AddAPiSignUpInterestedParticipant(req protocol.ReqAddAPiSignUpInterestedParticipant) (m *protocol.Message, err error) {

	if utils.IsEmpty(corecontext.Config().RPC.MeetingCenter) {
		err = exception.NewException(exception.CodeParamError, "会议系统配置获取失败")
		logger.Error(err)
		return nil, err
	}
	url := corecontext.Config().RPC.MeetingCenter + APiSignUpInterestedParticipant

	logger.Warnning("MeetingCenter url ------ ", url)

	data, err := json.Marshal(req)
	if err != nil {
		fmt.Println("marshal failed!", err)
		return nil, err
	}

	request, err := http.NewRequest("POST", url, bytes.NewBuffer(data))
	if err != nil {
		logger.Warnning(err)
		return m, err
	}
	request.Header.Set("Content-Type", "application/json")
	var client = http.DefaultClient
	response, err := client.Do(request)
	if err != nil {
		logger.Warnning(err)
		return m, err
	}
	if response.StatusCode != 200 {
		return m, errors.New(response.Status)
	}
	defer response.Body.Close()
	BodyData, err := ioutil.ReadAll(response.Body)
	if err != nil {
		logger.Warnning(err)
		return m, err
	}
	err = json.Unmarshal(BodyData, &m)
	if err != nil {
		logger.Warnning("MeetingCenter error:", err.Error())
		return m, err
	}
	return m, nil
}

type ViewerSignUp struct {
	MeetingNo      string `json:"meeting_no" form:"meeting_no" `
	Name           string `json:"name" form:"name" `
	CompanyName    string `json:"company_name" form:"company_name" `
	Position       string `json:"position" form:"position" `
	CellPhone      string `json:"cellphone" form:"cellphone" `
	Email          string `json:"email" form:"email" `
	FromId         string `json:"fromId" form:"fromId"`                                                       //渠道
	PlatformId     string `json:"platformId" form:"platformId"`                                               //渠道
	Country        string `gorm:"column:country" db:"country" json:"country" form:"country" excel_smm:"国家,M"` //国家
	CnOrEn         int    `json:"cn_or_en" form:"cn_or_en" `                                                  // 0 中文 1 英文
	MainProduct    string `json:"main_product"  form:"main_product"`
	CompanyType    string `json:"company_type"  form:"company_type"`
	PurchaseDemand string `json:"purchase_demand"  form:"purchase_demand"`
	PurchaseVolume string `json:"purchase_volume"  form:"purchase_volume"`
}
type ResViewerSignUp struct {
	Success bool   `json:"success"`
	Code    int    `json:"code"`
	Msg     string `json:"msg"`
	Status  int    `json:"status"`
	Data    string `json:"data"`
}

func SignUpViewer(req ViewerSignUp, cnOrEn int) (string, error) {
	url := corecontext.Config().RPC.MeetingCenter + APISignUpViewer
	logger.Warnning("MeetingCenter SignUpViewer url ------ ", url)
	req.CnOrEn = cnOrEn
	bytes, err := PostJson(url, req)
	if err != nil {
		logger.Error(fmt.Sprintf("观众同步出错：Err->%v;Req->%+v", err, req))
		return "", err
	}
	res := ResViewerSignUp{}
	err = json.Unmarshal(bytes, &res)
	if err != nil {
		logger.Error(fmt.Sprintf("序列化失败:Err->%v", err))
		return "", err
	}
	if res.Code != 0 {
		logger.Error(fmt.Sprintf("同步观众失败:Err->%v;Res->%+v", err, string(bytes)))
		if cnOrEn == constant.Cn {
			return res.Msg, errors.New(res.Msg)
		}
		if cnOrEn == constant.En {
			if res.Status == 1000 {
				return "Server error", errors.New("server error")
			}
			if res.Status == 1001 {
				return "Meeting does not exist", errors.New("meeting does not exist")
			}
			if res.Status == 1002 {
				return "The contact information cannot be empty", errors.New("the contact information cannot be empty")
			}
			if res.Status == 1003 {
				return "The name cannot be empty", errors.New("the name cannot be empty")
			}
			if res.Status == 1004 {
				return "The company cannot be empty", errors.New("the company cannot be empty")
			}
			if res.Status == 1005 {
				return "The position cannot be empty", errors.New("the position cannot be empty")
			}
			if res.Status == 1006 {
				return "The mobile phone number already exists", errors.New("the mobile phone number already exists")
			}
			if res.Status == 1007 {
				return "The mailbox already exists", errors.New("the mailbox already exists")
			}
			if res.Status == 10061 {
				return "The phone number format error!", errors.New("the phone number format error")
			}
			return "Server error", errors.New("server error")
		}
		return "", errors.New(res.Msg)
	}
	return res.Data, nil
}

//
//func GetMeetingChannelList() (resp []model.MeetingChannel, err error) {
//	// 编辑
//	meetingNo := "2024CLNB"
//	if !utils.IsTestEnv() {
//		meetingNo = "2024CLNB"
//	} else {
//		meetingNo = "SMM23110305"
//	}
//	var m protocol.Message
//
//	url := corecontext.Config().RPC.MeetingCenter + PicChannelListByMeeting + "?meetingNo=" + meetingNo
//	err = utils.HttpGetCall(url, nil, nil, &m)
//	if err != nil {
//		return resp, err
//	}
//	if m.Data == nil {
//		return resp, err
//	}
//
//	if err = json.Unmarshal(*m.Data, &resp); err != nil {
//		logger.Warnning(err.Error())
//		return resp, err
//	}
//	if m.Code != 0 {
//		return resp, err
//	}
//	return resp, nil
//}

func GetMeetingChannelList(meetingNo string) (resp []model.MeetingChannel, err error) {

	var m protocol.Message

	params := map[string]string{
		"meetingNo": meetingNo,
	}
	url := corecontext.Config().RPC.MeetingCenter + PicChannelListByMeeting
	err = utils.HttpGetCall(url, params, nil, &m)
	if err != nil {
		return resp, err
	}
	if m.Data == nil {
		return resp, err
	}

	if err = json.Unmarshal(*m.Data, &resp); err != nil {
		logger.Warnning(err.Error())
		return resp, err
	}
	if m.Code != 0 {
		return resp, err
	}
	return resp, nil
}

func GetMeetingChannelPlatformList() (resp []model.MeetingPlatform, err error) {

	var m protocol.Message

	url := corecontext.Config().RPC.MeetingCenter + PicChannelPlatformList
	err = utils.HttpGetCall(url, nil, nil, &m)
	if err != nil {
		return resp, err
	}
	if m.Data == nil {
		return resp, err
	}

	if err = json.Unmarshal(*m.Data, &resp); err != nil {
		logger.Warnning(err.Error())
		return resp, err
	}
	if m.Code != 0 {
		return resp, err
	}
	return resp, nil
}

func AddApiMediaSignUp(req protocol.ReqAddApiMediaSignUp) (m *protocol.Message, err error) {

	if utils.IsEmpty(corecontext.Config().RPC.MeetingCenter) {
		err = exception.NewException(exception.CodeParamError, "会议系统配置获取失败")
		logger.Error(err)
		return nil, err
	}
	url := corecontext.Config().RPC.MeetingCenter + ApiMediaSignUp

	logger.Info("MeetingCenter url ------ ", url)

	//req.CnOrEn = 1
	data, err := json.Marshal(req)
	if err != nil {
		fmt.Println("marshal failed!", err)
		return nil, err
	}

	request, err := http.NewRequest("POST", url, bytes.NewBuffer(data))
	if err != nil {
		logger.Warnning(err)
		return m, err
	}
	request.Header.Set("Content-Type", "application/json")
	var client = http.DefaultClient
	response, err := client.Do(request)
	if err != nil {
		logger.Warnning(err)
		return m, err
	}
	defer response.Body.Close()
	BodyData, err := ioutil.ReadAll(response.Body)
	if err != nil {
		logger.Warnning(err)
		return m, err
	}
	err = json.Unmarshal(BodyData, &m)
	if err != nil {
		logger.Warnning("MeetingCenter error:", err.Error())
		return m, err
	}

	//	markdown2 := fmt.Sprintf(`
	//				## 同步会议系统通知
	//				>会议系统编号名：%v
	//				>购票人first name：%s
	//				>购票人last name：%s
	//				>购票人邮箱：%v
	//				>同步会议返回信息：%v
	//				>时间：%s`, req.MeetingNo, req.FirstName, req.LastName, req.Email, m.Msg, util.GetCurDateTime())
	//	weChat.Send(markdown2, "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b375e7cc-6c99-4ad8-9ff8-70a368985402")
	//
	//	cardMsg := feishu.NewMdCardMsg("同步会议系统通知", "blue")
	//	cardMsg.AppendMd(`>会议系统编号名：%v
	//>购票人first name：%s
	//>购票人last name：%s
	//>购票人邮箱：%v
	//>同步会议返回信息：%v
	//>时间: %s`, req.MeetingNo, req.FirstName, req.LastName, req.Email, m.Msg, util.GetCurDateTime())
	//
	//weChat.FeiShuRobotSend(cardMsg, "https://open.feishu.cn/open-apis/bot/v2/hook/db47eccf-0849-4668-9adc-69f399efeec4")
	return m, nil
}
