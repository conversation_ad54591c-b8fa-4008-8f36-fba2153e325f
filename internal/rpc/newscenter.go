package rpc

import (
	"conferencecenter/internal/corecontext"
	"conferencecenter/internal/protocol"
	"encoding/json"
	"fmt"
	"git.code.tencent.com/smmit/smmbase/logger"
	"github.com/pkg/errors"
	"strconv"
)

const (
	APIGetNewsList     = "/v5/news/set/get_news_list"
	APIGetExpoNewsList = "/get/expo_news_list"
	APIGetNewsContent  = "/news/contentv2/"
)

type ReqGetNews struct {
	ID int `json:"id"`
	protocol.ReqPage
}

type ResNewsList struct {
	CodeMsg CodeMsg `json:"codeMsg"`
	Data    Data    `json:"data"`
}

type CodeMsg struct {
	Msg string `json:"msg"`
}

type Data struct {
	NewsList []News  `json:"news_list"`
	SetInfo  SetInfo `json:"set_info"`
}

type SetInfo struct {
	ID             int    `json:"id"`
	Title          string `json:"title"`
	Describe       string `json:"describe"`
	IsValid        int    `json:"is_valid"`
	URL            string `json:"url"`
	Length         int    `json:"length"`
	Creator        string `json:"creator"`
	LittleURL      string `json:"little_url"`
	NewsUpdateTime int    `json:"news_update_time"`
}

type News struct {
	Author          string `json:"author"`
	Date            string `json:"date"`
	NewsID          int64  `json:"news_id"`
	Keywords        string `json:"keywords"`
	KeywordsEng     string `json:"keywords_eng"`
	Language        string `json:"language"`
	LanguageInt     int    `json:"language_int"`
	ParentType      string `json:"parent_type"`
	ProductType     string `json:"product_type"`
	PubDate         int    `json:"pub_date"`
	Title           string `json:"title"`
	Type            string `json:"type"`
	TypeName        string `json:"type_name"`
	UpdateTime      int    `json:"update_time"`
	IsSet           bool   `json:"is_set"`
	Profile         string `json:"profile"`
	ProductTypeName string `json:"product_type_name"`
	Thumb           string `json:"thumb"`
	NewsURL         string `json:"news_url"`
	Source          string `json:"source"`
}

type CarGetNewsListReply struct {
	Total int64              `json:"total" form:"total"` //总条数
	News  []NewsProfileEntry `json:"news" form:"news"`
}

type NewsProfileEntry struct {
	Author     string `json:"author"`
	NewsId     int    `json:"news_id"`
	IsFocus    bool   `json:"is_focus"`
	IsOrigin   bool   `json:"is_origin"`
	Keywords   string `json:"keywords"`
	Profile    string `json:"profile"`
	PubDate    int    `json:"pub_date"`
	Source     string `json:"source"`
	Title      string `json:"title"`
	Type       string `json:"type"`
	TypeName   string `json:"type_name"`
	UpdateTime int    `json:"update_time"`
	ItemType   string `json:"item_type"`
	Thumb      string `json:"thumb"`
	NewsUrl    string `json:"news_url"`
}
type ResExpoNews struct {
	Code int                 `json:"code"`
	Msg  string              `json:"msg"`
	Data CarGetNewsListReply `json:"data"`
}

type ReqGetExpoNews struct {
	Language string `json:"language"`
	ExpoType string `json:"expo_type"`
	protocol.ReqPage
}

func GetNewsList(req ReqGetNews) (newsList []News, total int64, err error) {
	url := corecontext.Config().RPC.NewsCenter + APIGetNewsList
	bytes, err := Get(url, map[string]string{"set_id": strconv.Itoa(req.ID), "page": strconv.Itoa(req.Page), "number": strconv.Itoa(req.PageSize)})
	if err != nil {
		logger.Error(fmt.Sprintf("查询新闻列表出错：Err->%v;Req->%+v", err, req))
		return nil, 0, err
	}
	res := ResNewsList{}
	err = json.Unmarshal(bytes, &res)
	if err != nil {
		logger.Error(fmt.Sprintf("反序列化新闻数据失败：Err->%v", err))
		return nil, 0, err
	}
	if res.CodeMsg.Msg != "success" {
		return nil, 0, errors.New("查询新闻数据失败")
	}
	return res.Data.NewsList, int64(res.Data.SetInfo.Length), nil
}

func GetExpoNewsList(req ReqGetExpoNews) (list []NewsProfileEntry, total int64, err error) {
	url := corecontext.Config().RPC.NewsCenter + APIGetExpoNewsList
	bytes, err := Get(url, map[string]string{"expo_type": req.ExpoType, "language": req.Language, "page": strconv.Itoa(req.Page), "page_size": strconv.Itoa(req.PageSize)})
	if err != nil {
		logger.Error(fmt.Sprintf("查询新闻列表出错：Err->%v;Req->%+v", err, req))
		return nil, 0, err
	}
	res := ResExpoNews{}
	err = json.Unmarshal(bytes, &res)
	if err != nil {
		logger.Error(fmt.Sprintf("反序列化新闻数据失败：Err->%v", err))
		return nil, 0, err
	}
	if res.Code != 100000 {
		return nil, 0, errors.New("查询行业新闻数据失败")
	}
	return res.Data.News, res.Data.Total, nil
}

type ResNewsContent struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data NewsContent `json:"data"`
}
type NewsContent struct {
	Content  string `json:"content"`
	ID       string `json:"id"`
	KeyWords string `json:"keyWords"`
}

func GetNewsContent(newsID int64) (string, error) {
	url := corecontext.Config().RPC.NewsCenter + APIGetNewsContent + strconv.Itoa(int(newsID))
	bytes, err := Get(url, nil)
	if err != nil {
		logger.Error(fmt.Sprintf("查询新闻列表出错：Err->%v;newsId->%+v", err, newsID))
		return "", err
	}
	res := ResNewsContent{}
	err = json.Unmarshal(bytes, &res)
	if err != nil {
		logger.Error(fmt.Sprintf("反序列化新闻数据失败：Err->%v", err))
		return "", err
	}
	if res.Code != 100000 {
		return "", errors.New(res.Msg)
	}
	return res.Data.Content, nil

}
