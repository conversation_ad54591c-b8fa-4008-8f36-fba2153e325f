package router

import (
	"conferencecenter/internal/constant"
	"conferencecenter/internal/handler"
	"git.code.tencent.com/smmit/smmbase/admin"
	"github.com/gin-gonic/gin"
)

func AdminConferenceIntroduction(r *gin.RouterGroup) {
	cr := r.Group("")
	{
		AdminIntroductionManage(cr)
		AdminIntroductionDataManage(cr)
		AdminConferenceOrganizationManage(cr)
	}
}

func UserConferenceIntroduction(r *gin.RouterGroup) {
	cr := r.Group("/introduction")
	{
		UserIntroductionList(cr)
	}
}

func AdminIntroductionManage(r *gin.RouterGroup) {
	r.GET("/introduction/detail/id",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.AdminQueryConferenceIntroductionInfo)
	r.POST("/introduction/add_edit",
		handler.Admin<PERSON>uth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.AdminAddEditConferenceIntroduction)
}

// AdminIntroductionDataManage 大会数据
func AdminIntroductionDataManage(r *gin.RouterGroup) {
	r.GET("/data/list",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.AdminConferenceIntroductionDataList)
	r.GET("/data/detail/id",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.AdminQueryIntroductionDataInfo)
	r.POST("/data/add_edit",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.AdminAddEditIntroductionData)
	r.POST("/data/delete/id",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.AdminDeleteIntroductionData)
}

// AdminConferenceOrganizationManage 大会组织架构（主办单位，承办单位）
func AdminConferenceOrganizationManage(r *gin.RouterGroup) {
	r.GET("/organization/list",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.AdminConferenceOrganizationList)
	r.GET("/organization/detail/id",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.AdminQueryOrganizationInfo)
	r.POST("/organization/add_edit",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.AdminAddEditOrganization)
	r.POST("/organization/delete/id",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.AdminDeleteOrganization)

	//联系信息 ContactInformation
	r.GET("/contact_information/list",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.AdminContactInformationList)
	r.GET("/contact_information/detail/id",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.AdminQueryContactInformationInfo)
	r.POST("/contact_information/add_edit",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.AdminAddEditContactInformation)
	r.POST("/contact_information/delete/id",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.AdminDeleteContactInformation)

	/////页面底部二维码 bottom_page
	r.GET("/bottom_page/list",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.AdminConferenceBottomPageList)
	r.GET("/bottom_page/detail/id",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.AdminQueryBottomPageInfo)
	r.POST("/bottom_page/add_edit",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.AdminAddEditBottomPage)
	r.POST("/bottom_page/delete/id",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.AdminDeleteBottomPage)
}

func UserIntroductionList(r *gin.RouterGroup) {
	r.GET("/list",
		handler.UserConferenceIntroductionList)
}
