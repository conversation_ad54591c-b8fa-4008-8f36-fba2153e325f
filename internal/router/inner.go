package router

import (
	"conferencecenter/internal/handler"
	"github.com/gin-gonic/gin"
)

func registerInnerRouter(r *gin.RouterGroup) {
	registerHandler(
		r,
		methods("POST", "OPTIONS"),
		"/handbook/send_notify", // 发送展商手册变更通知
		handler.SendHandBookNotify,
	)
	registerHandler(
		r,
		methods("POST", "OPTIONS"),
		"/conference_register/upd",
		handler.UpdateMeetingRegister, //订单购买状态同步
	)

	registerHandler(
		r,
		methods("POST", "OPTIONS"),
		"/conference_ticket_price/update",
		handler.AdminUpdMeetingTicketPrice,
	)
}
