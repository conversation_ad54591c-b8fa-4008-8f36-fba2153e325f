package router

import (
	"conferencecenter/internal/constant"
	"conferencecenter/internal/handler"
	"git.code.tencent.com/smmit/smmbase/admin"
	"github.com/gin-gonic/gin"
)

func AdminConferenceGuideline(cr *gin.RouterGroup) {
	{
		//展馆概况
		HallInfo(cr)
		//酒店住宿
		Hotel(cr)
		//展商手册
		TrafficService(cr)
		//城市概况
		CityInfo(cr)
		//展馆管理
		VenueManagement(cr)
	}
}

func UserConferenceGuideline(cr *gin.RouterGroup) {
	{
		//展馆概况
		UserHallInfo(cr)
		//酒店住宿
		UserHotel(cr)
		//展商手册
		UserTrafficService(cr)
		//现场搭建时间
		UserCityInfo(cr)
	}
}

func CityInfo(r *gin.RouterGroup) {
	r.POST("/save/city/info",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.SaveCityInfo)
	r.GET("/get/city/info",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.GetCityInfo)
}

func UserCityInfo(r *gin.RouterGroup) {
	r.GET("/get/city/info",
		handler.UserGetCityInfo)
}

func TrafficService(r *gin.RouterGroup) {
	r.POST("/save/traffic/service",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.SaveTrafficService)
	r.GET("/get/traffic/service",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.GetTrafficService)
}

func UserTrafficService(r *gin.RouterGroup) {
	r.GET("/get/traffic/service",
		handler.UserGetTrafficService)
}

func Hotel(r *gin.RouterGroup) {
	r.POST("/save/hotel",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.SaveHotel)
	r.GET("/get/hotel",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.GetHotel)
}

func UserHotel(r *gin.RouterGroup) {
	r.GET("/get/hotel",
		handler.UserGetHotel)
}

func HallInfo(r *gin.RouterGroup) {
	r.POST("/save/hall/info",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.SaveHallInfo)
	r.GET("/get/hall/info",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.GetHallInfo)
}

func UserHallInfo(r *gin.RouterGroup) {
	r.GET("/get/hall/info",
		handler.UserGetHallInfo)
}

func VenueManagement(r *gin.RouterGroup) {
	r.GET("/get/venues",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.AdminGetVenues)
	r.POST("/add_edit/venue",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.SaveVenue)
	r.POST("/delete/venue",
		handler.AdminAuth(constant.AuthModuleInfo, admin.UPDATE_PERM),
		handler.DeleteVenue)
}
