package router

import (
	"conferencecenter/internal/corecontext"
	"conferencecenter/internal/mw"
	"fmt"
	"strings"

	"git.code.tencent.com/smmit/smmbase/admin"
	"git.code.tencent.com/smmit/smmbase/monitor"
	"github.com/gin-gonic/gin"
)

func registerHandler(router *gin.RouterGroup, methods []string, path string, handlers ...gin.HandlerFunc) {
	for _, method := range methods {
		method = strings.ToUpper(method)
		if method == "GET" {
			router.GET(
				path,
				handlers...,
			)
		} else if method == "POST" {
			router.POST(
				path,
				handlers...,
			)
		} else if method == "OPTIONS" {
			router.OPTIONS(
				path,
			)
		}
	}
}

func methods(m ...string) []string {
	return m
}

func RegisterAdminRouters(rg *gin.RouterGroup) {
	r := rg.Group("admin", admin.AuthDecorator(mw.AdminAuthArgs()))
	AdminCommonRouter(r)                // 通用
	AdminHomePageRouter(r)              // 首页
	AdminPreviousRouter(r)              // 往届
	AdminAbout(r)                       // 关于
	AdminAudience(r)                    // 观众
	AdminConferenceCompany(r)           // 展商
	AdminConferenceGuideline(r)         // 参会指南
	AdminConferenceIntroduction(r)      // 首页其他信息
	AdminConferenceUniversal(r)         // 通用栏目
	AdminConferenceEvent(r)             // 活动日程
	AdminSponsor(r)                     // 赞助
	AdminConferenceTickets(r)           // 票和报名
	AdminConferenceMediaRegistration(r) // 媒体报名
}

func RegisterUserRouters(rg *gin.RouterGroup) {
	r := rg.Group("user", mw.ClientIp(), mw.UserAuth())
	UserCommonRouter(r)                // 通用
	UserHomePageRouter(r)              // 首页
	UserAudience(r)                    // 观众
	UserAboutUs(r)                     // 关于我们
	UserConferenceCompany(r)           // 展商
	UserConferenceGuideline(r)         // 参会指南
	UserSponsor(r)                     // 赞助
	UserConferenceIntroduction(r)      // 首页其他信息
	UserConferenceEvent(r)             // 活动
	UserConferenceUniversal(r)         // 通用栏目
	UserConferenceTickets(r)           // 票和报名
	UserConferenceMediaRegistration(r) // 媒体报名
}

func StartService(ctx *corecontext.ServiceContext) {
	engine := gin.Default()
	monitor.GinWrap(engine)
	engine.Use(mw.CORS())

	baseGroup := engine.Group(ctx.Conf.App.UrlPrefix)
	if corecontext.Env() != "production" {
		swaggerRouter(baseGroup, ctx)
	}
	baseGroup.Use(mw.ClientIp(), mw.CORS())
	{
		RegisterAdminRouters(baseGroup) // 注册管理后台接口
		RegisterUserRouters(baseGroup)  // 注册用户接口
	}
	addr := fmt.Sprintf("%v:%v", ctx.Conf.Server.Http.Public.Host,
		ctx.Conf.Server.Http.Public.Port)
	if err := engine.Run(addr); err != nil {
		panic(err)
	}
}

func StartInner(ctx *corecontext.ServiceContext) {
	engine := gin.Default()

	baseGroup := engine.Group(ctx.Conf.App.UrlPrefix)
	registerInnerRouter(baseGroup.Group("/inner")) // 注册接口
	addr := fmt.Sprintf("%v:%v", ctx.Conf.Server.Http.Inner.Host,
		ctx.Conf.Server.Http.Inner.Port)
	if err := engine.Run(addr); err != nil {
		panic(err)
	}
}
