package router

import (
	"conferencecenter/internal/constant"
	"conferencecenter/internal/handler"

	"git.code.tencent.com/smmit/smmbase/admin"
	"github.com/gin-gonic/gin"
)

func AdminConferenceEvent(r *gin.RouterGroup) {

	//活动
	cr := r.Group("/event")
	{
		AdminEventCategoryManage(cr)
		AdminEventManage(cr)

	}

	//活动日程
	sc := r.Group("/schedule")
	{
		AdminEventScheduleDateManage(sc)
		AdminEventScheduleForumManage(sc)
		AdminEventScheduleManage(sc)
		AdminEventScheduleGuestManage(sc)
	}

}

func UserConferenceEvent(r *gin.RouterGroup) {
	cr := r.Group("")
	{
		UserEventScheduleList(cr)
	}

	ev := r.Group("event")
	{
		UserEventExpertList(ev)
	}
}

// AdminEventCategoryManage 活动分类
func AdminEventCategoryManage(r *gin.RouterGroup) {
	r.GET("/category/list",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.AdminConferenceEventCategoryList)
	r.GET("/category/detail/id",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.AdminQueryConferenceEventCategoryInfo)
	r.POST("/category/add_edit",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.AdminAddEditConferenceEventCategory)
	r.POST("/category/delete/id",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.AdminDeleteConferenceEventCategory)
}

// AdminEventManage 活动管理
func AdminEventManage(r *gin.RouterGroup) {
	r.GET("/list",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.AdminConferenceEventList)
	r.GET("/detail/id",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.AdminQueryConferenceEventInfo)
	r.POST("/add_edit",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.AdminAddEditConferenceEvent)
	r.POST("/delete/id",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.AdminDeleteConferenceEvent)
	// 批量导入活动日程
	r.POST("/schedule/import",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.AdminImportEventSchedule)
}

func AdminEventScheduleDateManage(r *gin.RouterGroup) {

	r.POST("/date/add_edit",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.AdminAddEditEventScheduleDate)
}

func AdminEventScheduleForumManage(r *gin.RouterGroup) {
	r.GET("/forum/list",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.AdminEventScheduleForumList)
	r.GET("/forum/detail/id",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.AdminQueryEventScheduleForumInfo)
	r.POST("/forum/add_edit",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.AdminAddEditEventScheduleForum)
	r.POST("/forum/delete/id",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.AdminDeleteEventScheduleForum)
}

func AdminEventScheduleManage(r *gin.RouterGroup) {
	r.GET("/list",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.AdminEventScheduleList)
	r.GET("/detail/id",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.AdminQueryEventScheduleInfo)
	r.POST("/add_edit",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.AdminAddEditEventSchedule)
	r.POST("/delete/id",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.AdminDeleteEventSchedule)
}

func AdminEventScheduleGuestManage(r *gin.RouterGroup) {
	r.GET("/guest/list",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.AdminEventScheduleGuestList)
	r.GET("/guest/detail/id",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.AdminQueryEventScheduleGuestInfo)
	r.POST("/guest/add_edit",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.AdminAddEditEventScheduleGuest)
	r.POST("/guest/delete/id",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.AdminDeleteEventScheduleGuest)
}

func UserEventScheduleList(r *gin.RouterGroup) {

	r.GET("/event/list",
		handler.UserConferenceEventList)
	r.GET("/schedule/detail",
		handler.UserQueryConferenceEventInfo)
	r.GET("/schedule/list",
		handler.UserEventScheduleList)
	r.GET("/schedule_forum/list",
		handler.UserEventScheduleForumList)
	r.GET("/forum_category/list",
		handler.UserForumCategoryConfigList)

}

func UserEventExpertList(r *gin.RouterGroup) {

	r.GET("/expert/list",
		handler.UserQueryExpertInfoV3List)

	r.GET("/annual_selection",
		handler.UserAnnualSelectionList)

}
