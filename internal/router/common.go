package router

import (
	"conferencecenter/internal/constant"
	"conferencecenter/internal/handler"
	"git.code.tencent.com/smmit/smmbase/admin"
	"github.com/gin-gonic/gin"
)

func AdminCommonRouter(r *gin.RouterGroup) {
	{
		registerHandler(r, methods("GET"), "/handbook/subscribe/list",
			handler.AdminAuth(constant.AuthModuleSubscribe, admin.READ_PERM),
			handler.AdminQuerySubscribeList) // 查询订阅列表
		registerHandler(r, methods("GET"), "/handbook/subscribe/export",
			handler.AdminAuth(constant.AuthModuleSubscribe, admin.READ_PERM),
			handler.AdminExportSubscribeList) // 导出订阅列表
		registerHandler(r, methods("GET"), "/clue/list",
			handler.AdminAuth(constant.AuthModuleClue, admin.READ_PERM),
			handler.GetConferenceClueList) // 查询线索列表

		r.GET("/register/source/list",
			handler.AdminA<PERSON>(constant.AuthModuleSignUp, admin.READ_PERM),
			handler.AdminGetConferenceSourceList) //来源列表

		registerHandler(r, methods("GET"), "/exhibition/list",
			handler.AdminAuth(constant.AuthModuleClue, admin.READ_PERM),
			handler.GetConferenceExhibitionList) // 查询参展列表
		registerHandler(r, methods("GET"), "/exhibition/export",
			handler.AdminAuth(constant.AuthModuleClue, admin.READ_PERM),
			handler.ExportConferenceExhibitionList) // 导出线索列表
		registerHandler(r, methods("GET"), "/sponsor/list",
			handler.AdminAuth(constant.AuthModuleClue, admin.READ_PERM),
			handler.GetConferenceSponsorList) // 查询参展列表
		registerHandler(r, methods("GET"), "/sponsor/export",
			handler.AdminAuth(constant.AuthModuleClue, admin.READ_PERM),
			handler.ExportConferenceSponsorList) // 导出线索列表

		registerHandler(r, methods("GET"), "/clue/export",
			handler.AdminAuth(constant.AuthModuleClue, admin.READ_PERM),
			handler.ExportConferenceClueList) // 导出线索列表
		registerHandler(r, methods("POST"), "/short_map/add_edit",
			handler.AdminAuth(constant.AuthModuleInfo, admin.UPDATE_PERM),
			handler.AdminAddEditShortMap) // 新增或修改映射关系

		registerHandler(r, methods("GET"), "/from/config/list",
			handler.AdminAuth(constant.AuthModuleClue, admin.READ_PERM),
			handler.AdminConferenceFromConfigList)
		registerHandler(r, methods("GET"), "/from/config/list/v2",
			handler.AdminAuth(constant.AuthModuleClue, admin.READ_PERM),
			handler.AdminConferenceFromConfigListV2)
	}
}

func UserCommonRouter(r *gin.RouterGroup) {
	registerHandler(
		r,
		methods("GET", "OPTIONS"),
		"/common/search", // 搜索内容
		handler.UserCommonSearch,
	)
	registerHandler(
		r,
		methods("GET", "OPTIONS"),
		"/previous/list", // 往届展会列表
		handler.UserQueryPreviousList,
	)
	registerHandler(
		r,
		methods("POST", "OPTIONS"),
		"/handbook/subscribe", // 订阅展商手册
		handler.UserSubscribeHandBook,
	)

	registerHandler(
		r,
		methods("POST", "OPTIONS"),
		"/apply/submit", // 用户提交申请
		handler.UserApplySubmit,
	)

	registerHandler(
		r,
		methods("POST", "OPTIONS"),
		"/apply/purchase_booth/submit", // 购买展览展位
		handler.UserApplyPurchaseBoothSubmit,
	)

	registerHandler(
		r,
		methods("POST", "OPTIONS"),
		"/apply/sponsor/submit", // 用户提交赞助申请
		handler.UserApplySponsorSubmit,
	)

	registerHandler(
		r,
		methods("GET"),
		"/get/venues", // 用户获取场馆信息
		handler.UserGetVenues,
	)

	registerHandler(
		r,
		methods("POST"),
		"/send_sms",
		handler.SendSms)

	registerHandler(
		r,
		methods("POST", "OPTIONS"),
		"/send/code", // 用户提交观众申请
		handler.UserAddAPiSendSms,
	)

	registerHandler(
		r,
		methods("POST", "OPTIONS"),
		"/apply/visit/submit", // 用户提交观众申请
		handler.UserApplyVisitSubmit,
	)

	registerHandler(
		r,
		methods("GET"),
		"/get/industry/list", // 用户获取行业信息
		handler.UserGetIndustry,
	)

	registerHandler(
		r,
		methods("GET"),
		"/get/apply/purchase_booth", // 用户获取行业信息
		handler.UserGetApplyPurchaseBooth,
	)
	registerHandler(
		r,
		methods("GET"),
		"/get/apply/sponsor", // 用户获取行业信息
		handler.UserGetApplySponsorList,
	)

}
