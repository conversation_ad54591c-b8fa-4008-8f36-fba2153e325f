package router

import (
	"conferencecenter/internal/constant"
	"conferencecenter/internal/handler"

	"git.code.tencent.com/smmit/smmbase/admin"
	"github.com/gin-gonic/gin"
)

func AdminHomePageRouter(r *gin.RouterGroup) {
	// 基本信息
	{
		registerHandler(r, methods("GET"), "/conference/list",
			handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
			handler.AdminQueryConferences) // 获取展会列表
		registerHandler(r, methods("GET"), "/conference/name/list",
			handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
			handler.AdminQueryConferenceNameList) // 获取展会列表
		registerHandler(r, methods("GET"), "/conference/:id",
			handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
			handler.AdminQueryConferenceInfo) // 展会基本信息
		registerHandler(r, methods("POST"), "/conference/set_status",
			handler.<PERSON>min<PERSON><PERSON>(constant.AuthModuleInfo, admin.UPDATE_PERM),
			handler.AdminSetConferenceStatus) // 设置展会状态

		registerHandler(r, methods("POST"), "/conference/set_template",
			handler.AdminAuth(constant.AuthModuleInfo, admin.UPDATE_PERM),
			handler.AdminSetConferenceTemplate) // 设置展会状态

		registerHandler(r, methods("POST"), "/conference/add_edit",
			handler.AdminAuth(constant.AuthModuleInfo, admin.UPDATE_PERM),
			handler.AdminAddEditConferenceInfo) // 新增或修改基本信息
	}
}

func AdminPreviousRouter(r *gin.RouterGroup) {
	{
		registerHandler(r, methods("GET"), "/previous/list",
			handler.AdminAuth(constant.AuthModuleInfo, admin.UPDATE_PERM),
			handler.AdminQueryPreviousList) // 查询往届列表
		registerHandler(r, methods("POST"), "/previous/add_edit",
			handler.AdminAuth(constant.AuthModuleInfo, admin.UPDATE_PERM),
			handler.AdminAddEditPreviousInfo) // 新增或修改往届
		registerHandler(r, methods("POST"), "/previous/delete/:id",
			handler.AdminAuth(constant.AuthModuleInfo, admin.UPDATE_PERM),
			handler.AdminDeletePreviousInfo) // 删除往届
	}
}

func UserHomePageRouter(r *gin.RouterGroup) {
	registerHandler(
		r,
		methods("GET", "OPTIONS"),
		"/conference/mapping", // 获取展会映射关系
		handler.UserQueryConferenceMap,
	)
	registerHandler(
		r,
		methods("GET", "OPTIONS"),
		"/conference/detail/:id", // 获取展会基本信息
		handler.UserQueryConferenceInfo,
	)

	registerHandler(
		r,
		methods("GET", "OPTIONS"),
		"/conference/info", // 根据会议编号获取展会基本信息
		handler.UserGetConferenceMeetingNo,
	)
}
