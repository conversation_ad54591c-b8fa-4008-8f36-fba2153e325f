package router

import (
	"conferencecenter/internal/constant"
	"conferencecenter/internal/handler"
	"git.code.tencent.com/smmit/smmbase/admin"
	"github.com/gin-gonic/gin"
)

func AdminConferenceCompany(r *gin.RouterGroup) {
	cr := r.Group("/company")
	{
		//申请参展
		AdminApplyConference(cr)
		//展商在线登记
		AdminOnlineRegister(cr)
		//展商手册
		AdminCompanyHandbook(cr)
		//现场搭建时间
		AdminSetUp(cr)
		//观展邀请函
		AdminInvitation(cr)
		//展商常见问题
		AdminCompanyCommonQuestion(cr)
	}
}

func UserConferenceCompany(r *gin.RouterGroup) {
	cr := r.Group("/company")
	{
		//申请参展
		UserApplyConference(cr)
		//展商在线登记
		UserOnlineRegister(cr)
		//展商手册
		UserCompanyHandbook(cr)
		UserSetUp(cr)
		UserInvitation(cr)
		UserCompanyCommonQuestion(cr)
	}
}

func AdminCompanyCommonQuestion(r *gin.RouterGroup) {
	//问题类型
	QuestionType(r)
	//问题内容
	QuestionContent(r)
}

func UserCompanyCommonQuestion(r *gin.RouterGroup) {
	r.GET("/qa/list",
		handler.UserGetCompanyQA)
}

func QuestionContent(r *gin.RouterGroup) {
	r.POST("/save/question/content",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.SaveCompanyQuestionContent)
	r.GET("/get/question/content/list",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.GetCompanyQuestionAnswerList)
	r.POST("/edit/question/content",
		handler.AdminAuth(constant.AuthModuleInfo, admin.UPDATE_PERM),
		handler.EditCompanyQuestionContent)
	r.POST("/delete/question/content",
		handler.AdminAuth(constant.AuthModuleInfo, admin.DELETE_PERM),
		handler.DeleteCompanyQuestionContent)
	r.GET("/get/question/content",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.GetCompanyQuestionAnswer)
}

func QuestionType(r *gin.RouterGroup) {
	r.POST("/save/question/type",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.SaveCompanyQuestionType)
	r.GET("/get/question/type/list",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.GetCompanyQuestionTypeList)
	r.POST("/delete/question/type",
		handler.AdminAuth(constant.AuthModuleInfo, admin.DELETE_PERM),
		handler.DeleteCompanyQuestionType)
	r.POST("/edit/question/type",
		handler.AdminAuth(constant.AuthModuleInfo, admin.UPDATE_PERM),
		handler.EditCompanyQuestionType)
	r.GET("/get/question/type",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.GetCompanyQuestionType)
}

func AdminInvitation(r *gin.RouterGroup) {
	r.POST("/save/invitation",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.SaveInvitation)
	r.GET("/get/invitation",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.GetInvitation)
}

func UserInvitation(r *gin.RouterGroup) {
	r.GET("/get/invitation",
		handler.UserGetInvitation)
}

func AdminOnlineRegister(r *gin.RouterGroup) {
	r.POST("/save/online/register",
		//handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.SaveOnlineRegister)
	r.GET("/online/register",
		//handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.GetOnlineRegister)
}
func UserOnlineRegister(r *gin.RouterGroup) {
	r.GET("/online/register",
		handler.UserGetOnlineRegister)
}

func AdminApplyConference(r *gin.RouterGroup) {
	r.GET("/apply/conference",
		//handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.GetApplyConference)
	r.POST("/save/apply/conference",
		//handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.SaveApplyConference)

	r.GET("/exhibition/success",
		//handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.GetExhibitionSuccess)
	r.POST("/save/exhibition/success",
		//handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.SaveExhibitionSuccess)
}

func UserApplyConference(r *gin.RouterGroup) {
	r.GET("/apply/conference",
		handler.UserGetApplyConference)

	r.GET("/exhibition/success",
		handler.UserGetExhibitionSuccess)

}

func AdminCompanyHandbook(r *gin.RouterGroup) {
	r.POST("/save/handbook",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.SaveCompanyHandbook)
	r.GET("/handbook/list",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.GetCompanyHandbookList)
	r.POST("/delete/handbook",
		handler.AdminAuth(constant.AuthModuleInfo, admin.DELETE_PERM),
		handler.DeleteCompanyHandbook)
	r.POST("/update/handbook",
		handler.AdminAuth(constant.AuthModuleInfo, admin.UPDATE_PERM),
		handler.UpdateCompanyHandbook)
	r.GET("/get/handbook",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.GetCompanyHandbook)
	r.POST("/save/handbook/content",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.SaveHandbookContent)
	r.GET("/get/handbook/content",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.GetHandbookContent)
}

func UserCompanyHandbook(r *gin.RouterGroup) {
	r.GET("/handbook/list",
		handler.UserGetCompanyHandbookList)
	r.GET("/get/handbook/content",
		handler.UserGetHandbookContent)
}

func AdminSetUp(r *gin.RouterGroup) {
	r.POST("/save/set/up",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.SaveSetUp)
	r.GET("/get/set/up",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.GetSetUp)
}

func UserSetUp(r *gin.RouterGroup) {
	r.GET("/get/set/up",
		handler.UserGetSetUp)
}
