package router

import (
	"conferencecenter/docs"
	"conferencecenter/internal/corecontext"
	"fmt"
	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

func swaggerHost(ctx *corecontext.ServiceContext) (string, string) {
	if corecontext.Env() == "test" {
		return "https", ctx.Conf.App.Domain
	}

	return "http", fmt.Sprintf("0.0.0.0:%v", ctx.Conf.Server.Http.Public.Port)
}

func swaggerRouter(r *gin.RouterGroup, ctx *corecontext.ServiceContext) {
	schema, host := swaggerHost(ctx)
	docs.SwaggerInfo.BasePath = fmt.Sprintf("/%s", ctx.Conf.App.UrlPrefix)
	docs.SwaggerInfo.Host = host

	ctx.Log.Infof("Documentation served at %s://%s/%s/swagger/index.html\n", schema, host, ctx.Conf.App.UrlPrefix)

	r.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
}
