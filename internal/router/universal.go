package router

import (
	"conferencecenter/internal/constant"
	"conferencecenter/internal/handler"
	"git.code.tencent.com/smmit/smmbase/admin"
	"github.com/gin-gonic/gin"
)

func AdminConferenceUniversal(r *gin.RouterGroup) {
	cr := r.Group("/column")
	{
		AdminColumnManage(cr)
	}
	in := r.Group("/information")
	{
		AdminInformationManage(in)
	}

}

func UserConferenceUniversal(r *gin.RouterGroup) {
	cr := r.Group("")
	{
		UserColumnInformationList(cr)

		UserEnterpriseTypeDownList(cr)
	}
}

// AdminColumnManage 栏目信息
func AdminColumnManage(r *gin.RouterGroup) {
	r.GET("/list",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.AdminQueryConferenceColumn)
	r.GET("/detail/id",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.AdminQueryColumnInfo)
	r.POST("/add_edit",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.AdminAddEditColumn)
	r.POST("/delete/id",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.AdminDeleteColumnInfo)
}

// AdminInformationManage 栏目数据信息
func AdminInformationManage(r *gin.RouterGroup) {
	r.GET("/list",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.AdminQueryInformationList)
	r.GET("/detail/id",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.AdminQueryInformationInfo)
	r.POST("/add_edit",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.AdminAddEditInformation)
	r.POST("/delete/id",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.AdminDeleteInformationInfo)

	//r.GET("/event/list",
	//	handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
	//	handler.AdminConferenceEventDownList)
	//r.POST("/event/add_edit",
	//	handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
	//	handler.AdminAddEditEventInformation)
}

func UserColumnInformationList(r *gin.RouterGroup) {
	r.GET("/column_information/list",
		handler.UserQueryColumnInformation)
	r.GET("/guest_information/list",
		handler.UserQueryGuestInformation)

	r.GET("/guest_information/detail/id",
		handler.UserQueryInformationInfo)
}

func UserEnterpriseTypeDownList(r *gin.RouterGroup) {
	r.GET("/enterprise_type/list",
		handler.UserEnterpriseTypeDownList)
}
