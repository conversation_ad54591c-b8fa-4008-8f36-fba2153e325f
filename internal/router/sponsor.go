package router

import (
	"conferencecenter/internal/constant"
	"conferencecenter/internal/handler"
	"git.code.tencent.com/smmit/smmbase/admin"
	"github.com/gin-gonic/gin"
)

func AdminSponsor(r *gin.RouterGroup) {
	cr := r.Group("/sponsor")
	cr.POST("/add_edit",
		handler.<PERSON><PERSON><PERSON><PERSON>(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.AdminAddEditConferenceSponsor)
	cr.GET("/detail/:id",
		handler.<PERSON><PERSON><PERSON><PERSON>(constant.AuthModuleInfo, admin.READ_PERM),
		handler.AdminQueryConferenceSponsorInfo)
	cr.OPTIONS("/detail/:id",
		handler.Admin<PERSON><PERSON>(constant.AuthModuleInfo, admin.READ_PERM),
		handler.AdminQueryConferenceSponsorInfo)
}

func UserSponsor(r *gin.RouterGroup) {
	cr := r.Group("/sponsor")
	cr.GET("/detail/:id",
		handler.UserQueryConferenceSponsorInfo)
}
