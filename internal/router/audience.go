package router

import (
	"conferencecenter/internal/constant"
	"conferencecenter/internal/handler"
	"git.code.tencent.com/smmit/smmbase/admin"
	"github.com/gin-gonic/gin"
)

func AdminAudience(r *gin.RouterGroup) {
	cr := r.Group("/audience") // 企业库
	{
		// 观众预登记
		AdminAudiencePreRegister(cr)
		// 展商管理
		CompanyManagement(cr)
		// 产品管理
		ProductionManagement(cr)
		// 展览平面图
		AdminFloorGraph(cr)
		// 观众常见问题
		AdminAudienceCommonQuestion(cr)
		// 免费观众预约
		AdminFreeVisitor(cr)
	}
}

func UserAudience(r *gin.RouterGroup) {
	cr := r.Group("/audience")
	{
		UserCompanyManagement(cr)
		UserAudienceCommonQuestion(cr)
		UserAudiencePreRegister(cr)
		UserFloorGraph(cr)
		// 免费观众
		UserFreeVisitor(cr)
	}
}

func AdminAudiencePreRegister(r *gin.RouterGroup) {
	r.GET("/pre/register",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.GetAudiencePreRegister)
	r.POST("/record/register",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.RecordAudiencePreRegister)

	r.GET("/visiting/info",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.GetAudienceVisitingValue)
	r.POST("/visiting/value",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.RecordAudienceVisitingValue) //参观价值

}

func UserAudiencePreRegister(r *gin.RouterGroup) {
	r.GET("/pre/register",
		handler.UserGetAudiencePreRegister)

}

func CompanyManagement(r *gin.RouterGroup) {
	r.GET("/company/management",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.GetCompanyManagement)
	r.POST("/record/company",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.RecordCompany)
	r.POST("/edit/company",
		handler.AdminAuth(constant.AuthModuleInfo, admin.UPDATE_PERM),
		handler.EditAudienceConferenceCompany)
	r.GET("/single/company",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.GetSingleAudienceConferenceCompany)
	r.POST("/delete/company",
		handler.AdminAuth(constant.AuthModuleInfo, admin.DELETE_PERM),
		handler.DeleteAudienceConferenceCompany)
	r.POST("/import",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.AdminAudienceImport)
}

// 展馆展品
func ProductionManagement(r *gin.RouterGroup) {
	r.GET("/production/management",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.AdminGetProductionManagement)
	r.POST("/add_edit/production",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.AdminSaveProduction)
	r.POST("/delete/production",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.AdminDeleteProduction)
}

func UserCompanyManagement(r *gin.RouterGroup) {
	r.GET("/company/directories",
		handler.GetCompanyDirectories)
	r.GET("/company/bearing/list",
		handler.UserGetCompanyBearing) // 获取展商风采
	r.GET("/company/bearing",
		handler.UserGetCompanyBearingByID)
	r.GET("/industry/info",
		handler.GetIndustryInfo) // 获取行业资讯
}

func AdminFloorGraph(r *gin.RouterGroup) {
	r.POST("/record/floor/graph",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.RecordFloorGraph)
	r.GET("/floor/graph",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.GetFloorGraphList)
	r.POST("/update/floor/graph",
		handler.AdminAuth(constant.AuthModuleInfo, admin.UPDATE_PERM),
		handler.UpdateFloorGraph)
	r.POST("/delete/floor/graph",
		handler.AdminAuth(constant.AuthModuleInfo, admin.DELETE_PERM),
		handler.DeleteFloorGraph)
	r.GET("/single/floor/graph",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.GetSingleFloorGraph)
}

func UserFloorGraph(r *gin.RouterGroup) {
	r.GET("/floor/graph",
		handler.UserGetFloorGraph)
}

func UserFreeVisitor(r *gin.RouterGroup) {
	r.GET("/paper",
		handler.GetQuestionPaper)
	r.POST("/submit/paper",
		handler.SubmitQuestionPaper)
	r.POST("/submit/free/visitor",
		handler.FreeVisitorSubmit)
}

func AdminAudienceCommonQuestion(r *gin.RouterGroup) {
	r.POST("/save/after/report",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.SaveAfterReport)
	r.GET("/after/report/list",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.GetAfterReportList)
	r.GET("/after/report",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.GetAfterReport)
	r.POST("/delete",
		handler.AdminAuth(constant.AuthModuleInfo, admin.UPDATE_PERM),
		handler.DeleteAfterReport)
	r.POST("/save/paper",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.SavePaperCollection)
	r.GET("/paper/collection",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.GetPaperCollection)
}

func UserAudienceCommonQuestion(r *gin.RouterGroup) {
	r.GET("/after/report/list",
		handler.UserGetAfterReportList)
	r.GET("/paper/collection",
		handler.UserGetPaperCollection)
}

func AdminFreeVisitor(r *gin.RouterGroup) {
	r.GET("/free/visitor",
		handler.GetConferenceFreeVisitorInfo)
	r.GET("/export/free/visitor",
		handler.ExportFreeVisitorInfo)
	r.GET("/fromId",
		handler.GetAllFromID)
}
