package router

import (
	"conferencecenter/internal/constant"
	"conferencecenter/internal/handler"
	"git.code.tencent.com/smmit/smmbase/admin"
	"github.com/gin-gonic/gin"
)

func AdminConferenceTickets(r *gin.RouterGroup) {
	cr := r.Group("/tickets")
	{
		AdminTicketsConfigManage(cr)
		AdminTicketsManage(cr)
		AdminRightsInterestsManage(cr)
	}
	re := r.Group("/register")
	{
		AdminConferenceRegisterManage(re)
	}
}

func UserConferenceTickets(r *gin.RouterGroup) {
	cr := r.Group("")
	{
		UserTicketsRegisterManage(cr)
	}
}

// AdminTicketsManage 票种信息
func AdminTicketsManage(r *gin.RouterGroup) {
	r.GET("/list",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.AdminTicketPriceList)
	r.GET("/detail/id",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.AdminQueryTicketPriceInfo)
	r.POST("/add_edit",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.AdminAddEditTicketPrice)
	r.POST("/delete/id",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.AdminDeleteTicketPrice)

	r.GET("/rights/list",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.AdminGetMeetingRightsTicketList)
	r.POST("/rights/add",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.AdminAddEditRightTicket)

}

// AdminTicketsConfigManage 票种信息
func AdminTicketsConfigManage(r *gin.RouterGroup) {
	r.GET("/config/detail/id",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.AdminQueryTicketConfigInfo)
	r.POST("/config/add_edit",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.AdminAddEditTicketConfig)
}

// AdminRightsInterestsManage 权益信息
func AdminRightsInterestsManage(r *gin.RouterGroup) {
	r.GET("/rights_interests/list",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.AdminRightsInterestsList)

	r.GET("/rights_interests/detail/id",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.AdminQueryRightInterestsInfo)
	r.POST("/rights_interests/add_edit",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.AdminAddEditRightInterests)
	r.POST("/rights_interests/delete/id",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.AdminDeleteRightInterests)
}

// AdminConferenceRegisterManage 报名信息
func AdminConferenceRegisterManage(r *gin.RouterGroup) {
	r.GET("/list",
		handler.AdminAuth(constant.AuthModuleSignUp, admin.READ_PERM),
		handler.AdminGetConferenceRegisterList)
	r.GET("/user/list",
		handler.AdminAuth(constant.AuthModuleSignUp, admin.READ_PERM),
		handler.AdminGetConferenceRegisterUserList)
	r.GET("/export",
		handler.AdminAuth(constant.AuthModuleSignUp, admin.READ_PERM),
		handler.AdminExportConferenceRegisterList)

}

func UserTicketsRegisterManage(r *gin.RouterGroup) {
	r.GET("/tickets/list",
		handler.UserTicketPriceList)
	r.POST("/register/add",
		handler.UserAddConferenceRegister)
	r.GET("/register/info",
		handler.UserConferenceRegisterInfo)
	r.GET("/forum_config/list",
		handler.UserForumConfigList)

}
