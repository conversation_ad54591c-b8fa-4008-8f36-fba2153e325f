package router

import (
	"conferencecenter/internal/constant"
	"conferencecenter/internal/handler"
	"git.code.tencent.com/smmit/smmbase/admin"
	"github.com/gin-gonic/gin"
)

func AdminAbout(cr *gin.RouterGroup) {
	{
		//关于我们
		AdminAboutUs(cr)
		//联系我们
		AdminContactUs(cr)
	}
}

func UserAboutUs(r *gin.RouterGroup) {
	r.GET("/get/contact/us",
		handler.UserGetContactUs)
	r.GET("/get/about/us",
		handler.UserGetAboutUs)
}

func AdminContactUs(r *gin.RouterGroup) {
	r.POST("/save/contact/us",
		handler.AdminA<PERSON>(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.SaveContactUs)
	r.GET("/get/contact/us",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.GetContactUsList)
}

func AdminAboutUs(r *gin.RouterGroup) {
	r.POST("/save/about/us",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.SaveAboutUs)
	r.GET("/get/about/us",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.AdminGetAboutUs)
}
