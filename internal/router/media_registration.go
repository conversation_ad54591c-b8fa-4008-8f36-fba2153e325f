package router

import (
	"conferencecenter/internal/constant"
	"conferencecenter/internal/handler"
	"git.code.tencent.com/smmit/smmbase/admin"
	"github.com/gin-gonic/gin"
)

func AdminConferenceMediaRegistration(r *gin.RouterGroup) {
	cr := r.Group("media_registration")
	{
		AdminMediaRegistrationManage(cr)
	}
}

func UserConferenceMediaRegistration(r *gin.RouterGroup) {
	cr := r.Group("/media_registration")
	{
		UserMediaRegistrationList(cr)
	}
}

func AdminMediaRegistrationManage(r *gin.RouterGroup) {

	r.GET("list",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.AdminMediaRegistrationList)

	r.GET("/config/detail",
		handler.AdminAuth(constant.AuthModuleInfo, admin.READ_PERM),
		handler.AdminQueryMediaRegistrationInfo)

	r.POST("/config/add_edit",
		handler.AdminAuth(constant.AuthModuleInfo, admin.CREATE_PERM),
		handler.AdminAddEditMediaRegistrationConfig)

	r.GET("/cn/export",
		handler.AdminAuth(constant.AuthModuleSignUp, admin.READ_PERM),
		handler.CnAdminExportMediaRegistrationList)

	r.GET("/en/export",
		handler.AdminAuth(constant.AuthModuleSignUp, admin.READ_PERM),
		handler.EnAdminExportMediaRegistrationList)
}

func UserMediaRegistrationList(r *gin.RouterGroup) {

	r.GET("/config/detail",
		handler.AdminQueryMediaRegistrationInfo)

	r.POST("/cn/add",
		handler.CnUserAddMediaRegistration)

	r.POST("/en/add",
		handler.EnUserAddMediaRegistration)

}
