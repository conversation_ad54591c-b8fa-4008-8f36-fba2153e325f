package corecontext

import (
	"conferencecenter/internal/conf"
	"conferencecenter/internal/pkg/cache"
	"conferencecenter/internal/pkg/logger"
	"conferencecenter/internal/pkg/mysql"
	"github.com/jmoiron/sqlx"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

var (
	SrvContext *ServiceContext
)

type ServiceContext struct {
	Conf     *conf.Configuration
	Log      *zap.SugaredLogger
	Orm      *gorm.DB
	EventOrm *gorm.DB
	Sqlx     *sqlx.DB
	Cache    *cache.Cache
}

func InitSvcContext(cfg *conf.Configuration) *ServiceContext {
	SrvContext = &ServiceContext{
		Conf:     cfg,
		Log:      logger.Init(logger.WithLevel(cfg.Log.Level)),
		Orm:      mysql.Init(&cfg.Data.Mysql),
		EventOrm: mysql.Init(&cfg.Data.MysqlEvent),
		Cache:    cache.Init(&cfg.Data.Redis),
	}

	return SrvContext
}

func Env() string {
	return SrvContext.Conf.App.Env
}

func Orm() *gorm.DB {
	if SrvContext == nil {
		panic("ServiceContext is not initialized!")
	}
	return SrvContext.Orm
}

func EventOrm() *gorm.DB {
	if SrvContext == nil {
		panic("ServiceContext is not initialized!")
	}
	return SrvContext.EventOrm
}

func Sqlx() *sqlx.DB {
	if SrvContext == nil {
		panic("ServiceContext is not initialized!")
	}
	return SrvContext.Sqlx
}

func Config() *conf.Configuration {
	if SrvContext == nil {
		panic("ServiceContext is not initialized!")
	}
	return SrvContext.Conf
}

func Log() *zap.SugaredLogger {
	if SrvContext == nil {
		panic("ServiceContext is not initialized!")
	}
	return SrvContext.Log
}

func Cache() *cache.Cache {
	if SrvContext == nil {
		panic("ServiceContext is not initialized!")
	}
	return SrvContext.Cache
}

func IsMainNode() bool {
	return SrvContext.Conf.ClientId == 1
}
